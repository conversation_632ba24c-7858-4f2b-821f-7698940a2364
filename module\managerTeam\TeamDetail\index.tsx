import {TextInput} from "@app/components/TextInput";
import {Col, Row, notification} from "antd";
import {Formik} from "formik";
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import ApiTeam, {IEmployee, IResTeamDetail} from "@app/api/ApiTeam";
import {useMutation, useQuery} from "react-query";
import {useRouter} from "next/router";
import AppLoading from "@app/components/AppLoading";
import AppAccessDenied from "@app/components/AppAccessDenied";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/lib/table";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppButton from "@app/components/AppButton";
import ModalDetailEmployee from "../ModalDetailEmployee";
import {IAccountRole, OptionSelect} from "@app/types";
import {statusEmployee} from "@app/utils/constants/state";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import AppModalConfirm from "@app/components/AppModalConfirm";
import config from "@app/config";

export default function TeamDetail(): JSX.Element {
  const router = useRouter();
  const timeOut = useRef<any>();
  const [employeeId, setEmployeeId] = useState(-1);
  const {user} = useSelector(selectUser);
  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);
  const [isShowModalConfirmDelete, setIsShowModalConfirmDelete] =
    useState<boolean>(false);

  let id: string;
  if (router.query.id) {
    id = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    id = Object.fromEntries(searchParams)?.id;
  }

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const getDetailTeam = useQuery(
    ["getDetailTeam", id],
    () => {
      return ApiTeam.getDetailTeam(id);
    },
    {
      enabled: !!id,
      onError: (error: any): void => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            router.back();
          }, 4000);
        }
      },
    }
  );

  const deleteGroup = useMutation(
    (id: string) => {
      return ApiTeam.deleteGroup(id);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Xóa nhóm thành công",
        });
      },
    }
  );

  const detailTeam = getDetailTeam.data;
  const listEmployee = (getDetailTeam.data?.employeeGroupList || [])?.filter(
    (i) => i.userId !== getDetailTeam.data?.groupLeaderId
  );
  const sortedListEmployeeByStatusName = (data: IEmployee[]): any => {
    return (
      data?.sort((a, b) => {
        if (a.statusName === "Hoạt động" && b.statusName !== "Hoạt động") {
          return -1;
        }
        if (a.statusName !== "Hoạt động" && b.statusName === "Hoạt động") {
          return 1;
        }
        return 0;
      }) ?? []
    );
  };

  const handleDeleteGroup = (): void => {
    deleteGroup.mutate(id);
    setIsShowModalConfirmDelete(false);
  };

  if (getDetailTeam.isLoading) {
    return (
      <div className=" h-[80vh]">
        <AppLoading />
      </div>
    );
  }

  if (getDetailTeam?.error?.errorCode === 400) {
    return (
      <div className="p-12">
        <AppAccessDenied />
      </div>
    );
  }

  const columns: ColumnsType<IEmployee> = [
    {
      title: "Tên nhân viên",
      dataIndex: "name",
      key: "name",
      className: "cursor-pointer",
      onCell: (item: IEmployee): any => {
        return {
          onClick: (): void => {
            setEmployeeId(item.userId);
          },
        };
      },
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
    },
    {
      title: "Trạng thái tài khoản",
      dataIndex: "statusName",
      key: "statusName",
      render: (_, item: IEmployee): JSX.Element => {
        const getStatusEmployee = (statusName: string): OptionSelect => {
          return (
            statusEmployee.find((item) => item.label === statusName) || {
              label: "",
              value: "",
              color: "white",
            }
          );
        };

        return (
          <span
            className="status"
            style={{backgroundColor: getStatusEmployee(item?.statusName).color}}
          >
            {getStatusEmployee(item?.statusName).label}
          </span>
        );
      },
    },
    {
      title: "Ngày thêm vào nhóm",
      dataIndex: "groupJoinDate",
      key: "groupJoinDate",
      render: (_, item: IEmployee) => (
        <span>
          {item.groupJoinDate
            ? moment(item.groupJoinDate).format(DATE_FORMAT)
            : ""}
        </span>
      ),
    },
  ];

  return (
    <div className="container-team-detail">
      <h5 className="font-bold text24 text-color-primary">
        Chi tiết nhóm nhân viên
      </h5>
      <Formik
        initialValues={detailTeam as IResTeamDetail}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          return (
            <div className="card-information">
              <span className="font-bold text16">Thông tin chung</span>
              <Row gutter={[64, 32]} className="mt-4">
                <Col xs={12}>
                  <TextInput
                    label="Tên nhóm"
                    name="name"
                    value={detailTeam?.name}
                    disabled
                  />
                </Col>
                <Col xs={12}>
                  <TextInput
                    label="Loại nhóm"
                    name="roleTypeName"
                    disabled
                    value={detailTeam?.roleTypeName}
                  />
                </Col>
              </Row>
              <Row gutter={[64, 32]} className="mt-6">
                <Col xs={12}>
                  <TextInput
                    label="Trưởng nhóm"
                    name="groupLeaderName"
                    disabled
                    value={detailTeam?.groupLeaderName}
                  />
                </Col>
              </Row>
            </div>
          );
        }}
      </Formik>
      <div className="mt-5">
        <span className="font-bold text16 text-color-primary">
          Danh sách nhân viên
        </span>
        <div className="mt-4">
          <AppTable
            dataSource={sortedListEmployeeByStatusName(listEmployee).map(
              (item: IEmployee, index: number) => ({
                ...item,
                key: index,
              })
            )}
            columns={
              isAdmin
                ? columns
                : columns.filter((item) => item.key !== "action")
            }
            scroll={{y: "32vh"}}
          />
        </div>
      </div>

      <Row className="justify-center items-center mt-6">
        {/* {isAdmin && (
          <AppButton
            classrow="w-48 btn-delete-group"
            label="Xóa nhóm"
            typebutton="normal"
            onClick={(): void => setIsShowModalConfirmDelete(true)}
          />
        )} */}
        <AppButton
          classrow="w-48 mx-8"
          label="Trở lại"
          typebutton="secondary"
          onClick={(): void => {
            router.back();
          }}
        />
        {isAdmin && (
          <AppButton
            classrow="w-48"
            label="Chỉnh sửa"
            typebutton="primary"
            onClick={(): void => {
              router.push(config.PATHNAME.TEAM_EDIT_PARAM(id));
            }}
          />
        )}
      </Row>

      <ModalDetailEmployee
        employeeId={employeeId}
        handleClose={(): void => setEmployeeId(-1)}
        groupId={id}
        detailTeam={detailTeam}
        getDetailTeam={getDetailTeam}
      />
      <AppModalConfirm
        open={isShowModalConfirmDelete}
        content="Bạn có chắc chắn muốn xóa nhóm?"
        title="Xác nhận xóa nhóm"
        onCancel={(): void => setIsShowModalConfirmDelete(false)}
        onOk={handleDeleteGroup}
      />
    </div>
  );
}
