import AppButton from "@app/components/AppButton";
import AppCollapse from "@app/components/AppCollapse";
import AppDatePicker from "@app/components/AppDatePicker";
import AppModal from "@app/components/AppModal";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {
  getMonthWarrantySelected,
  moneyToNumber,
} from "@app/utils/constants/function";
import {
  deadTimeFastSearch,
  optionGuarantee,
  statusRateCurrency,
} from "@app/utils/constants/state";
import {Col, Row, notification} from "antd";
import CollapsePanel from "antd/lib/collapse/CollapsePanel";
import {Formik, FormikProps} from "formik";
import moment from "moment";
import React, {useCallback, useEffect, useRef, useState} from "react";
import "./index.scss";
import ApiCustomerPayment, {
  ICustomerPayment,
  IResFilter,
} from "@app/api/ApiCustomerPayment";
import {OptionSelect} from "@app/types";
import {useDispatch} from "react-redux";
import {UseQueryResult, useMutation} from "react-query";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {messageValidate} from "@app/utils/constants/message";

interface FormPayment extends ICustomerPayment {
  monthTrailWorkSelected: OptionSelect;
  currencyOfferedTypeSelected: OptionSelect;
  paymentRateTypeSelected: OptionSelect;
  salaryConvert?: number;
  payAmount?: number;
  ratePay?: number;
  amountRemaining?: number;
  paymentMethod: string;
  paymentDate: string;
}

interface IProps {
  open?: boolean;
  closeModal: () => void;
  getCustomerPayment: UseQueryResult<IResFilter, unknown>;
  getCustomerPaymentDetail: UseQueryResult<ICustomerPayment, any>;
}
export default function ModalUpdateCustomerPayment(props: IProps): JSX.Element {
  const {open, closeModal, getCustomerPayment, getCustomerPaymentDetail} =
    props;
  const refPayment = useRef<FormikProps<FormPayment>>(null);
  const timeOutChangeAmountPay = useRef<any>();
  const dispatch = useDispatch();
  const customerPayment = getCustomerPaymentDetail?.data;
  const paymentTimes = (customerPayment?.listPaymentHistory?.length || 0) + 1;

  const [isShowPayment, setIsShowPayment] = useState(paymentTimes > 1);
  useEffect(() => {
    setIsShowPayment(paymentTimes > 1);
  }, [paymentTimes]);

  const salaryConvert =
    customerPayment?.salaryOffered && customerPayment?.salaryOfferedExchangeRate
      ? customerPayment.salaryOffered *
        customerPayment.salaryOfferedExchangeRate
      : 0;
  const disabledPaymentInfo = customerPayment?.listPaymentHistory?.length !== 0;

  const updateCustomerPayment = useMutation(
    (param: ICustomerPayment) => {
      return ApiCustomerPayment.updateCustomerPayment(param);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Lưu thông tin thành công",
        });
        getCustomerPayment.refetch();
        getCustomerPaymentDetail.refetch();
        onCloseModal();
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  useEffect(() => {
    refPayment.current?.setValues(initialForm);
  }, [JSON.stringify(customerPayment)]);

  useEffect(() => {
    return () => {
      if (timeOutChangeAmountPay.current) {
        clearTimeout(timeOutChangeAmountPay.current);
      }
    };
  }, [timeOutChangeAmountPay.current]);

  const onCloseModal = (): void => {
    refPayment.current?.setValues(initialForm);
    closeModal();
  };

  const onChangeShow = (key: any): void => {
    setIsShowPayment(key?.length > 0);
  };

  const updatePayment = (): void => {
    const values = refPayment.current?.values;
    if (
      !(
        values?.onboardDate &&
        values?.monthTrailWorkSelected.value &&
        moneyToNumber(String(values?.salaryOffered)) &&
        values?.paymentRate
      ) ||
      (values?.currencyOfferedTypeSelected?.value !== "VND" &&
        !values?.salaryOfferedExchangeRate) ||
      (isShowPayment &&
        !(
          values?.payAmount &&
          values?.paymentMethod.trim() &&
          values?.paymentDate
        ))
    ) {
      notification.error({
        message: "Thông báo",
        description: messageValidate,
        duration: 3,
      });
      return;
    }

    if (
      moneyToNumber(String(values.payAmount)) >
      moneyToNumber(String(values.amountRemaining))
    ) {
      notification.error({
        message: "Thông báo",
        description: "Số tiền thanh toán phải nhỏ hơn số tiền cần thanh toán.",
        duration: 3,
      });
      return;
    }

    const amountRemain = isShowPayment
      ? moneyToNumber(String(values.amountRemaining)) -
        moneyToNumber(String(values.payAmount))
      : moneyToNumber(String(values.amountRemaining));

    const param: ICustomerPayment = {
      ...(customerPayment || ({} as ICustomerPayment)),
      onboardDate: moment(values.onboardDate).format(DATE_FORMAT),
      salaryOffered: moneyToNumber(String(values.salaryOffered)),
      salaryOfferedExchangeRate:
        moneyToNumber(String(values.salaryOfferedExchangeRate)) || 1,
      paymentRate: moneyToNumber(String(values.paymentRate)) || 1,
      paymentDateExpected: values?.paymentDateExpected
        ? moment(values.paymentDateExpected).format(DATE_FORMAT)
        : "",
      monthTrailWork: Number(values.monthTrailWorkSelected?.value),
      vat: values.vat,
      amount: values.amount,
      salesTransacted: values?.salesTransacted,
      amountPaid: isShowPayment
        ? (customerPayment?.amountPaid || 0) +
          moneyToNumber(String(values.payAmount))
        : customerPayment?.amountPaid || 0,
      amountRemain,
      listPaymentHistoryAddNew: isShowPayment
        ? [
            {
              amount: moneyToNumber(String(values.payAmount)),
              paymentDate: moment(values.paymentDate).format(DATE_FORMAT),
              paymentMethod: values.paymentMethod,
            },
          ]
        : [],
      status: amountRemain === 0 ? 2 : customerPayment?.status || 0,
    };
    updateCustomerPayment.mutate(param);
  };

  const onChangePayAmount = (e: any): void => {
    const amountPaid = e.target.value;
    const amountRemaining = moneyToNumber(
      String(refPayment.current?.values?.amountRemaining)
    );
    if (timeOutChangeAmountPay.current) {
      clearTimeout(timeOutChangeAmountPay.current);
    }
    timeOutChangeAmountPay.current = setTimeout(() => {
      const ratePay = Math.floor(
        (moneyToNumber(String(amountPaid)) * 100) / amountRemaining
      );
      refPayment.current?.setFieldValue("ratePay", ratePay);
    }, deadTimeFastSearch);
  };

  const initialForm: FormPayment = {
    ...(customerPayment || ({} as ICustomerPayment)),
    onboardDate: customerPayment?.onboardDate
      ? moment(customerPayment?.onboardDate, DATE_FORMAT)
      : "",
    paymentDateExpected: customerPayment?.paymentDateExpected
      ? moment(customerPayment?.paymentDateExpected, DATE_FORMAT)
      : "",
    currencyOfferedTypeSelected: customerPayment?.currencyOfferedType
      ? {
          value: customerPayment.currencyOfferedType,
          label: customerPayment?.currencyOfferedType,
        }
      : {value: "VND", label: "VND"},
    paymentRateTypeSelected:
      customerPayment?.paymentRateType === "0"
        ? statusRateCurrency[0]
        : statusRateCurrency[1],
    salaryConvert,
    monthTrailWorkSelected: getMonthWarrantySelected(
      customerPayment?.monthTrailWork
    ),
    amountRemaining: customerPayment?.amountRemain || 0,
    payAmount: 0,
    ratePay: 0,
    paymentDate: "",
    paymentMethod: "",
    amountPaid: customerPayment?.amountPaid || 0,
  };

  return (
    <AppModal
      className="update-customer-payment-modal"
      open={open}
      footer={null}
      onCancel={onCloseModal}
      width="65%"
      title="Cập nhật thông tin thanh toán"
    >
      <Formik
        innerRef={refPayment}
        initialValues={initialForm}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            const timeOut = setTimeout(() => {
              const salaryOffered = values?.salaryOffered
                ? moneyToNumber(String(values?.salaryOffered))
                : null;

              const rate =
                values.currencyOfferedTypeSelected.value !== "VND" &&
                values?.salaryOfferedExchangeRate
                  ? moneyToNumber(String(values?.salaryOfferedExchangeRate))
                  : 1;

              const salaryConvert = salaryOffered
                ? Number(salaryOffered) * rate
                : 0;

              let salesTransacted = 0;

              if (values?.paymentRateTypeSelected?.value === "0") {
                salesTransacted =
                  salaryConvert * moneyToNumber(String(values?.paymentRate));
              } else {
                salesTransacted = moneyToNumber(String(values?.paymentRate));
              }

              const amount =
                salesTransacted +
                salesTransacted * moneyToNumber(String(values?.vat)) * 0.01;

              const amountRemain = amount - (values?.amountPaid || 0);

              if (values.currencyOfferedTypeSelected.value === "VND") {
                refPayment.current?.setFieldValue(
                  "salaryOfferedExchangeRate",
                  1
                );
              }
              const value: FormPayment = {
                ...(refPayment.current?.values as FormPayment),
                salaryConvert,
                salesTransacted,
                amountRemaining: amountRemain,
                amount,
                payAmount: 0,
                ratePay: 0,
              };
              refPayment.current?.setValues(value);
            }, deadTimeFastSearch);

            return () => {
              clearTimeout(timeOut);
            };
          }, [
            values?.salaryOffered,
            values?.salaryOfferedExchangeRate,
            values?.currencyOfferedTypeSelected.value,
            values?.paymentRateTypeSelected?.value,
            values?.paymentRate,
            values?.vat,
          ]);

          // eslint-disable-next-line react-hooks/rules-of-hooks
          const disabledPaymentDate = useCallback(
            (current: any): boolean => {
              if (!values.onboardDate) {
                return false;
              }
              return (
                current && current < moment(values.onboardDate).endOf("day")
              );
            },
            [values.onboardDate]
          );

          const onChangeOnboardDate = (): void => {
            refPayment.current?.setFieldValue("paymentDateExpected", "");
            refPayment.current?.setFieldValue("paymentDate", "");
          };

          return (
            <div className="text-color-primary px-10">
              <div className="payment-info">
                <p className="font-bold text16">Thông tin thanh toán</p>
                <Row className="justify-center mt-1">
                  <Col className="pr-4" span={10}>
                    <AppDatePicker
                      classNameContainer="mt-2"
                      name="onboardDate"
                      label="Ngày onboard"
                      format={DATE_FORMAT}
                      valueAppDatePicker={values?.onboardDate}
                      free={!values?.onboardDate}
                      required
                      status={values?.onboardDate ? undefined : "error"}
                      disabled={disabledPaymentInfo}
                      allowClear={false}
                      onChange={onChangeOnboardDate}
                    />
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Lương offer"
                        name="salaryOffered"
                        onlynumber
                        iscurrency
                        value={values?.salaryOffered}
                        free={!values?.salaryOffered}
                        typeInput="salary"
                        maxLength={50}
                        required
                        status={values?.salaryOffered ? undefined : "error"}
                        disabled={disabledPaymentInfo}
                      />
                      <AppSelectCurrency
                        name="currencyOfferedTypeSelected"
                        value={values?.currencyOfferedTypeSelected}
                        style={{width: "90px"}}
                        disabled={disabledPaymentInfo}
                      />
                    </Row>
                    <TextInput
                      containerclassname="mt-2"
                      label="Tỷ giá"
                      name="salaryOfferedExchangeRate"
                      onlynumber
                      iscurrency
                      value={values?.salaryOfferedExchangeRate}
                      free={!values?.salaryOfferedExchangeRate}
                      typeInput="salary"
                      maxLength={50}
                      disabled={
                        values?.currencyOfferedTypeSelected?.value === "VND" ||
                        disabledPaymentInfo
                      }
                      required={
                        values?.currencyOfferedTypeSelected?.value !== "VND"
                      }
                      status={
                        values?.currencyOfferedTypeSelected?.value !== "VND" &&
                        !values?.salaryOfferedExchangeRate
                          ? "error"
                          : undefined
                      }
                    />
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Số tiền quy đổi được"
                        name="salaryConvert"
                        onlynumber
                        value={values?.salaryConvert}
                        free={!values?.salaryConvert}
                        typeInput="salary"
                        iscurrency
                        maxLength={50}
                        disabled
                      />
                      <AppSelectCurrency
                        name="currencyConvert"
                        value="VND"
                        style={{width: "90px"}}
                        disabled
                      />
                    </Row>
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Rate khách hàng"
                        name="paymentRate"
                        onlynumber
                        value={values?.paymentRate}
                        free={!values?.paymentRate}
                        typeInput={
                          values?.paymentRateTypeSelected?.value === "1"
                            ? "salary"
                            : ""
                        }
                        iscurrency={
                          values?.paymentRateTypeSelected?.value === "1"
                        }
                        maxLength={50}
                        required
                        status={values?.paymentRate ? undefined : "error"}
                        disabled={disabledPaymentInfo}
                      />
                      <AppSelectCurrency
                        name="paymentRateTypeSelected"
                        value={values?.paymentRateTypeSelected}
                        style={{width: "90px"}}
                        options={statusRateCurrency}
                        disabled={disabledPaymentInfo}
                      />
                    </Row>
                  </Col>
                  <Col className="pl-4" span={10}>
                    <AppDatePicker
                      classNameContainer="mt-2"
                      name="paymentDateExpected"
                      label="Ngày thanh toán dự kiến"
                      format={DATE_FORMAT}
                      valueAppDatePicker={values?.paymentDateExpected}
                      free={!values?.paymentDateExpected}
                      disabledDate={disabledPaymentDate}
                    />
                    <SelectInput
                      containerclassname="mt-2"
                      name="monthTrailWorkSelected"
                      labelselect="Số tháng thử việc"
                      data={optionGuarantee}
                      value={values?.monthTrailWorkSelected}
                      required
                      disabled={disabledPaymentInfo}
                    />
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="VAT"
                        name="vat"
                        onlynumber
                        value={values?.vat}
                        free={!values?.vat}
                        maxLength={2}
                        disabled={disabledPaymentInfo}
                      />
                      <AppSelectCurrency
                        name=""
                        value="%"
                        style={{width: "90px"}}
                        disabled
                      />
                    </Row>
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Doanh thu"
                        name="salesTransacted"
                        value={values?.salesTransacted}
                        free={!values?.salesTransacted}
                        typeInput="salary"
                        iscurrency
                        maxLength={100}
                        disabled
                        required
                      />
                      <AppSelectCurrency
                        name="currencyTotalAmount"
                        value="VND"
                        style={{width: "90px"}}
                        disabled
                      />
                    </Row>
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Tổng tiền"
                        name="amount"
                        value={values?.amount}
                        free={!values?.amount}
                        typeInput="salary"
                        iscurrency
                        maxLength={100}
                        disabled
                        required
                      />
                      <AppSelectCurrency
                        name="currencyTotalAmount"
                        value="VND"
                        style={{width: "90px"}}
                        disabled
                      />
                    </Row>
                  </Col>
                </Row>
              </div>
              <div className="payment-info">
                <AppCollapse
                  defaultActiveKey={paymentTimes === 1 ? [] : ["1"]}
                  onChange={onChangeShow}
                >
                  <CollapsePanel
                    header={`Thanh toán lần ${paymentTimes}`}
                    key="1"
                  >
                    <Row className="justify-center">
                      <Col className="pr-4" span={10}>
                        <Row className="mt-2 input-salary">
                          <TextInput
                            containerclassname="flex-1"
                            label="Số tiền cần thanh toán"
                            name="amountRemaining"
                            onlynumber
                            value={values.amountRemaining}
                            typeInput="salary"
                            iscurrency
                            maxLength={50}
                            disabled
                          />
                          <AppSelectCurrency
                            name=""
                            value="VND"
                            style={{width: "90px"}}
                            disabled
                            showArrow={false}
                          />
                        </Row>
                        <Row className="mt-2 input-salary">
                          <TextInput
                            containerclassname="flex-1"
                            label="Số tiền thanh toán"
                            name="payAmount"
                            onlynumber
                            iscurrency
                            value={values?.payAmount}
                            typeInput="salary"
                            maxLength={50}
                            required
                            status={values?.payAmount ? undefined : "error"}
                            onChange={onChangePayAmount}
                          />
                          <AppSelectCurrency
                            name="ratePay"
                            value={`${values?.ratePay}%`}
                            style={{width: "90px"}}
                            showArrow={false}
                            disabled
                          />
                        </Row>
                      </Col>
                      <Col className="pl-4" span={10}>
                        <TextInput
                          containerclassname="mt-2"
                          label="Hình thức thanh toán"
                          name="paymentMethod"
                          value={values?.paymentMethod}
                          required
                          status={values?.paymentMethod ? undefined : "error"}
                        />
                        <AppDatePicker
                          classNameContainer="mt-2"
                          name="paymentDate"
                          label="Ngày thanh toán"
                          format={DATE_FORMAT}
                          valueAppDatePicker={values?.paymentDate}
                          free={!values?.paymentDate}
                          required
                          status={values?.paymentDate ? undefined : "error"}
                          disabledDate={disabledPaymentDate}
                        />
                      </Col>
                    </Row>
                  </CollapsePanel>
                </AppCollapse>
              </div>
              <Row className="flex justify-center items-center mt-5">
                <AppButton
                  classrow="mr-2 w-64 btn-cancel"
                  label="Hủy"
                  typebutton="primary"
                  onClick={onCloseModal}
                />
                <AppButton
                  classrow="ml-2 w-64 btn-edit"
                  label="Cập nhật"
                  typebutton="primary"
                  onClick={updatePayment}
                />
              </Row>
            </div>
          );
        }}
      </Formik>
    </AppModal>
  );
}
