import config from "@app/config";
import {IAccountRole} from "../types";
import Icon from "@app/components/Icon/Icon";
import Image from "next/image";

export enum GroupNavbarKey {
  CANDIDATE = "CANDIDATE",
  CUSTOMER = "CUSTOMER",
  UTILITY = "UTILITY",
}

export interface IRoute {
  path: string;
  name: string;
  role?: Array<IAccountRole>;
  icon?: React.ReactNode;
  isSidebar?: boolean;
  isPrivate?: boolean;
  isPublic?: boolean;
  isUpdating?: boolean;
  isAuth?: boolean;
  isSSR?: boolean;
  children?: IRoute[];
  isLandingPage?: boolean;
  group?: GroupNavbarKey;
}

const routes: IRoute[] = [
  {
    path: config.PATHNAME.HOME,
    name: "Landing Page",
    icon: <Icon size={16} icon="file-user-line" />,
    isSSR: true,
    isSidebar: false,
    isPrivate: false,
    isPublic: true,
    isLandingPage: true,
  },
  {
    path: config.PATHNAME.JOB_PUBLIC_DETAIL,
    name: "Landing Page",
    icon: <Icon size={16} icon="file-user-line" />,
    isSSR: true,
    isSidebar: false,
    isPrivate: false,
    isPublic: true,
    isLandingPage: true,
  },
  {
    path: config.PATHNAME.MANAGER_REPORT,
    name: "Báo cáo",
    icon: <Icon size={16} icon="icon-report" />,
    isSSR: true,
    isSidebar: true,
    role: [IAccountRole.CSL, IAccountRole.ADMIN],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.MANAGER_APPLICATION,
    name: "Vị trí ứng tuyển",
    icon: <Icon size={16} icon="file-user-line" />,
    isSSR: true,
    isSidebar: true,
    role: [
      IAccountRole.CTV,
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.MANAGER_COLLABORATOR,
    name: "Quản lý CTV",
    role: [IAccountRole.CSL, IAccountRole.CST, IAccountRole.ADMIN],
    icon: <Icon size={16} icon="user-add-line" />,
    isPrivate: true,
    isSidebar: true,
  },
  {
    path: config.PATHNAME.COLLABORATOR_DETAIL,
    name: "Quản lý CTV",
    role: [IAccountRole.CSL, IAccountRole.CST, IAccountRole.ADMIN],
    icon: <Icon size={16} icon="group-line" />,
    isPrivate: true,
    isSidebar: false,
  },
  {
    path: config.PATHNAME.MANAGER_CANDIDATE,
    name: "Ứng viên của tôi",
    icon: <Icon size={16} icon="group-line" />,
    isSidebar: true,
    role: [
      IAccountRole.CTV,
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
    ],
    isPrivate: true,
    group: GroupNavbarKey.CANDIDATE,
  },
  {
    path: config.PATHNAME.MANAGER_POTENTIAL_CANDIDATE,
    name: "Ứng viên tiềm năng",
    icon: <Icon size={16} icon="user-star-line" />,
    isSidebar: true,
    role: [
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
    group: GroupNavbarKey.CANDIDATE,
  },
  {
    path: config.PATHNAME.CANDIDATE_DETAIL,
    name: "Quản lý ứng viên",
    icon: <Icon size={16} icon="group-line" />,
    isSidebar: false,
    role: [
      IAccountRole.CTV,
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
    ],
    isPrivate: true,
  },
  // {
  //   path: config.PATHNAME.DATA_POOL,
  //   name: "Data pool",
  //   icon: <Icon size={16} icon="user-add-line" />,
  //   isSidebar: true,
  //   isPrivate: true,
  //   role: [IAccountRole.ADMIN, IAccountRole.CSL, IAccountRole.CST],
  //   group: GroupNavbarKey.CANDIDATE,
  // },
  {
    path: config.PATHNAME.MANAGER_COMPANY,
    name: "Danh sách công ty",
    icon: <Icon size={16} icon="company-building" />,
    isSSR: true,
    isSidebar: true,
    role: [
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
      IAccountRole.CSL,
      IAccountRole.CST,
    ],
    isPrivate: true,
    group: GroupNavbarKey.UTILITY,
  },
  {
    path: config.PATHNAME.MANAGER_REQUEST,
    name: "Danh sách request",
    icon: <Icon size={16} icon="file-user-line" />,
    isSidebar: true,
    role: [
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
    group: GroupNavbarKey.CUSTOMER,
  },
  {
    path: config.PATHNAME.MANAGER_JD,
    name: "Tạo JD",
    icon: (
      <Image
        src="/img/bag.svg"
        width={16}
        height={16}
        alt="icon"
        // style={{marginRight: "10px"}}
      />
    ),
    isSidebar: true,
    role: [
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
    group: GroupNavbarKey.UTILITY,
  },
  {
    path: config.PATHNAME.MANAGER_CUSTOMER,
    name: "Quản lý khách hàng",
    icon: <Icon size={16} icon="grommet-icons-user-manager" />,
    isSidebar: true,
    role: [
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.ADMIN,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
    group: GroupNavbarKey.CUSTOMER,
  },
  {
    path: config.PATHNAME.CUSTOMER_DETAIL,
    name: "Quản lý khách hàng",
    icon: <Icon size={16} icon="grommet-icons-user-manager" />,
    isSidebar: false,
    role: [
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.ADMIN,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.CUSTOMER_CREATE,
    name: "Quản lý khách hàng",
    icon: <Icon size={16} icon="grommet-icons-user-manager" />,
    isSidebar: false,
    role: [
      IAccountRole.AML,
      IAccountRole.ADMIN,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.CUSTOMER_EDIT,
    name: "Quản lý khách hàng",
    icon: <Icon size={16} icon="grommet-icons-user-manager" />,
    isSidebar: false,
    role: [IAccountRole.AML, IAccountRole.BD, IAccountRole.BDL],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.CUSTOMER_PAYMENT,
    name: "Thanh toán khách hàng",
    icon: <Icon size={16} icon="money-cash-bag" stroke="#324054" />,
    isSidebar: true,
    role: [
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.ADMIN,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.MANAGER_REQUEST_ADD,
    name: "Danh sách request",
    icon: <Icon size={16} icon="file-user-line" />,
    isSidebar: false,
    role: [
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.MANAGER_REQUEST_EDIT,
    name: "Danh sách request",
    icon: <Icon size={16} icon="file-user-line" />,
    isSidebar: false,
    role: [
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.MANAGER_REQUEST_DETAIL,
    name: "Danh sách request",
    icon: <Icon size={16} icon="file-user-line" />,
    isSidebar: false,
    role: [
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
      IAccountRole.CSL,
      IAccountRole.CST,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.REQUEST_JOB,
    name: "Jobs",
    icon: <Icon size={16} icon="file-list-line" />,
    isSidebar: true,
    role: [
      IAccountRole.CTV,
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
    group: GroupNavbarKey.CUSTOMER,
  },
  {
    path: config.PATHNAME.JOB_DETAIL,
    name: "Jobs",
    icon: <Icon size={16} icon="file-list-line" />,
    isSidebar: false,
    role: [
      IAccountRole.CTV,
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.JOB,
    name: "Jobs",
    icon: <Icon size={16} icon="file-list-line" />,
    isSidebar: false,
    role: [
      IAccountRole.CTV,
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.ADMIN,
      IAccountRole.AML,
      IAccountRole.AMG,
    ],
    isPrivate: true,
  },
  // {
  //   path: "/notification",
  //   name: "Thông báo",
  //   icon: <Icon size={16} icon="notification-line" />,
  //   isSidebar: true,
  //   role: [IAccountRole.CTV, IAccountRole.CSL],
  //   isPrivate: true,
  // },
  {
    path: config.PATHNAME.MANAGER_TEAM,
    name: "Quản lý team",
    icon: <Icon size={16} icon="team-line" />,
    isSidebar: true,
    role: [
      IAccountRole.AML,
      IAccountRole.ADMIN,
      IAccountRole.CSL,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.TEAM_EDIT,
    name: "Quản lý team",
    icon: <Icon size={16} icon="team-line" />,
    isSidebar: false,
    role: [IAccountRole.ADMIN],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.TEAM_CREATE,
    name: "Quản lý team",
    icon: <Icon size={16} icon="team-line" />,
    isSidebar: false,
    role: [IAccountRole.ADMIN],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.TEAM_DETAIL,
    name: "Quản lý team",
    icon: <Icon size={16} icon="team-line" />,
    isSidebar: false,
    role: [
      IAccountRole.AML,
      IAccountRole.ADMIN,
      IAccountRole.CSL,
      IAccountRole.BDL,
    ],
    isPrivate: true,
  },
  {
    path: config.PATHNAME.MANAGER_BONUS,
    name: "Quản lý bonus",
    icon: <Icon size={16} icon="hand-coin-line" />,
    isSidebar: true,
    isPrivate: true,
    role: [
      IAccountRole.ADMIN,
      IAccountRole.CTV,
      IAccountRole.CSL,
      IAccountRole.CST,
    ],
  },
  {
    path: config.PATHNAME.REGISTER,
    name: "Đăng ký",
    isSidebar: false,
    isPrivate: false,
    isPublic: true,
  },
  {
    path: config.PATHNAME.RULE_BONUS,
    name: "Quản lý bonus",
    icon: <Icon size={16} icon="hand-coin-line" />,
    isSidebar: false,
    isPrivate: true,
    role: [IAccountRole.ADMIN],
  },
  {
    path: config.PATHNAME.REGISTER_WITH_CODE,
    name: "Đăng ký",
    isSidebar: false,
    isPrivate: false,
    isPublic: true,
  },
  {
    path: config.PATHNAME.MANAGER_JOB_SUGGESTION_COLLABORATORS,
    name: "Gợi ý job cho CTV",
    icon: <Icon size={16} icon="mail-line" />,
    isSidebar: true,
    isPrivate: true,
    role: [IAccountRole.ADMIN, IAccountRole.CSL, IAccountRole.CST],
    group: GroupNavbarKey.UTILITY,
  },
  {
    path: config.PATHNAME.LINKEDIN_SEARCH,
    name: "Linkedin search",
    icon: <Icon size={16} icon="search-icon" />,
    isSidebar: true,
    isPrivate: true,
    role: [
      IAccountRole.ADMIN,
      IAccountRole.CSL,
      IAccountRole.CST,
      IAccountRole.AMG,
      IAccountRole.AML,
      IAccountRole.CTV,
      IAccountRole.BD,
      IAccountRole.BDL,
    ],
    group: GroupNavbarKey.UTILITY,
  },
  // {
  //   path: config.PATHNAME.CANDIDATE_JAPAN,
  //   name: "Ứng viên Nhật Bản",
  //   icon: <Icon size={16} icon="temple" />,
  //   isSidebar: true,
  //   isPrivate: true,
  //   role: [IAccountRole.ADMIN, IAccountRole.CSL, IAccountRole.CST],
  //   group: GroupNavbarKey.CANDIDATE,
  // },
  {
    path: config.PATHNAME.FORGOT_PASSWORD,
    name: "Quên mật khẩu",
    isSidebar: false,
    isPrivate: false,
    isPublic: true,
  },
  {
    path: config.PATHNAME.PREVIEW_CV,
    name: "Chi tiết CV",
    isSidebar: false,
    isPrivate: false,
    isPublic: true,
  },
];

export default routes;
