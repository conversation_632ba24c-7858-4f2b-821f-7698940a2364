# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
/tsconfig.tsbuildinfo

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.development.local
.env.test.local
.env.production.local

# env files
.env
.env.development
.env.production

# lock files
yarn.lock
package-lock.json

/iisnode
