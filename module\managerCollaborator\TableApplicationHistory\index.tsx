import ApiApplication, {IApplication} from "@app/api/ApiApplication";
import {ICollaborator} from "@app/api/ApiCollaborator";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import ModalDetailApplication from "@app/module/managerApplication/ModalDetailApplication";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {ColumnsType} from "antd/lib/table";
import moment from "moment";
import React, {useEffect, useState} from "react";
import {useQuery} from "react-query";
import "./index.scss";
import {IAccountRole} from "@app/types";

interface TableApplicationHistoryProps {
  collaborator?: ICollaborator;
}
export default function TableApplicationHistory(
  props: TableApplicationHistoryProps
): JSX.Element {
  const {collaborator} = props;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [applicationId, setApplicationId] = useState<number>(-1);
  const [isShowDetail, setIsShowDetail] = useState<boolean>(false);
  const dataListApplications = useQuery(
    ["requestJobList", currentPage, pageSize],
    () => {
      return ApiApplication.getListApplications({
        pageSize: pageSize,
        currentPage: currentPage,
        partnerId: collaborator?.userId,
        isAdvanceSearch: true,
        roleFilter: IAccountRole.CSL,
      });
    },
    {
      enabled: false,
    }
  );

  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  useEffect(() => {
    if (collaborator?.userId) {
      dataListApplications.refetch();
    }
  }, [collaborator]);

  const showModalDetail = (id: number): void => {
    setApplicationId(id);
    setIsShowDetail(true);
  };

  const closeModalDetail = (): void => {
    setIsShowDetail(false);
  };

  const columns: ColumnsType<IApplication> = [
    {
      title: "STT",
      dataIndex: "",
      key: "stt",
      render: (_, item: IApplication, index: number) => (
        <span className="text-base text12">{index + 1}</span>
      ),
      width: "70px",
    },
    {
      title: "Application",
      dataIndex: "applicationName",
      key: "applicationName",
      className: "cursor-pointer",
      onCell: (record: IApplication): any => {
        return {
          onClick: (): void => {
            showModalDetail(record?.applicationId);
          },
        };
      },
    },
    {
      title: "Vị trí",
      dataIndex: "positionName",
      key: "positionName",
    },
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
    },
    {
      title: "Trạng thái",
      key: "statusName",
      dataIndex: "statusName",
      render: (_, {stageName, statusName}: IApplication): JSX.Element => {
        let status = "";
        if (stageName) {
          status += stageName;
        }

        if (statusName) {
          status += " > " + statusName;
        }

        return (
          <span className={`status-cv ${status.length === 0 ? "hidden" : ""}`}>
            {status}
          </span>
        );
      },
    },
    {
      title: "Ngày tạo",
      dataIndex: "createDate",
      key: "createDate",
      render: (_, {createdDate}: IApplication) => (
        <span>{moment(createdDate).format(DATE_FORMAT)}</span>
      ),
    },
  ];

  return (
    <div className="table-application-history">
      <AppTable
        dataSource={dataListApplications?.data?.applicationsPaging?.map(
          (item: IApplication, index: number) => ({
            ...item,
            key: index,
          })
        )}
        columns={columns}
        loading={dataListApplications.isLoading}
        scroll={{y: "25vh"}}
      />
      <AppPagination
        className="mt-3"
        defaultPageSize={pageSize}
        current={currentPage}
        pageSize={pageSize}
        total={dataListApplications.data?.totalCount}
        onChange={handlePagination}
      />
      <ModalDetailApplication
        dataListApplications={dataListApplications}
        applicationId={applicationId}
        open={isShowDetail}
        closeModal={closeModalDetail}
        typeRoleUsed={IAccountRole.CSL}
      />
    </div>
  );
}
