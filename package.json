{"name": "nextjs-core", "version": "1.0.0", "author": {"email": "<EMAIL>", "name": "Thanh Do"}, "private": true, "scripts": {"bootstrap": "yarn --fronzen-lockfile", "dev": "next dev -p 8000", "build": "next build", "start": "next start -p 5000", "lint": "eslint .", "lint:fix": "eslint --fix .", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "check-types": "tsc --pretty --noEmit", "pre-commit": "yarn lint && yarn prettier && yarn check-types", "test-all": "yarn lint && yarn prettier && yarn check-types && yarn build", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@ckeditor/ckeditor5-build-classic": "^35.1.0", "@ckeditor/ckeditor5-react": "5.0.2", "@fortawesome/fontawesome-free": "^5.15.4", "@patternfly/react-charts": "^7.2.2", "@reduxjs/toolkit": "^1.8.4", "antd": "^4.24.1", "autoprefixer": "^10.4.13", "axios": "^1.1.3", "classnames": "^2.3.2", "firebase": "^10.0.0", "formik": "^2.2.9", "formik-antd": "^2.0.4", "i18next": "^22.0.4", "icomoon-react": "2.0.19", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "13.0.2", "postcss": "^8.4.16", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^12.0.0", "react-is": "^17.0.2", "react-pdf": "^7.5.1", "react-query": "^3.39.2", "react-redux": "^8.0.5", "react-responsive": "^10.0.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "sass": "^1.54.4", "styled-components": "^5.3.6", "swiper": "^9.3.2", "tailwindcss": "^3.2.2", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/plugin-syntax-flow": "^7.18.6", "@babel/plugin-transform-react-jsx": "^7.19.0", "@commitlint/cli": "^15.0.0", "@commitlint/config-conventional": "^15.0.0", "@next/bundle-analyzer": "13.0.2", "@next/eslint-plugin-next": "^12.2.6", "@types/file-saver": "^2.0.5", "@types/lodash": "^4.14.180", "@types/node": "^18.7.5", "@types/react": "^18.0.25", "@types/react-dom": "^18.0.8", "@types/react-redux": "^7.1.24", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.33.1", "eslint": "^8.23.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "13.0.2", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.1", "prettier": "^2.7.1", "typescript": "^4.8.4"}}