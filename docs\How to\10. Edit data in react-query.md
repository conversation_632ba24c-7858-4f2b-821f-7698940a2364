# Sửa dữ liệu trong react-query

Trong một số trường hợp chúng ta muốn sửa dữ liệu trong react-query để render thay vì gọi lại API để lấy dữ liệu thì sẽ sử dụng hàm `queryClient.setQueryData`

```tsx
const handleChangeFollowing = (action: boolean): void => {
  const followData = followUser?.data;
  if (followData) {
    followData.isFollow = action;
  }

  queryClient.setQueryData(
    "followUser",  // Key của query data muốn sửa
    (queryData) =>  // Trả về dữ liệu đã sửa trong hàm này. queryData là data cũ trước khi sửa
      queryData && { // Nếu mà queryData = null thì tự trả về null
        ...followData,  // Nếu có queryData thì sẽ thay bằng data của mình
      }
  );
};
```
