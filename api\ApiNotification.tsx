import {fetcher} from "./Fetcher";

export interface IParamNotificationSave {
  ApplicationId?: number;
  ApplicationName?: string;
  NotiType?: string;
  AmountPaid?: number;
  PaymentId?: number;
  PositionName?: string;
  CandidateName?: string;
  PartnerName?: string;
}

const path = {
  list: "/api/notification/save",
};

function createNotification(body?: IParamNotificationSave): Promise<any> {
  return fetcher({url: path.list, method: "post", data: body});
}

export default {
  createNotification,
};
