.container-list-candidate {
  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .container-filter-candidate {
    border: 1px dashed $header_tf;
    border-radius: 15px;
    margin-top: 10px;
  }

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .btn-cancel {
    button {
      background-color: $red-color;
    }
  }

  .filter-salary-expect {
    input {
      border-radius: 8px 0px 0px 8px;
    }
  }

  .div-add-cv {
    color: $text-color-input;
  }

  .list-candidate {
    height: 62vh;
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 12px;
    border: 1px solid $header_tf05;
    border-radius: 8px;
    padding: 0px 10px;
  }
}

.modal-detail-candidate {
  .green-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: $green_color;
  }

  .ant-modal-body {
    padding: 0;
  }

  .ant-upload {
    width: 100%;
  }
}
