# Tổng quan

- Language: Typescript.
- Framework: NextJs.
- Form & Validation: formik-antd và yup được dùng để tạo và validate form.
- Package manager: Project này sử dụng yarn, nếu xuất hiện file `package-lock.json` trong project tức là đang sử dụng **SAI** package manager.
- UI component: Project sử dụng Ant Design để có các component như: Select, Modal, Table, ...
- HTTP client: Sử dụng axios kết hợp với React-query để lấy dữ liệu từ server
- CSS: Sử dụng tailwindcss hoặc có thể viết tay sass để style component
- Date: Sử dụng moment.js
- Translation: Sử dụng React-i18next
- Global state: Sử dụng redux cùng với redux-toolkit để trao đổi state giữa các component
- Utilities: Lodash cho các hàm xử lý object, array hay sử dụng
- Format code: Project được cài thêm eslint và prettier để check tiêu chuẩn và làm đẹp code
