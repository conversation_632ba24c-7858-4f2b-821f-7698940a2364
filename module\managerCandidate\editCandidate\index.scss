.border-dash {
  border-style: dashed;
  border-color: $border-color;
  border-radius: 15px;
  border-width: 1px;
  margin-top: 10px;
}

.div-time {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}

.modal-edit-candidate {
  top: 20px;
  color: $text-color-input;

  .container-item-detail-modal {
    border: 1px dashed $border-color;
    border-radius: 16px;
  }

  .content-modal-edit-candidate {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    padding: 12px 24px;
  }

  .container-name-application {
    flex-direction: column;
    display: flex;
    flex: 1;
    padding-left: 24px;
    padding-right: 18px;
  }

  .name-application {
    font-weight: 700;
    color: $text_color;
    font-size: 24px;
  }

  .positionName-application {
    font-weight: 400;
    color: $text_color;
    font-size: 16px;
  }

  .dot-status {
    height: 12px;
    width: 12px;
    border-radius: 12px;
  }

  .text-property-candidate {
    color: $text-color-input;
    font-size: 16px;
    font-weight: 400;
    margin-left: 6px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: 1;
  }

  .line {
    height: 0.5px;
    width: 70%;
    background-color: $header_tf05;
    margin: 16px 15%;
  }

  .title-job {
    color: $text-color-input;
    font-size: 16px;
    font-weight: 700;
  }

  .status-application-title {
    color: $green_color;
    font-size: 14px;
    font-weight: 400;
    align-items: center;
  }

  .dot-content {
    display: flex;
    height: 25px;
    width: 25px;
    border-radius: 25px;
    align-items: center;
    justify-content: center;
  }

  .line-step {
    height: 0.5px;
    background-color: $header_tf05;
    width: 100%;
  }

  .title-history {
    margin-right: 14px;
    font-size: 16px;
    font-weight: 400;
    color: $text-color-input;
  }

  .ant-steps {
    margin-left: -30px;
    margin-top: 20px;

    .ant-steps-item {
      width: 25%;
    }

    .ant-steps-item-tail {
      padding: 3.5px 12px;
    }
  }

  .current-step {
    font-size: 12px;
    font-weight: 400;
    color: $white-color;
  }

  .next-step {
    font-size: 12px;
    font-weight: 400;
    color: $text-color-input;
  }

  .ant-steps-item-title {
    font-size: 12px !important;
    font-weight: 400 !important;
    color: $text-color-input !important;
  }

  .history-item {
    font-size: 12px;
    font-weight: 400;
    color: $text-color-input;
  }

  .dot-history {
    height: 8px;
    width: 8px;
    border-radius: 8px;
    background-color: $header_tf;
  }

  .btn-cancel {
    button {
      background-color: $secondary-color;
      color: $primary-color;
    }
  }

  .btn-detail-job {
    button {
      border: none;
      font-size: 10px;
      font-weight: 400;
      color: $primary-color;
      span {
        text-decoration: underline;
      }
    }
  }

  .ant-modal-body {
    padding: 0px;
  }
}
