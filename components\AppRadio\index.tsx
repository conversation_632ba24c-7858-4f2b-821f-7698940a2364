import {CheckboxOptionType} from "antd/lib/checkbox";
import classNames from "classnames";
import {Radio, RadioGroupProps} from "formik-antd";
import React from "react";

interface Props extends RadioGroupProps {
  options: Array<CheckboxOptionType>;
}

function AppRadio(props: Props) {
  const {name, className, options, value} = props;

  return (
    <div>
      <Radio.Group
        name={name}
        className={classNames("app-radio", className)}
        options={options}
        value={value}
      />
    </div>
  );
}

export default AppRadio;
