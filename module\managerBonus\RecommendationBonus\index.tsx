import ApiPaymentBonus, {
  DashboardPagingDatas,
  IResRecommendationBonus,
} from "@app/api/ApiPaymentBonus";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {formatMoney, getPaymentStatus} from "@app/utils/constants/function";
import {ColumnType} from "antd/lib/table";
import moment from "moment";
import React, {useMemo, useState} from "react";
import {useQuery} from "react-query";
import FilterRecommendation from "./FilterRecommendation";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import "./index.scss";
import ModalRecommendationBonusDetail from "./ModalRecommendationBonusDetail";
import ModalUpdatePayment from "../ModalUpdatePayment";
import {IAccountRole} from "@app/types";

function RecommendationBonus(): JSX.Element {
  const [searchParams, setSearchParams] = useState<IResRecommendationBonus>({
    currentPage: 1,
    pageSize: 20,
    isFirstInitialization: true,
    isViewDetail: false,
  });
  const {listColShowRecommendationBonus, user} = useSelector(selectUser);

  const [selectRecommendation, setSelectRecommendation] =
    useState<DashboardPagingDatas>({} as DashboardPagingDatas);
  const [showModalDetail, setShowModalDetail] = useState<boolean>(false);
  const [showModalPayment, setShowModalPayment] = useState<boolean>(false);
  const isRoleCst = (user?.role || [])?.includes(IAccountRole.CST);

  const getRecommendationBonus = useQuery(
    ["recommendation", searchParams],
    () => {
      return ApiPaymentBonus.getRecommendationBonus(searchParams);
    }
  );

  const {data, isLoading} = getRecommendationBonus;

  const columnAll: ColumnType<DashboardPagingDatas>[] = [
    {
      title: "CST quản lí",
      dataIndex: "consultantName",
      key: "consultantName",
      align: "left",
      className: "cursor-pointer",
      onCell: (record: DashboardPagingDatas): any => {
        return {
          onClick: (): void => {
            setSelectRecommendation(record);
            setShowModalDetail(true);
          },
        };
      },
    },
    {
      title: "CTV giới thiệu",
      dataIndex: "userFullName",
      key: "userFullName",
    },
    {
      title: "Người tạo application",
      dataIndex: "applicationCreator",
      key: "applicationCreator",
    },
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
    },
    {
      title: "Số tiền",
      dataIndex: "amount",
      key: "amount",
      render: (_: string, record: DashboardPagingDatas): JSX.Element => {
        return (
          <span>{record?.amount ? formatMoney(record.amount, "VND") : ""}</span>
        );
      },
    },
    {
      title: "Trạng thái thanh toán",
      dataIndex: "paymentStatus",
      key: "paymentStatus",
      render: (_: string, record: DashboardPagingDatas): JSX.Element => {
        return (
          <span
            style={{
              backgroundColor: getPaymentStatus(record?.paymentStatus).color,
            }}
            className="status-payment"
          >
            {getPaymentStatus(record?.paymentStatus).label}
          </span>
        );
      },
    },
    {
      title: "Ngày thanh toán",
      dataIndex: "paymentDate",
      key: "paymentDate",
      render: (_: string, record: DashboardPagingDatas): JSX.Element => {
        return (
          <span>
            {record?.paymentDate
              ? moment(record.paymentDate).format(DATE_FORMAT)
              : ""}
          </span>
        );
      },
    },
    {
      title: "Ngày Onboard",
      dataIndex: "onboardDate",
      key: "onboardDate",
      render: (_: string, record: DashboardPagingDatas): JSX.Element => {
        return (
          <span>
            {record?.onboardDate
              ? moment(record.onboardDate).format(DATE_FORMAT)
              : ""}
          </span>
        );
      },
    },
    {
      title: "Vị trí ứng tuyển",
      dataIndex: "positionName",
      key: "positionName",
    },
    {
      title: "Khách hàng",
      dataIndex: "customerName",
      key: "customerName",
    },
  ];

  const currentColumns = useMemo(() => {
    return columnAll.filter((col) =>
      [
        !isRoleCst ? "consultantName" : "",
        "userFullName",
        "applicationCreator",
        ...listColShowRecommendationBonus,
      ]?.some((i) => i === col.key)
    );
  }, [listColShowRecommendationBonus]);

  const handlePagination = (page: number, pageSize: number): void => {
    setSearchParams((prev) => ({
      ...prev,
      pageSize: pageSize,
      currentPage: page,
    }));
  };

  return (
    <div className="recommendation-bonus">
      <div className="recommendation-bonus__search">
        <FilterRecommendation
          setSearchParams={setSearchParams}
          allColumn={columnAll}
          searchParams={searchParams}
          currentColumns={currentColumns}
        />
      </div>
      <div className="recommendation-bonus__table mt-6">
        <AppTable
          dataSource={
            getRecommendationBonus?.data?.dashboardPagingDatas?.map(
              (item, index) => ({
                ...item,
                key: index,
              })
            ) || []
          }
          columns={currentColumns as any}
          loading={isLoading}
          style={{
            maxHeight: "60vh",
            overflowY: "auto",
          }}
          key="index"
        />
        <div className="recommendation-bonus__paging mt-2">
          <AppPagination
            pageSize={searchParams.pageSize}
            current={searchParams.currentPage}
            total={data?.totalCount ?? 0}
            onChange={handlePagination}
          />
        </div>
      </div>
      <ModalRecommendationBonusDetail
        title="Chi tiết recommendation bonus"
        open={showModalDetail}
        dataRecommendation={selectRecommendation}
        onCancel={(): void => setShowModalDetail(false)}
        onOpenModalPayment={(): void => {
          setShowModalDetail(false);
          setShowModalPayment(true);
        }}
      />
      <ModalUpdatePayment
        onCancel={(): void => {
          setShowModalPayment(false);
          setSelectRecommendation({} as DashboardPagingDatas);
        }}
        title="Chi tiết thanh toán"
        open={showModalPayment}
        dataPayment={{
          amount: selectRecommendation?.amount || "",
          note: selectRecommendation?.note || "",
          paymentBonusId: selectRecommendation.paymentBonusId,
          paymentDate: selectRecommendation.paymentDateString
            ? moment(selectRecommendation.paymentDateString, DATE_FORMAT)
            : "",
          paymentType: "",
        }}
        refreshList={(): void => {
          getRecommendationBonus.refetch();
        }}
      />
    </div>
  );
}

export default React.memo(RecommendationBonus);
