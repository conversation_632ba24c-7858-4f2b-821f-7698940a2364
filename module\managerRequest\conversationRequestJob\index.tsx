import ApiRequestJob, {
  DataChattingRequestJob,
  DataUpdateChatting,
} from "@app/api/ApiRequestJob";
import AppButton from "@app/components/AppButton";
import {TextInput} from "@app/components/TextInput";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {Col, notification, Row} from "antd";
import classNames from "classnames";
import {Formik, FormikProps} from "formik";
import {memo, useMemo, useRef} from "react";
import {useMutation, useQuery} from "react-query";
import {useDispatch, useSelector} from "react-redux";
import "./index.scss";
import moment, {Moment} from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {selectUser} from "@app/redux/slices/UserSlice";

interface ConversationRequestJobProps {
  requestJobId: number;
  className?: string;
  showTitle?: boolean;
  noteCTV?: boolean;
}

interface ChattingForm {
  text: string;
}

const initialValueChattingForm: ChattingForm = {
  text: "",
};

interface DataChattingGroup {
  createDate: string | Moment | Date;
  data: Array<DataChattingRequestJob>;
}

function ConversationRequestJob(props: ConversationRequestJobProps) {
  const {requestJobId, className, showTitle = true, noteCTV} = props;
  const dispatch = useDispatch();
  const chatFormikRef = useRef<FormikProps<ChattingForm>>(null);
  const {user} = useSelector(selectUser);

  const {data: dataChatting, refetch: refetchChatting} = useQuery(
    [
      noteCTV ? "getChattingRequestJobCTV" : "getChattingRequestJob",
      requestJobId,
    ],
    () => {
      return noteCTV
        ? ApiRequestJob.getChattingRequestJobCTV(Number(requestJobId))
        : ApiRequestJob.getChattingRequestJob(Number(requestJobId));
    },
    {
      enabled: !!requestJobId && !!user?.userId,
    }
  );

  const {mutate: updateChatting, isLoading: isLoadingChatting} = useMutation(
    (data: DataUpdateChatting) => {
      return noteCTV
        ? ApiRequestJob.updateChattingRequestJobCTV(data)
        : ApiRequestJob.updateChattingRequestJob(data);
    },
    {
      onSuccess() {
        notification.success({
          message: "Gửi thông tin thành công",
        });
        refetchChatting();
        chatFormikRef.current?.resetForm();
      },
      onSettled() {
        dispatch(setLoading(false));
      },
    }
  );

  const onSubmitChatting = (values: ChattingForm) => {
    const content = values.text.trim();
    if (!content) {
      notification.error({
        message: "Error",
        description: "Vui lòng nhập thông tin",
      });
    } else {
      dispatch(setLoading(true));
      const dataSend: DataUpdateChatting = {
        requestJobId: requestJobId,
        requestComment: content,
      };
      updateChatting(dataSend);
    }
  };

  const groupChatting = useMemo(() => {
    const result: Array<DataChattingGroup> = [];
    const cloneDataChatting = dataChatting ?? [];
    const listDayChatting: Array<string> = [];
    let date = "";

    cloneDataChatting.forEach((item) => {
      const dateOfChat = moment(item.createdDate).format(DATE_FORMAT);
      if (date !== dateOfChat) {
        listDayChatting.push(dateOfChat);
        date = dateOfChat;
      }
    });

    listDayChatting.reduce((_: any, currentValue) => {
      const filterDate = cloneDataChatting.filter(
        (i) => moment(i.createdDate).format(DATE_FORMAT) === currentValue
      );
      const dataChattingPerDay: DataChattingGroup = {
        createDate: currentValue,
        data: filterDate,
      };
      result.push(dataChattingPerDay);

      return result;
    }, result);

    return result;
  }, [dataChatting]);

  return (
    <div className={classNames("w-full conversation", className)}>
      <div className="conversation__chatting mt-2 text14 flex flex-col justify-between">
        <div>
          {showTitle && <h3 className="font-bold text16">Lịch sử trao đổi</h3>}
          <div className="conversation__chatting-content">
            {groupChatting.map((item) => {
              return (
                <div
                  className="conversation__per_day"
                  key={item.createDate.toString()}
                >
                  <p className="conversation__date mt-2">
                    {`Ngày: ${item.createDate.toString()}`}
                  </p>
                  <div className="pl-6">
                    {item.data.map((i) => (
                      <p key={i.createdDate.toString()}>
                        <span className="font-bold">{i.username}: </span>
                        <span> {i.requestComment}</span>
                      </p>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div className="mt-2">
          <Formik
            innerRef={chatFormikRef}
            initialValues={initialValueChattingForm}
            onSubmit={onSubmitChatting}
          >
            {({handleSubmit, values, isSubmitting}): JSX.Element => (
              <form className="" onSubmit={handleSubmit}>
                <Row gutter={[16, 16]}>
                  <Col xs={20}>
                    <TextInput
                      name="text"
                      label="Nhập nội dung"
                      value={values.text}
                      free={!values.text}
                      maxLength={500}
                    />
                  </Col>
                  <Col xs={4}>
                    <AppButton
                      typebutton="primary"
                      htmlType="submit"
                      disabled={isLoadingChatting}
                      loading={isLoadingChatting}
                      onClick={handleSubmit}
                    >
                      Gửi
                    </AppButton>
                  </Col>
                </Row>
              </form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
}

export default memo(ConversationRequestJob);
