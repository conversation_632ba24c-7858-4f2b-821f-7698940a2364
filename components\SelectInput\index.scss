.input-select-container {
  position: relative;
  height: 52px;

  .ant-select {
    width: 100%;
  }

  .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
    border-color: $primary-color;
  }

  .ant-select-multiple .ant-select-selector {
    display: block;
  }

  .ant-select-single {
    .ant-select-selection-search {
      left: 15px;
      bottom: 6px;
      display: flex;
      align-items: flex-end;

      input {
        height: 20px !important;
      }
    }
  }

  .ant-select-multiple {
    .ant-select-selection-search {
      height: 20px;
      line-height: 20px;
      margin-left: 0px;
    }

    input {
      height: 20px;
    }
  }

  .ant-select-selector {
    border-radius: 8px !important;
    padding: 23px 20px 7px 15px !important;
    height: 52px !important;

    .ant-select-selection-item {
      line-height: 20px !important;
      font-size: 0.875rem;
      height: 20px;
      align-items: center;
      color: $text-color-input;
    }

    .ant-select-selection-overflow-item {
      height: 22px;

      .ant-select-selection-item {
        color: $text-color-input;
        font-weight: 400;
        background-color: $select_color;
        border-radius: 4px;
        padding-left: 4px;
        margin-top: 0px;
        margin-bottom: 0px;
        height: 22px;
      }

      .ant-select-selection-item-remove {
        color: $text-color-input;
        margin-top: -1px;
        font-size: 10px;
      }
    }
  }

  .label {
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 15px;
    top: 16px;
    transition: 0.2s ease all;
    line-height: 20px;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 70%;
  }

  .as-placeholder {
    color: $header_tf;
  }

  .as-label {
    top: 6px;
    font-size: 0.75rem;
    padding: 0 4px;
    margin-left: -4px;
    color: $header_tf;
  }

  .text-required {
    color: red;
  }

  .arrow-drop-multiple {
    position: absolute;
    top: 21px;
    right: 12px;
  }
}

.ant-select-item-option-content {
  font-size: 14px;
  font-weight: 400;
  height: 20px;
}

.ant-select-item-option-selected {
  background-color: $select_color !important;
  border-radius: 4px;

  .ant-select-item-option-state {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
  }

  .anticon-check {
    color: $text-color;
    font-size: 14px;
  }
}

.ant-select-item {
  height: 29px;
  min-height: auto;
}

.ant-select-item-option {
  align-items: center;
}
