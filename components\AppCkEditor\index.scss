.app-editor {
  .ck.ck-content:not(.ck-comment__input *) {
    // height: 200px;
    border-radius: 0 0 8px 8px;
  }

  .ck-editor {
    margin-top: 4px !important;
  }

  &__label-required {
    position: relative;
    font-weight: 500;
    color: $text-color-input;

    &::after {
      display: block;
      content: "*";
      position: absolute;
      right: -8px;
      top: 0;
      color: #ff4d4f;
      font-weight: 600;
    }
  }

  .ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar {
    border-radius: 8px 8px 0 0;
  }

  .ck-rounded-corners .ck.ck-editor__main > .ck-editor__editable,
  .ck.ck-editor__main > .ck-editor__editable.ck-rounded-corners {
    border-radius: 0 0 8px 8px;
  }

  .message-required {
    color: #ff4d4f;
  }

  &__required {
    .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
      border-color: #ff4d4f;
    }
  }

  ol {
    list-style-type: decimal !important;
    list-style: decimal !important;
  }

  ul,
  menu {
    list-style: disc !important;
    list-style-type: disc !important;
  }

  ol,
  ul,
  menu {
    list-style: inside;
    padding-left: 20px;
    margin: 12px 0 14px 0;
  }
}
