import React, {useMemo} from "react";
import "./index.scss";
import {Select, SelectProps} from "formik-antd";
import Icon from "../Icon/Icon";
import {currencyList} from "@app/utils/constants/state";

interface AppSelectCurrencyProps extends SelectProps {
  isAlone?: boolean;
  classNameContainer?: string;
}

export default function AppSelectCurrency(
  props: AppSelectCurrencyProps
): JSX.Element {
  const {isAlone, classNameContainer} = props;

  const IconDown = useMemo(
    () => (
      <Icon
        size={10}
        icon="arrow-drop-down-line"
        color="#324054"
        className=""
      />
    ),
    []
  );
  return (
    <div className={classNameContainer || ""}>
      <Select
        className={isAlone ? "alone-select-currency" : "select-currency"}
        labelInValue
        suffixIcon={IconDown}
        options={props.options || currencyList}
        showArrow
        maxTagCount="responsive"
        dropdownStyle={{
          borderRadius: "4px",
          borderWidth: "1px",
          borderColor: "rgba(157, 157, 157,0.5)",
          padding: "4px",
        }}
        {...props}
      />
    </div>
  );
}
