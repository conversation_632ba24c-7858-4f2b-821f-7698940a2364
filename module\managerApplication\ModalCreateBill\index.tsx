import AppModal from "@app/components/AppModal";
import React, {useCallback, useEffect, useRef} from "react";
import "./index.scss";
import {Formik, FormikProps} from "formik";
import {Col, Row, notification} from "antd";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment, {Moment} from "moment";
import {TextInput} from "@app/components/TextInput";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {SelectInput} from "@app/components/SelectInput";
import {
  deadTimeFastSearch,
  optionGuarantee,
  statusRateCurrency,
} from "@app/utils/constants/state";
import AppButton from "@app/components/AppButton";
import ApiApplication, {IParamCustomerPayment} from "@app/api/ApiApplication";
import {useMutation, useQuery} from "react-query";
import {OptionSelect} from "@app/types";
import {
  getMonthWarrantySelected,
  moneyToNumber,
} from "@app/utils/constants/function";
import AppLoading from "@app/components/AppLoading";
import {
  messageCheckCustomerPaymentExist,
  messageValidate,
} from "@app/utils/constants/message";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {useDispatch} from "react-redux";
import {useRouter} from "next/router";
import config from "@app/config";

interface ModalCreateBillProps {
  isShow: boolean;
  setIsShow: (isShow: boolean) => void;
  applicationId: number;
  onSuccess?: () => void;
  createdDateCandidate?: string;
}

interface FormCreateBill {
  amount: number;
  paymentRateTypeSelected: OptionSelect;
  onboardDate: string | Moment;
  paymentDateExpected: string | Moment;
  paymentRate: number;
  salaryOffered: number;
  salesTransacted: number;
  vat: number;
  monthTrailWorkSelected: OptionSelect;
  currencyOfferedTypeSelected: OptionSelect;
  salaryOfferedExchangeRate: number;
  salaryVND: number;
}
export default function ModalCreateBill(
  props: ModalCreateBillProps
): JSX.Element {
  const {isShow, setIsShow, createdDateCandidate, applicationId, onSuccess} =
    props;
  const refPayment = useRef<FormikProps<FormCreateBill>>(null);
  const dispatch = useDispatch();
  const router = useRouter();

  const getCustomerPaymentByApplicationId = useQuery(
    ["getCustomerPaymentByApplicationId", applicationId],
    () => {
      return ApiApplication.getCustomerPaymentByApplicationId(applicationId);
    },
    {
      enabled: isShow,
    }
  );
  const createCustomerPayment = useMutation(
    (data: IParamCustomerPayment) => {
      return ApiApplication.createCustomerPayment(data);
    },
    {
      onSuccess: (data) => {
        if (data === true) {
          notification.success({
            message: "Thông báo",
            description: "Tạo thanh toán thành công",
            duration: 3,
          });
          onSuccess?.();
          dispatch(setLoading(false));
          router.push(config.PATHNAME.CUSTOMER_PAYMENT);
          return;
        }
        dispatch(setLoading(false));
      },
      onError: (): void => {
        dispatch(setLoading(false));
      },
    }
  );

  const checkCustomerPaymentExist = useQuery(
    ["checkCustomerPaymentExistWhenCreate", applicationId],
    () => {
      return ApiApplication.checkCustomerPaymentExist(applicationId);
    },
    {
      onSuccess: (res): void => {
        if (!res) {
          const values = refPayment.current?.values;
          const param: IParamCustomerPayment = {
            amount: values?.amount || 0,
            amountRemain: null,
            applicationId,
            currencyOfferedType:
              values?.currencyOfferedTypeSelected.value || "VND",
            hasVAT: !values?.vat || values?.vat === 0 ? 0 : 1, // 0 là ko có , 1 là có
            monthTrailWork: Number(values?.monthTrailWorkSelected.value),
            onboardDate: values?.onboardDate
              ? moment(values.onboardDate).format(DATE_FORMAT)
              : "",
            paymentDateExpected: values?.paymentDateExpected
              ? moment(values.paymentDateExpected).format(DATE_FORMAT)
              : "",
            paymentId: 0,
            paymentRate: values?.paymentRate || 0,
            paymentRateType: values?.paymentRateTypeSelected.value === "0", // true là tỷ lệ , false là số tiền
            paymentRateUnit: "VND",
            requestJobId: data?.requestJobId || 0,
            salaryOffered: values?.salaryOffered
              ? moneyToNumber(String(values.salaryOffered))
              : 0,
            salaryOfferedExchangeRate: values?.salaryOfferedExchangeRate
              ? moneyToNumber(String(values.salaryOfferedExchangeRate))
              : 1,
            salesTransacted: values?.salesTransacted
              ? moneyToNumber(String(values.salesTransacted))
              : 0,
            vat: values?.vat ? moneyToNumber(String(values.vat)) : null,
          };
          createCustomerPayment.mutate(param);
        } else {
          notification.error({
            message: "Thông báo",
            description: messageCheckCustomerPaymentExist,
            duration: 3,
          });
          dispatch(setLoading(false));
        }
      },
      onError: (): void => {
        dispatch(setLoading(false));
      },
      enabled: false,
    }
  );

  const data = getCustomerPaymentByApplicationId?.data;

  const handleClose = (): void => {
    refPayment.current?.setValues(initialValues);
    setIsShow(false);
  };

  const disableOnboardDate = (current: any): boolean => {
    return current && current < moment(createdDateCandidate).startOf("day");
  };

  const onChangeRateType = (): void => {
    refPayment.current?.setFieldValue("paymentRate", null);
  };

  const onChangeOnboardDate = (): void => {
    refPayment.current?.setFieldValue("paymentDateExpected", "");
  };

  const createBill = (): void => {
    const values = refPayment.current?.values;
    if (values) {
      if (
        !(
          values?.onboardDate &&
          values?.paymentDateExpected &&
          values?.salaryOffered &&
          values?.paymentRate &&
          values?.salesTransacted
        ) ||
        (values?.currencyOfferedTypeSelected?.value !== "VND" &&
          !values?.salaryOfferedExchangeRate)
      ) {
        notification.error({
          message: "Thông báo",
          description: messageValidate,
          duration: 3,
        });
      } else {
        dispatch(setLoading(true));
        checkCustomerPaymentExist.refetch();
      }
    }
  };

  const initialValues: FormCreateBill = {
    amount: data?.amount || 0,
    onboardDate: data?.onboardDate ? moment(data.onboardDate, DATE_FORMAT) : "",
    paymentDateExpected: data?.paymentDateExpected
      ? moment(data.paymentDateExpected)
      : "",
    paymentRate: data?.paymentRate || 0,
    // trả về paymentRateType : false tỷ lệ , : true  là số tiền
    paymentRateTypeSelected: !data?.paymentRateType
      ? statusRateCurrency[0]
      : statusRateCurrency[1],
    salaryOffered: data?.salaryOffered || 0,
    salesTransacted: data?.salesTransacted || 0,
    vat: data?.vat || 10,
    monthTrailWorkSelected: getMonthWarrantySelected(data?.monthTrailWork),
    currencyOfferedTypeSelected: data?.currencyOfferedType
      ? {value: data.currencyOfferedType, label: data?.currencyOfferedType}
      : {value: "VND", label: "VND"},
    salaryOfferedExchangeRate: data?.salaryOfferedExchangeRate || 0,
    salaryVND:
      (data?.salaryOffered || 0) * (data?.salaryOfferedExchangeRate || 1),
  };

  const renderBodyModal = (): JSX.Element => {
    if (getCustomerPaymentByApplicationId.isLoading) {
      return (
        <div className=" h-[20vh]">
          <AppLoading />
        </div>
      );
    }

    return (
      <Formik
        innerRef={refPayment}
        initialValues={initialValues}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            const timeOut = setTimeout(() => {
              const salaryOffered = values.salaryOffered
                ? moneyToNumber(String(values.salaryOffered))
                : 1;
              const salaryOfferedExchangeRate =
                values.salaryOfferedExchangeRate &&
                values.currencyOfferedTypeSelected.value !== "VND"
                  ? moneyToNumber(String(values.salaryOfferedExchangeRate))
                  : 1;
              const paymentRate = values.paymentRate
                ? moneyToNumber(String(values.paymentRate))
                : 0;

              const salaryVND = salaryOffered * salaryOfferedExchangeRate;
              const salesTransacted =
                values.paymentRateTypeSelected.value === "0"
                  ? salaryOffered * salaryOfferedExchangeRate * paymentRate
                  : paymentRate;

              const amount = salesTransacted
                ? salesTransacted +
                  (salesTransacted * moneyToNumber(String(values.vat))) / 100
                : 0;
              if (values.currencyOfferedTypeSelected.value === "VND") {
                refPayment.current?.setFieldValue(
                  "salaryOfferedExchangeRate",
                  0
                );
              }
              refPayment.current?.setFieldValue("salaryVND", salaryVND);
              refPayment.current?.setFieldValue("amount", amount);
              refPayment.current?.setFieldValue(
                "salesTransacted",
                salesTransacted
              );
            }, deadTimeFastSearch);

            return () => {
              if (timeOut) {
                clearTimeout(timeOut);
              }
            };
          }, [
            values.salaryOffered,
            values.salaryOfferedExchangeRate,
            values.vat,
            values.paymentRate,
            values.currencyOfferedTypeSelected.value,
          ]);

          // eslint-disable-next-line react-hooks/rules-of-hooks
          const disablePaymentDateExpected = useCallback(
            (current: any): boolean => {
              if (!values.onboardDate) {
                return false;
              }
              return (
                current && current < moment(values.onboardDate).startOf("day")
              );
            },
            [values.onboardDate]
          );

          return (
            <div>
              <Row className="px=15">
                <Col span={12} className="pr-3">
                  <AppDatePicker
                    classNameContainer="mt-2"
                    name="onboardDate"
                    label="Ngày onboard"
                    format={DATE_FORMAT}
                    valueAppDatePicker={values?.onboardDate}
                    free={!values?.onboardDate}
                    required
                    status={values?.onboardDate ? undefined : "error"}
                    disabledDate={disableOnboardDate}
                    onChange={onChangeOnboardDate}
                  />
                  <Row className="mt-2 input-salary">
                    <TextInput
                      containerclassname="flex-1"
                      label="Lương offer"
                      name="salaryOffered"
                      onlynumber
                      iscurrency
                      value={values?.salaryOffered}
                      free={!values?.salaryOffered}
                      typeInput="salary"
                      maxLength={10}
                      required
                      status={values?.salaryOffered ? undefined : "error"}
                    />
                    <AppSelectCurrency
                      name="currencyOfferedTypeSelected"
                      value={values.currencyOfferedTypeSelected}
                      style={{width: "90px"}}
                    />
                  </Row>
                  <TextInput
                    containerclassname="mt-2"
                    label="Tỷ giá"
                    name="salaryOfferedExchangeRate"
                    onlynumber
                    iscurrency
                    value={values?.salaryOfferedExchangeRate}
                    free={!values?.salaryOfferedExchangeRate}
                    typeInput="salary"
                    maxLength={50}
                    disabled={
                      values?.currencyOfferedTypeSelected?.value === "VND"
                    }
                    required={
                      values?.currencyOfferedTypeSelected?.value !== "VND"
                    }
                    status={
                      values?.currencyOfferedTypeSelected?.value !== "VND" &&
                      !values?.salaryOfferedExchangeRate
                        ? "error"
                        : undefined
                    }
                  />
                  <Row className="mt-2 input-salary not-show-arrow">
                    <TextInput
                      containerclassname="flex-1"
                      label="Số tiền quy đổi được"
                      name="salaryVND"
                      onlynumber
                      value={values?.salaryVND}
                      free={!values?.salaryVND}
                      typeInput="salary"
                      iscurrency
                      disabled
                    />
                    <AppSelectCurrency
                      name=""
                      value="VND"
                      style={{width: "90px"}}
                      disabled
                      showArrow={false}
                    />
                  </Row>
                  <Row className="mt-2 input-salary">
                    <TextInput
                      containerclassname="flex-1"
                      label="Rate khách hàng"
                      name="paymentRate"
                      onlynumber
                      value={values?.paymentRate}
                      free={!values?.paymentRate}
                      typeInput={
                        values?.paymentRateTypeSelected?.value === "1"
                          ? "salary"
                          : ""
                      }
                      iscurrency={
                        values?.paymentRateTypeSelected?.value === "1"
                      }
                      maxLength={50}
                      required
                      status={values?.paymentRate ? undefined : "error"}
                    />
                    <AppSelectCurrency
                      name="paymentRateTypeSelected"
                      value={values?.paymentRateTypeSelected}
                      style={{width: "90px"}}
                      options={statusRateCurrency}
                      onChange={onChangeRateType}
                    />
                  </Row>
                </Col>
                <Col span={12} className="pl-3">
                  <AppDatePicker
                    classNameContainer="mt-2"
                    name="paymentDateExpected"
                    label="Ngày thanh toán dự kiến"
                    format={DATE_FORMAT}
                    valueAppDatePicker={values?.paymentDateExpected}
                    free={!values?.paymentDateExpected}
                    required
                    status={values?.paymentDateExpected ? undefined : "error"}
                    disabledDate={disablePaymentDateExpected}
                  />
                  <SelectInput
                    containerclassname="mt-2"
                    name="monthTrailWorkSelected"
                    labelselect="Số tháng bảo hành"
                    data={optionGuarantee}
                    value={values?.monthTrailWorkSelected}
                    required
                  />
                  <Row className="mt-2 input-salary not-show-arrow">
                    <TextInput
                      containerclassname="flex-1"
                      label="Doanh thu"
                      name="salesTransacted"
                      onlynumber
                      iscurrency
                      value={values?.salesTransacted}
                      free={!values?.salesTransacted}
                      typeInput="salary"
                      maxLength={100}
                    />
                    <AppSelectCurrency
                      name=""
                      value="VND"
                      style={{width: "70px"}}
                      disabled
                      showArrow={false}
                    />
                  </Row>
                  <Row className="mt-2 input-salary not-show-arrow">
                    <TextInput
                      containerclassname="flex-1"
                      label="VAT"
                      name="vat"
                      onlynumber
                      value={values?.vat}
                      free={!values?.vat}
                      maxLength={2}
                    />
                    <AppSelectCurrency
                      name="currencyTotalAmount"
                      value="%"
                      style={{width: "70px"}}
                      disabled
                      showArrow={false}
                    />
                  </Row>
                  <Row className="mt-2 input-salary not-show-arrow">
                    <TextInput
                      containerclassname="flex-1"
                      label="Tổng tiền"
                      name="amount"
                      onlynumber
                      value={values?.amount}
                      free={!values?.amount}
                      typeInput="salary"
                      iscurrency
                      maxLength={100}
                      disabled
                    />
                    <AppSelectCurrency
                      name="currencyTotalAmount"
                      value="VND"
                      style={{width: "70px"}}
                      disabled
                      showArrow={false}
                    />
                  </Row>
                </Col>
              </Row>
              <Row className="flex justify-center mt-10 items-center">
                <AppButton
                  classrow="mr-2 w-40 btn-cancel"
                  label="Hủy"
                  typebutton="primary"
                  onClick={handleClose}
                />
                <AppButton
                  onClick={createBill}
                  classrow="ml-2 w-40"
                  label="Tạo thanh toán"
                  typebutton="primary"
                  disabled={
                    checkCustomerPaymentExist.isLoading ||
                    createCustomerPayment.isLoading
                  }
                />
              </Row>
            </div>
          );
        }}
      </Formik>
    );
  };

  return (
    <AppModal
      className="modal-create-bill"
      open={isShow}
      footer={null}
      onCancel={handleClose}
      width="50%"
      title="Tạo hóa đơn cho khách hàng"
    >
      {renderBodyModal()}
    </AppModal>
  );
}
