import {SelectInput} from "@app/components/SelectInput";
import {Col, DatePicker, Row} from "antd";
import {Formik, FormikProps} from "formik";
import {FormEvent, useEffect, useRef} from "react";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {OptionSelect} from "@app/types";
import {
  DataFormSearchJobCompany,
  ValuesSearchJobCompany,
} from "@app/api/ApiCompany";
import moment from "moment";
import {getTimeZone} from "@app/utils/constants/function";
import "./index.scss";

interface Props {
  companyId: number;
  techStacks: OptionSelect[];
  onResetCurrentPage: () => void;
  onSearchParams: (data: ValuesSearchJobCompany) => void;
}

const initialValue: ValuesSearchJobCompany = {
  techStacks: [],
  startDate: "",
  endDate: "",
  currentPage: 1,
  pageSize: 20,
};

const initialValueForm: DataFormSearchJobCompany = {
  techStacks: [],
  startDate: "",
  endDate: "",
  rangeDate: null,
};

const {RangePicker} = DatePicker;

export function FilterJobCompany(props: Props): JSX.Element {
  const {companyId, techStacks, onResetCurrentPage, onSearchParams} = props;
  const formikRef = useRef<FormikProps<DataFormSearchJobCompany>>(null);
  const timeOut = useRef<any>();

  const handleSearch = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      onResetCurrentPage();
      onSearchParams({
        ...initialValue,
        companyId: companyId,
        startDate: formikRef?.current?.values?.rangeDate?.length
          ? moment(formikRef?.current?.values?.rangeDate[0])
              .set("date", 1)
              .startOf("day")
              .add(getTimeZone(), "hour")
              .toISOString()
          : "",
        endDate: formikRef?.current?.values?.rangeDate?.length
          ? moment(formikRef?.current?.values?.rangeDate[1])
              .set(
                "date",
                moment(formikRef?.current?.values?.rangeDate[1]).daysInMonth()
              )
              .endOf("day")
              .add(getTimeZone(), "hour")
              .toISOString()
          : "",
        techStacks:
          formikRef?.current?.values?.techStacks?.map(
            (item) => (item?.key as string) || (item.value as string)
          ) || [],
      });
    }, deadTimeFastSearch);
  };

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  return (
    <Formik
      initialValues={initialValueForm}
      innerRef={formikRef}
      onSubmit={() => {
        //
      }}
    >
      {({values}) => {
        return (
          <form
            onSubmit={(e: FormEvent<HTMLFormElement>): void => {
              e.preventDefault();
            }}
          >
            <div className="search-form-detail-company">
              <Row gutter={[16, 16]} justify="start" className="w-full">
                <Col xs={8}>
                  <SelectInput
                    name="techStacks"
                    labelselect="Tech stack"
                    data={techStacks}
                    mode="tags"
                    onSelect={handleSearch}
                    onClear={handleSearch}
                    onDeselect={handleSearch}
                    value={values?.techStacks}
                    free={values?.techStacks?.length === 0}
                    allowClear
                  />
                </Col>
                <Col xs={8}>
                  <RangePicker
                    picker="month"
                    name="rangeDate"
                    className="w-full p-[14px] rounded-lg range-picker-month"
                    placeholder={["Thời gian từ", "Đến"]}
                    onCalendarChange={(event: any): void => {
                      formikRef.current?.setFieldValue("rangeDate", event);
                      formikRef.current?.setFieldTouched(
                        "rangeDate",
                        true,
                        false
                      );
                      handleSearch();
                    }}
                  />
                </Col>
              </Row>
            </div>
          </form>
        );
      }}
    </Formik>
  );
}
