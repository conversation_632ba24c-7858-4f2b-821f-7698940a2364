import React from "react";
import ApplicationBonus from "../ApplicationBonus";
import PerformanceBonus from "../PerformanceBonus";
import RecommendationBonus from "../RecommendationBonus";
import HotBonus from "../HotBonus";
import AppTabs from "@app/components/AppTabs";
import {useRouter} from "next/router";
import config from "@app/config";

export default function CSLManagerBonus(): JSX.Element {
  const router = useRouter();
  let defaultTab = "applicationBonus";

  if (router.query.tab) {
    defaultTab = router.query.tab as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    if (Object.fromEntries(searchParams)?.tab) {
      defaultTab = Object.fromEntries(searchParams)?.tab;
    }
  }

  const listTabs = [
    {
      key: "applicationBonus",
      title: "Application bonus",
      component: <ApplicationBonus />,
    },
    {
      key: "performanceBonus",
      title: "Performance bonus",
      component: <PerformanceBonus />,
    },
    {
      key: "recommendationBonus",
      title: "Recommendation bonus",
      component: <RecommendationBonus />,
    },
    {
      key: "hotBonus",
      title: "Hot bonus",
      component: <HotBonus />,
    },
  ];
  return (
    <div className="block csl-manager-bonus-container">
      <span className="text24 text-color-primary">Quản lý bonus</span>
      <AppTabs
        classNameContainer="mt-4"
        defaultActiveKey={defaultTab}
        listTabs={listTabs}
        onTabClick={(key: string): void => {
          router.push(config.PATHNAME.MANAGER_BONUS, {query: {tab: key}});
        }}
      />
    </div>
  );
}
