/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import {Col, Image, Row, notification} from "antd";
import {Formik, FormikProps} from "formik";
import {TextInput} from "@app/components/TextInput";
import AppButton from "@app/components/AppButton";
import {validateEmail, validatePassword} from "@app/utils/constants/function";
import {useMutation} from "react-query";
import ApiAccount, {
  AccountChangePasswordParams,
  CheckExpiredTokenParams,
} from "@app/api/ApiAccount";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import ModalRegisterSuccess from "./ModalSendMailSuccess";
import {useRouter} from "next/router";
import config from "@app/config";
import {AppPassword} from "@app/components/AppPassword";
import _ from "lodash";

interface ForgotPasswordForm {
  email: string;
  name?: string;
  id?: string;
  token?: string;
  password?: string;
  passwordConfirmed?: string;
}

const initialValues: ForgotPasswordForm = {
  email: "",
};

function ForgotPassword(): JSX.Element {
  const formikRef = useRef<FormikProps<ForgotPasswordForm>>(null);
  const [showModalSuccess, setShowModalSuccess] = useState<boolean>(false);
  const [ready, setReady] = useState<boolean>(false);
  const {query, push} = useRouter();

  const redirectHomepage = (): void => {
    push(config.PATHNAME.HOME);
  };

  const id = query?.id || "";
  const name = query?.name || "";
  const token = query?.token || "";
  const expired = query?.expired || "";

  useEffect(() => {
    setReady(true);
  }, []);

  useEffect(() => {
    if (id && ready) {
      initialValues.id = query?.id as string;
      initialValues.token = query?.token as string;
      checkExpiredToken.mutate({
        id: String(id),
        token: encodeURIComponent(String(token)),
      });
    }
    if (expired && ready) {
      notification.error({
        message: "Thông báo",
        description: "Link đổi mật khẩu đã hết hạn. Vui lòng gửi lại yêu cầu",
      });
    }
  }, [ready]);

  const checkExpiredToken = useMutation(
    (data: CheckExpiredTokenParams) => {
      return ApiAccount.checkExpiredToken(data);
    },
    {
      onSuccess(data) {
        if (data.expired) {
          notification.error({
            message: "Thông báo",
            description:
              "Link đổi mật khẩu đã hết hạn. Vui lòng gửi lại yêu cầu",
          });
          push(`${config.PATHNAME.FORGOT_PASSWORD}?expired=1`);
        }
      },
    }
  );

  const dispatch = useDispatch();

  const forgotPassword = useMutation(
    (data: string) => {
      return ApiAccount.forgotPassword(data);
    },
    {
      onSuccess() {
        setShowModalSuccess(true);
      },
    }
  );

  const changePassword = useMutation(
    (data: AccountChangePasswordParams) => {
      return ApiAccount.changePassword(data);
    },
    {
      onSuccess() {
        notification.success({
          message: "Thông báo",
          description: "Đổi mật khẩu thành công",
        });
        push(config.PATHNAME.LOGIN);
      },
    }
  );

  const handleChangePassword = (): void => {
    dispatch(setLoading(true));
    const values = formikRef.current?.values;
    const valueConverter: AccountChangePasswordParams = {
      password: values?.password?.trim() || "",
      authenId: String(id),
      token: encodeURIComponent(String(token)),
    };

    if (!values?.password) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng nhập mật khẩu mới",
      });
    } else if (!validatePassword(values?.password)) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng nhập đúng định dạng mật khẩu",
      });
    } else if (!_.isEqual(values?.password, values?.passwordConfirmed)) {
      notification.error({
        message: "Thông báo",
        description: "Mật khẩu chưa khớp",
      });
    } else {
      changePassword.mutate(valueConverter);
    }
    dispatch(setLoading(false));
  };

  const handleForgotPassword = (): void => {
    dispatch(setLoading(true));
    const values = formikRef.current?.values;

    if (!values?.email) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng nhập email của bạn",
      });
    } else if (!validateEmail(values?.email)) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng nhập đúng định dạng email",
      });
    } else {
      forgotPassword.mutate(values?.email?.trim());
    }
    dispatch(setLoading(false));
  };

  return (
    <div className="ui-forgot-password">
      <Row gutter={[16, 16]} justify="center" className="w-full">
        <Col xs={24} sm={16} xl={8}>
          <div className="ui-forgot-password__form md:m-6 m-4 md:p-9 p-4 rounded shadow-lg bg-gray-300 -mt-16 bg-dark">
            <div
              className="mt-4 mb-10 cursor-pointer flex justify-center gap-6"
              onClick={redirectHomepage}
            >
              <Image
                src="/img/logo-icon.svg"
                alt="logo"
                height={84}
                width={84}
                preview={false}
              />
              <Image
                src="/img/logo-text.svg"
                alt="logo"
                height={86}
                width={105}
                preview={false}
                className="ml-2"
              />
            </div>
            <p className="text-left mb-6 text-base font-normal">
              {id ? (
                <span>
                  Xin chào <strong className="uppercase">{name}</strong>. Vui
                  lòng đặt lại mật khẩu cho tài khoản của bạn.
                </span>
              ) : (
                "Hãy nhập email vào khung bên dưới. Nếu có tài khoản gắn với email này, chúng tôi sẽ gửi lại hướng dẫn đổi mật khẩu vào email đó."
              )}
            </p>
            <Row justify="center">
              <Col xs={24}>
                <Formik
                  innerRef={formikRef}
                  initialValues={initialValues}
                  onSubmit={id ? handleChangePassword : handleForgotPassword}
                >
                  {({values, handleSubmit}): JSX.Element => {
                    return (
                      <form onSubmit={handleSubmit} autoComplete="off">
                        {id ? (
                          <>
                            <AppPassword
                              name="password"
                              label="Mật khẩu mới"
                              placeholder="Mật khẩu từ 8-20 ký tự, ít nhất 1 chữ, 1 số"
                              containerclassname="mt-2"
                              value={values?.password}
                              free={!values?.password}
                              autoComplete="off"
                              required
                              status={
                                !validatePassword(String(values?.password))
                                  ? "error"
                                  : undefined
                              }
                            />
                            <AppPassword
                              name="passwordConfirmed"
                              label="Xác nhận mật khẩu mới"
                              placeholder="Nhập lại mật khẩu mới"
                              containerclassname="mt-2"
                              value={values?.passwordConfirmed}
                              free={!values?.passwordConfirmed}
                              autoComplete="off"
                              required
                              status={
                                !validatePassword(
                                  String(values?.passwordConfirmed)
                                )
                                  ? "error"
                                  : undefined
                              }
                            />
                            <AppButton
                              typebutton="primary"
                              classrow="mt-6"
                              onClick={handleSubmit}
                              disabled={changePassword.isLoading}
                              isSubmitting={changePassword.isLoading}
                              htmlType="submit"
                            >
                              Đổi Mật Khẩu
                            </AppButton>
                          </>
                        ) : (
                          <>
                            <TextInput
                              name="email"
                              label="Email"
                              placeholder="Nhập email của bạn"
                              containerclassname="mt-2"
                              value={values?.email}
                              free={!values?.email}
                              required
                              status={
                                !validateEmail(values?.email)
                                  ? "error"
                                  : undefined
                              }
                            />
                            <AppButton
                              typebutton="primary"
                              classrow="mt-6"
                              onClick={handleSubmit}
                              disabled={forgotPassword.isLoading}
                              isSubmitting={forgotPassword.isLoading}
                              htmlType="submit"
                            >
                              Gửi yêu cầu
                            </AppButton>
                          </>
                        )}
                      </form>
                    );
                  }}
                </Formik>
              </Col>
            </Row>
          </div>
        </Col>

        <Col xs={16}>
          <div className="flex justify-center items-center h-full">
            <div>
              <div className="flex justify-center">
                <Image
                  src="/img/image-security.png"
                  alt="logo"
                  height={240}
                  width={400}
                  preview={false}
                />
              </div>
              <div className="text-center mt-4 text32">
                Bảo mật & An toàn tuyệt đối
              </div>
            </div>
          </div>
        </Col>
      </Row>
      <ModalRegisterSuccess
        open={showModalSuccess}
        email={formikRef?.current?.values?.email?.trim() || ""}
      />
    </div>
  );
}

export default ForgotPassword;
