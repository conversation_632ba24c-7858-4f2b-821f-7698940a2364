import axios, {AxiosError, AxiosRequestConfig, AxiosResponse} from "axios";
import {notification} from "antd";
import Config from "../config";
// eslint-disable-next-line import/no-cycle
import store from "../redux/store";
// eslint-disable-next-line import/no-cycle
import {urlGetNew} from "./ApiJob";
// eslint-disable-next-line import/no-cycle
import {logoutUser} from "@app/redux/slices/UserSlice";
import moment from "moment";
// eslint-disable-next-line import/no-cycle
import ApiRequestJob from "./ApiRequestJob";
// eslint-disable-next-line import/no-cycle
import ApiCandidate from "./ApiCandidate";
// eslint-disable-next-line import/no-cycle
import ApiCollaborator from "./ApiCollaborator";
import _ from "lodash";
import {collection, addDoc, getFirestore} from "firebase/firestore";
import {app} from "@app/utils/firebase";
// eslint-disable-next-line import/no-cycle
import ApiCustomerPayment from "./ApiCustomerPayment";
import {setCandidateIdDuplicate} from "@app/redux/slices/JobSlice";
import {unRegisterServiceWorker} from "@app/utils/constants/function";

export interface IDataError {
  errorCode?: string;
  errorMessage?: string;
}

export interface IResponseDTO<T> {
  success: boolean;
  code: string;
  messages?: string;
  errors?: string;
  data?: T;
}

interface IFetcherOptions {
  token?: string;
  withToken?: boolean;
  withMetadata?: boolean;
  displayError?: boolean;
}

function logout(): void {
  store.dispatch(logoutUser());
  unRegisterServiceWorker();
}
function displayError(dataError: IDataError): void {
  notification.error({
    message: "Đã có lỗi. Vui lòng thử lại.",
    description: dataError.errorMessage ?? "",
    duration: 3,
  });
}

const logErrorFirebase = async (config: AxiosRequestConfig, response: any) => {
  try {
    const db = getFirestore(app);
    const param = config.data;
    if ((config?.headers as any)["Content-Type"] === "multipart/form-data") {
      config.data.forEach(
        // eslint-disable-next-line no-return-assign
        (value: any, key: any) => (param[key] = value)
      );
    }

    await addDoc(collection(db, "api_error"), {
      informationUser: `${store.getState().user.user?.userId} ${
        store.getState().user.user?.userName
      } `,
      url: config.url,
      param: JSON.stringify(param),
      response: JSON.stringify(response),
      time: moment(new Date()).format("DD/MM/YYYY HH:mm:ss"),
    });
  } catch (error) {
    //
  }
};

const listUrlApiExport: {url: string; fileName: string}[] = [
  {url: ApiCandidate.path.exportCandidate, fileName: "Candidate"},
  {url: ApiRequestJob.path.exportRequestJob, fileName: "Request"},
  {url: ApiCollaborator.paths.export, fileName: "CTV"},
  {url: ApiCustomerPayment.path.export, fileName: "CustomerPayment"},
];

export async function fetcher<T>(
  config: AxiosRequestConfig,
  options: IFetcherOptions = {}
): Promise<T> {
  const defaultOptions: IFetcherOptions = {
    withToken: Config.NETWORK_CONFIG.USE_TOKEN,
    withMetadata: Config.NETWORK_CONFIG.WITH_METADATA,
    displayError: Config.NETWORK_CONFIG.DISPLAY_ERROR,
    ...options,
  };

  const apiClient = axios.create({
    headers: {
      "Content-Type": "application/json",
    },
    baseURL: Config.NETWORK_CONFIG.API_BASE_URL,
    timeout: Config.NETWORK_CONFIG.TIMEOUT,
    withCredentials: true,
  });

  // Access Token
  if (defaultOptions.token) {
    apiClient.defaults.headers.common.Authorization = `Bearer ${defaultOptions.token}`;
  } else {
    if (defaultOptions.withToken) {
      const state = store.getState();
      const token = state.user?.accessToken;
      if (token) {
        apiClient.defaults.headers.common.Authorization = `Bearer ${token}`;
      }
    }
  }

  return new Promise<T | any>((resolve, reject) => {
    apiClient
      .request<T, AxiosResponse<IResponseDTO<T>>>(config)
      .then((response) => {
        if (
          response?.data &&
          typeof response?.data === "string" &&
          String(response?.data)?.includes("<!DOCTYPE html>")
        ) {
          notification.error({
            message: "Phiên đăng nhập của bạn đã hết hạn.",
            duration: 3,
          });
          logout();
        }

        if (config.url === urlGetNew) {
          if (response.status === 200 && response.data !== undefined) {
            resolve(response.data);
            return;
          }
          const errorNews: IDataError = {
            errorCode: response.data.code,
            errorMessage: response.data.messages,
          };
          reject(errorNews);
          return;
        }

        const indexApiExport = listUrlApiExport.findIndex(
          (e) => e.url === config.url
        );
        if (indexApiExport > -1) {
          if (response.status === 200 && response.data instanceof Blob) {
            const a = document.createElement("a");
            document.body.appendChild(a);
            const url = window.URL.createObjectURL(response.data);
            a.href = url;
            a.download = `${
              listUrlApiExport[indexApiExport].fileName
            }_${moment().format("DD_MM_YYYY")}`;
            a.click();
            resolve(response.data);
            return;
          }

          const errorDisplay: IDataError = {
            errorCode: "ERROR",
            errorMessage: "Sever error",
          };
          logErrorFirebase(config, response);
          displayError(errorDisplay);
          reject(response);
          return;
        }

        if (response.data.success) {
          if (response.data.data === undefined) {
            const dataEmpty: IDataError = {
              errorCode: "ERROR???",
              errorMessage: "Data is empty",
            };
            if (defaultOptions.displayError) {
              displayError(dataEmpty);
            }
            logErrorFirebase(config, response);
            reject(dataEmpty);
            return;
          }
          resolve(response.data.data);
          return;
        }

        const dataError: IDataError = {
          errorCode: response.data.code,
          errorMessage:
            response.data.messages ||
            (response.data?.data as any)?.message ||
            (response.data?.errors as any)?.[0]?.message,
        };
        if (defaultOptions.displayError) {
          if (
            response.data.data &&
            (response.data.data as any)?.isEmailOrPhoneExisted &&
            window.location.pathname.includes(Config.PATHNAME.JOB_DETAIL)
          ) {
            store.dispatch(
              setCandidateIdDuplicate(
                Number((response.data.data as any)?.candidateIdDuplicate)
              )
            );
            reject(dataError);
            logErrorFirebase(config, response);
            return;
          }
          displayError(dataError);
        }
        logErrorFirebase(config, response);
        reject(dataError);
      })
      .catch((error: Error | AxiosError) => {
        if (axios.isAxiosError(error)) {
          // Axios error
          const somethingsWrong: IDataError = {
            errorCode: "Lỗi",
            errorMessage: "Đã có lỗi xảy ra. Vui lòng thử lại",
          };

          const dataError: IDataError =
            (error?.response?.data as IDataError) ?? somethingsWrong;

          if (dataError?.errorCode === "AUTH3001.NotAuthenticated") {
            logout();
          } else {
            if (
              defaultOptions.displayError &&
              config.url !== ApiCandidate.path.update &&
              config.url !== ApiCandidate.path.create
            ) {
              displayError(dataError);
            }
          }
        } else {
          // Native error
          notification.error({
            message: "Đã có lỗi xảy ra. Vui lòng thử lại",
            description: _.toString(error),
          });
        }
        logErrorFirebase(config, error);
        return reject(error);
      });
  });
}
