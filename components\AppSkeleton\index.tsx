import {Skeleton, SkeletonProps} from "antd";
import React from "react";

interface Props extends SkeletonProps {
  totalItem?: number;
}

function AppSkeleton(props: Props) {
  const {totalItem = 1} = props;
  const array = Array.from({length: totalItem}, (_, index) => index + 1);
  return (
    <>
      {array.map((i) => (
        <Skeleton {...props} active key={i} />
      ))}
    </>
  );
}

export default AppSkeleton;
