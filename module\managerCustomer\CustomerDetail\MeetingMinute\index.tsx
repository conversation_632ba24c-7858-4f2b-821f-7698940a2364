import {PlusCircleOutlined} from "@ant-design/icons";
import AppTable from "@app/components/AppTable";
import ModalDetailMeeting from "../ModalMeetingDetail";
import useModal from "@app/hooks/useModal";
import {useEffect, useRef, useState} from "react";
import ApiCustomer, {IMeetingMinute} from "@app/api/ApiCustomer";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {ColumnsType} from "antd/lib/table";
import {useRouter} from "next/router";
import {useQuery} from "react-query";

export enum EnumRoleMeeting {
  CREATE = "CREATE",
  DETAIL = "DETAIL",
}

interface IMeetingMinuteModule {
  idCustomer: string;
  role?: EnumRoleMeeting;
  customerName?: string;
}

export default function MeetingMinute({
  idCustomer,
  role = EnumRoleMeeting.CREATE,
  customerName,
}: IMeetingMinuteModule): JSX.Element {
  const router = useRouter();
  const timeOut = useRef<any>();

  const {
    open: openModalMeetingMinute,
    onOpen: onOpenModalMeetingMinute,
    onClose: onCloseModalMeetingMinute,
  } = useModal();
  const [idMeetingMinute, setIdMeetingMinute] = useState<string>();

  const handleCloseModalMeetingMinute = () => {
    onCloseModalMeetingMinute();
    setIdMeetingMinute(undefined);
  };

  const getListMeetingMinute = useQuery(
    ["getListMeetingMinute", idCustomer],
    () => {
      return ApiCustomer.getListInfoMeetingMinute(idCustomer);
    },
    {
      enabled: !!idCustomer,
      onError: (error: any) => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            router.back();
          }, 4000);
        }
      },
    }
  );

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const columnsMeeting: ColumnsType<IMeetingMinute> = [
    {
      title: "Tên file",
      dataIndex: "name",
      key: "name",
      render: (_, item: IMeetingMinute) => (
        // eslint-disable-next-line jsx-a11y/no-static-element-interactions
        <span
          className="cursor-pointer"
          onClick={() => {
            onOpenModalMeetingMinute();
            setIdMeetingMinute(item?.id);
          }}
        >
          {item?.name || ""}
        </span>
      ),
    },
    {
      title: "Người tạo",
      dataIndex: "createdByName",
      key: "createdByName",
    },
    {
      title: "Ngày tạo",
      dataIndex: "requestDate",
      key: "requestDate",
      render: (_, item: IMeetingMinute) => (
        <span>
          {item?.createdDate
            ? moment(item?.createdDate).format(DATE_FORMAT)
            : ""}
        </span>
      ),
    },
  ];

  return (
    <div>
      <div className="mt-4">
        {role === EnumRoleMeeting.CREATE && (
          <div className="text24 text-color-primary mb-4 flex items-center gap-2">
            <div>Meeting minutes</div>
            <PlusCircleOutlined
              className="cursor-pointer"
              onClick={onOpenModalMeetingMinute}
            />
          </div>
        )}
        <AppTable
          dataSource={getListMeetingMinute?.data || []}
          columns={columnsMeeting}
          loading={getListMeetingMinute.isLoading}
          scroll={{y: "55vh"}}
        />
      </div>
      <ModalDetailMeeting
        role={role}
        open={openModalMeetingMinute}
        onCloseModal={handleCloseModalMeetingMinute}
        refetchDataListMeeting={getListMeetingMinute.refetch}
        id={idMeetingMinute}
        customerId={idCustomer}
        customerName={customerName}
      />
    </div>
  );
}
