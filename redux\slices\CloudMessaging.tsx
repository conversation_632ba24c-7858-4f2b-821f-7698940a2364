import {createSlice, PayloadAction} from "@reduxjs/toolkit";

interface DataCloudMessage {
  cloudToken: string;
}

const initialState: DataCloudMessage = {
  cloudToken: "",
};

const cloudMessageSlice = createSlice({
  name: "cloudMessage",
  initialState,
  reducers: {
    setCloudToken: (state: DataCloudMessage, action: PayloadAction<string>) => {
      state.cloudToken = action.payload;
    },
  },
});

export const {setCloudToken} = cloudMessageSlice.actions;

export default cloudMessageSlice.reducer;
