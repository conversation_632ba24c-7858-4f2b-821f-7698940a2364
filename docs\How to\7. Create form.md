# Tạo Form

Base sẽ sử dụng formik-antd cho việc quản lý state của form, về việc validate dữ liệu thì sẽ sử dụng yup

```tsx
import FormItem from "@app/components/FormItem";  //Sử dụng FormItem trong components
import {Input} from "formik-antd";  //Sử dụng input từ thư viện formik-antd thay vì antd
import {
  getValidationSchema,
  ILoginForm,
} from "@app/module/login/NewPassword/form-config";  // Import interface và validation

interface ILoginForm {
  email?: string;
  password?: string;
}

export function LoginForm() {
  const loginMutation = useMutation(ApiUser.login);

  const handleLogin =
    ((
      loginBody: ILoginForm,
      {setSubmitting}: {setSubmitting: (isSubmitting: boolean) => void}
    ): void => {
      // Gọi API
      // ...
      setSubmitting(false);  //Set lại biến isSubmitting của form về false để không show loading sau khi gọi API xong
    },
    []);

  return (
    <Formik
      initialValues={{email: "", password: ""}}
      validateOnChange={false}
      validateOnBlur
      validationSchema={getValidationSchema()}
      onSubmit={handleLogin}
    >
      {({isSubmitting, handleSubmit}): JSX.Element => (
        <div className="container-sign-in">
          <Form onFinish={handleSubmit} className="container-sign-in">
            <div className="header-wrapper">
              <Image
                className="login-image"
                src="img/logo.png"
                preview={false}
              />
              <div className="login-text">Đăng nhập</div>
            </div>
            <FormItem name="email" label="Tài khoản" required>
              <Input name="email" placeholder="Nhập tài khoản" />
            </FormItem>
            <FormItem
              className="pt-20"
              name="password"
              label="Mật khẩu"
              required
            >
              <Input.Password name="password" placeholder="Nhập mật khẩu" />
            </FormItem>
            <Row
              role="button"
              tabIndex={0}
              className="forgot-pass pt-20"
              onClick={(): void => changeTab("forgotPassword")}
            >
              Quên mật khẩu?
            </Row>

            <ButtonSubmit
              label="Đăng nhập"
              isSubmitting={isSubmitting}
              classrow="pt-20"
            />
          </Form>
        </div>
      )}
    </Formik>
  );
}
```
