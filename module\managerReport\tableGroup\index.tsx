import {IApplication} from "@app/api/ApiApplication";
import {IResponseReportManagerForTeam} from "@app/api/ApiReport";
import AppTable from "@app/components/AppTable";
import {TableColumnsType} from "antd";
import classNames from "classnames";
import React, {useMemo} from "react";

interface ITableGroup {
  data: IResponseReportManagerForTeam[] | undefined;
  loading: boolean;
  fetching: boolean;
  onOpenModalTableCV: (data: IApplication[]) => void;
}

export default function TableGroup({
  data,
  loading,
  fetching,
  onOpenModalTableCV,
}: ITableGroup): JSX.Element {
  const dataConvert = useMemo(() => {
    return data?.map((item, index) => {
      return {
        ...item,
        key: index,
        name: item?.groupName,
        children: item?.users?.map((user) => ({...user, name: user?.userName})),
      };
    });
  }, [data]);

  interface DataType {
    key: React.ReactNode;
    name: string;
    candidateCount: number;
    cvSentCount: number;
    interviewCount: number;
    offerCount: number;
    listApplicationCVSent: IApplication[];
    listApplicationInterview: IApplication[];
    listApplicationOffer: IApplication[];
  }

  const columns: TableColumnsType<DataType> = [
    {
      title: "Team",
      dataIndex: "name",
      key: "name",
      align: "left",
      width: "40%",
      render: (value) => <div className="mt-1 whitespace-normal">{value}</div>,
    },
    {
      title: "CV log",
      dataIndex: "candidateCount",
      key: "candidateCount",
    },
    {
      title: "CV sent",
      dataIndex: "cvSentCount",
      key: "cvSentCount",
      render: (count: number, record: DataType) => (
        <div
          role="button"
          tabIndex={0}
          className={classNames(!count && "cursor-auto")}
          onClick={() =>
            onOpenModalTableCV(record?.listApplicationCVSent || [])
          }
        >
          {count}
        </div>
      ),
    },
    {
      title: "Interview",
      dataIndex: "interviewCount",
      key: "interviewCount",
      render: (count: number, record: DataType) => (
        <div
          role="button"
          tabIndex={0}
          className={classNames(!count && "cursor-auto")}
          onClick={() =>
            onOpenModalTableCV(record?.listApplicationInterview || [])
          }
        >
          {count}
        </div>
      ),
    },
    {
      title: "Offer",
      dataIndex: "offerCount",
      key: "offerCount",
      render: (count: number, record: DataType) => (
        <div
          role="button"
          tabIndex={0}
          className={classNames(!count && "cursor-auto")}
          onClick={() => {
            onOpenModalTableCV(record?.listApplicationOffer || []);
          }}
        >
          {count}
        </div>
      ),
    },
  ];

  return (
    <AppTable
      columns={columns}
      dataSource={dataConvert}
      loading={loading || fetching}
      scroll={{y: 600}}
    />
  );
}
