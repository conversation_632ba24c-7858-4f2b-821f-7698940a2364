.update-payment-modal {
  top: 20px;

  .ant-modal-body {
    padding: 10px;
    overflow-y: auto;
    max-height: 86vh;
  }

  .payment-info {
    border: 1px dashed $header_tf;
    padding: 10px 40px;
    overflow: hidden;
    border-radius: 5px;
    margin-top: 10px;
  }

  .input-salary {
    input {
      border-radius: 8px 0px 0px 8px;
    }
  }

  .border-dash {
    border: 1px dashed $header_tf;
    border-radius: 8px;
    margin-top: 21px;
  }

  .ant-input:focus,
  .ant-input-focused {
    box-shadow: none;
  }

  .btn-cancel {
    button {
      background-color: $status-reject;

      &:focus {
        background-color: $status-reject;
      }
    }

    .ant-btn[disabled] {
      background-color: $status-reject;
      opacity: 0.5;
      color: $white-color;
    }
  }
  .ant-btn[disabled] {
    background-color: $primary-color;
    color: $white-color;
  }
}
