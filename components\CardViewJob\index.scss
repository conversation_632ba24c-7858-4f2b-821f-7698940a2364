.container-card {
  color: $text-color-input;
  border-radius: 8px;

  .ant-card:hover {
    background-color: #f1f2f2;
  }

  .ant-card-bordered {
    border-radius: 8px;
  }

  .container-icon-book-mark {
    position: absolute;
    top: 15px;
    right: 15px;
  }

  .container-icon-checkbox {
    position: absolute;
    top: 22px;
    right: 15px;
  }

  .container-icon-count-candidate {
    position: absolute;
    top: 88px;
    right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 26px;
    width: 26px;
    background-color: #ff0000b2;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 600;
    color: #fff;
    padding: 5px;
  }

  .color-text {
    color: $status-reject;
  }

  .card-padding {
    .ant-card-body {
      padding: 15px;
    }
  }

  .icon-book-mark {
    path {
      fill: $status-reject !important;
    }
  }

  &__keyword {
    padding: 4px 8px;
    border-radius: 8px;
    border: 1px solid $header_tf05;
    margin-right: 8px;
  }

  &__label {
    position: absolute;
    top: 0;
    left: 0;
  }

  .text-label-request {
    font-size: 8px;
  }

  &__line-height {
    line-height: 14px;
  }

  .job-check-box {
    position: absolute;
    bottom: 15px;
    right: 15px;
  }

  &__assigned {
    background-color: #ff0000b2;
    padding: 2px 4px;
    border-radius: 8px;
  }

  &__name {
    max-width: 60%;
    width: fit-content;
    @media screen and (max-width: 991px) {
      max-width: 90%;
    }
  }

  &__bonus {
    display: flex;
    @media screen and (max-width: 991px) {
      flex-direction: column;
    }
  }
}
