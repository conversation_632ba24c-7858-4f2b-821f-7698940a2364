import React, {useState} from "react";
import "./index.scss";
import FilterCollaborator from "./FilterCollaborator";
import {Row, Select, notification} from "antd";
import Icon from "@app/components/Icon/Icon";
import {useMutation, useQuery} from "react-query";
import ApiCollaborator, {
  IListCollaborator,
  IManagerFilters,
  IParamsCollaborator,
  IParamsItemChangePartnerManager,
  IParamsItemChangePartnerStatuses,
  IPartnerPaging,
} from "@app/api/ApiCollaborator";
import AppTable from "@app/components/AppTable";
import AppPagination from "@app/components/AppPagination";
import {ColumnsType} from "antd/es/table";
import AppButton from "@app/components/AppButton";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {
  getStatusCollaborator,
  statusCollaborator,
} from "@app/utils/constants/state";

import {useDispatch, useSelector} from "react-redux";
import AppModal from "@app/components/AppModal";
import {useRouter} from "next/router";
import config from "@app/config";
import {
  changeListColShowCollaborator,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {IAccountRole} from "@app/types";
import _ from "lodash";
import {timeSince} from "@app/utils/constants/function";

const initialValuesFilterCollaborator: IParamsCollaborator = {
  textSearch: "",
  name: "",
  phoneNumber: "",
  email: "",
  managerSelected: [],
  statusSelected: [],
  from: "", // DD/MM/YYYY
  to: "", // DD/MM/YYYY
  pageSize: 20,
  currentPage: 1,
};

export const listColDefaultCollaborator = ["name", "activeDate"];

export default function ManagerCollaborator(): JSX.Element {
  const router = useRouter();
  let query = {} as any;

  if (!_.isEmpty(router.query)) {
    query = router.query;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    query = Object.fromEntries(searchParams);
  }

  query = {
    ...initialValuesFilterCollaborator,
    ...query,
    currentPage: query.currentPage ? Number(query.currentPage) : 1,
    pageSize: query.pageSize ? Number(query.pageSize) : 20,
    statuses: query.statuses
      ? query.statuses?.split(",")?.map((i: any) => Number(i))
      : [],
    managers: query.managers
      ? query.managers?.split(",")?.map((i: any) => Number(i))
      : [],
  };

  const [valuesFilter, setValuesFilter] = useState<IParamsCollaborator>(query);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const dispatch = useDispatch<any>();
  const {listColShowCollaborator, user} = useSelector(selectUser);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [typeModal, setTypeModal] = useState<"changeStatus" | "assign">(
    "changeStatus"
  );
  const [statusSelected, setStatusSelected] = useState(
    statusCollaborator[0].id
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [collaboratorIsAssign, setCollaboratorIsAssign] = useState<any>();

  const isCSL =
    user?.role?.includes(IAccountRole.CSL) ||
    user?.role?.includes(IAccountRole.ADMIN);

  const dataListCollaborators = useQuery(
    ["getListCollaborator", valuesFilter],
    () => {
      return ApiCollaborator.getListCollaborator({
        ...valuesFilter,
        isBackFromChildrenView: true,
      });
    },
    {
      onSuccess: (res: IListCollaborator): void => {
        const newQuery = {
          email: res.email,
          name: res.name,
          phoneNumber: res.phoneNumber,
          textSearch: res.textSearch,
          to: res.to,
          from: res.from,
          statuses: res.statuses ? res.statuses.join() : "",
          managers: res.managers ? res.managers.join() : "",
          pageSize: res.pageSize,
          currentPage: res.currentPage,
        } as any;

        Object.keys(newQuery).forEach((key) => {
          if (!newQuery[key]) {
            delete newQuery[key];
          }
        });

        router.push(router.pathname, {
          query: newQuery,
        });
        setCollaboratorIsAssign({
          value: res.managerFilters[0]?.userId || "",
          label: res.managerFilters[0]?.name || "",
        });
      },
    }
  );

  const exportDataCollaborators = useMutation(
    (param: IParamsCollaborator) => {
      return ApiCollaborator.exportListCollaborator(param);
    },
    {
      onSuccess: () => {
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const saveChangePartnerStatuses = useMutation(
    (param: IParamsItemChangePartnerStatuses[]) => {
      return ApiCollaborator.saveChangePartnerStatuses(param);
    },
    {
      onSuccess: () => {
        notification.success({message: "Thay đổi trạng thái thành công"});
        dataListCollaborators.refetch();
        setIsOpenModal(false);
      },
    }
  );

  const saveChangePartnerManager = useMutation(
    (param: IParamsItemChangePartnerManager[]) => {
      return ApiCollaborator.saveChangePartnerManager(param);
    },
    {
      onSuccess: () => {
        notification.success({message: "Thay đổi người quản lý thành công"});
        dataListCollaborators.refetch();
        setIsOpenModal(false);
      },
    }
  );

  const managerFilters =
    dataListCollaborators.data?.managerFilters?.map(
      (manager: IManagerFilters) => ({
        key: manager.userId ? String(manager.userId) : "",
        value: manager.userId ? String(manager.userId) : "",
        label: manager?.name || "",
      })
    ) || [];

  const onSelectChange = (newSelectedRowKeys: React.Key[]): void => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesFilter({
      ...valuesFilter,
      pageSize,
      currentPage: page,
    });
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const setListColShow = (listCheckBox: string[]): void => {
    dispatch(changeListColShowCollaborator(listCheckBox));
  };

  const handleClose = (): void => {
    setIsOpenModal(false);
  };

  const showModalChangeStatusCollaborator = (): void => {
    if (selectedRowKeys.length === 0) {
      notification.error({message: "Bạn cần chọn cộng tác viên"});
    } else {
      setTypeModal("changeStatus");
      setIsOpenModal(true);
    }
  };

  const showModalAssignCollaborator = (): void => {
    if (selectedRowKeys.length === 0) {
      notification.error({message: "Bạn cần chọn cộng tác viên"});
    } else {
      setTypeModal("assign");
      setIsOpenModal(true);
    }
  };

  const exportData = (): void => {
    dispatch(setLoading(true));
    exportDataCollaborators.mutate({
      ...valuesFilter,
      isBackFromChildrenView: true,
    });
  };

  const updateCollaborator = (): void => {
    if (typeModal === "changeStatus") {
      const listUpdate = selectedRowKeys.map((item) => ({
        partnerId: Number(item),
        newPartnerStatus: Number(statusSelected),
      }));
      saveChangePartnerStatuses.mutate(listUpdate);
    } else {
      if (collaboratorIsAssign?.value) {
        const listUpdate = selectedRowKeys.map((item) => ({
          partnerId: Number(item),
          consultantId: collaboratorIsAssign.value,
        }));
        saveChangePartnerManager.mutate(listUpdate);
      } else {
        notification.error({
          message: "Bạn cần chọn CTV",
        });
      }
    }
  };

  const routerToDetail = (collaboratorId: number): void => {
    router.push(`${config.PATHNAME.COLLABORATOR_DETAIL}?id=${collaboratorId}`);
  };

  const onChangeCollaborator = (value: number): void => {
    const manager =
      managerFilters?.find((item) => item.key === String(value)) || {};
    setCollaboratorIsAssign(manager);
  };

  const columnAll: ColumnsType<IPartnerPaging> = [
    {
      title: "Họ tên",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      className: "cursor-pointer",
      render: (_, item: IPartnerPaging) => (
        <span className="flex w-full pr-1 text-start">
          {`${item.userId} - ${item.name}`}
        </span>
      ),
      onCell: (record: IPartnerPaging): any => {
        return {
          onClick: (): void => {
            routerToDetail(record.userId);
          },
        };
      },
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {title: "Số điện thoại", dataIndex: "phoneNumber", key: "phoneNumber"},
    {
      title: "Quản lý",
      dataIndex: "consultantName",
      key: "consultantName",
    },
    {
      title: "Người giới thiệu",
      dataIndex: "recomenndationPersonName",
      key: "recomenndationPersonName",
    },
    {
      title: "Lần cuối hoạt động",
      dataIndex: "activeDate",
      key: "activeDate",
      render: (activeDate: string) => timeSince(activeDate),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (_, item: IPartnerPaging): JSX.Element => {
        const status = getStatusCollaborator(item.statusId);
        return (
          <div>
            <span
              className="status-collaborator"
              style={{backgroundColor: status?.color}}
            >
              {item.status}
            </span>
          </div>
        );
      },
    },
    {
      title: "Giới tính",
      dataIndex: "gender",
      key: "gender",
      render: (_, item: IPartnerPaging) => (
        <span>{item.gender === 1 ? "Nam" : "Nữ"}</span>
      ),
      width: "7%",
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      render: (_, item: IPartnerPaging) => (
        <span>{moment(item.createdDate).format(DATE_FORMAT)}</span>
      ),
      width: "10%",
    },
    {
      title: "Số ứng viên",
      dataIndex: "countCandidate",
      key: "countCandidate",
    },
    {
      title: "Interview",
      dataIndex: "countInterview",
      key: "countInterview",
      width: "7%",
    },
    {
      title: "Offer",
      dataIndex: "countOffer",
      key: "countOffer",
      width: "5%",
    },
    {
      title: "Bonus",
      dataIndex: "bonus",
      key: "bonus",
      width: "5%",
    },
    {
      title: "Ghi chú",
      dataIndex: "note",
      key: "note",
      width: "10%",
    },
  ];

  const columns = columnAll.filter((item) =>
    [...listColShowCollaborator, ...listColDefaultCollaborator].some(
      (i) => i === item.key
    )
  );

  return (
    <div className="container-manager-collaborator flex-1">
      <FilterCollaborator
        columnAll={columnAll}
        listColShow={listColShowCollaborator}
        setListColShow={setListColShow}
        setValuesFilter={setValuesFilter}
        managerFilters={managerFilters}
        isCSL={isCSL}
        valuesFilter={valuesFilter}
        initialValuesFilterCollaborator={initialValuesFilterCollaborator}
      />
      <Row className="mt-3 mb-3 items-center">
        <span
          onClick={showModalChangeStatusCollaborator}
          role="button"
          tabIndex={0}
        >
          <Icon className="mr-4" icon="tab-arrow" size={24} color="#324054" />
        </span>
        {isCSL && (
          <span
            onClick={showModalAssignCollaborator}
            role="button"
            tabIndex={0}
          >
            <Icon
              className="mr-4"
              icon="user_shared_line"
              size={24}
              color="#324054"
            />
          </span>
        )}
        <span onClick={exportData} role="button" tabIndex={0}>
          <Icon icon="download-cloud-line" size={24} color="#324054" />
        </span>
      </Row>
      <AppTable
        rowClassName="row-table-ctv"
        dataSource={dataListCollaborators?.data?.partnerPaging?.map(
          (item: IPartnerPaging, index: number) => ({
            ...item,
            key: index,
          })
        )}
        columns={columns}
        scroll={{x: "100%", y: "60vh"}}
        rowSelection={rowSelection}
        loading={dataListCollaborators.isLoading}
        rowKey={(record: IPartnerPaging): number => record.userId}
      />
      <AppModal
        className="modal-update-collaborator"
        open={isOpenModal}
        footer={null}
        onCancel={handleClose}
        width={600}
        title={
          typeModal === "changeStatus"
            ? "Đổi  trạng thái CTV"
            : "Assign hàng loạt CTV cho CST"
        }
      >
        {typeModal === "changeStatus" ? (
          <div className="content-modal">
            {`Bạn muốn đổi trạng thái của ${selectedRowKeys.length} CTV thành`}
            <Select
              className="ml-3"
              value={statusSelected}
              options={statusCollaborator.map((i) => ({
                label: i.label,
                value: i.id,
              }))}
              suffixIcon={
                <Icon
                  size={10}
                  icon="arrow-drop-down-line"
                  color="#324054"
                  className=""
                />
              }
              onChange={setStatusSelected}
            />
          </div>
        ) : (
          <div className="content-modal">
            {`Bạn muốn assign ${selectedRowKeys.length} CTV cho`}
            <Select
              className="ml-3"
              value={collaboratorIsAssign}
              options={managerFilters}
              suffixIcon={
                <Icon
                  size={10}
                  icon="arrow-drop-down-line"
                  color="#324054"
                  className=""
                />
              }
              onChange={onChangeCollaborator}
            />
          </div>
        )}

        <Row className="mt-8 justify-center flex">
          <AppButton
            classrow="btn-cancel mr-2"
            typebutton="secondary"
            label="Hủy bỏ"
            onClick={handleClose}
          />
          <AppButton
            classrow="btn-confirm ml-2"
            typebutton="primary"
            label={typeModal === "changeStatus" ? "Đổi Trạng thái" : "Assign"}
            onClick={updateCollaborator}
            disabled={
              saveChangePartnerManager.isLoading ||
              saveChangePartnerStatuses.isLoading
            }
          />
        </Row>
      </AppModal>
      <AppPagination
        className="mt-6"
        defaultPageSize={valuesFilter.pageSize}
        current={valuesFilter.currentPage}
        pageSize={valuesFilter.pageSize}
        total={dataListCollaborators.data?.totalCount}
        onChange={handlePagination}
      />
    </div>
  );
}
