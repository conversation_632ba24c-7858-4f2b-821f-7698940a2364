import {IDataPagingHotBonus} from "@app/api/ApiPaymentBonus";
import AppModal from "@app/components/AppModal";
import {formatMoney, getPaymentStatus} from "@app/utils/constants/function";
import React, {useMemo} from "react";
import "./index.scss";
import {Col, Row} from "antd";
import AppButton from "@app/components/AppButton";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";

interface Props {
  title: string;
  open: boolean;
  dataHotBonus: IDataPagingHotBonus;
  onCancel: () => void;
  onOpenModalPayment: () => void;
}

function ModalHotBonusDetail(props: Props): JSX.Element {
  const {title, open, dataHotBonus, onCancel, onOpenModalPayment} = props;

  const leftInformation = [
    {
      label: "V<PERSON> trí ứng tuyển",
      value: dataHotBonus?.positionName || "N/A",
    },
    {
      label: "Cộng tác viên",
      value: dataHotBonus?.creatorName || "N/A",
    },
    {
      label: "Mức bonus",
      value: dataHotBonus?.hotBonusAmount
        ? formatMoney(dataHotBonus?.hotBonusAmount)
        : "N/A",
    },
  ];

  const rightInformation = [
    {
      label: "Trạng thái ứng tuyển",
      value: `${dataHotBonus?.stageName ? dataHotBonus?.stageName : ""} ${
        dataHotBonus?.statusName ? dataHotBonus?.statusName : ""
      }`,
    },
    {
      label: "Khách hàng",
      value: dataHotBonus?.customerName || "N/A",
    },
  ];

  const totalMoney = useMemo((): string => {
    if (dataHotBonus?.paymentBonusStatus === 0) {
      return dataHotBonus?.hotBonusUnpaid
        ? formatMoney(dataHotBonus?.hotBonusUnpaid)
        : "N/A";
    }

    if (dataHotBonus?.paymentBonusStatus === 2) {
      return dataHotBonus?.hotBonusPaid
        ? formatMoney(dataHotBonus?.hotBonusPaid)
        : "N/A";
    }

    return "";
  }, [dataHotBonus]);

  return (
    <AppModal
      title={title}
      open={open}
      footer={null}
      className="hot-bonus-detail-ui"
      onCancel={onCancel}
      centered
    >
      <div className="hot-bonus-detail-ui__detail">
        <div className="hot-bonus-detail-ui__border">
          <div className="hot-bonus-detail-ui__header flex justify-between items-center">
            <div className="text24">{dataHotBonus?.candidateName || "N/A"}</div>
            <div className="flex items-center">
              <div
                className="hot-bonus-detail-ui__status mr-2"
                style={{
                  backgroundColor: getPaymentStatus(
                    dataHotBonus?.paymentBonusStatus
                  ).color,
                }}
              />
              <div>
                {getPaymentStatus(dataHotBonus?.paymentBonusStatus).label}
              </div>
            </div>
          </div>
          <div className="hot-bonus-detail-ui__container mt-4 text16">
            <div className="hot-bonus-detail-ui__padding mt-4">
              <div className="flex justify-between">
                <div>
                  {leftInformation.map((item) => (
                    <div className="mt-4" key={item.value}>
                      <p className="hot-bonus-detail-ui__label text12">
                        {item.label}
                      </p>
                      <p className="hot-bonus-detail-ui__value text14">
                        {item.value}
                      </p>
                    </div>
                  ))}
                </div>
                <div>
                  {rightInformation.map((item) => (
                    <div className="mt-4" key={item.value}>
                      <p className="hot-bonus-detail-ui__label text12">
                        {item.label}
                      </p>
                      <p className="hot-bonus-detail-ui__value text14">
                        {item.value}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <hr className="my-4" />
              <div className="flex justify-between text16">
                <p>Tổng tiền</p>
                <p>{totalMoney}</p>
              </div>
              <div className="flex justify-between text14">
                <p>Ngày thanh toán</p>
                <p>
                  {dataHotBonus?.paymentDate
                    ? moment(dataHotBonus?.paymentDate).format(DATE_FORMAT)
                    : "N/A"}
                </p>
              </div>
              <p className="mt-2 text14">Ghi chú</p>
              <p className="text12">{dataHotBonus?.note || "N/A"}</p>
            </div>
          </div>
        </div>
        <div className="hot-bonus-detail-ui__footer mt-4">
          <Row gutter={[16, 16]} justify="center">
            <Col xs={8}>
              <AppButton
                label="Đóng"
                onClick={onCancel}
                typebutton="secondary"
              />
            </Col>
            {dataHotBonus?.paymentBonusStatus === 0 && (
              <Col xs={8}>
                <AppButton
                  label="Cập nhật"
                  typebutton="primary"
                  onClick={onOpenModalPayment}
                />
              </Col>
            )}
          </Row>
        </div>
      </div>
    </AppModal>
  );
}

export default React.memo(ModalHotBonusDetail);
