import {DatePicker} from "antd";
import React, {ReactNode, useCallback, useEffect, useState} from "react";
import {useRouter} from "next/router";
import PanelRangePicker from "./panelRangePicker";
import {
  DATE_FORMAT,
  NORMAL_DATE_FORMAT,
} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import {useSearchParams} from "next/navigation";
import {QueryObserverResult} from "react-query";

const {RangePicker} = DatePicker;

interface IRangeDateFilter {
  refetch: () => Promise<QueryObserverResult<any, unknown>>;
}

export default function RangeDateFilter({
  refetch,
}: IRangeDateFilter): JSX.Element {
  const router = useRouter();

  const searchParams = useSearchParams();

  const startDateQuery = searchParams.get("startDate");
  const endDateQuery = searchParams.get("endDate");

  const [valueRangePicker, setValueRangePicker] = useState<any>([
    moment().startOf("isoWeek"),
    moment().endOf("isoWeek"),
  ]);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (startDateQuery && endDateQuery) {
      setValueRangePicker([
        moment(startDateQuery, NORMAL_DATE_FORMAT),
        moment(endDateQuery, NORMAL_DATE_FORMAT),
      ]);
    } else {
      router.push(router.pathname, {
        query: {
          startDate: valueRangePicker?.[0]?.format(NORMAL_DATE_FORMAT),
          endDate: valueRangePicker?.[1]?.format(NORMAL_DATE_FORMAT),
        },
      });
    }
  }, [startDateQuery, endDateQuery]);

  const handleChangeDate = (value: any) => {
    const startDate = value?.[0]?.format(NORMAL_DATE_FORMAT);
    const endDate = value?.[1]?.format(NORMAL_DATE_FORMAT);

    router.push(router.pathname, {
      query: {
        startDate,
        endDate,
      },
    });
    setValueRangePicker(value);
  };

  const handleCloseModalRangePicker = () => {
    setOpen(false);
  };

  const panelRender = useCallback(
    (panelNode: ReactNode) => (
      <PanelRangePicker
        panelNode={panelNode}
        onChangeDate={handleChangeDate}
        onCloseModalRangePicker={handleCloseModalRangePicker}
      />
    ),
    []
  );

  return (
    <div>
      <RangePicker
        className="w-full p-[14px] rounded-lg min-w-[260px] max-w-[280px]"
        value={valueRangePicker}
        onChange={handleChangeDate}
        format={DATE_FORMAT}
        style={{width: 300}}
        panelRender={panelRender}
        open={open}
        onOpenChange={setOpen}
        allowClear={false}
      />
    </div>
  );
}
