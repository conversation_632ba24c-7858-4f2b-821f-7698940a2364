import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {TextInput} from "@app/components/TextInput";
import {Checkbox, Col, Popover, Row} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useCallback, useEffect, useRef, useState} from "react";
import "./index.scss";
import {SelectInput} from "@app/components/SelectInput";
import {IResCustomer} from "@app/api/ApiCustomer";
import {
  companyTypes,
  deadTimeFastSearch,
  statusCustomers,
} from "@app/utils/constants/state";
// eslint-disable-next-line import/no-cycle
import {IFormFilter} from "..";
import AppCheckBox from "@app/components/AppCheckbox";
import {useDispatch, useSelector} from "react-redux";
import {
  changeColCustomer,
  initListColShowCustomer,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {CheckboxValueType} from "antd/lib/checkbox/Group";
import {useRouter} from "next/router";
import config from "@app/config";
import {IAccountRole} from "@app/types";
import _ from "lodash";

interface FormFilterProps {
  listCustomerData?: IResCustomer;
  setValuesSearch: (value: IFormFilter) => void;
  valuesSearch: IFormFilter;
  sumCol: number;
}

interface IItemCol {
  label: string;
  value: string;
}

export default function FormFilter(props: FormFilterProps): JSX.Element {
  const {listCustomerData, setValuesSearch, valuesSearch, sumCol} = props;
  const router = useRouter();
  const [isShowFilterAdvance, setIsShowFilterAdvance] =
    useState<boolean>(false);
  const timeOut = useRef<any>();
  const filterAdvancedRef = useRef<FormikProps<IFormFilter>>(null);
  const dispatch = useDispatch();
  const {listColShowCustomer, user} = useSelector(selectUser);
  const isAML = user?.role?.includes(IAccountRole.AML);
  const isAMG = user?.role?.includes(IAccountRole.AMG);
  const isBD = [IAccountRole.BD, IAccountRole.BDL].some((item) =>
    user?.role?.includes(item)
  );
  // const managerFilters =
  //   listCustomerData?.managerFilters?.map((item) => ({
  //     key: item.userId,
  //     value: item.name,
  //     label: item.name,
  //   })) || [];

  // useEffect(() => {
  //   const managersSelected =
  //     managerFilters.filter((item) =>
  //       valuesSearch.managers?.some((i) => String(i) === String(item.key))
  //     ) || [];
  //   filterAdvancedRef.current?.setFieldValue(
  //     "managersSelected",
  //     managersSelected
  //   );
  // }, [JSON.stringify(managerFilters)]);

  const listColLeft: IItemCol[] = [
    {
      label: "Tên người liên hệ",
      value: "contact",
    },
    {
      label: "Số điện thoại",
      value: "phone",
    },
    {
      label: "Email",
      value: "email",
    },
    {
      label: "Loại khách hàng",
      value: "rank",
    },
  ];

  const listColRight: IItemCol[] = [
    {
      label: "Loại hình",
      value: "companyType",
    },
    {
      label: "Trạng thái",
      value: "customerStatus",
    },
    {
      label: "Địa chỉ",
      value: "address",
    },
    {
      label: "Số lượng request job",
      value: "countRequestJob",
    },
  ];

  const listColShow = listColShowCustomer || initListColShowCustomer;
  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const handleSearch = () => {
    const values = filterAdvancedRef.current?.values;
    const searchParams = {
      companyTypes: values?.companyTypeSelected?.value
        ? [values.companyTypeSelected?.value]
        : [],
      customerStatuses: values?.customerStatusSelected?.value
        ? [values.customerStatusSelected?.value]
        : [],
      name: values?.name || "",
      currentPage: 1,
      pageSize: valuesSearch.pageSize,
      managers:
        values?.managersSelected?.map((item) => String(item?.key) || "") || [],
      textSearch: values?.textSearch?.trim() || "",
    };
    setValuesSearch(searchParams);
  };

  const handleSearchDebounce = useCallback(
    _.debounce(() => {
      handleSearch();
    }, deadTimeFastSearch),
    [handleSearch]
  );

  const resetData = (): void => {
    const initialValuesSearch = {
      textSearch: "",
      managersSelected: [],
      name: "",
      companyTypeSelected: "",
      customerStatusSelected: "",
      pageSize: valuesSearch.pageSize,
      currentPage: 1,
    };
    filterAdvancedRef.current?.setValues(initialValuesSearch as any);
    setValuesSearch({
      textSearch: "",
      currentPage: 1,
      pageSize: valuesSearch.pageSize,
    });
  };

  const onClickSearch = (): void => {
    handleSearch();
  };

  const onChangeListCol = (checkedValues: CheckboxValueType[]): void => {
    dispatch(changeColCustomer(checkedValues as string[]));
  };

  const addCustomer = (): void => {
    router.push(config.PATHNAME.CUSTOMER_CREATE);
  };

  const filterCol = (
    <Checkbox.Group
      className="group-check-box-list-col"
      value={listColShow}
      onChange={onChangeListCol}
    >
      <Row>
        <Col span={12}>
          {listColLeft.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col span={12}>
          {listColRight.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );

  function contentFilterAdvance(values: IFormFilter): JSX.Element {
    return (
      <div className="content-filter-advance">
        <Row className="flex items-center justify-between mb-4">
          <span className="title-filter">Tất cả bộ lọc</span>
          <AppButton
            classrow="btn-close-popover"
            typebutton="normal"
            onClick={(): void => setIsShowFilterAdvance(false)}
          >
            <Icon icon="close-circle-line" size={20} />
          </AppButton>
        </Row>
        {!isAMG && (
          <SelectInput
            containerclassname="mt-2"
            name="managersSelected"
            mode="multiple"
            labelselect="AMG quản lý"
            data={
              listCustomerData?.managerFilters?.map((item) => ({
                key: item.userId,
                value: item.name,
                label: item.name,
              })) || []
            }
            value={values.managersSelected}
            free={values?.managersSelected?.length === 0}
            allowClear
            handleChange={handleSearchDebounce}
          />
        )}
        <TextInput
          containerclassname="mt-2"
          label="Tên khách hàng"
          name="name"
          value={values.name}
          free={!values.name}
          onChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2"
          name="companyTypeSelected"
          labelselect="Loại hình"
          data={companyTypes}
          value={values.companyTypeSelected}
          free={!values?.companyTypeSelected?.value}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2"
          name="customerStatusSelected"
          labelselect="Trạng thái"
          data={statusCustomers}
          value={values.customerStatusSelected}
          free={!values?.customerStatusSelected?.value}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <Row className="mt-6 div-time">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={resetData}
          />
          {/* <AppButton
            label="Tìm kiếm"
            typebutton="primary"
            onClick={onClickSearch}
          /> */}
        </Row>
      </div>
    );
  }

  return (
    <Formik
      initialValues={valuesSearch}
      innerRef={filterAdvancedRef}
      onSubmit={() => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <Row className="justify-between items-center filter-customer-container">
            <TextInput
              containerclassname="w-2/5"
              label="Nhập tên khách hàng, mã khách hàng để tìm kiếm"
              name="textSearch"
              value={values.textSearch}
              onChange={handleSearchDebounce}
            />
            <Row>
              {(isAML || isBD) && (
                <AppButton
                  typebutton="normal"
                  classrow="add-btn"
                  onClick={addCustomer}
                >
                  <Icon icon="add-line" size={16} />
                  Thêm mới khách hàng
                </AppButton>
              )}
              <Popover
                className="mr-1"
                placement="bottom"
                trigger="click"
                content={(): React.ReactNode => contentFilterAdvance(values)}
                open={isShowFilterAdvance}
                onOpenChange={setIsShowFilterAdvance}
              >
                <AppButton typebutton="normal" classrow="btn-filter">
                  <Icon
                    className="mr-1"
                    icon="filter-line"
                    size={12}
                    color="#324054"
                  />
                  Tìm kiếm nâng cao
                </AppButton>
              </Popover>
              <Popover placement="bottom" trigger="click" content={filterCol}>
                <AppButton typebutton="normal" classrow="btn-filter">
                  <Icon className="mr-1" icon="eye" size={16} color="#324054" />
                  Hiển thị {listColShow.length + 2}/{sumCol}
                </AppButton>
              </Popover>
            </Row>
          </Row>
        );
      }}
    </Formik>
  );
}
