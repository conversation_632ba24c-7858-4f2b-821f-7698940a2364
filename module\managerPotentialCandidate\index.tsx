import AppTable from "@app/components/AppTable";
import "./index.scss";
import {ColumnsType} from "antd/es/table";
import ApiPotentialCandidate, {
  PotentialCandidateColumns,
  ValuesSearchPotential,
} from "@app/api/ApiPotentialCandidate";
import {useMutation, useQuery} from "react-query";
import {useEffect, useMemo, useState} from "react";
import AppPagination from "@app/components/AppPagination";
import {FilterPotentialCandidate} from "./filterPotentialCandidate";
import ApiCandidate from "@app/api/ApiCandidate";
import {Tooltip} from "antd";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppModal from "@app/components/AppModal";
import {CandidateInformationDetail} from "../managerCandidate/candidateInformationDetail";
import {useRouter} from "next/router";
import {
  logEventFirebase,
  setQueryUrl,
  sortWorkLocation,
} from "@app/utils/constants/function";
import {OptionSelect} from "@app/types";
import config from "@app/config";
// import {candidateSource} from "@app/utils/constants/state";

export default function ManagerPotentialCandidate(): JSX.Element {
  const [pageParams, setPageParams] = useState({
    pageSize: 20,
    currentPage: 1,
  });
  const [searchParams, setSearchParams] = useState<ValuesSearchPotential>({
    currentPage: 1,
    hasContact: true,
    // source: [candidateSource[0].key],
  } as ValuesSearchPotential);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [dataSource, setDataSource] = useState<PotentialCandidateColumns[]>([]);
  const [showCandidateDetail, setShowCandidateDetail] =
    useState<boolean>(false);
  const [id, setId] = useState<number | string>("");
  const router = useRouter();

  let idCandidate: string | undefined;
  if (router.query.id) {
    idCandidate = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    idCandidate = Object.fromEntries(searchParams)?.id;
  }

  useEffect(() => {
    if (idCandidate) {
      showModalDetail(idCandidate);
    }
  }, [idCandidate]);

  const showModalDetail = (idCandidate: string): void => {
    setId(idCandidate);
    setShowCandidateDetail(true);
  };

  const requestWorkLocationList = useQuery("requestWorkLocationList", () => {
    return ApiCandidate.getWorkLocationList();
  });

  const workLocations: OptionSelect[] = useMemo(() => {
    const newData: OptionSelect[] =
      requestWorkLocationList?.data?.map((location) => ({
        label: location?.name,
        value: location?.name,
        key: location?.workLocationId,
      })) ?? [];
    return newData;
  }, [requestWorkLocationList?.data]);

  const requestSkillList = useQuery("requestSkillList", () => {
    return ApiCandidate.getListSkill();
  });

  const skills: OptionSelect[] = useMemo(() => {
    const newData: OptionSelect[] =
      requestSkillList?.data?.map((skill) => ({
        label: skill?.name,
        value: skill?.name,
        key: skill?.skillId,
      })) ?? [];
    return newData;
  }, [requestSkillList?.data]);

  const requestPotentialCandidateList = useMutation(
    (data: FormData) => {
      logEventFirebase(config.EVENT_ANALYTICS_KEY.SEARCH_POTENTIAL_CANDIDATE);
      return ApiPotentialCandidate.getListPotentialCandidate(data);
    },
    {
      onSuccess: (data) => {
        setTotalCount(data?.length || 0);
      },
    }
  );

  const onSearchParams = () => {
    const formData = new FormData();
    const dataClone: ValuesSearchPotential = searchParams;

    Object.entries(dataClone)?.forEach(([key, value]) => {
      if (typeof value === "string" && !value) {
        delete dataClone[key as keyof ValuesSearchPotential];
      }

      if (Array.isArray(value) && value?.length === 0) {
        delete dataClone[key as keyof ValuesSearchPotential];
      }
    });
    Object.entries(dataClone)?.forEach(([key, value]) => {
      if (Array.isArray(value) && value?.length > 0) {
        value.forEach((item) => {
          const keyAppend = `${key}[]`;
          formData.append(keyAppend, item);
        });
      } else {
        formData.append(key, value);
      }
    });
    requestPotentialCandidateList.mutate(formData);
  };

  useEffect(() => {
    onSearchParams();
  }, [searchParams]);

  const columns: ColumnsType<PotentialCandidateColumns> = [
    {
      title: "Họ và tên",
      key: "name",
      dataIndex: "name",
      align: "left",
      width: "15%",
      ellipsis: true,
    },
    {
      title: "Địa điểm làm việc",
      key: "address",
      dataIndex: "address",
      width: "15%",
      render: (_: string, record: PotentialCandidateColumns): JSX.Element => {
        const address = workLocations?.find(
          (item) => item?.key === record?.address
        );
        return (
          <div className="truncate w-full text-ellipsis overflow-hidden">
            {address?.label || ""}
          </div>
        );
      },
    },
    {
      title: "Vị trí",
      key: "position",
      dataIndex: "position",
      width: "18%",
      ellipsis: true,
      render: (_: string, record: PotentialCandidateColumns): JSX.Element => (
        <div className="truncate w-full text-ellipsis overflow-hidden">
          {record?.position || ""}
        </div>
      ),
    },
    {
      title: "Kĩ năng",
      key: "skills",
      dataIndex: "skills",
      width: "18%",
      ellipsis: true,
      render: (_: string, record: PotentialCandidateColumns): JSX.Element => {
        const content =
          record?.skills?.length > 0 ? record?.skills?.join(", ") : "";
        return (
          <div className="truncate w-full text-ellipsis overflow-hidden">
            {content ? (
              <Tooltip title={content} placement="bottomLeft">
                {content}
              </Tooltip>
            ) : (
              ""
            )}
          </div>
        );
      },
    },
    {
      title: "Ngoại ngữ",
      key: "language",
      dataIndex: "language",
      ellipsis: true,
      width: "18%",
      render: (_: string, record: PotentialCandidateColumns): JSX.Element => {
        let content = "";
        if (record?.language && record?.language?.length > 0) {
          const newData =
            record?.language?.map(
              // eslint-disable-next-line no-unsafe-optional-chaining
              (item) => item?.language + " - " + item?.level
            ) || [];
          content = newData?.join(", ");
        }
        return (
          <div className="truncate text-ellipsis overflow-hidden">
            {content ? (
              <Tooltip title={content} placement="bottomLeft">
                {content}
              </Tooltip>
            ) : (
              ""
            )}
          </div>
        );
      },
    },
    {
      title: "Nguồn ứng viên",
      key: "source",
      dataIndex: "source",
      ellipsis: true,
      width: "10%",
    },
    {
      title: "Ngày tạo",
      key: "time",
      dataIndex: "time",
      ellipsis: true,
      width: "10%",
      render: (_: string, record: PotentialCandidateColumns): JSX.Element => {
        return (
          <span>
            {record?.time ? moment(record.time).format(DATE_FORMAT) : ""}
          </span>
        );
      },
    },
  ];

  const handlePagination = (page: number, pageSize: number): void => {
    setPageParams({
      currentPage: page,
      pageSize: pageSize,
    });
  };

  const handleResetCurrentPage = (): void => {
    setPageParams((prev) => ({
      ...prev,
      currentPage: 1,
    }));
  };

  const handleSearchParams = (data: ValuesSearchPotential): void => {
    setSearchParams(data);
  };

  const visibleDetailCandidate = (): void => {
    window.history.pushState({}, "", router.route);
    setShowCandidateDetail(false);
  };

  useEffect(() => {
    if (requestPotentialCandidateList?.data?.length === 0) {
      setDataSource([]);
    } else {
      const cloneData = requestPotentialCandidateList?.data;
      const positionStart =
        pageParams?.currentPage > 1
          ? (pageParams.currentPage - 1) * pageParams.pageSize
          : 0;
      const positionEnd = pageParams.currentPage * pageParams.pageSize - 1;

      const newData = cloneData?.slice(positionStart, positionEnd) ?? [];
      setDataSource(newData);
    }
  }, [
    JSON.stringify(requestPotentialCandidateList?.data),
    JSON.stringify(pageParams),
  ]);

  return (
    <div className="potential-candidate w-full">
      <div className="potential-candidate__search w-full">
        <FilterPotentialCandidate
          workLocations={sortWorkLocation(workLocations, "key")}
          skills={skills}
          onResetCurrentPage={handleResetCurrentPage}
          onSearchParams={handleSearchParams}
        />
      </div>
      <div className="potential-candidate__table mt-2">
        <AppTable
          columns={columns}
          dataSource={dataSource}
          loading={requestPotentialCandidateList?.isLoading}
          style={{
            maxHeight: "72vh",
            overflowY: "scroll",
          }}
          onRow={(record: PotentialCandidateColumns): any => {
            return {
              onClick: (): void => {
                setQueryUrl({id: String(record.id)});
                showModalDetail(record.id);
              },
            };
          }}
        />
      </div>
      <div className="potential-candidate__pagination mt-2">
        <AppPagination
          defaultPageSize={pageParams?.pageSize}
          current={pageParams?.currentPage}
          pageSize={pageParams?.pageSize}
          total={totalCount}
          onChange={handlePagination}
        />
      </div>

      <AppModal
        className="modal-detail-candidate"
        centered
        footer={null}
        open={showCandidateDetail}
        onCancel={visibleDetailCandidate}
        title="Chi tiết ứng viên"
        width="70%"
      >
        <CandidateInformationDetail
          setShowCandidateDetail={setShowCandidateDetail}
          reloadData={onSearchParams}
          tab="potentialCandidate"
          idPotentialCandidate={id}
        />
      </AppModal>
    </div>
  );
}
