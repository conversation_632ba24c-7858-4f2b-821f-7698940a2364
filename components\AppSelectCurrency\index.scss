.select-currency {
  height: 52px;

  .ant-select-selector {
    height: 52px !important;
    display: flex;
    align-items: center;
    background-color: $header_tf;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 400;
    padding: 0px 5px;
    color: $text-color-input !important;
    border-radius: 0px 8px 8px 0px !important;
  }

  .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
    border-color: $primary-color;
  }
}

.alone-select-currency {
  height: 52px;

  .ant-select-selector {
    height: 52px !important;
    display: flex;
    align-items: center;
    background-color: $header_tf;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 400;
    padding: 0px 5px;
    color: $text-color-input !important;
    border-radius: 8px !important;
  }

  .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
    border-color: $primary-color;
  }
}
