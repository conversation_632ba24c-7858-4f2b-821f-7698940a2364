import {Row, Tooltip, notification} from "antd";
import React, {useEffect, useRef, useState, useMemo} from "react";
import {TextInput} from "@app/components/TextInput";
import AppDatePicker from "@app/components/AppDatePicker";
import "./index.scss";
import {Formik} from "formik";
import {FormikProps} from "formik/dist/types";
import {SelectInput} from "@app/components/SelectInput";
import {useMutation, useQuery} from "react-query";
import ApiCandidate, {
  IWorkLocationList,
  IDataCvCandidate,
  IWorkHistories,
  IEducations,
  FileUpload,
  IModeViewCandidate,
  IDataFormDataCandidateSubmit,
} from "@app/api/ApiCandidate";
import AppModal from "@app/components/AppModal";
import AppButton from "@app/components/AppButton";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import TableCandidateWorkHistories from "../tableCandidateWorkHistories";
import TableCandidateEducation from "../tableCandidateEducation";
import ViewCvCandidate from "@app/components/viewCvCandidate";
import {Input} from "formik-antd";
import {
  listExperience,
  listLanguage,
  salaryType,
  statusCandidateList,
} from "@app/utils/constants/state";
import ApiApplication, {
  IParamsCreateApplication,
} from "@app/api/ApiApplication";
import {
  messageConfirmCommission,
  messageCreateCandidateSuccess,
  messageIntroApplication,
  messageRequired,
  messageValidateEmail,
} from "@app/utils/constants/message";
import {useRouter} from "next/router";
import {
  appendFormData,
  autoFormatPhoneNumber,
  getFirst20Item,
  mapFilterSkillData,
  moneyToNumber,
  sortWorkLocation,
  validateEmail,
  validatePhoneNumber,
} from "@app/utils/constants/function";
import {useDispatch, useSelector} from "react-redux";
import {
  initialStateTable,
  setStateTable,
} from "@app/redux/slices/EditTableSlice";
import {IAccountRole, IMultiSelect, OptionSelect} from "@app/types";
import AppModalConfirm from "@app/components/AppModalConfirm";
import {selectUser} from "@app/redux/slices/UserSlice";
import {REGEX_EMAIL, REGEX_VIETNAMESE} from "@app/utils/constants/regex";
import {IRootState} from "@app/redux/store";
import {setCandidateIdDuplicate} from "@app/redux/slices/JobSlice";
import TableHistoryApplyReco from "../tableHistoryApplyReco";

interface ModalInfoProps {
  isModalVisible: boolean;
  handleOk?: () => void;
  handleCancel: () => void;
  cvCandidateData: IDataCvCandidate;
  fileUpload: FileUpload;
  getListCandidateAfterUpdate?: (id?: number) => void;
  type: "newCandidate" | "introCandidate";
  reloadData?: () => void;
}

interface DataInitialForm {
  name: string;
  phoneNumber: string;
  email: string;
  birthday: any;
  positionName: string;
  website: string;
  languages: OptionSelect[];
  workLocationName: OptionSelect[];
  currencyTypeId: OptionSelect;
  listCandidateSkillId?: OptionSelect[];
  onboardDate?: any;
  note: string;
  salaryExpected: number;
  currentSalary: number;
  linkedIn?: string;
  status?: OptionSelect;
  github: string;
  experienceYear: OptionSelect;
  currentSalaryTypeSelected: OptionSelect;
  salaryExpectedTypeSelected: OptionSelect;
}

const mapFilterWorkLocation = (filters: IWorkLocationList[]): OptionSelect[] =>
  filters.map((item) => ({
    value: item.name,
    label: item.name,
    key: item.workLocationId,
  }));

const mapFilterData = (filters: IMultiSelect[]): OptionSelect[] =>
  filters.map((item) => ({
    value: item.id,
    label: item.label,
  }));

function AddCandidate(props: ModalInfoProps): JSX.Element {
  const {
    isModalVisible,
    handleOk,
    handleCancel,
    cvCandidateData,
    fileUpload,
    getListCandidateAfterUpdate,
    type,
    reloadData,
  } = props;
  const {user} = useSelector(selectUser);
  const isCTV = user?.role?.includes(IAccountRole.CTV);

  const {candidateIdDuplicate} = useSelector((state: IRootState) => state.job);

  const commissionFlag = useRef<boolean>(false);
  const [isOpenModalCommission, setIsOpenModalCommission] = useState(false);

  const [dataWorkHistories, setDataWorkHistories] = useState<IWorkHistories[]>(
    []
  );
  const [dataEducations, setDataEducations] = useState<IEducations[]>([]);

  const getDataWorkHistoryFromTable = (data: IWorkHistories[]): void => {
    setDataWorkHistories(data);
  };

  const getDataEducationFromTable = (data: IEducations[]): void => {
    setDataEducations(data);
  };

  const dispatch = useDispatch();

  const onCancel = () => {
    handleCancel();
    dispatch(setStateTable(initialStateTable));
  };

  const formikRef = useRef<FormikProps<DataInitialForm>>(null);
  const router = useRouter();
  let idJob: string | undefined;
  if (router.query.id) {
    idJob = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    idJob = Object.fromEntries(searchParams)?.id;
  }

  const defaultStatus = statusCandidateList.find((item) => item.value === "1");

  const getLastItemArray = (value: any[]): any => {
    if (Array.isArray(value) && value?.length > 0) {
      const length = value?.length;
      return value[length - 1];
    }
    return null;
  };

  const initialValues: DataInitialForm = useMemo(() => {
    return {
      name: cvCandidateData?.full_name || "",
      phoneNumber: autoFormatPhoneNumber(cvCandidateData?.phone_number || ""),
      email: cvCandidateData?.email || "",
      birthday: cvCandidateData?.birthday
        ? moment(cvCandidateData?.birthday, DATE_FORMAT)
        : "",
      positionName:
        getLastItemArray(cvCandidateData?.workHistories)?.position || "",
      website: cvCandidateData?.facebook_URL || "",
      listCandidateSkillId:
        cvCandidateData?.skills && cvCandidateData?.skills?.length > 0
          ? cvCandidateData?.skills?.map((item) => ({label: item, value: item}))
          : [],
      languages:
        cvCandidateData?.languages && cvCandidateData?.languages?.length > 0
          ? cvCandidateData?.languages?.map((item) => ({
              label: item,
              value: item,
            }))
          : [],
      workLocationName:
        cvCandidateData?.workLocationNames &&
        cvCandidateData?.workLocationNames?.length > 0
          ? cvCandidateData?.workLocationNames?.map((item) => ({
              label: item,
              value: item,
            }))
          : [],
      onboardDate: cvCandidateData?.onboardDate
        ? moment(cvCandidateData?.onboardDate, DATE_FORMAT)
        : "",
      currencyTypeId: cvCandidateData?.currencyTypeId
        ? {
            value: cvCandidateData?.currencyTypeId,
            label: cvCandidateData?.currencyTypeId,
          }
        : {
            value: "VND",
            label: "VND",
          },
      note: cvCandidateData?.note || "",
      currentSalary: cvCandidateData?.currentSalary
        ? (cvCandidateData.currentSalary as any)
        : "",
      salaryExpected: cvCandidateData?.salaryExpected
        ? (cvCandidateData?.salaryExpected as any)
        : "",
      github: cvCandidateData?.github_URL || "",
      linkedIn: cvCandidateData?.linkedin_URL || "",
      status: defaultStatus,
      experienceYear: cvCandidateData?.yoe
        ? {value: cvCandidateData?.yoe, label: cvCandidateData?.yoe}
        : ({} as OptionSelect),
      currentSalaryTypeSelected:
        cvCandidateData?.currentSalaryType === 0
          ? salaryType[0]
          : salaryType[1],
      salaryExpectedTypeSelected:
        cvCandidateData?.salaryExpectedType === 0
          ? salaryType[0]
          : salaryType[1],
    };
  }, [cvCandidateData]);

  const requestSkillList = useQuery("requestSkillList", () => {
    return ApiCandidate.getListSkill();
  });

  const requestWorkLocationList = useQuery("requestWorkLocationList", () => {
    return ApiCandidate.getWorkLocationList();
  });

  const createCandidate = useMutation(
    (data: FormData) => {
      return ApiCandidate.createCandidateFromCv(data);
    },
    {
      onSuccess: (data) => {
        notification.success({
          message: messageCreateCandidateSuccess,
          duration: 3,
        });
        if (type === "newCandidate") {
          onCancel();
          getListCandidateAfterUpdate?.();
        }

        if (type === "introCandidate") {
          const params: IParamsCreateApplication = {
            requestJobId: idJob ? Number(idJob) : undefined,
            candidateIds: [
              {
                candidateId: Number(data),
                commissionFlag: commissionFlag.current,
              },
            ],
          };
          handleIntroCandidate.mutate(params);
        }
      },
    }
  );

  useEffect(() => {
    if (candidateIdDuplicate) {
      if (type === "newCandidate") {
        onCancel();
        getListCandidateAfterUpdate?.();
      }

      if (type === "introCandidate") {
        const params: IParamsCreateApplication = {
          requestJobId: idJob ? Number(idJob) : undefined,
          candidateIds: [
            {
              candidateId: Number(candidateIdDuplicate),
              commissionFlag: commissionFlag.current,
            },
          ],
        };
        handleIntroCandidate.mutate(params);
      }
    }
    return () => {
      dispatch(setCandidateIdDuplicate(null));
    };
  }, [candidateIdDuplicate]);

  useEffect(() => {
    if (
      cvCandidateData?.workHistories &&
      cvCandidateData?.workHistories.length > 0
    ) {
      const newData: IWorkHistories[] = cvCandidateData?.workHistories?.map(
        (item, index) => ({
          ...item,
          key: String(index),
          startDate: item?.startDate ? `01/${item?.startDate}` : "",
          endDate: item?.endDate
            ? item?.endDate?.toLowerCase() === "now"
              ? moment(new Date()).format(DATE_FORMAT)
              : `01/${item?.endDate}`
            : "",
        })
      );
      setDataWorkHistories(newData);
    } else {
      setDataWorkHistories([]);
    }

    if (
      cvCandidateData?.educations &&
      cvCandidateData?.educations?.length > 0
    ) {
      const newData: IEducations[] = cvCandidateData?.educations?.map(
        (item, index) => ({
          ...item,
          key: String(index),
          startDate: item?.startDate ? `01/${item?.startDate}` : "",
          endDate: item?.endDate
            ? item?.endDate?.toLowerCase() === "now"
              ? moment(new Date()).format(DATE_FORMAT)
              : `01/${item?.endDate}`
            : "",
        })
      );
      setDataEducations(newData);
    } else {
      setDataEducations([]);
    }
  }, [cvCandidateData?.workHistories, cvCandidateData?.educations]);

  const handleCreateCandidate = () => {
    const newDataWorkHistory = dataWorkHistories?.map((item) => ({
      companyName: item?.companyName,
      position: item?.position,
      startDate: item?.startDate ? item?.startDate : "",
      endDate: item?.endDate ? item?.endDate : "",
    }));

    const newDataEducation = dataEducations?.map((item) => ({
      schoolName: item?.schoolName,
      degreeType: item?.degreeType,
      endDate: item?.endDate ? item?.endDate : "",
      fieldOfStudy: item?.fieldOfStudy,
      startDate: item?.startDate ? item?.startDate : "",
    }));
    const valuesRef = formikRef.current?.values;

    if (
      !(
        valuesRef?.name?.trim() &&
        valuesRef?.phoneNumber?.trim() &&
        valuesRef?.email?.trim() &&
        valuesRef?.positionName?.trim() &&
        valuesRef?.experienceYear?.value &&
        valuesRef?.onboardDate &&
        valuesRef?.salaryExpected
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: messageRequired,
      });
      return;
    }

    if (!valuesRef?.email?.match(REGEX_EMAIL)) {
      notification.error({
        message: "Thông báo",
        description: messageValidateEmail,
      });
      return;
    }

    const value: IDataFormDataCandidateSubmit = {
      birthday: valuesRef?.birthday
        ? moment(valuesRef?.birthday).format(DATE_FORMAT)
        : "",
      facebookURL: valuesRef?.website?.trim() || "",
      languages: valuesRef?.languages?.map((item) => item.value) || [],
      onboardDate: valuesRef?.onboardDate
        ? moment(valuesRef?.onboardDate).format(DATE_FORMAT)
        : "",
      workLocationIds:
        valuesRef?.workLocationName && valuesRef?.workLocationName.length > 0
          ? valuesRef?.workLocationName.map((item) => item.key)?.join(",")
          : "",
      currencyTypeId: valuesRef?.currencyTypeId?.value || "",
      workHistories: newDataWorkHistory ?? [],
      educations: newDataEducation ?? [],
      name: valuesRef?.name?.trim() || "",
      phoneNumber: valuesRef?.phoneNumber?.trim() || "",
      email: valuesRef?.email?.trim() || "",
      currentSalary: moneyToNumber(String(valuesRef?.currentSalary)),
      salaryExpected: moneyToNumber(String(valuesRef?.salaryExpected)),
      note: valuesRef?.note?.trim() || "",
      tags: getFirst20Item(cvCandidateData?.tag),
      fileCVName: fileUpload.fileName || "",
      fileAvatarPath: cvCandidateData?.profile_Image_Local_Path || "",
      source: "Reco_Job_Database",
      githubURL: valuesRef?.github?.trim(),
      status: Number(valuesRef?.status?.value) || 1,
      isUploadFile: true,
      experienceYear: valuesRef?.experienceYear?.value,
      linkedinURL: valuesRef?.linkedIn?.trim() || "",
      positionExpected: valuesRef?.positionName?.trim() || "",
      listCandidateSkillId:
        valuesRef?.listCandidateSkillId?.map((item) => item.value) || [],
      salaryExpectedType:
        valuesRef?.salaryExpectedTypeSelected.value === "0" ? 0 : 1,
      currentSalaryType:
        valuesRef?.currentSalaryTypeSelected.value === "0" ? 0 : 1,
    };
    const formData = new FormData();
    formData.append("fileCV", fileUpload.file);
    appendFormData(formData, value, "");
    createCandidate.mutate(formData);
  };

  const handleIntroCandidate = useMutation(
    (param: IParamsCreateApplication) => {
      return ApiApplication.createApplications(param);
    },
    {
      onSuccess: (data) => {
        notification.success({
          message: messageIntroApplication.success,
        });
        formikRef?.current?.resetForm();
        handleCancel?.();
        dispatch(setCandidateIdDuplicate(null));
        reloadData?.();
      },
    }
  );

  const onClickConfirm = (): void => {
    if (type === "introCandidate" && isCTV) {
      setIsOpenModalCommission(true);
    } else {
      handleCreateCandidate();
    }
  };

  useEffect(() => {
    Object.entries(initialValues).forEach(([key, value]) => {
      formikRef.current?.setFieldValue(key, value);
    });
  }, [initialValues]);

  function renderContent(): React.ReactNode {
    return (
      <div>
        <Row className="content-modal-add-candidate">
          <div className="container-item-detail-modal left-4 p-3">
            <div className="h-[71vh] overflow-y-auto p-3">
              <div className="flex flex-col">
                <Formik
                  initialValues={initialValues}
                  innerRef={formikRef}
                  onSubmit={handleCreateCandidate}
                >
                  {({
                    values,
                    handleChange,
                    handleBlur,
                    handleSubmit,
                    handleReset,
                  }): JSX.Element => {
                    return (
                      <form>
                        <span className="font-bold">Thông tin ứng viên</span>
                        <Row className="mt-2 div-time">
                          <Tooltip
                            title={
                              !REGEX_VIETNAMESE.test(values?.name) &&
                              "Nên nhập tên có dấu"
                            }
                            color="#faad14"
                          >
                            <TextInput
                              label="Họ và tên"
                              name="name"
                              value={values?.name}
                              status={
                                !values?.name
                                  ? "error"
                                  : !REGEX_VIETNAMESE.test(values?.name)
                                  ? "warning"
                                  : undefined
                              }
                              required
                              free={!values?.name}
                            />
                          </Tooltip>
                          <TextInput
                            label="Số điện thoại"
                            name="phoneNumber"
                            value={values?.phoneNumber}
                            status={
                              !values?.phoneNumber ||
                              !validatePhoneNumber(values?.phoneNumber)
                                ? "error"
                                : undefined
                            }
                            required
                            free={!values.phoneNumber}
                            isphonenumber
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="Email"
                            name="email"
                            status={
                              !validateEmail(values?.email)
                                ? "error"
                                : undefined
                            }
                            value={values?.email}
                            required
                            free={!values?.email}
                          />
                          <AppDatePicker
                            name="birthday"
                            label="Năm sinh"
                            format={DATE_FORMAT}
                            valueAppDatePicker={values?.birthday}
                            free={!values?.birthday}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="Website"
                            name="website"
                            value={values?.website}
                            free={!values?.website}
                            disabled
                          />
                          <TextInput
                            label="Vị trí"
                            name="positionName"
                            value={values?.positionName}
                            free={!values?.positionName}
                            required
                            status={!values?.positionName ? "error" : undefined}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="LinkedIn"
                            name="linkedIn"
                            value={values?.linkedIn}
                            free={!values?.linkedIn}
                          />
                          <SelectInput
                            name="status"
                            labelselect="Trạng thái"
                            data={statusCandidateList}
                            value={values?.status}
                            free={!values?.status}
                            defaultValue="1"
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="Github"
                            name="github"
                            value={values?.github}
                            free={!values?.github}
                          />
                          <SelectInput
                            name="experienceYear"
                            labelselect="Kinh nghiệm"
                            data={listExperience}
                            value={values?.experienceYear?.value}
                            free={!values?.experienceYear?.value}
                            required
                            status={
                              !values?.experienceYear?.value
                                ? "error"
                                : undefined
                            }
                          />
                        </Row>
                        <div className="mt-5 font-bold">Sơ lược</div>
                        <Row className="mt-2 div-time">
                          <SelectInput
                            mode="multiple"
                            name="listCandidateSkillId"
                            labelselect="Kỹ năng"
                            data={mapFilterSkillData(
                              requestSkillList.data ?? []
                            )}
                            value={values?.listCandidateSkillId}
                            free={values?.listCandidateSkillId?.length === 0}
                          />
                          <SelectInput
                            mode="multiple"
                            name="languages"
                            labelselect="Ngoại ngữ"
                            data={mapFilterData(listLanguage)}
                            value={values?.languages}
                            free={values?.languages?.length === 0}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <SelectInput
                            mode="multiple"
                            name="workLocationName"
                            labelselect="Địa điểm làm việc"
                            data={sortWorkLocation(
                              mapFilterWorkLocation(
                                requestWorkLocationList.data ?? []
                              ),
                              "key"
                            )}
                            value={values?.workLocationName}
                            free={values?.workLocationName?.length === 0}
                          />
                          <AppDatePicker
                            name="onboardDate"
                            label="Ngày có thể onboard"
                            format={DATE_FORMAT}
                            valueAppDatePicker={values?.onboardDate}
                            free={!values?.onboardDate}
                            required
                            status={!values?.onboardDate ? "error" : undefined}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            containerclassname="flex"
                            label="Mức lương hiện tại"
                            name="currentSalary"
                            onlynumber
                            value={values?.currentSalary}
                            free={!values?.currentSalary}
                            typeInput="salary"
                            iscurrency
                            maxLength={100}
                          />
                          <Row className="flex">
                            <AppSelectCurrency
                              name="currencyTypeId"
                              value={values?.currencyTypeId}
                              style={{width: "80px"}}
                              isAlone
                            />
                            <AppSelectCurrency
                              classNameContainer="ml-2"
                              name="currentSalaryTypeSelected"
                              value={values?.currentSalaryTypeSelected}
                              style={{width: "90px"}}
                              options={salaryType}
                              isAlone
                            />
                          </Row>
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            containerclassname="flex"
                            label="Mức lương mong muốn"
                            name="salaryExpected"
                            onlynumber
                            value={values?.salaryExpected}
                            free={!values?.salaryExpected}
                            typeInput="salary"
                            iscurrency
                            maxLength={100}
                            required
                            status={
                              !values?.salaryExpected ? "error" : undefined
                            }
                          />
                          <Row className="flex">
                            <AppSelectCurrency
                              name="currencyTypeId"
                              value={values?.currencyTypeId}
                              style={{width: "80px"}}
                              isAlone
                            />
                            <AppSelectCurrency
                              classNameContainer="ml-2"
                              name="salaryExpectedTypeSelected"
                              value={values?.salaryExpectedTypeSelected}
                              style={{width: "90px"}}
                              options={salaryType}
                              isAlone
                            />
                          </Row>
                        </Row>
                        <Input.TextArea
                          name="note"
                          className="border-dash"
                          rows={4}
                          placeholder="Cộng tác viên vui lòng nhập thêm thông tin của ứng viên về lý do chuyển việc, trình độ ngoại ngữ, kinh nghiệm bổ sung để rút ngắn thời gian xử lý hồ sơ"
                          maxLength={1000}
                          value={values?.note}
                        />
                        {/* <Row>
                          <div className="candidate-tag mt-2 w-full">
                            <span>Tag</span>
                            <div className="flex flex-wrap">
                              {getFirst20Item(cvCandidateData.tag).length > 0
                                ? getFirst20Item(cvCandidateData.tag).map(
                                    (item, index) => (
                                      <div
                                        key={index}
                                        className="candidate-tag-item mr-4 px-2 py-0 text-[12px] mt-2"
                                      >
                                        {item}
                                      </div>
                                    )
                                  )
                                : ""}
                            </div>
                          </div>
                        </Row> */}
                        <div className="mt-4">
                          <TableHistoryApplyReco
                            email={formikRef.current?.values?.email?.trim()}
                            phoneNumber={formikRef.current?.values?.phoneNumber?.trim()}
                          />
                        </div>
                        <div className="mt-4">
                          <TableCandidateWorkHistories
                            dataSource={dataWorkHistories}
                            updateDataCandidateWorkHistories={
                              getDataWorkHistoryFromTable
                            }
                            experienceString={
                              cvCandidateData?.experienceYear || ""
                            }
                          />
                        </div>
                        <div className="mt-4">
                          <TableCandidateEducation
                            dataSource={dataEducations}
                            updateDataCandidateEducation={
                              getDataEducationFromTable
                            }
                          />
                        </div>
                      </form>
                    );
                  }}
                </Formik>
              </div>
            </div>
          </div>
          <div className="container-item-detail-modal right-4 p-6">
            <ViewCvCandidate
              docs={{
                filePathBase64: cvCandidateData?.fileCVPath
                  ? cvCandidateData.fileCVPath
                  : "",
                fileName: cvCandidateData?.fileCVName
                  ? cvCandidateData.fileCVName
                  : fileUpload && fileUpload?.fileName
                  ? fileUpload?.fileName
                  : "",
              }}
              modeViewCandidate={IModeViewCandidate.create}
              createdDate=""
            />
          </div>
        </Row>
        <Row className="flex justify-center mt-4 items-center">
          <AppButton
            classrow="mr-2 w-64 btn-cancel"
            label="Hủy bỏ"
            typebutton="primary"
            onClick={handleCancel}
          />

          <AppButton
            classrow="ml-2 w-64	"
            label={
              type && type === "introCandidate"
                ? "Giới thiệu ngay"
                : "Thêm ứng viên"
            }
            typebutton="primary"
            onClick={onClickConfirm}
            isSubmitting={
              createCandidate.isLoading || handleIntroCandidate.isLoading
            }
          />
        </Row>
      </div>
    );
  }

  return (
    <div>
      <AppModal
        className="modal-add-candidate"
        open={isModalVisible}
        onCancel={onCancel}
        footer={null}
        title={type === "introCandidate" ? "Giới thiệu ngay" : "Thêm ứng viên"}
        onOk={handleOk}
        width="85%"
      >
        {renderContent()}
      </AppModal>
      <AppModalConfirm
        open={isOpenModalCommission}
        title="Xác nhận ủy quyền"
        content={messageConfirmCommission}
        onCancel={(): void => {
          commissionFlag.current = false;
          setIsOpenModalCommission(false);
          handleCreateCandidate();
        }}
        onOk={(): void => {
          commissionFlag.current = true;
          setIsOpenModalCommission(false);
          handleCreateCandidate();
        }}
      />
    </div>
  );
}

export default React.memo(AddCandidate);
