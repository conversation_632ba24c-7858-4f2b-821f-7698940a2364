.landing-header {
  height: $header-landing-page;
  padding: 0 24px;

  &__drop-menu {
    top: $header-landing-page;
    width: 100%;
    background-color: #9d9d9d;
    opacity: 1;
    display: none;
  }

  @media screen and (max-width: 991px) {
    padding: 0 12px;
    &__drop-menu {
      display: block;
    }
  }
}

.about {
  &__link {
    padding: 0 12px;

    &:hover {
      color: $text-color-input;
    }

    @media screen and (max-width: 991px) {
      &:hover {
        color: $text-color-input;
      }

      &:focus {
        color: $text-color-input;
      }
    }
  }

  &__item {
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    &::after {
      content: "";
      width: 100%;
      height: 1px;
      display: block;
      position: absolute;
      background-color: $status-reject;
      bottom: 0;
      transition: all 0.3s ease-out;
      left: -100%;
    }
    &:hover::after {
      left: 0;
    }

    @media screen and (max-width: 991px) {
      &::after {
        background-color: white;
      }
    }
  }

  @media screen and (max-width: 991px) {
    .about-list {
      height: 0;
      opacity: 0;
      font-size: 0;
      transition: all 0.3s ease-in-out;
    }

    .drop-menu-active {
      opacity: 1;
      font-size: 16px;
      height: 184px;
    }

    &__item {
      justify-content: left;
      align-items: center;
      background-color: white;
      padding: 2px 4px;
      margin: 0 12px 8px;
      border-radius: 8px;
      height: 36px;

      &:first-child {
        margin-top: 8px;
      }
    }
  }
}
