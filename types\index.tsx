export enum IAccountRole {
  ADMIN = "Admin",
  CTV = "CTV",
  CST = "CST",
  CSL = "CSL",
  AMG = "AMG",
  AML = "AML",
  BD = "BD",
  BDL = "BDL",
}

export enum IState {
  INACTIVE,
  ACTIVE,
  DELETED,
}

export interface IUserLogin {
  _id?: string;
  fullName?: string;
  state?: IState;
  email?: string;
  dateOfBirth?: string;
  positionId?: number;
  avatar?: string;
  personId?: number;
  address?: string;
  phoneNumber?: string;
  role?: {
    id?: IAccountRole;
    roleName?: string;
  };
  phoneNumberRelative?: string;
  baseSalary?: number;
  manageSalary?: number;
  gender?: string;
}

export interface IUserIsLogin {
  userId?: string;
  name?: string;
  email?: string;
  pathImage?: string;
  role?: IAccountRole[];
  roleList?: string;
  userName?: string;
}

export interface IListProfile {
  _applicationId?: number;
  customerName?: string;
  positionName?: string;
  candidatename?: string;
  candidateEmail?: string;
}

export interface IUserFilter {
  applicationsPaging?: IListProfile[];
  currentPage?: number;
}

export interface IProfile {
  _id?: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  bio?: string;
  phone?: string;
  location?: string;
  website?: string;
  facebook?: string;
  twitter?: string;
  avatar?: string;
  newEmail?: string;
}

export interface IQueryParamsPages {
  managerRequest?: string;
  managerApplication?: string;
}

export interface IAccountInfo {
  user: IUserIsLogin | null;
  accessToken?: string;
  refreshToken?: string;
  isConfirmed?: boolean;
  dataProfile?: IProfile;
  listColShowCollaborator: string[];
  listColShowRequest: string[];
  listColShowCandidate: string[];
  listColShowApplication: string[];
  listColShowRecommendationBonus: string[];
  listColShowBonusApplication: string[];
  lisColShowHotBonus: string[];
  listColShowManagerRequestRoleAm: string[];
  listColShowCustomer: string[];
  listColShowCustomerPayment: string[];
  groupByApplication: string;
  queryParamsPages: IQueryParamsPages;
}

export interface OptionSelect {
  value: string;
  label: string;
  key?: string | null;
  id?: string | number | null;
  color?: string;
  disabled?: boolean;
  order?: number;
}

export enum ActiveDateEnum {
  SEVEN_DATE = "SEVEN_DATE",
  ONE_MONTH = "ONE_MONTH",
  THREE_MONTH = "THREE_MONTH",
  SIX_MONTH = "SIX_MONTH",
  ONE_YEAR = "ONE_YEAR",
}

export enum RequestType {
  internal = 0,
  external = 1,
}

export interface IMultiSelect {
  id: string;
  label: string;
  key?: string;
}

export interface SearchParamsJobFilter {
  textSearch: string;
  workLocations: string[];
  jobLabels: string[];
  levels: string[];
  salaryRanges: string[] | number[];
  jobTypes: string[];
  isSearchHotJob: boolean;
  isBookmark: boolean;
  pageSize?: number;
  currentPage?: number;
  sortOption?: number;
  haveRecommendCandidate?: boolean;
  services: string[];
}

export interface MessageChatbot {
  content: string;
  isBot: boolean;
  createdDate?: string;
  isFetchSuccess?: boolean;
}

export interface SearchParamsJobSuggestionFilter {
  jobSearch?: string;
  partnersSearch?: string;
  partnerTags?: string[];
  pageSizePartner?: number;
  pageSizeJob?: number;
  currentPageJob?: number;
  currentPagePartner?: number;
  typeSearch?: string;
}

export interface DataGroupByItem {
  groupByValue: number | string;
  groupByName: string;
  totalRecords: number;
  data?: Array<any>;
  pageNumber?: number;
  pageSize?: number;
  totalCount?: number;
  totalPages?: number;
}

export enum StatusRequestJob {
  Draft = "-1",
  Open = "1",
  Processing = "2",
  PendingFromAM = "3",
  PendingFromCustomer = "4",
  Review = "5",
  Unclear = "6",
  Reject = "7",
  Closed = "0",
  NoUpdate = "null",
}

export enum TypeOfBrowserSupport {
  Chrome = "chrome",
  Firefox = "firefox",
  Edge = "edge-chromium",
  Safari = "safari",
}

export enum KeyStorage {
  DEVICE_TOKEN = "deviceToken",
}

export interface DataGroupByField {
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  data: Array<{
    groupByValue: number | string;
    groupByName: string;
    totalRecords: number;
  }>;
}

export enum BREAK_POINT {
  sm = 576,
  md = 768,
  lg = 992,
  xl = 1024,
  xxl = 1400,
}
