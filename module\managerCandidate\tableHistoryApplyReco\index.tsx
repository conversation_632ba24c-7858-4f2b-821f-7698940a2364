import {Tooltip, Row} from "antd";
import ApiCandidate, {IResHistoryApplyReco} from "@app/api/ApiCandidate";
import AppTable from "@app/components/AppTable";
import {useQuery} from "react-query";
import moment from "moment";
import {MONTH_YEAR_FORMAT} from "@app/utils/constants/formatDateTime";
import React from "react";

interface Prop {
  email?: string;
  phoneNumber?: string;
}

// eslint-disable-next-line react/function-component-definition
const TableCandidateWorkHistories: React.FC<Prop> = ({email, phoneNumber}) => {
  const {data} = useQuery(
    ["requestSkillList", email, phoneNumber],
    () => {
      return ApiCandidate.getHistoryApplyReco({email, phoneNumber});
    },
    {enabled: !!email || !!phoneNumber}
  );

  const column = [
    {
      title: "Vị trí",
      dataIndex: "position",
      ellipsis: true,
      render: (position: string) => {
        return position ? (
          <Tooltip title={position} placement="bottomLeft">
            <span>{position}</span>
          </Tooltip>
        ) : (
          <span />
        );
      },
    },
    {
      title: "<PERSON><PERSON>ời đăng",
      dataIndex: "userName",
      ellipsis: true,
      render: (position: string, record: IResHistoryApplyReco) => {
        return (
          <>
            {record?.userName && (
              <>
                <Tooltip title={record?.userName} placement="bottomLeft">
                  <span>{record?.userName}</span>
                </Tooltip>
                <br />
              </>
            )}
            {record?.email && (
              <Tooltip title={record?.email} placement="bottomLeft">
                <span>{record?.email}</span>
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      title: "Thời gian",
      dataIndex: "createAt",
      width: 80,
      render: (time: string) => (
        <div>{moment(time).format(MONTH_YEAR_FORMAT)}</div>
      ),
    },
  ];

  return (
    <div>
      {Number(data?.length) > 0 && (
        <>
          <Row className="text16 font-bold mt-4 mb-1 items-baseline">
            Lịch sử ứng tuyển Reco
          </Row>
          <AppTable
            className="table-work-education"
            pagination={false}
            bordered
            dataSource={data ?? []}
            columns={column ?? []}
            rowKey="key"
            rowClassName="editable-row"
          />
        </>
      )}
    </div>
  );
};

export default React.memo(TableCandidateWorkHistories);
