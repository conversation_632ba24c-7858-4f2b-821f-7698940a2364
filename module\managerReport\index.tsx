import React, {useState} from "react";
import RangeDateFilter from "./rangeDateFilter";
import Statistical from "./statistical";
import {useQuery} from "react-query";
import ApiReport from "@app/api/ApiReport";
import moment from "moment";
import {
  DATE_FORMAT,
  NORMAL_DATE_FORMAT,
} from "@app/utils/constants/formatDateTime";
import TableGroup from "./tableGroup";
import ModalTableCV from "./modalTableCV";
import useModal from "@app/hooks/useModal";
import {IApplication} from "@app/api/ApiApplication";

export default function ManagerReport(): JSX.Element {
  const searchParams = new URLSearchParams(window.location.search);
  const searchParamsObject = Object.fromEntries(searchParams);

  const [dataTableCV, setDataTableCV] = useState<IApplication[]>([]);

  const {
    open: openModalTableCV,
    onOpen: onOpenModalTableCV,
    onClose: onCloseModalTableCV,
  } = useModal();

  const query = {
    FromDate: moment(searchParamsObject?.startDate, NORMAL_DATE_FORMAT).format(
      DATE_FORMAT
    ),
    ToDate: moment(searchParamsObject?.endDate, NORMAL_DATE_FORMAT).format(
      DATE_FORMAT
    ),
  };

  const handleOpenModalTableCV = (data: IApplication[]) => {
    if (!!data && data?.length > 0) {
      onOpenModalTableCV();
      setDataTableCV(data);
    }
  };

  const {
    data: dataReportManage,
    refetch,
    isLoading: isLoadingReportNumber,
    isFetching: isFetchingReportNumber,
  } = useQuery(["getReportManage", query], {
    queryFn: () => ApiReport.getReportManager(query),
  });

  const fetchData = async () => {
    const [teams, ctv] = await Promise.all([
      ApiReport.getReportManagerForTeam(query),
      ApiReport.getReportManagerForCTV(query),
    ]);
    return [...(teams || []), ...(ctv || [])];
  };

  const {
    data: dataGroupTable,
    isLoading,
    isFetching,
  } = useQuery(["getReportTable", query], {
    queryFn: fetchData,
    keepPreviousData: true,
  });

  return (
    <div className="flex gap-x-[100px]">
      <div className="min-w-[260px] max-w-[400px]">
        <div className="w-[280px] mb-10">
          <RangeDateFilter refetch={refetch} />
        </div>
        <div>
          <Statistical
            data={dataReportManage}
            loading={isLoadingReportNumber || isFetchingReportNumber}
            onOpenModalTableCV={handleOpenModalTableCV}
          />
        </div>
      </div>
      <div className="flex-1">
        <TableGroup
          data={dataGroupTable}
          loading={isLoading}
          fetching={isFetching}
          onOpenModalTableCV={handleOpenModalTableCV}
        />
      </div>
      <ModalTableCV
        open={openModalTableCV}
        onCloseModal={onCloseModalTableCV}
        data={dataTableCV}
      />
    </div>
  );
}
