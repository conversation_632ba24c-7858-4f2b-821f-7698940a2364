# Internationalization(i18n)
I18N trong mục này hiện tại sẽ chỉ tập trung vào cách tạo web đa ngôn ngữ


## 1. <PERSON><PERSON> báo ngôn ngữ
Hiện tại, đang hỗ trợ ngôn ngữ cho tiếng <PERSON>h và tiếng Việt, các ngôn ngữ khác sẽ được update sau.

Tại file dịch cho tiếng Anh `i18n/en/translation.json`
```json
"sidebar": {
    "content_title": "NEWS FEEDS",
    "ui_title": "UI LANGUAGE",
    "logout_success": "Log out success",
    "logout": "Log out",
    "confirm_logout": "Are you sure ?",
    "region": "Region",
    "language": "Language",
}
```
Tại file dịch cho tiếng Việt `i18n/vi/translation.json`
```json
"sidebar": {
    "content_title": "BẢNG TIN MỚI",
    "ui_title": "NGÔN NGỮ",
    "logout_success": "Đ<PERSON>ng xuất thành công",
    "logout": "Đăng xuất",
    "confirm_logout": "Bạn có chắc không ?",
    "region": "Khu vực",
    "language": "Ngôn ngữ",
}
```
## 2. Cách sử dụng
```tsx
import {useTranslation} from "react-i18next"; //import thư viện

export default function Login(): JSX.Element {
    const {t} = useTranslation(); //Khai báo biến t
    
    <span>
        {t("sidebar.content_title")} //Dùng value đã được khai báo trong file tranlation.json
    </span>
}
```
