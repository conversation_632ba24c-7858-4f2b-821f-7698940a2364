import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole} from "@app/types";
import React from "react";
import {useSelector} from "react-redux";
import TeamList from "./TeamList";
import AppTabs from "@app/components/AppTabs";
import ManagerEmployee from "./ManagerEmployee";

export default function ManagerTeam(): JSX.Element {
  const {user} = useSelector(selectUser);
  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);

  if (isAdmin) {
    return (
      <div>
        <span className="text24 text-color-primary">Quản lý Team</span>
        <AppTabs
          classNameContainer="mt-4"
          defaultActiveKey="teamList"
          listTabs={[
            {
              key: "teamList",
              title: "Nhóm nhân viên",
              component: <TeamList isAdmin={isAdmin} />,
            },
            {
              key: "managerEmployee",
              title: "Nh<PERSON> viên",
              component: <ManagerEmployee />,
            },
          ]}
        />
      </div>
    );
  }

  return <TeamList isAdmin={isAdmin} />;
}
