import ApiCandidate, {ICandidateObject} from "@app/api/ApiCandidate";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/es/table";
import React, {useEffect, useState} from "react";
import {useQuery} from "react-query";
import "./index.scss";
import {getStatusCandidate} from "@app/utils/constants/state";
import {ICollaborator} from "@app/api/ApiCollaborator";
import AppModal from "@app/components/AppModal";
import {CandidateInformationDetail} from "@app/module/managerCandidate/candidateInformationDetail";
import {Select} from "antd";

interface TableCandidateListProps {
  collaborator?: ICollaborator;
}
export default function TableCandidateList(
  props: TableCandidateListProps
): JSX.Element {
  const {collaborator} = props;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [showCandidateDetail, setShowCandidateDetail] =
    useState<boolean>(false);
  const [idCandidate, setIdCandidate] = useState<number>(-1);

  const handleVisibleModalCandidateDetail = (): void => {
    setShowCandidateDetail(false);
  };

  const openModal = (id: number): void => {
    setIdCandidate(id);
    setShowCandidateDetail(true);
  };

  const requestCandidateList = useQuery(
    ["requestCandidateList", currentPage, pageSize],
    () => {
      return ApiCandidate.getListCandidate({
        currentPage: currentPage,
        pageSize: pageSize,
        partnerId: collaborator?.userId,
        isAdvanceSearch: true,
      });
    },
    {
      enabled: false,
    }
  );

  useEffect(() => {
    if (collaborator?.userId) {
      requestCandidateList.refetch();
    }
  }, [collaborator]);

  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const columns: ColumnsType<ICandidateObject> = [
    {
      title: "STT",
      dataIndex: "",
      key: "stt",
      render: (_, item: ICandidateObject, index: number) => (
        <span className="text-base text12">{index + 1}</span>
      ),
      width: "70px",
    },
    {
      title: "Họ tên",
      dataIndex: "name",
      key: "name",
      className: "cursor-pointer",
      onCell: (record: ICandidateObject): any => {
        return {
          onClick: (): void => {
            openModal(Number(record.candidateId));
          },
        };
      },
      width: "15%",
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
      width: "15%",
    },
    {
      title: "Kĩ năng",
      dataIndex: "skill",
      key: "skill",
      render: (_, {skills}: ICandidateObject): JSX.Element => {
        if (!skills) {
          return <div />;
        }
        const listSkill = skills.split(",");
        const listOption = listSkill.map((item, index) => ({
          label: item,
          value: item,
        }));
        return (
          <Select
            mode="multiple"
            className="list-skill w-full text12"
            value={listSkill}
            options={listOption}
            disabled
            maxTagCount="responsive"
          />
        );
      },
    },
    {
      title: "Trạng thái",
      key: "statusName",
      dataIndex: "statusName",
      render: (_, {status, statusName}: ICandidateObject): JSX.Element => {
        return (
          <span
            className="status-candidate text12"
            style={{backgroundColor: getStatusCandidate(status).color}}
          >
            {statusName}
          </span>
        );
      },
      width: "10%",
    },
    {
      title: "Ngày tạo ứng viên",
      dataIndex: "createdDate",
      key: "createdDate",
      width: "14%",
    },
  ];

  return (
    <div className="container-candidate-list">
      <AppTable
        dataSource={requestCandidateList.data?.candidatesPaging?.map(
          (item: ICandidateObject, index: number) => ({
            ...item,
            key: index,
          })
        )}
        columns={columns}
        loading={requestCandidateList.isLoading}
        scroll={{y: "25vh"}}
      />
      <AppPagination
        className="mt-3"
        defaultPageSize={pageSize}
        current={currentPage}
        pageSize={pageSize}
        total={requestCandidateList.data?.totalCount}
        onChange={handlePagination}
      />
      <AppModal
        className="modal-detail-candidate"
        centered
        footer={null}
        open={showCandidateDetail}
        onCancel={handleVisibleModalCandidateDetail}
        title="Chi tiết ứng viên"
        width="70%"
      >
        <CandidateInformationDetail
          idCandidate={idCandidate}
          setShowCandidateDetail={setShowCandidateDetail}
          reloadData={requestCandidateList.refetch}
        />
      </AppModal>
    </div>
  );
}
