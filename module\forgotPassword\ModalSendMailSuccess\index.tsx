import AppButton from "@app/components/AppButton";
import AppModal from "@app/components/AppModal";
import config from "@app/config";
import {Col, ModalProps, Row} from "antd";
import {useRouter} from "next/router";
import React from "react";

interface Props extends ModalProps {
  email: string;
}

function ModalSentMailSuccess(props: Props): JSX.Element {
  const {open, email} = props;
  const router = useRouter();

  const redirectHomepage = (): void => {
    router.push(config.PATHNAME.HOME);
  };

  return (
    <AppModal
      footer={null}
      title="Thông báo"
      open={open}
      onCancel={redirectHomepage}
      centered
    >
      <div className="text-center">
        <p className="text14 mt-4">
          Hướng dẫn đặt lại mật khẩu đã được gửi đến email{" "}
          <span className="font-bold">{email}</span>. Vui lòng kiểm tra để tạo
          lại mật khẩu mới.
        </p>
        <Row justify="center" className="mt-4">
          <Col xs={8}>
            <AppButton typebutton="primary" onClick={redirectHomepage}>
              Về Trang Chủ
            </AppButton>
          </Col>
        </Row>
      </div>
    </AppModal>
  );
}

export default React.memo(ModalSentMailSuccess);
