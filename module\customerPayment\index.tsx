/* eslint-disable import/no-cycle */
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import FormFilter from "./FormFilter";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import AppTable from "@app/components/AppTable";
import {useMutation, useQuery} from "react-query";
import AppPagination from "@app/components/AppPagination";
import {ColumnsType} from "antd/lib/table";
import {IAccountRole, OptionSelect} from "@app/types";
import {
  changeColCustomerPayment,
  initListColShowCustomerPayment,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {useSelector, useDispatch} from "react-redux";
import ApiCustomerPayment, {
  IParamsFilter,
  IPaymentPaging,
  IResFilter,
} from "@app/api/ApiCustomerPayment";
import {formatMoney, getPaymentStatus} from "@app/utils/constants/function";
import {setLoading} from "@app/redux/slices/SystemSlice";
import ModalPaymentDetail from "./ModalPaymentDetail";

export interface IFormFilter extends IParamsFilter {
  creatorFilterSelected?: OptionSelect[];
  positionsSelected?: OptionSelect[];
  statusSelected?: OptionSelect[];
}

export default function CustomerPayment(): JSX.Element {
  const [valuesSearch, setValuesSearch] = useState<IFormFilter>({
    currentPage: 1,
    pageSize: 20,
    isFirstInitialization: true,
  } as IFormFilter);
  const [isShowDetail, setIsShowDetail] = useState(false);
  const [paymentId, setPaymentId] = useState<number>();
  const {listColShowCustomerPayment, user} = useSelector(selectUser);
  const isAML =
    user?.role?.includes(IAccountRole.AML) ||
    user?.role?.includes(IAccountRole.ADMIN);
  const isBD = [IAccountRole.BD, IAccountRole.BDL].some((item) =>
    user?.role?.includes(item)
  );

  const dispatch = useDispatch();
  const listFilter = useRef<{
    creatorFilters: OptionSelect[];
    positionFilters: OptionSelect[];
  }>({
    creatorFilters: [],
    positionFilters: [],
  });

  const getCustomerPayment = useQuery(["getListCustomer", valuesSearch], () => {
    return ApiCustomerPayment.getCustomerPayment({
      ...valuesSearch,
    });
  });

  const exportCustomerPayment = useMutation(
    (param: IResFilter) => {
      return ApiCustomerPayment.exportCustomerPayment(param);
    },
    {
      onSuccess: () => {
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  listFilter.current.creatorFilters =
    getCustomerPayment.data?.creatorFilters &&
    getCustomerPayment.data?.creatorFilters?.length > 0
      ? getCustomerPayment.data?.creatorFilters?.map((item) => ({
          ...item,
          key: item.id,
          value: item.label,
        }))
      : listFilter.current.creatorFilters;

  listFilter.current.positionFilters =
    getCustomerPayment.data?.positionFilters &&
    getCustomerPayment.data?.positionFilters?.length > 0
      ? getCustomerPayment.data?.positionFilters?.map((item) => ({
          ...item,
          key: item.id,
          value: item.label,
        }))
      : listFilter.current.positionFilters;

  useEffect(() => {
    if (!listColShowCustomerPayment) {
      dispatch(changeColCustomerPayment(initListColShowCustomerPayment));
    }
  }, []);

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesSearch({
      ...valuesSearch,
      currentPage: page,
      pageSize,
    });
  };

  const exportData = (): void => {
    if (getCustomerPayment.data) {
      dispatch(setLoading(true));
      exportCustomerPayment.mutate(getCustomerPayment.data);
    }
  };

  const closeModalDetail = (): void => {
    setIsShowDetail(false);
    setPaymentId(0);
  };

  const columnAll: ColumnsType<IPaymentPaging> = [
    {
      title: "AM quản lý",
      dataIndex: "accountManagerName",
      key: "accountManagerName",
    },
    {
      title: "Khách hàng",
      dataIndex: "customerName",
      key: "customerName",
    },
    {
      title: "Tên request",
      dataIndex: "requestJobName",
      key: "requestJobName",
    },
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
    },
    {
      title: "Vị trí",
      dataIndex: "positionName",
      key: "positionName",
    },
    {
      title: "Doanh thu",
      dataIndex: "salesTransacted",
      key: "salesTransacted",
      render: (_, item: IPaymentPaging) => (
        <span>{formatMoney(item.salesTransacted)}</span>
      ),
    },
    {
      title: "VAT (%)",
      dataIndex: "vat",
      key: "vat",
    },
    {
      title: "Thành tiền",
      dataIndex: "amount",
      key: "amount",
      render: (_, item: IPaymentPaging) => (
        <span>{formatMoney(item.amount)}</span>
      ),
    },
    {
      title: "Đã thanh toán",
      dataIndex: "amountPaid",
      key: "amountPaid",
      render: (_, item: IPaymentPaging) => (
        <span>{formatMoney(item.amountPaid)}</span>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "statusName",
      key: "statusName",
      render(_: string, record: IPaymentPaging): JSX.Element {
        return (
          <span
            style={{
              backgroundColor: getPaymentStatus(record?.status).color,
            }}
            className="status-payment"
          >
            {getPaymentStatus(record?.status).label}
          </span>
        );
      },
    },
    {
      title: "Ghi chú",
      dataIndex: "note",
      key: "note",
    },
  ];

  const columns = columnAll.filter((item) =>
    [
      ...(listColShowCustomerPayment || initListColShowCustomerPayment),
      isAML || isBD ? "accountManagerName" : "",
      "customerName",
      "requestJobName",
    ].some((i) => i === item.key)
  );

  return (
    <div className="customer-payment-container">
      <FormFilter
        setValuesSearch={setValuesSearch}
        valuesSearch={valuesSearch}
        sumCol={columnAll.length}
        listFilter={listFilter.current}
      />
      <AppButton typebutton="normal" classrow="btn-export" onClick={exportData}>
        <Icon icon="download-cloud-line" size={24} color="#324054" />
      </AppButton>
      <div className="mt-3">
        <AppTable
          rowClassName="cursor-pointer"
          dataSource={getCustomerPayment.data?.paymentsPaging?.map(
            (item: IPaymentPaging, index: number) => ({
              ...item,
              key: index,
            })
          )}
          columns={columns}
          loading={getCustomerPayment.isLoading}
          scroll={{y: "60vh"}}
          onRow={(item: IPaymentPaging): any => {
            return {
              onClick: (): void => {
                setIsShowDetail(true);
                setPaymentId(item.paymentId);
              },
            };
          }}
        />
        <AppPagination
          className="mt-6"
          defaultPageSize={valuesSearch.pageSize}
          current={valuesSearch.currentPage}
          pageSize={valuesSearch.pageSize}
          total={getCustomerPayment.data?.totalCount}
          onChange={handlePagination}
        />
      </div>
      <ModalPaymentDetail
        isShow={isShowDetail}
        handleClose={closeModalDetail}
        paymentId={paymentId}
        getCustomerPayment={getCustomerPayment}
        setIsShowDetail={setIsShowDetail}
      />
    </div>
  );
}
