import React, {useState} from "react";
import "./index.scss";
import {OptionSelect} from "@app/types";
import config from "@app/config";
import {Image} from "antd";
import Account from "../Account";
import Desktop from "../Desktop";
import Mobile from "../Mobile";
import Icon from "@app/components/Icon/Icon";
import classNames from "classnames";

interface DetailContactProps {
  linkClassName?: string;
  className?: string;
  listClassName?: string;
}

export function About(props: DetailContactProps): JSX.Element {
  const {linkClassName, className, listClassName} = props;
  const detailContactLeft: OptionSelect[] = [
    {
      label: "Tin tức",
      value: "https://job.reco-vn.com/reco-manpower-for-partners/tin-tuc",
    },
    {
      label: "Job Nhật Bản",
      value: "https://job.reco-vn.com/japan-jobs",
    },
    {
      label: "Nhà tuyển dụng",
      value: "https://reco-vn.com/",
    },
    {
      label: "Cộng tác viên",
      value: "https://job.reco-vn.com/reco-manpower-for-partners/",
    },
  ];
  return (
    <div className={classNames("about flex", className)}>
      <ul className={classNames("flex about-list", listClassName)}>
        {detailContactLeft.map((i, index) => (
          <li key={index} className={classNames("about__item")}>
            <a
              href={i.value}
              target="_blank"
              rel="noreferrer"
              className={`text16 about__link ${linkClassName}`}
            >
              {i.label}
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
}

function Header(): JSX.Element {
  const [toggle, setToggle] = useState<boolean>(false);

  const onClick = () => {
    setToggle(!toggle);
  };

  const iconToggle = toggle ? "cancel" : "bar";
  return (
    <div className="landing-header w-full flex justify-between items-center relative">
      <div className="flex items-center">
        <div className="mr-4">
          <a href={config.PATHNAME.HOME} className="flex items-center">
            <Image
              src="/img/logo-icon.svg"
              alt="logo"
              height={50}
              width={50}
              preview={false}
            />
            <Image
              src="/img/logo-text.svg"
              alt="logo"
              height={56}
              width={75}
              preview={false}
              className="ml-2"
            />
          </a>
        </div>
        <Desktop>
          <About linkClassName="text-color-primary" />
        </Desktop>
      </div>
      <div>
        <Desktop>
          <Account />
        </Desktop>
        <Mobile>
          <button onClick={onClick} type="button">
            <Icon icon={iconToggle} size={24} />
          </button>
        </Mobile>
      </div>

      <div
        className={classNames(
          "landing-header__drop-menu absolute left-0",
          toggle && "landing-header__drop-menu-active"
        )}
      >
        <About
          linkClassName="text-left text-color-primary"
          listClassName={classNames(
            "flex-col justify-items-start w-full",
            toggle && "drop-menu-active block"
          )}
        />
      </div>
    </div>
  );
}

export default React.memo(Header);
