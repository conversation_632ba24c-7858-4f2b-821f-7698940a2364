.container-app-table {
  border: 1px solid $header_tf05;
  border-radius: 8px;
  overflow: hidden;

  thead[class*="ant-table-thead"] th {
    text-align: center !important;
    font-size: 1rem;
    font-weight: 700;
    color: $text-color-input;
  }
  .ant-table-tbody tr {
    text-align: center !important;
    font-size: 16px;
    font-weight: 400;
  }

  .ant-table-cell {
    padding: 12px;
    font-size: 0.75rem;
    color: $text-color-input;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td,
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    background-color: $white-color;
  }

  .ant-table.ant-table-bordered
    > .ant-table-container
    > .ant-table-content
    > table
    > thead
    > tr
    > .ant-table-selection-column,
  .ant-table.ant-table-bordered
    > .ant-table-container
    > .ant-table-content
    > table
    > tbody
    > tr
    > .ant-table-selection-column {
    border-right: none;
  }
}
