import {ModalProps} from "antd";
import React from "react";
import AppModal from "../AppModal";
import "./index.scss";
import AppButton from "../AppButton";

interface Props extends ModalProps {
  open?: boolean;
  title?: string;
  onOk?: () => void;
  onCancel?: () => void;
  content?: string;
}

function ModalConFirm(props: Props): JSX.Element {
  const {open, title, onOk, onCancel, content} = props;

  function contentModal(): JSX.Element {
    return (
      <div className="modal-confirm">
        <div className="modal-confirm__content">
          {content ? (
            <p>{content}</p>
          ) : (
            <div>
              <p>Dữ liệu sẽ bị xóa vĩnh viễn.</p>
              <p>Bạn có chắc chắn</p>
            </div>
          )}
        </div>
        <div className="modal-confirm__action">
          <AppButton
            classrow="w-32 mr-2"
            typebutton="secondary"
            onClick={onCancel}
          >
            Không
          </AppButton>
          <AppButton classrow="w-32 ml-2" typebutton="primary" onClick={onOk}>
            Có
          </AppButton>
        </div>
      </div>
    );
  }
  return (
    <AppModal open={open} title={title} footer={null} onCancel={onCancel}>
      {contentModal()}
    </AppModal>
  );
}

export default React.memo(ModalConFirm);
