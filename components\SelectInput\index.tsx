import "./index.scss";
import {Select, SelectProps} from "formik-antd";
import classNames from "classnames";
import {useCallback, useEffect, useMemo, useRef, useState} from "react";
import Icon from "../Icon/Icon";
import _ from "lodash";

interface SelectInputProps extends SelectProps {
  containerclassname?: string;
  labelselect: string;
  data: {
    value: string;
    label: string;
  }[];
  required?: boolean;
  handleChange?: (value: any) => void;
  free?: boolean;
}

export function SelectInput(props: SelectInputProps): JSX.Element {
  const {
    labelselect,
    mode,
    data,
    required,
    containerclassname,
    value,
    handleChange,
    free,
    showSearch,
  } = props;
  const [focus, setFocus] = useState(false);
  const [valueSelect, setValueSelect] = useState(value);
  const [options, setOptions] = useState<SelectProps["options"]>(data);
  const isOccupied =
    focus || (free ? false : valueSelect && valueSelect.length !== 0);
  const labelClass = isOccupied ? "label as-label" : "label as-placeholder";
  const timeOut = useRef<any>();
  const requiredMark = required ? (
    <span className="text-required">*</span>
  ) : null;

  useEffect(() => {
    setValueSelect(value);
  }, [value]);

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, []);

  const onChange = (value: any): void => {
    setValueSelect(value);
    if (handleChange) {
      handleChange(value);
    }
  };

  const onFocus = useCallback((): void => {
    setFocus(true);
    timeOut.current = setTimeout(() => {
      sortOption();
    }, 50);
  }, [value, data, mode]);

  const sortOption = (): void => {
    if (
      mode === "multiple" &&
      !!value &&
      value?.length > 0 &&
      !!data &&
      data?.length > 0
    ) {
      let newOptions = [];
      const valueSelect = data.filter((item) =>
        value.some((i: any) => i.value === item.value)
      );
      if (valueSelect?.length > 0) {
        newOptions = _.uniqBy([...valueSelect, ...data], (e) => {
          return e.value;
        });
        setOptions(newOptions);
      }
    } else {
      setOptions(data);
    }
  };

  const IconDown = useMemo(
    () => (
      <Icon
        size={10}
        icon="arrow-drop-down-line"
        color="#324054"
        className=""
      />
    ),
    []
  );

  return (
    <div
      className={classNames("input-select-container", containerclassname)}
      onBlur={() => setFocus(false)}
      onFocus={onFocus}
    >
      <Select
        {...props}
        showSearch={showSearch || mode === "multiple" ? true : !valueSelect}
        labelInValue
        suffixIcon={IconDown}
        options={options}
        showArrow
        maxTagCount="responsive"
        dropdownStyle={{
          borderRadius: "8px",
          borderWidth: "1px",
          borderColor: "rgba(157, 157, 157,0.5)",
          padding: "8px",
        }}
        onChange={onChange}
      />
      <span className={labelClass}>
        {labelselect} {requiredMark}
      </span>
    </div>
  );
}
