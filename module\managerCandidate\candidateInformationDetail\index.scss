.candidate-information-ui {
  width: 100%;
  min-height: 70vh;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  &__evaluate {
    right: -38%;
    width: 32%;
    border-radius: 16px;
    transform: translateY(-50%);
  }

  &_padding {
    padding: 24px 24px 120px 24px;
  }

  &_first {
    width: 50%;
    margin-right: 4px;
  }

  &_detail-overview {
    width: 100%;
  }

  &__detail-avatar {
    width: 20%;
  }

  &_avatar {
    background-color: $primary-color;
    color: $white-color;
  }

  &_detail-summary {
    width: 70%;
  }

  &_detail-status {
    width: 10%;
  }

  .dot-status {
    height: 12px;
    width: 12px;
    border-radius: 12px;
  }

  &_detail {
    width: 100%;
    border: 2px dotted $border-color;
    padding: 12px;
    border-radius: 5px;
    margin-right: 4px;

    hr {
      width: 80%;
      margin: 26px auto;
    }

    &_job {
      .ant-table-cell {
        padding: 6px !important;
      }
      .ant-table-body {
        .ant-table-cell {
          padding: 0px !important;
          a {
            padding: 7px !important;
            display: block;
            &:hover {
              color: inherit;
            }
          }
        }
      }
    }
  }

  &_detail-overview-information-second,
  &_detail-overview-information-first {
    width: 50%;
  }

  &_skill-item {
    background-color: $primary-color;
    padding: 0px 8px;
    border-radius: 8px;
    margin-bottom: 8px;
    color: $white-color;
  }

  &_history {
    width: 100%;
    border: 2px dotted $border-color;
    padding: 12px;
    border-radius: 5px;
    margin-top: 28px;
  }

  .candidate-information-ui_second {
    width: 50%;
    border: 2px dotted $border-color;
    padding: 12px;
    border-radius: 5px;
  }

  &__footer {
    width: 100%;
    height: 95px;
    background-color: $sidebar-background;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    bottom: 0;
  }

  &__footer-delete {
    cursor: pointer;
    color: $link-color;
  }

  &__footer-btn {
    width: 240px;
  }

  .shadow-footer {
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  }

  .status-recruitment {
    padding: 2px 10px;
    border-radius: 5px;
    color: $sidebar-background;
  }

  .cancel {
    background-color: $text-color-input;
  }

  .fail {
    background-color: $status-reject;
  }

  .pending {
    background-color: $status-pending;
  }

  .onboard {
    background-color: $primary-color;
  }

  .pass {
    background-color: $green_color;
  }

  .processing {
    background-color: $header_tf;
  }

  .skill {
    background-color: $primary-color;
    border-radius: 8px;
    color: $white-color;
    margin-bottom: 4px;
  }

  &_note {
    border: 2px dotted $border-color;
    border-radius: 5px;
    padding: 12px;
    margin-top: 28px;
  }

  &_btn-save {
    border-radius: 12px;
    background-color: $primary-color;
    color: $white-color;
    padding: 4px 8px;
    text-align: center;
  }

  &_detail-overview-mail {
    text-wrap: wrap;
  }

  &_detail-overview-information-first {
    padding-right: 0.75rem;
  }

  &_note {
    textarea {
      white-space: pre-line;
      word-break: break-word;
    }
  }

  .potential-candidate {
    padding: 0.75rem;
  }

  &_first-wrapper {
    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 2px grey;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: $scrollbar-color;
      border-radius: 8px;
    }
  }

  .pdf-preview-container {
    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 2px grey;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: $scrollbar-color;
      border-radius: 8px;
    }
  }
}
