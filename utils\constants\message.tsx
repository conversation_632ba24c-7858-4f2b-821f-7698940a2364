const messageErrorUploadCv = {
  errFileSize: "Dung lượng file không được vượt quá 5Mb.",
  errFileType: "Định dạng file cho phép là PDF, Docx, Excel(xls, xlsx, csv)",
};

const messageIntroApplication = {
  success: "Đã giới thiệu ứng viên thành công",
  errorDuplicate: "Ứng viên đã được giới thiệu",
  error: "",
};

const messageRequiredFieldEducation =
  "Vui lòng điền đầy đủ thông tin trình độ học vấn trước khi lưu";

const messageRequiredFieldWorkHistories =
  "Vui lòng điền đầy đủ thông tin lịch sử làm việc trước khi lưu";

const messageValidateEmail = "Vui lòng nhập đúng định dạng địa chỉ email";

const messageRequired = "Vui lòng nhập đầy đủ thông tin ứng viên";

const messageCreateCandidateSuccess = "Tạo ứng viên thành công";

const messageCheckPartnerPaymentExist = "Ứng tuyển này đã được tạo thanh toán";

const messageValidate = "Vui lòng điền đầy đủ thông tin trước khi lưu";

const messageCheckCustomerPaymentExist =
  "Ứng tuyển này đã được tạo thanh toán cho khách hàng";

const messageSuccessRequestJob = "Lưu thông tin thành công";

const messageConfirmCommission =
  "Bạn có muốn ủy thác ứng viên cho Reco hỗ trợ?";

const messageValidatePassword = "Mật khẩu không hợp lệ";

const messageRemind = "Vui lòng đăng nhập để sử dụng";

const messageValidateURL = "Vui lòng nhập đúng định dạng địa chỉ ";

export {
  messageErrorUploadCv,
  messageIntroApplication,
  messageRequiredFieldEducation,
  messageRequiredFieldWorkHistories,
  messageValidateEmail,
  messageRequired,
  messageCreateCandidateSuccess,
  messageCheckPartnerPaymentExist,
  messageValidate,
  messageCheckCustomerPaymentExist,
  messageSuccessRequestJob,
  messageConfirmCommission,
  messageValidatePassword,
  messageRemind,
  messageValidateURL,
};
