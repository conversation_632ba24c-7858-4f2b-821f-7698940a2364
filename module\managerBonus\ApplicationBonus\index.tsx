import AppButton from "@app/components/AppButton";
import AppCheckBox from "@app/components/AppCheckbox";
import AppDatePicker from "@app/components/AppDatePicker";
import Icon from "@app/components/Icon/Icon";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {Checkbox, Col, Popover, Row} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import {useQuery} from "react-query";
import ApiPaymentBonus, {
  IApplicationBonusItem,
  IParamApplicationBonus,
  IPaymentHistories,
  IPaymentInfo,
} from "@app/api/ApiPaymentBonus";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/lib/table";
import moment from "moment";
import {formatMoney, getPaymentStatus} from "@app/utils/constants/function";
import {
  changeListColShowBonusApplication,
  initListColShowBonusApplication,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {useSelector, useDispatch} from "react-redux";
import {CheckboxValueType} from "antd/lib/checkbox/Group";
import ApplicationBonusDetail from "../ApplicationBonusDetail";
import {IAccountRole, OptionSelect} from "@app/types";

const initialValuesFilter: IParamApplicationBonus = {
  isFirstInitialization: true,
};

interface IItemCol {
  label: string;
  value: string;
}

const dataPayment = (data: IApplicationBonusItem): IPaymentInfo[] => {
  if (!data.paymentHistories?.length) {
    // tự động tạo 2 đợt thanh toán
    return [
      {
        amount: Math.floor(Number(data.amount) * 0.25),
        paymentDateExpected: data.onboardDate
          ? moment(data.onboardDate, DATE_FORMAT)
              .add(10, "days")
              .format(DATE_FORMAT)
          : "",
        paymentDate: "",
        status: data.status === 3 ? 3 : 0, // đang trong chờ thanh toán(0) hoặc  hủy thanh toán(3)
      },
      {
        amount: data.amount - Math.floor(Number(data.amount) * 0.25),
        paymentDateExpected: data.onboardDate
          ? moment(data.onboardDate, DATE_FORMAT)
              .add(Number(data.monthWarranty), "months")
              .format(DATE_FORMAT)
          : "",
        paymentDate: "",
        status: data.status === 3 ? 3 : 0, // đang trong chờ thanh toán(0) hoặc  hủy thanh toán(3)
      },
    ];
  }

  let payments: IPaymentInfo[] = data.paymentHistories.map(
    (i: IPaymentHistories) => ({
      amount: i.amount,
      paymentDateExpected: i.paymentDateExpected
        ? moment(i.paymentDateExpected).format(DATE_FORMAT)
        : "",
      paymentDate: i.paymentDate
        ? moment(i.paymentDate).format(DATE_FORMAT)
        : "",
      // có ngày thanh toán sẽ là hoàn thành , còn chưa thì sẽ là hủy hoặc chờ tùy thuộc trạng thái hiện tại
      status: i.paymentDate ? 2 : data.status === 3 ? 3 : 0,
    })
  );
  // trường hợp đang thanh toán hoặc dừng thanh toán
  if (data.status === 1 || data.status === 3) {
    let paid = 0;
    data.paymentHistories.forEach((e) => {
      paid += e.amount;
    });
    // tạo thêm 1 item payment
    const payment: IPaymentInfo = {
      amount: Number(data.amount) - paid,
      paymentDateExpected: data.paymentDateExpectedString,
      paymentDate: "",
      status: data.status,
    };
    payments = [...payments, payment];
  }

  return payments;
};

export default function ApplicationBonus(): JSX.Element {
  const {listColShowBonusApplication, user} = useSelector(selectUser);
  const dispatch = useDispatch();
  const [isShowFilterAdvance, setIsShowFilterAdvance] = useState(false);
  const timeOut = useRef<any>();
  const filterAdvancedRef = useRef<FormikProps<IParamApplicationBonus>>(null);
  const [pageSize, setPageSize] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const [valuesFilter, setValuesFilter] =
    useState<IParamApplicationBonus>(initialValuesFilter);
  const positionFilters = useRef<OptionSelect[]>([]);
  const statusFilters = useRef<OptionSelect[]>([]);
  const [isShowDetail, setIsShowDetail] = useState<boolean>(false);
  const [applicationBonusItem, setApplicationBonusItem] =
    useState<IApplicationBonusItem>();
  const isCSL =
    user?.role?.includes(IAccountRole.CSL) ||
    user?.role?.includes(IAccountRole.ADMIN);

  useEffect(() => {
    if (!listColShowBonusApplication) {
      dispatch(
        changeListColShowBonusApplication(initListColShowBonusApplication)
      );
    }
  }, []);

  const getApplicationBonus = useQuery(
    ["getApplicationBonus", currentPage, pageSize, valuesFilter],
    () => {
      return ApiPaymentBonus.getApplicationBonus({
        ...valuesFilter,
        pageSize: pageSize,
        currentPage: currentPage,
      });
    }
  );

  const dashboardPagingDatas = getApplicationBonus.data?.dashboardPagingDatas;

  positionFilters.current = getApplicationBonus.data?.positionFilters?.length
    ? getApplicationBonus.data?.positionFilters.map((e) => ({
        key: String(e.id),
        value: e.label,
        label: e.label,
      })) || []
    : positionFilters.current;

  statusFilters.current = getApplicationBonus.data?.statusFilters?.length
    ? getApplicationBonus.data?.statusFilters.map((e) => ({
        key: String(e.id),
        value: e.label,
        label: e.label,
      })) || []
    : statusFilters.current;

  const listData: IApplicationBonusItem[] = [];

  if (dashboardPagingDatas?.length && dashboardPagingDatas?.length > 0) {
    dashboardPagingDatas.forEach((element: IApplicationBonusItem) => {
      const listPayment = dataPayment(element);
      listPayment.forEach((e) => {
        listData.push({...element, paymentInfo: e});
      });
    });
  }

  const isFirstInitialization =
    statusFilters.current.length === 0 || positionFilters.current.length === 0;

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  const handleSearch = () => {
    const values = filterAdvancedRef.current?.values;
    const searchParams = {
      ...values,
      statuses:
        values?.statuses?.map((e: any) => ({id: e?.key, label: e.label})) || [],
      positions:
        values?.positions?.map((e: any) => ({id: e?.key, label: e.label})) ||
        [],
      isFirstInitialization,
      from: values?.from ? moment(values.from).format(DATE_FORMAT) : "",
      to: values?.to ? moment(values?.to).format(DATE_FORMAT) : "",
      textSearch: values?.textSearch?.trim() || "",
    };
    setCurrentPage(1);
    setValuesFilter(searchParams);
  };

  const onChangeTextSearch = (e: any): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const validateTime = (from: any, to: any): boolean => {
    return !from || !to || moment(from).isSameOrBefore(moment(to));
  };

  const closeModalDetail = (): void => {
    setApplicationBonusItem(undefined);
    setIsShowDetail(false);
  };

  const onClickSearch = (): void => {
    const values = filterAdvancedRef.current?.values;
    if (!values) {
      return;
    }
    if (!validateTime(values?.from, values?.to)) {
      return;
    }
    handleSearch();
  };

  const resetData = (): void => {
    filterAdvancedRef.current?.resetForm();
    setCurrentPage(1);
    // setIsShowFilterAdvance(false);
    setValuesFilter({textSearch: "", isFirstInitialization});
  };

  const listColLeft: IItemCol[] = [
    {label: "Vị trí", value: "positionName"},
    {label: "Trạng thái ứng tuyển", value: "statusApplication"},
    {label: "Khách hàng", value: "customerName"},
    {label: "Ngày onboard", value: "onboardDate"},
    {label: "Ngày chấm dứt hợp đồng", value: "contractTerminationDate"},
  ];

  const listColRight: IItemCol[] = [
    {label: "Trạng thái thanh toán", value: "statusName"},
    {label: "Ngày thanh toán dự kiến", value: "paymentDateExpected"},
    {label: "Thanh toán ngày", value: "paymentDate"},
    {label: "Ghi chú", value: "note"},
  ];

  const onChangeListCol = (checkedValues: CheckboxValueType[]): void => {
    dispatch(changeListColShowBonusApplication(checkedValues as string[]));
  };

  const filterCol = (
    <Checkbox.Group
      className="group-check-box-list-col"
      value={listColShowBonusApplication || []}
      onChange={onChangeListCol}
    >
      <Row>
        <Col span={12}>
          {listColLeft.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col span={12}>
          {listColRight.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );

  function contentFilterAdvance(values: IParamApplicationBonus): JSX.Element {
    return (
      <div className="content-filter-advance">
        <Row className="flex items-center justify-between mb-4">
          <span className="title-filter">Tất cả bộ lọc</span>
          <AppButton
            classrow="btn-close-popover"
            typebutton="normal"
            onClick={(): void => setIsShowFilterAdvance(false)}
          >
            <Icon className="" icon="close-circle-line" size={20} />
          </AppButton>
        </Row>
        {isCSL && (
          <TextInput
            containerclassname="mt-2"
            label="CST quản lý"
            name="consultantName"
            value={values.consultantName}
            free={!values.consultantName}
          />
        )}
        <TextInput
          containerclassname="mt-2"
          label="Người tạo"
          name="partnerName"
          value={values.partnerName}
          free={!values.partnerName}
        />
        <TextInput
          containerclassname="mt-2"
          label="Nhập tên ứng viên"
          name="candidateName"
          value={values.candidateName}
          free={!values.candidateName}
        />
        <SelectInput
          containerclassname="mt-2"
          name="positions"
          mode="multiple"
          labelselect="Vị trí ứng tuyển"
          data={positionFilters.current}
          value={values.positions}
          free={values?.positions?.length === 0}
          allowClear
        />
        <SelectInput
          containerclassname="mt-2"
          name="statuses"
          mode="multiple"
          labelselect="Trạng thái"
          data={statusFilters.current}
          value={values.statuses}
          free={values?.statuses?.length === 0}
          allowClear
        />
        <div className="mt-2 text16">Ngày thanh toán</div>
        <Row className="mt-2 div-time">
          <AppDatePicker
            name="from"
            label="Từ"
            format={DATE_FORMAT}
            free={!values.from}
            status={!validateTime(values?.from, values?.to) ? "error" : ""}
            allowClear
          />
          <AppDatePicker
            name="to"
            label="Đến"
            format={DATE_FORMAT}
            free={!values.to}
            status={!validateTime(values?.from, values?.to) ? "error" : ""}
            allowClear
          />
        </Row>
        <Row className="mt-6 div-time">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={resetData}
          />
          <AppButton
            label="Tìm kiếm"
            typebutton="primary"
            onClick={onClickSearch}
          />
        </Row>
      </div>
    );
  }

  const rowSpan = (row: IApplicationBonusItem, index: number): number => {
    const indexFirstSameId = listData.findIndex(
      (e) => e.paymentId === row.paymentId
    );
    const listPayment = dataPayment(row);
    if (index === indexFirstSameId) {
      return listPayment.length;
    }
    return 0;
  };

  const renderCell = (
    value: any,
    row: IApplicationBonusItem,
    index: number
  ): any => {
    return {
      children: value,
      props: {
        rowSpan: rowSpan(row, index),
      },
    };
  };

  const renderCellStatusApplication = (
    value: any,
    row: IApplicationBonusItem,
    index: number
  ): any => {
    let status = "Onboard";

    if (row.contractTerminationDate) {
      status = "Onboard Fail";
    }

    if (
      row.onboardDate &&
      row.monthWarranty &&
      moment(row.onboardDate, DATE_FORMAT)
        .add(row.monthWarranty, "months")
        .isBefore(moment())
    ) {
      status = "Onboard Pass";
    }

    const className =
      status === "Onboard"
        ? "status-application opacity-60"
        : "status-application";
    return {
      children: <div className={className}>{status}</div>,
      props: {
        rowSpan: rowSpan(row, index),
      },
    };
  };

  const columnAll: ColumnsType<IApplicationBonusItem> = [
    {
      title: "CST quản lý",
      dataIndex: "consultantName",
      key: "consultantName",
      render: renderCell,
    },
    {
      title: "Người tạo",
      dataIndex: "applicationCreatorName",
      key: "applicationCreatorName",
      render: renderCell,
    },
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
      render: renderCell,
    },
    {
      title: "Vị trí",
      dataIndex: "positionName",
      key: "positionName",
      render: renderCell,
    },
    {
      title: "Trạng thái ứng tuyển",
      dataIndex: "statusApplication",
      key: "statusApplication",
      render: renderCellStatusApplication,
      width: "10%",
    },
    {
      title: "Khách hàng",
      dataIndex: "customerName",
      key: "customerName",
      render: renderCell,
    },
    {
      title: "Ngày onboard",
      dataIndex: "onboardDate",
      key: "onboardDate",
      render: renderCell,
      width: "8%",
    },
    {
      title: "Ngày chấm dứt hợp đồng",
      dataIndex: "contractTerminationDate",
      key: "contractTerminationDate",
      render: renderCell,
    },
    {
      title: "Số tiền",
      dataIndex: "amount",
      key: "amount",
      className: "cell-multiple-row",
      render: (_, item: IApplicationBonusItem) => (
        <div>{formatMoney(item.paymentInfo?.amount)}</div>
      ),
    },
    {
      title: "Trạng thái thanh toán",
      dataIndex: "statusName",
      key: "statusName",
      className: "cell-multiple-row",
      render: (_, item: IApplicationBonusItem) => (
        <div
          className="status-payment"
          style={{
            backgroundColor: getPaymentStatus(item.paymentInfo?.status || 0)
              .color,
          }}
        >
          {getPaymentStatus(item.paymentInfo?.status || 0).label}
        </div>
      ),
      width: "11%",
    },
    {
      title: "Ngày thanh toán dự kiến",
      dataIndex: "paymentDateExpected",
      key: "paymentDateExpected",
      className: "cell-multiple-row",
      render: (_, item: IApplicationBonusItem) => (
        <div>{item.paymentInfo?.paymentDateExpected}</div>
      ),
      width: "10%",
    },
    {
      title: "Ngày thanh toán",
      dataIndex: "paymentDate",
      key: "paymentDate",
      className: "cell-multiple-row",
      render: (_, item: IApplicationBonusItem) => (
        <div>{item.paymentInfo?.paymentDate}</div>
      ),
      width: "10%",
    },
    {
      title: "Ghi chú",
      dataIndex: "note",
      key: "note",
      render: renderCell,
    },
  ];

  const columns = columnAll.filter((item) =>
    [
      isCSL ? "consultantName" : "",
      "applicationCreatorName",
      "candidateName",
      "amount",
      ...(listColShowBonusApplication || []),
    ].some((i) => i === item.key)
  );

  return (
    <div className="block application-bonus-container">
      <Formik
        initialValues={initialValuesFilter}
        innerRef={filterAdvancedRef}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          return (
            <Row className="flex justify-between items-center mb-4">
              <TextInput
                containerclassname="w-1/3"
                name="textSearch"
                label={`Tìm kiếm theo ${
                  isCSL ? "CST quản lý, " : ""
                }người tạo, tên ứng viên`}
                value={values.textSearch}
                onChange={onChangeTextSearch}
                disabled={isShowFilterAdvance}
              />
              <Row>
                <Popover
                  className="mr-1"
                  placement="bottom"
                  trigger="click"
                  content={(): React.ReactNode => contentFilterAdvance(values)}
                  open={isShowFilterAdvance}
                  onOpenChange={setIsShowFilterAdvance}
                >
                  <AppButton typebutton="normal" classrow="btn-filter">
                    <Icon
                      className="mr-1"
                      icon="filter-line"
                      size={12}
                      color="#324054"
                    />
                    Tìm kiếm nâng cao
                  </AppButton>
                </Popover>
                <Popover placement="bottom" trigger="click" content={filterCol}>
                  <AppButton typebutton="normal" classrow="btn-filter">
                    <Icon
                      className="mr-1"
                      icon="eye"
                      size={16}
                      color="#324054"
                    />
                    Hiển thị {columns.length}/
                    {isCSL ? columnAll.length : columnAll.length - 1}
                  </AppButton>
                </Popover>
              </Row>
            </Row>
          );
        }}
      </Formik>
      <AppTable
        rowClassName="row-table cursor-pointer"
        onRow={(record: IApplicationBonusItem): any => {
          return {
            onClick: (): void => {
              setApplicationBonusItem(record);
              setIsShowDetail(true);
            },
          };
        }}
        dataSource={listData?.map(
          (item: IApplicationBonusItem, index: number) => ({
            ...item,
            key: index,
          })
        )}
        columns={columns}
        scroll={{x: "100%", y: "52vh"}}
        loading={getApplicationBonus.isLoading}
        rowKey="key"
      />
      <AppPagination
        className="mt-3"
        defaultPageSize={pageSize}
        current={currentPage}
        pageSize={pageSize}
        total={getApplicationBonus.data?.totalCount}
        onChange={handlePagination}
      />
      <ApplicationBonusDetail
        closeModal={closeModalDetail}
        open={isShowDetail}
        applicationBonusItem={applicationBonusItem}
        getApplicationBonus={getApplicationBonus}
        setIsShowDetail={setIsShowDetail}
      />
    </div>
  );
}
