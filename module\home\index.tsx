import React from "react";
import "./index.scss";
import {Col, Empty, Image, List, Row} from "antd";
import FilterJobsLanding from "./FilterJobsLanding";
import {useQuery} from "react-query";
import ApiJob, {INews} from "@app/api/ApiJob";
import {CardViewJob} from "@app/components/CardViewJob";
import AppPagination from "@app/components/AppPagination";
import {useDispatch, useSelector} from "react-redux";
import {selectNews, setListNews} from "@app/redux/slices/NewsSlice";
import {Swiper, SwiperSlide} from "swiper/react";
import {Autoplay, Pagination} from "swiper";
import AppLoading from "@app/components/AppLoading";
import config from "@app/config";
import {useRouter} from "next/router";
import HomeContact from "./HomeContact";
import {heightBanner} from "@app/utils/constants/state";
import Mobile from "@app/components/Layout/Mobile";
import Desktop from "@app/components/Layout/Desktop";
import AppSkeleton from "@app/components/AppSkeleton";
import HomePagination from "./HomePagination";
import {IRootState} from "@app/redux/store";
import {setSearchParamJobFilter} from "@app/redux/slices/DataHomePageSlice";
import LayoutJobPublicCustom from "./LayoutJobPublicCustom";

export default function HomePageComponent(): JSX.Element {
  const dispatch = useDispatch();
  const router = useRouter();
  const {listNews} = useSelector(selectNews);
  const system = useSelector((state: IRootState) => state.system);
  const homePageData = useSelector((state: IRootState) => state.homePageData);

  const getListNews = useQuery(
    ["getListNews"],
    () => {
      return ApiJob.getListNews();
    },
    {
      onSuccess: (data) => {
        dispatch(setListNews(data ?? []));
      },
    }
  );

  const onChangePagination = (page: number, pageSize: number): void => {
    const values: any = {
      ...homePageData.searchParamsJobFilter,
      pageSize: pageSize,
      currentPage: page,
    };

    dispatch(setSearchParamJobFilter(values));
  };

  const redirectJobPublic = (id: string | number): void => {
    router.push(config.PATHNAME.JOB_PUBLIC_DETAIL_PARAMS(id));
  };

  return (
    <div className="home">
      <Desktop>
        <div className="home__search">
          <div className="home__border">
            <Row justify="center" className="w-full">
              <Col xs={24}>
                <FilterJobsLanding />
              </Col>
            </Row>
          </div>
        </div>
        <div className="home__container mt-4">
          <Row>
            <Col xs={18}>
              <div>
                <div className="flex justify-between">
                  <p className="font-bold text20">
                    Danh sách việc làm ({homePageData.data?.totalJob || 0} việc
                    làm)
                  </p>
                </div>
                <div className="home__job mt-2">
                  <List
                    loading={system.isLoading}
                    grid={{
                      gutter: 48,
                      xs: 1,
                      sm: 1,
                      md: 1,
                      lg: 2,
                      xl: 2,
                      xxl: 2,
                    }}
                    dataSource={homePageData.data.jobs ?? []}
                    renderItem={(item) => (
                      <List.Item>
                        <CardViewJob
                          requestJob={item}
                          onClick={(): void =>
                            redirectJobPublic(item?.requestJobId)
                          }
                        />
                      </List.Item>
                    )}
                  />
                  <div>
                    <AppPagination
                      current={homePageData.data.currentPage || 1}
                      pageSize={homePageData.data.pageSize || 20}
                      onChange={onChangePagination}
                      total={homePageData.data?.totalJob ?? 0}
                    />
                  </div>
                </div>
              </div>
            </Col>
            <Col span={6} className="pl-4 container-list-news">
              <Row className="justify-between mb-2 items-baseline">
                <span className="title-news">Tin Mới nhất</span>
                <a
                  className="see-all"
                  href={config.HREF_NEWS}
                  target="_blank"
                  rel="noreferrer"
                >
                  Xem tất cả
                </a>
              </Row>
              <div className="home__banner">
                {listNews && listNews.length > 0 ? (
                  <Swiper
                    pagination
                    modules={[Pagination, Autoplay]}
                    autoplay={{
                      delay: 3000,
                      disableOnInteraction: false,
                    }}
                  >
                    {listNews.map((item: INews, index) => (
                      <SwiperSlide key={index} className="cursor-pointer">
                        <Image
                          width="100%"
                          height={heightBanner}
                          src={item?.yoast_head_json?.og_image[0]?.url}
                          preview={false}
                          onClick={() => {
                            window.open(
                              item.link,
                              "_blank",
                              "noopener,noreferrer"
                            );
                          }}
                        />
                      </SwiperSlide>
                    ))}
                  </Swiper>
                ) : (
                  <div
                    style={{height: heightBanner}}
                    className="flex justify-center items-center"
                  >
                    {getListNews.isLoading ? <AppLoading /> : <Empty />}
                  </div>
                )}
                <HomeContact />
              </div>
            </Col>
          </Row>
        </div>
      </Desktop>
      <Mobile>
        <LayoutJobPublicCustom>
          <div className="home__container mt-4">
            <Row>
              <Col xs={24}>
                <div>
                  <div className="flex justify-between">
                    <p className="font-bold text20">
                      Danh sách việc làm ({homePageData.data?.totalJob || 0}{" "}
                      việc làm)
                    </p>
                  </div>
                  <div className="mt-2">
                    {system.isLoading ? (
                      <div>
                        <AppSkeleton paragraph={{rows: 4}} totalItem={4} />
                      </div>
                    ) : (
                      <div>
                        <List
                          loading={system.isLoading}
                          grid={{
                            xs: 1,
                            sm: 1,
                            md: 1,
                            lg: 1,
                            xl: 1,
                            xxl: 1,
                          }}
                          dataSource={homePageData.data.jobs ?? []}
                          renderItem={(item) => (
                            <List.Item>
                              <CardViewJob
                                requestJob={item}
                                onClick={(): void =>
                                  redirectJobPublic(item?.requestJobId)
                                }
                              />
                            </List.Item>
                          )}
                        />
                      </div>
                    )}

                    <div className="flex justify-center mb-4">
                      <HomePagination
                        pageNumber={homePageData.data.currentPage || 1}
                        pageSize={homePageData.data.pageSize || 20}
                        onPageChange={onChangePagination}
                        total={homePageData.data?.totalJob ?? 0}
                      />
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </LayoutJobPublicCustom>
      </Mobile>
    </div>
  );
}
