// eslint-disable-next-line import/no-cycle
import {
  DataListApplicationGroupBy,
  ParamsGroupByApplication,
} from "@app/api/ApiApplication";
import {DataGroupByItem} from "@app/types";
import {createSlice, PayloadAction} from "@reduxjs/toolkit";

interface GroupByApplicationState {
  groupByFieldApplication: ParamsGroupByApplication;
  dataGroupApplication: {
    totalCount: number;
    data: Array<DataGroupByItem>;
  };
  selectGroupByValue: string;
  isLoadingApplication: boolean;
}

export const initialGroupByFieldApplication: ParamsGroupByApplication = {
  groupByRequest: {
    groupBy: "",
    groupByValue: "1",
    pageNumber: 1,
    pageSize: 20,
  },
  applicationSearch: {
    candidateInformation: "",
    customerName: "",
    requestJobName: "",
    workingLocationIds: [],
    stages: [],
    statuses: [],
    creators: [],
    from: "",
    to: "",
    isAdvanceSearch: true,
  },
};

const initialState: GroupByApplicationState = {
  groupByFieldApplication: initialGroupByFieldApplication,
  dataGroupApplication: {
    totalCount: 0,
    data: [],
  },
  selectGroupByValue: "",
  isLoadingApplication: false,
};

export const groupByApplicationSlice = createSlice({
  name: "groupByApplication",
  initialState,
  reducers: {
    setGroupByFieldApplication: (
      state: GroupByApplicationState,
      action: PayloadAction<ParamsGroupByApplication>
    ) => {
      state.groupByFieldApplication = action.payload;
    },
    setGroupByList: (
      state: GroupByApplicationState,
      action: PayloadAction<Array<DataGroupByItem>>
    ) => {
      state.dataGroupApplication.data = action.payload;
    },
    setSelectGroupByValue: (
      state: GroupByApplicationState,
      action: PayloadAction<string>
    ) => {
      state.selectGroupByValue = action.payload;
    },
    setDataGroupByValue: (
      state: GroupByApplicationState,
      action: PayloadAction<DataListApplicationGroupBy>
    ) => {
      const {
        pageNumber,
        applicationSearchDetail,
        pageSize,
        totalCount,
        totalPages,
      } = action.payload;

      state.dataGroupApplication.data = state.dataGroupApplication.data.map(
        (item) => {
          const key = String(item.groupByValue);
          if (state.selectGroupByValue === key) {
            return {
              ...item,
              data: applicationSearchDetail,
              pageNumber,
              pageSize,
              totalCount,
              totalPages,
            };
          }
          return item;
        }
      );
    },
    setTotalCount: (
      state: GroupByApplicationState,
      action: PayloadAction<number>
    ) => {
      state.dataGroupApplication.totalCount = action.payload;
    },
    setLoadingApplication: (
      state: GroupByApplicationState,
      action: PayloadAction<boolean>
    ) => {
      state.isLoadingApplication = action.payload;
    },
  },
});

export const {
  setGroupByFieldApplication,
  setGroupByList,
  setSelectGroupByValue,
  setDataGroupByValue,
  setTotalCount,
  setLoadingApplication,
} = groupByApplicationSlice.actions;
export default groupByApplicationSlice.reducer;
