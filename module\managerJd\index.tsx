/* eslint-disable jsx-a11y/no-static-element-interactions */
import {
  DataCreateJd,
  JD,
  createJd,
  getJd,
  getJds,
} from "@app/api/ApiJobRequest";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {Col, Row, notification} from "antd";
import {LabeledValue} from "antd/lib/select";
import classNames from "classnames";
import {Formik, FormikProps} from "formik";
import {useRef, useState} from "react";
import {useMutation, useQuery} from "react-query";
import "./index.scss";
import AppLoadingSystem from "@app/components/AppLoadingSystem";

type JdForm = {
  tags: string;
  language: string;
};

export default function JobDescription(): JSX.Element {
  const [selectJD, setSelectJD] = useState<number>(-1);
  const formikRef = useRef<FormikProps<JdForm>>(null);
  const timeout = useRef<any>(null);

  const handleSelectJD = (id: number) => {
    setSelectJD(id);
  };

  const dataLanguage: LabeledValue[] = [
    {
      label: "Tiếng Việt",
      value: "vietnamese",
      key: "vietnamese",
    },
    {
      label: "Tiếng Anh",
      value: "english",
      key: "english",
    },
  ];

  const scrollToJd = () => {
    // eslint-disable-next-line no-unused-expressions
    timeout.current && clearTimeout(timeout.current);
    timeout.current = setTimeout(() => {
      const target = document.getElementById("jd__active");

      target?.scrollIntoView({
        behavior: "smooth",
      });
    }, 200);
  };

  const {data: jds, refetch} = useQuery(["useJds"], () => {
    return getJds();
  });

  const {data: detailJd, isLoading} = useQuery(
    ["useDetailJd", selectJD],
    () => {
      return getJd(selectJD);
    },
    {
      enabled: selectJD !== -1,
      onSuccess(data) {
        formikRef.current?.setValues({
          tags: data?.tags?.join() || "",
          language: data?.language || "",
        });
      },
    }
  );

  const {mutate, isLoading: isLoadingCreate} = useMutation(
    ["creatJd"],
    (dataReq: DataCreateJd) => {
      return createJd(dataReq);
    },
    {
      onSuccess(data) {
        notification.success({
          message: "Thành công",
          description: "Tạo JD thành công",
        });
        setSelectJD(data.jobDescriptionId);
        refetch();
        scrollToJd();
      },
    }
  );

  const handleCreateJd = (values: any): void => {
    const dataReq: DataCreateJd = {
      tags: [values.tags.trim()],
      language: values.language.value || values.language,
    };

    mutate(dataReq);
  };

  const handleResetForm = () => {
    formikRef.current?.setValues({
      tags: "",
      language: dataLanguage[0].value as string,
    });

    setSelectJD(-1);
  };
  return (
    <div className="jd">
      <AppLoadingSystem isLoading={isLoadingCreate || isLoading} />
      <Row gutter={[16, 16]} className="h-full">
        <Col xs={4}>
          <div className="border-dashed-dot jd__history">
            <div className="flex justify-end">
              <div className="icon cursor-pointer" onClick={handleResetForm}>
                <Icon size={16} icon="edit-line" />
              </div>
            </div>
            <div className="jd__list mt-2 overflow-y-auto overflow-x-hidden">
              {jds?.map((i: JD) => (
                // eslint-disable-next-line jsx-a11y/no-static-element-interactions
                <div
                  key={i.jobDescriptionId}
                  className={classNames("jd__item", "cursor-pointer", {
                    jd__active: i.jobDescriptionId === selectJD,
                  })}
                  onClick={(): void => {
                    handleSelectJD(i.jobDescriptionId);
                  }}
                  id={i.jobDescriptionId === selectJD ? "jd__active" : "jd"}
                >
                  {i.title}
                </div>
              ))}
            </div>
          </div>
        </Col>
        <Col xs={20}>
          {/* {isLoading ? (
            <Skeleton paragraph={{rows: 10}} />
          ) : (
            <div className="border-dashed-dot jd__description">
              <Formik
                initialValues={{
                  tags: detailJd?.tags?.join("") || "",
                  language: detailJd?.language || "vietnamese",
                }}
                onSubmit={handleCreateJd}
                innerRef={formikRef as any}
              >
                {({values, handleSubmit}) => (
                  <div>
                    <Row gutter={[16, 16]}>
                      <Col xs={16}>
                        <TextInput
                          name="tags"
                          value={values.tags}
                          free={!values.tags}
                          label="Nhập từ khóa"
                          placeholder="Nhập từ khóa: android developer, OOP, Jetpack compose, MVVM model "
                        />
                      </Col>
                      <Col xs={5}>
                        <SelectInput
                          name="language"
                          value={values.language}
                          free={!values.language}
                          data={dataLanguage as any}
                          labelselect="Ngôn Ngữ"
                          defaultValue="vietnamese"
                        />
                      </Col>
                      <Col xs={3}>
                        <AppButton
                          onClick={() => {
                            handleSubmit(values as any);
                          }}
                          typebutton="primary"
                          loading={isLoadingCreate}
                          disabled={isLoadingCreate}
                        >
                          Tạo JD
                        </AppButton>
                      </Col>
                    </Row>
                  </div>
                )}
              </Formik>
              <div className="jd__description-block mt-2 overflow-y-auto overflow-x-hidden">
                {selectJD !== -1 ? (
                  <div>
                    <div className="jd__description-block-item flex">
                      <div className="mr-1 font-semibold">Title:</div>
                      <div>{detailJd?.title || ""}</div>
                    </div>
                    <div className="jd__description-block-item">
                      <div className="font-semibold">Responsibilities:</div>
                      <div className="list-disc pl-6">
                        {detailJd?.responsibilities?.map(
                          (i: string, idx: number) => (
                            <div key={idx}>{i}</div>
                          )
                        )}
                      </div>
                    </div>
                    <div className="jd__description-block-item">
                      <div className="font-semibold">Requirement:</div>
                      <ul className="list-disc pl-6">
                        {detailJd?.requirements?.map(
                          (i: string, idx: number) => (
                            <li key={idx}>{i}</li>
                          )
                        )}
                      </ul>
                    </div>
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          )} */}
          <div className="border-dashed-dot jd__description">
            <Formik
              initialValues={{
                tags: detailJd?.tags?.join("") || "",
                language: detailJd?.language || "vietnamese",
              }}
              onSubmit={handleCreateJd}
              innerRef={formikRef as any}
            >
              {({values, handleSubmit}) => (
                <div>
                  <Row gutter={[16, 16]}>
                    <Col xs={16}>
                      <TextInput
                        name="tags"
                        value={values.tags}
                        free={!values.tags}
                        label="Nhập từ khóa"
                        placeholder="Nhập từ khóa: android developer, OOP, Jetpack compose, MVVM model "
                      />
                    </Col>
                    <Col xs={5}>
                      <SelectInput
                        name="language"
                        value={values.language}
                        free={!values.language}
                        data={dataLanguage as any}
                        labelselect="Ngôn Ngữ"
                        defaultValue="vietnamese"
                      />
                    </Col>
                    <Col xs={3}>
                      <AppButton
                        onClick={() => {
                          handleSubmit(values as any);
                        }}
                        typebutton="primary"
                        loading={isLoadingCreate}
                        disabled={isLoadingCreate}
                      >
                        Tạo JD
                      </AppButton>
                    </Col>
                  </Row>
                </div>
              )}
            </Formik>
            <div className="jd__description-block mt-2 overflow-y-auto overflow-x-hidden">
              {selectJD !== -1 ? (
                <div>
                  <div className="jd__description-block-item flex">
                    <div className="mr-1 font-semibold">Title:</div>
                    <div>{detailJd?.title || ""}</div>
                  </div>
                  <div className="jd__description-block-item">
                    <div className="font-semibold">Responsibilities:</div>
                    <ul className="list-disc pl-6">
                      {detailJd?.responsibilities?.map(
                        (i: string, idx: number) => (
                          <li key={idx}>{i}</li>
                        )
                      )}
                    </ul>
                  </div>
                  <div className="jd__description-block-item">
                    <div className="font-semibold">Requirement:</div>
                    <ul className="list-disc pl-6">
                      {detailJd?.requirements?.map((i: string, idx: number) => (
                        <li key={idx}>{i}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
}
