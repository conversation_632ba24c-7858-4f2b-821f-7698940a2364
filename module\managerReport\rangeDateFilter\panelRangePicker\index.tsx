/* eslint-disable jsx-a11y/no-static-element-interactions */
import moment from "moment";

interface IPanelRangePicker {
  panelNode: any;
  onChangeDate: (value: any) => void;
  onCloseModalRangePicker: () => void;
}

export default function PanelRangePicker({
  panelNode,
  onChangeDate,
  onCloseModalRangePicker,
}: IPanelRangePicker): JSX.Element {
  const presetRanges = [
    {
      label: "Hôm nay",
      value: [moment(), moment()],
    },
    {
      label: "Hôm qua",
      value: [moment().add(-1, "d"), moment().add(-1, "d")],
    },
    {
      label: "7 ngày gần nhất",
      value: [moment().add(-6, "d"), moment()],
    },
    {
      label: "30 ngày gần nhất",
      value: [moment().add(-29, "d"), moment()],
    },
    {
      label: "Tuần này",
      value: [moment().startOf("isoWeek"), moment().endOf("isoWeek")],
    },
    {
      label: "Th<PERSON>g này",
      value: [moment().startOf("month"), moment().endOf("month")],
    },
    {
      label: "Quý gần nhất",
      value: [
        moment().subtract(1, "quarter").startOf("quarter"),
        moment().subtract(1, "quarter").endOf("quarter"),
      ],
    },
  ];

  const handleClickPreset = (value: moment.Moment[]): void => {
    if (value.length === 2) {
      onChangeDate(value);
      onCloseModalRangePicker();
    }
  };

  return (
    <div className="flex">
      <div className="w-[160px] pl-4 py-2">
        {presetRanges.map((item, index) => (
          <div
            key={index}
            className="cursor-pointer py-1.5"
            onClick={() => handleClickPreset(item.value)}
          >
            {item?.label}
          </div>
        ))}
      </div>
      <div className="">{panelNode}</div>
    </div>
  );
}
