// eslint-disable-next-line import/no-cycle
import {
  ActiveDateEnum,
  IMultiSelect,
  OptionSelect,
  StatusRequestJob,
} from "@app/types";
import _ from "lodash";

const listLanguage: IMultiSelect[] = [
  {
    label: "Anh - <PERSON>ginner",
    id: "Anh - Beginner",
  },
  {
    label: "Anh - Intermediate",
    id: "Anh - Intermediate",
  },
  {
    label: "Anh - Good",
    id: "Anh - Good",
  },
  {
    label: "Anh - Fluent",
    id: "Anh - Fluent",
  },
  {
    label: "Anh - Professional",
    id: "Anh - Professional",
  },
  {
    label: "<PERSON>ứ<PERSON>",
    id: "<PERSON>ứ<PERSON>",
  },
  {
    label: "Hàn - Topik 1",
    id: "Hàn - Topik 1",
  },
  {
    label: "Hàn - Topik 2",
    id: "Hàn - Topik 2",
  },
  {
    label: "Hàn - Topik 3",
    id: "Hàn - Topik 3",
  },
  {
    label: "Hàn - Topik 4",
    id: "Hàn - Topik 4",
  },
  {
    label: "Hàn - Topik 5",
    id: "Hàn - Topik 5",
  },
  {
    label: "Hàn - Topik 6",
    id: "Hàn - Topik 6",
  },

  {
    label: "Nga",
    id: "Nga",
  },
  {
    label: "Nhật - N1",
    id: "Nhật - N1",
  },
  {
    label: "Nhật - N2",
    id: "Nhật - N2",
  },
  {
    label: "Nhật - N3",
    id: "Nhật - N3",
  },
  {
    label: "Nhật - N4",
    id: "Nhật - N4",
  },
  {
    label: "Nhật - N5",
    id: "Nhật - N5",
  },

  {
    label: "Pháp",
    id: "Pháp",
  },
  {
    label: "Tây Ban Nha",
    id: "Tây Ban Nha",
  },
  {
    label: "Thái",
    id: "Thái",
  },

  {
    label: "Trung - HSK1",
    id: "Trung - HSK1",
  },
  {
    label: "Trung - HSK2",
    id: "Trung - HSK2",
  },
  {
    label: "Trung - HSK3",
    id: "Trung - HSK3",
  },
  {
    label: "Trung - HSK4",
    id: "Trung - HSK4",
  },
  {
    label: "Trung - HSK5",
    id: "Trung - HSK5",
  },
  {
    label: "Trung - HSK6",
    id: "Trung - HSK6",
  },
  {
    label: "Việt",
    id: "Việt",
  },
  {
    label: "Ý",
    id: "Ý",
  },
];

const statusCandidate: {id: number; label: string; color: string}[] = [
  {label: "Open", color: "green", id: 1},
  {label: "Consider", color: "Orange", id: 2},
  {label: "Closed", color: "gray", id: 3},
];

const statusCollaborator: OptionSelect[] = [
  {id: 2, label: "Hoạt động", value: "Hoạt động", color: "#2F6BFF", key: "2"},
  {id: 4, label: "Khóa", value: "Khoá", color: "#DC2323", key: "4"},
  {
    id: 3,
    label: "Không hoạt động",
    value: "Không hoạt động",
    color: "#DC2323",
    key: "3",
  },
  {id: 1, label: "Mới", value: "Mới", color: "#329932", key: "1"},
];

export const activeDateOptions: OptionSelect[] = [
  {
    id: ActiveDateEnum.SEVEN_DATE,
    label: "7 ngày",
    value: ActiveDateEnum.SEVEN_DATE,
  },
  {
    id: ActiveDateEnum.ONE_MONTH,
    label: "1 tháng",
    value: ActiveDateEnum.ONE_MONTH,
  },
  {
    id: ActiveDateEnum.THREE_MONTH,
    label: "3 tháng",
    value: ActiveDateEnum.THREE_MONTH,
  },
  {
    id: ActiveDateEnum.SIX_MONTH,
    label: "6 tháng",
    value: ActiveDateEnum.SIX_MONTH,
  },
  {
    id: ActiveDateEnum.ONE_YEAR,
    label: "1 năm",
    value: ActiveDateEnum.ONE_YEAR,
  },
];

const getStatusCollaborator = (id?: number): OptionSelect => {
  return (
    statusCollaborator.find((element) => element.id === id) || {
      id: 0,
      label: "",
      value: "",
      color: "white",
    }
  );
};

function getStatusCandidate(id: number | null | undefined): {
  id: number;
  label: string;
  color: string;
} {
  return (
    statusCandidate.find((element) => element.id === id) || {
      id: 0,
      label: "",
      color: "white",
    }
  );
}

const getColorCandidateRate = (ratio: number | null | undefined): string => {
  const SMALL = _.isNumber(ratio) && ratio <= 60;
  const MEDIUM = _.isNumber(ratio) && ratio > 60 && ratio < 80;
  const HIGHT = _.isNumber(ratio) && ratio >= 80;
  switch (true) {
    case SMALL:
      return "#DC2323";
    case MEDIUM:
      return "#F4B41A";
    case HIGHT:
      return "#329932";
    default:
      return "#FFF";
  }
};

const fileTypeAllow = ["docx", "pdf", "xls", "xlsx", "csv"];

const fileExcelAllow = ["xls", "xlsx", "csv"];

const messageErrorUploadCv = {
  errFileSize: "Dung lượng file không được vượt quá 5Mb.",
  errFileType: "Định dạng file cho phép là PDF, docx.",
};

const statusCandidateList = [
  {
    label: "Open",
    value: "1",
  },
  {
    label: "Consider",
    value: "2",
  },
  {
    label: "Closed",
    value: "3",
  },
];

const listPolicyIntroCollaborator = [
  {
    id: "1",
    content:
      "Khi bạn giới thiệu bạn bè trở thành Cộng tác viên (CTV) của RECO:",
    sortOrder: "1",
    children: [
      {
        id: "2",
        content: "Nhận được 2% lương offer của mỗi ứng viên",
        sortOrder: "1",
      },
      {
        id: "3",
        content: "Thanh toán ngay sau khi bảo hành thành công ứng viên",
        sortOrder: "2",
      },
    ],
  },
  {
    id: "5",
    content: "Bạn có thể giới thiệu nhiều CTV",
    sortOrder: "2",
  },
  {
    id: "6",
    content:
      "Bạn và CTV được giới thiệu cùng hoạt động tới khi bảo hành thành công ứng viên",
    sortOrder: "2",
  },
  {
    id: "7",
    content: "Chương trình được áp dụng từ ngày 01/01/2022",
    sortOrder: "3",
  },
];

const listJobLabels: OptionSelect[] = [
  {value: "0", label: "Không dán nhãn", key: "0"},
  {value: "2", label: "Urgent", key: "2"},
  {value: "3", label: "Siêu bonus", key: "3"},
];

const listSalaryRanges: OptionSelect[] = [
  {value: "null", label: "Theo thỏa thuận", key: "null"},
  {value: "1000", label: "<=$1000", key: "1000"},
  {value: "2000", label: "$1000-$2000", key: "2000"},
  {value: "3000", label: "$2000-$3000", key: "3000"},
  {value: "10000000000000000", label: ">=$3000", key: "10000000000000000"},
];

const listExperience: OptionSelect[] = [
  {value: "Không yêu cầu", label: "Không yêu cầu", key: "Không yêu cầu"},
  {value: "1-3 năm", label: "1-3 năm", key: "1-3 năm"},
  {value: "3-5 năm", label: "3-5 năm", key: "3-5 năm"},
  {value: "Trên 5 năm", label: "Trên 5 năm", key: "Trên 5 năm"},
];

const candidateSource: OptionSelect[] = [
  {
    label: "Reco Job Database",
    value: "Reco Job Database",
    key: "Reco_Job_Database",
  },
  {
    label: "OPMS",
    value: "OPMS",
    key: "OPMS",
  },
  {
    label: "TopCV",
    value: "TopCV",
    key: "TOPCV",
  },
  {
    label: "TopCV NTQ",
    value: "TopCV NTQ",
    key: "TOPCV_NTQ",
  },
  {
    label: "LinkedIn",
    value: "LinkedIn",
    key: "Linkedin",
  },
  {
    label: "Vietnamwork",
    value: "Vietnamwork",
    key: "VNW",
  },
  {
    label: "Github",
    value: "Github",
    key: "Github",
  },
  {
    label: "HR1",
    value: "HR1",
    key: "HR1",
  },
];

const deadTimeFastSearch = 1000;

const currencyList = [
  {
    key: "AED",
    value: "AED",
    label: "AED",
  },
  {
    key: "JPY",
    value: "JPY",
    label: "JPY",
  },
  {
    key: "USD",
    value: "USD",
    label: "USD",
  },
  {
    key: "VND",
    value: "VND",
    label: "VND",
  },
];

const paymentStatus: OptionSelect[] = [
  {
    label: "Chưa thanh toán",
    value: "Chưa thanh toán",
    key: "0",
    id: "0",
    color: "#FFA726",
  },
  {
    label: "Đang thanh toán",
    value: "Đang thanh toán",
    key: "1",
    id: "1",
    color: "#2F6BFF",
  },
  {
    label: "Hoàn thành",
    value: "Hoàn thành",
    key: "2",
    id: "2",
    color: "#329932",
  },
  {
    label: "Dừng thanh toán",
    value: "Dừng thanh toán",
    key: "3",
    id: "3",
    color: "#DC2323",
  },
];

const optionGuarantee: OptionSelect[] = [
  {
    value: "1",
    label: "1 tháng",
    key: "1",
  },
  {
    value: "2",
    label: "2 tháng",
    key: "2",
  },
  {
    value: "3",
    label: "3 tháng",
    key: "3",
  },
  {
    value: "4",
    label: "4 tháng",
    key: "4",
  },
];

const stagesApplication: OptionSelect[] = [
  {
    id: "1",
    label: "Review",
    value: "Review",
    key: "1",
    order: 1,
  },
  {
    id: "4",
    label: "CV Sent",
    value: "CVSent",
    key: "4",
    order: 2,
  },
  {
    id: "2",
    label: "Interview",
    value: "Interview",
    key: "2",
    order: 3,
  },
  {
    id: "3",
    label: "Offer",
    value: "Offer",
    key: "3",
    order: 4,
  },
];

const statusesApplication: OptionSelect[] = [
  {
    id: null,
    label: "Chưa hoàn thành",
    value: "Chưa hoàn thành",
    color: "#cecece",
    key: "null",
  },
  {
    id: "1",
    label: "Pending",
    value: "Pending",
    color: "#F4B41A",
    key: "1",
  },
  {
    id: "2",
    label: "Cancel",
    value: "Cancel",
    color: "#DC2323",
    key: "2",
  },
  {
    id: "3",
    label: "Pass",
    value: "Pass ",
    color: "#329932",
    key: "3",
  },
  {
    id: "4",
    label: "Fail",
    value: "Fail",
    color: "#DC2323",
    key: "4",
  },
  {
    id: "5",
    label: "Onboard",
    value: "Onboard",
    color: "#2F6BFF",
    key: "5",
  },
];

const statusRequest: OptionSelect[] = [
  {
    label: "Không chỉnh sửa",
    value: StatusRequestJob.NoUpdate,
    key: StatusRequestJob.NoUpdate,
  },
  {
    label: "Draft",
    value: StatusRequestJob.Draft,
    key: StatusRequestJob.Draft,
    color: "#329932",
  },
  {
    label: "Open",
    value: StatusRequestJob.Open,
    key: StatusRequestJob.Open,
    color: "#2F6BFF",
  },
  {
    label: "Processing",
    value: StatusRequestJob.Processing,
    key: StatusRequestJob.Processing,
    color: "#FF842C",
  },
  {
    label: "Pending từ AM",
    value: StatusRequestJob.PendingFromAM,
    key: StatusRequestJob.PendingFromAM,
    color: "#FFA726",
  },
  {
    label: "Pending từ KH",
    value: StatusRequestJob.PendingFromCustomer,
    key: StatusRequestJob.PendingFromCustomer,
    color: "#FFA726",
  },
  {
    label: "Review",
    value: StatusRequestJob.Review,
    key: StatusRequestJob.Review,
    color: "#DC2323",
  },
  {
    label: "Closed",
    value: StatusRequestJob.Closed,
    key: StatusRequestJob.Closed,
    color: "#DC2323",
  },
  {
    label: "Reject",
    value: StatusRequestJob.Reject,
    key: StatusRequestJob.Reject,
    color: "#9D9D9D",
  },
  {
    label: "Unclear",
    value: StatusRequestJob.Unclear,
    key: StatusRequestJob.Unclear,
    color: "#F4B41A99",
  },
];

const getStatusRequestJob = (value: string): OptionSelect => {
  const defaultRequestStatus: OptionSelect = {
    color: "white",
    label: "",
    value: "",
    key: "",
  } as OptionSelect;
  if (!value) return defaultRequestStatus;
  return (
    statusRequest.find((item) => item.key === value) || defaultRequestStatus
  );
};

const labelRequestBatch: OptionSelect[] = [
  {
    label: "Không chỉnh sửa",
    value: "null",
  },
  {
    label: "Không dán nhãn",
    value: "0",
  },
  {
    label: "Pending",
    value: "1",
  },
  {
    label: "Urgent",
    value: "2",
  },
  {
    label: "Siêu bonus",
    value: "3",
  },
];

const requestTypeBatch: OptionSelect[] = [
  {
    label: "Không chỉnh sửa",
    value: "null",
  },
  {
    label: "Internal",
    value: "0",
  },
  {
    label: "External",
    value: "1",
  },
];

const statusCustomers: OptionSelect[] = [
  {
    label: "Open",
    value: "1",
    color: "#329932",
  },
  {
    label: "New",
    value: "2",
    color: "#2F6BFF",
  },
  {
    label: "Pending",
    value: "3",
    color: "#FFA726",
  },
  {
    label: "Close",
    value: "4",
    color: "#DC2323",
  },
];

const companyTypes: OptionSelect[] = [
  {
    label: "Product",
    value: "Product",
  },
  {
    label: "Outsource",
    value: "Outsource",
  },
];

const rankOptions: OptionSelect[] = [
  {
    label: "A",
    value: "1",
  },
  {
    label: "B",
    value: "2",
  },
  {
    label: "C",
    value: "3",
  },
];

const labelOption: OptionSelect[] = [
  {value: "0", label: "Không dán nhãn", key: "0"},
  // {
  //   value: "1",
  //   label: "Pending",
  //   key: "1",
  // },
  {value: "2", label: "Urgent", key: "2"},
  {value: "3", label: "Siêu bonus", key: "3"},
];

const priorities: OptionSelect[] = [
  {
    key: "1",
    value: "1",
    label: "High",
  },
  {
    value: "2",
    label: "Medium",
    key: "2",
  },
  {
    value: "3",
    label: "Low",
    key: "3",
  },
];

const requestTypes: OptionSelect[] = [
  {
    label: "Internal",
    value: "0",
    key: "0",
  },
  {
    label: "External",
    value: "1",
    key: "1",
  },
];

const durationExpertTime: OptionSelect[] = [
  {
    label: "Tháng",
    value: "MONTH",
    key: "MONTH",
  },
  {
    label: "Năm",
    value: "YEAR",
    key: "YEAR",
  },
];

const fileTypeJdAllow = ["docx", "pdf", "doc"];

const statusEmployee: OptionSelect[] = [
  {value: "1", label: "Mới", color: "#2F6BFF"},
  {value: "2", label: "Hoạt động", color: "#329932"},
  {value: "3", label: "Không hoạt động", color: "#F4B41A"},
  {value: "4", label: "Khóa", color: "#DC2323"},
];

const statusRateCurrency: OptionSelect[] = [
  {value: "0", label: "Tỉ lệ", key: "0"},
  {value: "1", label: "Số tiền", key: "1"},
];

const statusRateCurrencyPercent: OptionSelect[] = [
  {value: "0", label: "Tỉ lệ (%)", key: "0"},
  {value: "1", label: "Số tiền", key: "1"},
];

const salaryType: OptionSelect[] = [
  {value: "0", label: "Net", key: "0"},
  {value: "1", label: "Gross", key: "1"},
];

const keyHotJobs: string[] = [
  "BrSE",
  "Sale",
  "Java",
  "C#",
  "Javascript",
  "NodeJS",
  "ReactJS",
  "PM",
  "Tester",
  "Python",
  "iOS",
  "Android",
];

const recruitmentTypes: OptionSelect[] = [
  {
    label: "IT",
    value: "IT",
    key: "1",
  },
  {
    label: "non-IT",
    value: "non-IT",
    key: "2",
  },
  {
    label: "Chưa có kinh nghiệm tuyển dụng",
    value: "Chưa có kinh nghiệm tuyển dụng",
    key: "3",
  },
];

const careerTypes: OptionSelect[] = [
  {
    label: "Nội bộ",
    value: "Nội bộ",
    key: "1",
  },
  {
    label: "Head hunt",
    value: "Head hunt",
    key: "2",
  },
  {
    label: "Freelancer",
    value: "Freelancer",
    key: "3",
  },
];

const heightBanner = 288;

const listStatuses: OptionSelect[] = [
  {value: "1", label: "Open"},
  {value: "2", label: "Consider"},
  {value: "3", label: "Closed"},
];

const optionServiceRequest: OptionSelect[] = [
  {
    label: "Hunt",
    value: "HUNT",
    key: "HUNT",
    id: "HUNT",
  },
  {
    label: "Expert",
    value: "EXPERT",
    key: "EXPERT",
    id: "EXPERT",
  },
];

const yearOfRecruitmentExperience: Array<OptionSelect> = [
  {
    label: "Chưa có kinh nghiệm",
    value: "0",
    key: "0",
  },
  {
    label: "Dưới 1 năm",
    value: "1",
    key: "1",
  },
  {
    label: "1 - 3 năm",
    value: "2",
    key: "2",
  },
  {
    label: "3 - 5 năm",
    value: "3",
    key: "3",
  },
  {
    label: "Trên 5 năm",
    value: "4",
    key: "4",
  },
];

const recruitmentLanguages: Array<OptionSelect> = [
  {
    label: "Tiếng Anh",
    value: "EN",
    key: "EN",
  },
  {
    label: "Tiếng Nhật",
    value: "JA",
    key: "JA",
  },
  {
    label: "Tiếng Hàn",
    value: "KO",
    key: "KO",
  },
  {
    label: "Tiếng Trung",
    value: "ZH",
    key: "ZH",
  },
  {
    label: "Không",
    value: "NO",
    key: "NO",
  },
];

const timeForReco: Array<OptionSelect> = [
  {
    label: "1 giờ",
    value: "1",
    key: "1",
  },
  {
    label: "2-3 giờ",
    value: "2",
    key: "2",
  },
  {
    label: "4 giờ",
    value: "3",
    key: "3",
  },
  {
    label: "Nhiều hơn 4 giờ",
    value: "4",
    key: "4",
  },
];

const levelRecruitment: Array<OptionSelect> = [
  {
    label: "Fresher",
    value: "FRESHE",
    key: "FRESHE",
  },
  {
    label: "Junior",
    value: "JUNIOR",
    key: "JUNIOR",
  },
  {
    label: "Middle",
    value: "MIDDLE",
    key: "MIDDLE",
  },
  {
    label: "Senior",
    value: "SENIOR",
    key: "SENIOR",
  },
  {
    label: "Expert",
    value: "EXP",
    key: "EXP",
  },
  {
    label: "Leader",
    value: "LEAD",
    key: "LEAD",
  },
  {
    label: "Other",
    value: "OTHER",
    key: "OTHER",
  },
];

export {
  statusCandidate,
  getStatusCandidate,
  statusCollaborator,
  fileTypeAllow,
  messageErrorUploadCv,
  statusCandidateList,
  listPolicyIntroCollaborator,
  getStatusCollaborator,
  listJobLabels,
  listSalaryRanges,
  listExperience,
  candidateSource,
  deadTimeFastSearch,
  currencyList,
  paymentStatus,
  optionGuarantee,
  stagesApplication,
  statusesApplication,
  statusRequest,
  labelRequestBatch,
  requestTypeBatch,
  statusCustomers,
  companyTypes,
  labelOption,
  priorities,
  requestTypes,
  getStatusRequestJob,
  fileTypeJdAllow,
  statusEmployee,
  statusRateCurrency,
  listLanguage,
  salaryType,
  keyHotJobs,
  recruitmentTypes,
  careerTypes,
  fileExcelAllow,
  heightBanner,
  getColorCandidateRate,
  statusRateCurrencyPercent,
  listStatuses,
  optionServiceRequest,
  yearOfRecruitmentExperience,
  levelRecruitment,
  timeForReco,
  recruitmentLanguages,
  rankOptions,
  durationExpertTime,
};
