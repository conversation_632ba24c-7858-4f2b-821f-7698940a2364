.filter-container {
  color: $text-color-input;
  display: flex;
  justify-content: flex-end;
  height: 52px;

  .filter-content {
    font-size: 12px;
    font-weight: 400;
    border: 1px dashed black;
    padding: 2px 8px;
    border-radius: 8px;
    display: flex;
    align-items: baseline;
    align-self: center;
  }

  button {
    font-size: 14px;
    font-weight: 400;
    border: 1px dashed $header_tf;
    border-radius: 8px;
    display: flex;
    align-items: center;
    align-self: center;
    color: $text-color-input;
    height: auto;
  }
  button:hover,
  button:focus {
    background: none;
    color: $text-color-input;
    border: 1px dashed $header_tf;
  }
}

.filter-content {
  background-color: $white-color;
  width: 312px;

  .title-filter {
    font-size: 1.5rem;
    font-weight: 400;
    color: $text-color-input;
  }

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .btn-close-popover {
    button {
      border: none;
    }
  }
}

.ant-popover-inner {
  border-radius: 16px;
}

.group-check-box-list-col {
  width: 380px;
}
