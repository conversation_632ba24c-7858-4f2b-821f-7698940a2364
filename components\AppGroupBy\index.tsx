import {Select, SelectProps} from "antd";
import "./index.scss";
import classNames from "classnames";
import {memo} from "react";
import {OptionSelect} from "@app/types";

interface Props extends SelectProps {
  options: OptionSelect[];
}

function AppGroupBy(props: Props) {
  const {className, options} = props;

  return (
    <div
      className={classNames(
        "app-group-by flex items-center justify-end",
        className
      )}
    >
      <p className="mr-1">Nhóm theo: </p>
      <Select
        {...props}
        // className="w-full"
        allowClear
        options={options}
        showArrow
        maxTagCount="responsive"
        dropdownStyle={{
          borderRadius: "8px",
          borderWidth: "1px",
          borderColor: "rgba(157, 157, 157,0.5)",
          padding: "8px",
        }}
        style={{width: "150px"}}
        // tagRender={tagRender}
      />
    </div>
  );
}

export default memo(AppGroupBy);
