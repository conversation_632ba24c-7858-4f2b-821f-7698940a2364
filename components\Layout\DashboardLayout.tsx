import React, {useEffect} from "react";
import Sidebar from "./Sidebar/Sidebar";
import Navbar from "./Navbar/Navbar";
import Footer from "./Footer/Footer";
import Content from "./Content/Content";
import BottomNavigator from "./BottomNavigator/BottomNavigator";
import Main from "./Main/Main";
import Config from "../../config";
import {useSelector, useDispatch} from "react-redux";
import {IRootState} from "@app/redux/store";
import AppLoadingSystem from "../AppLoadingSystem";
import {
  initialStateTable,
  setStateTable,
} from "@app/redux/slices/EditTableSlice";
import {
  getMessaging,
  getToken,
  isSupported,
  onMessage,
} from "firebase/messaging";
import {app, firebaseConfig} from "@app/utils/firebase";
import {localStorageKey} from "@app/utils/constants/constants";
import {saveTokenNotification} from "@app/api/ApiFirebaseNotification";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({
  children,
}: DashboardLayoutProps): JSX.Element {
  const {useSidebar, useNavbar, useFooter, useBottomNavigator} =
    Config.LAYOUT_CONFIG;
  const systemState = useSelector((state: IRootState) => state.system);
  const {user} = useSelector((state: IRootState) => state.user);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setStateTable(initialStateTable));
  }, []);

  useEffect(() => {
    const firebaseConfigParams = new URLSearchParams(
      firebaseConfig as any
    ).toString();

    const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPIKEY;

    if ("serviceWorker" in navigator) {
      navigator.serviceWorker
        .register(`/firebase-messaging-sw.js?${firebaseConfigParams}`)
        .then((registration) => {
          isSupported()
            .then((isSupport) => {
              if (isSupport) {
                const messaging = getMessaging(app);
                Notification.requestPermission().then((permission) => {
                  if (permission === "granted") {
                    getToken(messaging, {
                      vapidKey: vapidKey,
                      serviceWorkerRegistration: registration,
                    })
                      .then((token) => {
                        const currentToken = localStorage.getItem(
                          localStorageKey.deviceToken
                        );
                        if (token && token !== currentToken) {
                          saveTokenNotification({
                            deviceToken: token,
                            userId: Number(user?.userId),
                          });

                          localStorage.setItem(
                            localStorageKey.deviceToken,
                            token
                          );
                        }
                      })
                      .catch((e) => {
                        console.log(e);
                      });
                  }
                });
              }
            })
            .catch((e) => {
              console.log(e, "No support");
            });
        })
        .catch((e) => {
          console.log(e, "Failed to register service worker");
        });
    }
  }, []);

  useEffect(() => {
    isSupported()
      .then((isSupport) => {
        if (isSupport) {
          const messagingFCM = getMessaging(app);
          onMessage(messagingFCM, (payload) => {
            if (Notification.permission === "granted") {
              const notification = new Notification(
                payload.data?.title as string,
                {
                  body: payload.data?.body,
                  icon: "/img/logo-icon.svg",
                }
              );

              notification.onclick = (e) => {
                e.preventDefault();
                window.open(payload.data?.link, "_blank");
              };

              notification.close();
            }
          });
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);

  return (
    <div className="wrapper">
      {useSidebar && <Sidebar />}
      <Main>
        {useNavbar && <Navbar />}
        <AppLoadingSystem isLoading={systemState.isLoading} />
        <Content>
          {children}
          {useBottomNavigator && <BottomNavigator />}
          {useFooter && <Footer />}
        </Content>
      </Main>
    </div>
  );
}
