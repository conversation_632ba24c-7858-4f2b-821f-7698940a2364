.modal-cancel-application {
  .ant-modal-title {
    display: flex;
    justify-content: center;
    font-size: 24px;
    color: $text-color-input;
    font-weight: 700;
  }

  .ant-modal-header {
    padding: 24px;
    border-bottom: none;
  }

  .ant-modal-body {
    padding: 0px 24px 24px 24px;
  }

  .input-note {
    margin: 8px 0px 4px 0px;
    height: 110px;
    border-radius: 8px;
    border: 1px dashed $header_tf05;
    padding: 8px 16px;
    font-size: 16px;
    color: $text-color-input;
  }

  .item-reason {
    button {
      border: none;
      box-shadow: none;
      color: $text-color-input;
      background-color: $header_tf;
      border-radius: 8px;
      height: 20px;
      padding: 4px 20px;
      overflow: hidden;
      display: flex;
      margin-top: 4px;
      margin-right: 10px;

      span {
        height: 12px;
        line-height: 12px;
        font-size: 12px;
      }
    }

    button:active,
    button:hover,
    button:focus {
      color: $text-color-input;
      border: none;
      box-shadow: none;
    }
  }

  .btn-cancel-application {
    margin-left: 8px;
    width: 140px;
  }

  .btn-close {
    margin-right: 8px;
    width: 140px;
  }
}
