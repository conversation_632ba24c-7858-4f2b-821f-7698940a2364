import React from "react";
import IcomoonReact from "icomoon-react";
import iconSet from "./selection.json";

interface IIcon {
  className?: string;
  color?: string;
  icon: string;
  size: string | number;
  stroke?: string;
  onClick?: () => void;
}

function Icon({
  color = "",
  size = "100%",
  icon,
  className = "",
  stroke,
  onClick,
}: IIcon): JSX.Element {
  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    <span onClick={onClick} className={className}>
      <IcomoonReact
        iconSet={iconSet}
        color={color}
        size={size}
        icon={icon}
        style={{stroke}}
      />
    </span>
  );
}

export default Icon;
