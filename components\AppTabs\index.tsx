import {Tabs} from "antd";
import TabPane from "antd/lib/tabs/TabPane";
import React from "react";
import "./index.scss";

interface AppTabsProps {
  listTabs: {
    key: string;
    title: string;
    component: JSX.Element;
  }[];
  defaultActiveKey: string;
  classNameContainer?: string;
  onTabClick?: (key: string) => void;
}

export default function AppTabs(props: AppTabsProps): JSX.Element {
  const {listTabs, defaultActiveKey, classNameContainer, onTabClick} = props;
  return (
    <div className={`custom-tab ${classNameContainer}`}>
      <Tabs defaultActiveKey={defaultActiveKey} onTabClick={onTabClick}>
        {listTabs.map(
          (item: {key: string; title: string; component: JSX.Element}) => (
            <TabPane tab={item.title} key={item.key}>
              {item.component}
            </TabPane>
          )
        )}
      </Tabs>
    </div>
  );
}
