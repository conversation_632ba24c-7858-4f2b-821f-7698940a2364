.container-job-detail {
  line-height: 1.5rem;

  @media screen and (max-width: 991px) {
    padding: 0;
  }

  &__address {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .list-candidate {
    height: max-content;
    max-height: 65vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 4px;
    margin-top: 4px;

    .ant-pagination-total-text {
      display: none;
    }

    .ant-pagination-item,
    .ant-pagination-next,
    .ant-pagination-prev,
    .ant-pagination-jump-next {
      min-width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 12px;
      margin-right: 4px;
    }

    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      width: 100%;
      height: 28px;
      padding: 0 11px;
      font-size: 12px;
      line-height: 16px;
    }

    .ant-select-arrow {
      font-size: 8px;
      top: 52%;
    }
  }

  ol {
    list-style-type: decimal;
    list-style: decimal;
  }

  ul,
  menu {
    list-style-type: disc;
    list-style: disc;
  }

  ol,
  ul,
  menu {
    margin-left: 1rem;
  }
}

.button-add {
  border-start-start-radius: 12px;
  border-start-end-radius: 12px;
  color: $white-color;
  background: $green_color;
}

.div-time {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}

.custom-ant-tab-active {
  .ant-tabs {
    .ant-tabs-nav {
      .ant-tabs-tab-active {
        background: $green_color;
        color: $white-color;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        .ant-tabs-tab-btn {
          color: $white-color;
          font-size: 1rem;
          line-height: 20.24px;
        }
      }

      .ant-tabs-tab {
        border-start-start-radius: 12px;
        border-start-end-radius: 12px;
        .ant-tabs-tab-btn {
          font-size: 1rem;
          line-height: 20.24px;
        }
      }
    }
  }

  .ant-tabs-tab-btn {
    color: black;
  }

  .job-detail-upload-file {
    svg {
      width: 24px;
      height: 24px;
    }

    .app-upload-title {
      font-size: 16px;
    }

    .app-upload-desc {
      font-size: 12px;
    }
  }

  .conversation__chatting {
    border-radius: 15px !important;
  }
}

.ant-modal-content {
  overflow: unset !important;
}

.ant-modal-body {
  overflow: unset !important;
}

.modal-detail-pick-candidate {
  left: -12%;
}
