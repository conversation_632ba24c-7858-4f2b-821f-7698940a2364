import React, {useEffect, useMemo, useState} from "react";
import "./index.scss";
import classNames from "classnames";
import Icon from "../Icon/Icon";
import {DatePicker, DatePickerProps} from "formik-antd";
import moment from "moment";

type AppDatePickerProps = DatePickerProps & {
  classNameContainer?: string;
  required?: boolean;
  label: string;
  valueAppDatePicker?: moment.Moment | string | null;
  free?: boolean;
};

export default function AppDatePicker(props: AppDatePickerProps): JSX.Element {
  const {
    classNameContainer,
    required,
    valueAppDatePicker,
    label,
    placeholder,
    free,
  } = props;
  const [valueDatePicker, setValueDatePicker] = useState(valueAppDatePicker);
  const [focus, setFocus] = useState(false);
  const isOccupied = focus ? true : free ? false : !!valueDatePicker;
  const labelClass = isOccupied ? "label as-label" : "label as-placeholder";

  useEffect(() => {
    setValueDatePicker(valueAppDatePicker);
  }, [valueAppDatePicker]);

  const requiredMark = required ? (
    <span className="text-required">*</span>
  ) : null;

  const onChange = (e: any) => {
    setValueDatePicker(moment(e));
  };

  const IconDown = useMemo(
    () => (
      <Icon size={16} icon="calendar-2-line" color="#324054" className="" />
    ),
    []
  );

  return (
    <div
      className={classNames("text-input-container", classNameContainer)}
      onBlur={() => setFocus(false)}
      onFocus={() => setFocus(true)}
    >
      <DatePicker
        onChange={onChange}
        {...props}
        placeholder={focus ? placeholder : ""}
        suffixIcon={IconDown}
      />
      <span className={labelClass}>
        {label} {requiredMark}
      </span>
    </div>
  );
}
