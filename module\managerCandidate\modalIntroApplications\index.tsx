import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import AppModal from "@app/components/AppModal";
import FilterJob, {initialValuesFilterJob} from "@app/module/job/filterJob";
// eslint-disable-next-line import/namespace
import {getListJob} from "@app/module/job";
import {List, Row, notification} from "antd";
import {CardViewJob} from "@app/components/CardViewJob";
import AppPagination from "@app/components/AppPagination";
import {IJobObject} from "@app/api/ApiJob";
import {useMutation} from "react-query";
import ApiApplication, {
  IInfoPersonIntroduced,
  IParamsCreateApplication,
} from "@app/api/ApiApplication";
import {
  messageConfirmCommission,
  messageIntroApplication,
} from "@app/utils/constants/message";
import AppButton from "@app/components/AppButton";
import {useDispatch, useSelector} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import AppModalConfirm from "@app/components/AppModalConfirm";
import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole} from "@app/types";

interface ModalIntroApplicationsProps {
  isOpen: boolean;
  candidateId: number;
  handleCancel: () => void;
}

export default function ModalIntroApplications(
  props: ModalIntroApplicationsProps
): JSX.Element {
  const {isOpen, candidateId, handleCancel} = props;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [valuesFilter, setValuesFilter] = useState(
    initialValuesFilterJob as any
  );
  const [idJob, setIdJob] = useState<null | number>(null);
  const refFilter = useRef<any>(null);
  const jobList = getListJob(currentPage, pageSize, valuesFilter as any, false);
  const dispatch = useDispatch();
  const [isOpenModalCommission, setIsOpenModalCommission] = useState(false);
  const {user} = useSelector(selectUser);
  const isCTV = user?.role?.includes(IAccountRole.CTV);
  const [infoPersonIntroduced, setInfoPersonIntroduced] = useState<
    IInfoPersonIntroduced[]
  >([]);

  const createApplication = useMutation(
    (param: IParamsCreateApplication) => {
      return ApiApplication.createApplications(param);
    },
    {
      onSuccess: (data) => {
        if (data.applications.length === 0) {
          setInfoPersonIntroduced(data?.duplicateDetails);
          return;
        }
        notification.success({
          message: messageIntroApplication.success,
        });
        onCloseModal();
      },
    }
  );

  const onCloseModal = (): void => {
    refFilter.current?.resetForm();
    setPageSize(20);
    setIdJob(null);
    handleCancel?.();
  };

  const handleIntro = (commissionFlag: boolean): void => {
    setIsOpenModalCommission(false);
    if (idJob) {
      createApplication.mutate({
        requestJobId: idJob,
        candidateIds: [{candidateId: candidateId, commissionFlag}],
      });
    }
  };

  useEffect(() => {
    dispatch(setLoading(createApplication.isLoading));
  }, [createApplication.isLoading]);

  const handlePagination = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const renderContent = () => {
    return (
      <div className="container-modal-intro-application">
        <div className="p-4">
          <FilterJob
            ref={refFilter}
            setCurrentPage={setCurrentPage}
            updateValueFilter={setValuesFilter}
            isSaveValueSearch={false}
          />
          <div className="list-job-container" style={{height: "480px"}}>
            <List
              loading={jobList.isFetching}
              grid={{
                gutter: 16,
                xs: 1,
                sm: 1,
                md: 1,
                lg: 3,
                xl: 3,
                xxl: 3,
              }}
              dataSource={jobList.data?.jobs ?? []}
              renderItem={(item: IJobObject) => (
                <List.Item>
                  <CardViewJob
                    requestJob={item}
                    isHasCheckBox
                    onCheckBox={setIdJob}
                    checked={idJob === item.requestJobId}
                  />
                </List.Item>
              )}
            />
            {!jobList.isFetching && (
              <AppPagination
                className="mt-4"
                defaultCurrent={1}
                defaultPageSize={20}
                current={currentPage}
                pageSize={pageSize}
                total={jobList.data?.totalJob}
                onChange={handlePagination}
              />
            )}
          </div>
        </div>
        <Row className="flex justify-center items-center p-4 footer-modal-intro-application ">
          <AppButton
            classrow="mr-2 w-64  btn-cancel"
            label="Hủy bỏ"
            typebutton="primary"
            onClick={onCloseModal}
          />
          <AppButton
            classrow="ml-2 w-64 btn-intro-application	"
            label="Giới thiệu ngay"
            typebutton="primary"
            onClick={(): void => {
              if (isCTV) {
                setIsOpenModalCommission(true);
              } else {
                handleIntro(false);
              }
            }}
            disabled={createApplication.isLoading || !idJob}
          />
        </Row>
      </div>
    );
  };

  const onCloseModalPersonIntroduced = (): void => {
    setInfoPersonIntroduced([]);
  };

  const renderContentPersonIntroduced = () => {
    return (
      <div>
        <div className="mb-2 text-base">
          <strong>Tên người giới thiệu: </strong>
          <span>{infoPersonIntroduced[0]?.creatorName}</span>
        </div>
        <div className="mb-2 text-base">
          <strong>Email người giới thiệu: </strong>
          <span>{infoPersonIntroduced[0]?.creatorEmail}</span>
        </div>
        <div className="mb-2 text-base">
          <strong>Thời gian giới thiệu: </strong>
          <span>{infoPersonIntroduced[0]?.createdDate}</span>
        </div>
        <div className="flex justify-center mt-6">
          <AppButton
            classrow="w-32 btn-cancel"
            label="Ok"
            typebutton="primary"
            onClick={onCloseModalPersonIntroduced}
          />
        </div>
      </div>
    );
  };

  return (
    <div>
      <AppModal
        className="modal-edit-candidate"
        open={isOpen}
        onCancel={onCloseModal}
        footer={null}
        title="Chọn công việc để giới thiệu ứng viên"
        width="85%"
      >
        {renderContent()}
      </AppModal>
      <AppModalConfirm
        open={isOpenModalCommission}
        title="Xác nhận ủy quyền"
        content={messageConfirmCommission}
        onCancel={(): void => handleIntro(false)}
        onOk={(): void => handleIntro(true)}
      />
      <AppModal
        open={!!infoPersonIntroduced?.length}
        onCancel={onCloseModalPersonIntroduced}
        centered
        footer={null}
        onOk={onCloseModalPersonIntroduced}
        title="Ứng viên đã được giới thiệu"
      >
        {renderContentPersonIntroduced()}
      </AppModal>
    </div>
  );
}
