import ApiCustomer, {IDataPostMeeting} from "@app/api/ApiCustomer";
import AppButton from "@app/components/AppButton";
import AppCkEditor from "@app/components/AppCkEditor";
import AppModal from "@app/components/AppModal";
import {notification, Skeleton} from "antd";
import {useEffect, useState} from "react";
import {useMutation, useQuery} from "react-query";
import {EnumRoleMeeting} from "../MeetingMinute";

interface IModalDetailMeeting {
  open: boolean;
  onCloseModal: () => void;
  id?: string;
  refetchDataListMeeting: () => void;
  customerId: string;
  role: EnumRoleMeeting;
  customerName?: string;
}

const initialMeetingMinuteDetail = {
  infor:
    "<ol><li>Giới thiệu công ty<ol><li>Thông tin chung\u2028</li><li>Lịch sử phát triển\u2028</li><li><PERSON><PERSON>n phẩm dịch vụ\u2028</li><li>Văn phòng\u2028</li><li>Ưu điểm\u2028</li><li>Kế hoạch tuyển dụng\u2028</li></ol></li><li>Thông tin job<ol><li>Thời gian và chế độ</li><li>Chân dung ứng viên</li><li>Quy trình phỏng vấn\u2028</li><li>Chế độ\u2028</li><li>Lưu ý</li></ol></li></ol>",
  customerID: "",
};

function ModalDetailMeeting({
  open,
  onCloseModal,
  id,
  refetchDataListMeeting,
  customerId,
  role,
  customerName,
}: IModalDetailMeeting) {
  const [meetingMinuteDetail, setMeetingMinuteDetail] =
    useState<IDataPostMeeting>(structuredClone(initialMeetingMinuteDetail));

  const getDetailMeetingMinute = useQuery(["getDetailMeetingMinute", id], {
    queryFn: () => ApiCustomer.getDetailMeetingMinute(id as string),
    enabled: !!id,
  });

  const saveOrCreateMeetingMinute = useMutation(
    (meetingMinuteDetail: IDataPostMeeting) => {
      return ApiCustomer.saveOrCreateMeetingMinute(meetingMinuteDetail);
    },
    {
      onSuccess: () => {
        refetchDataListMeeting();
        onCloseModal();
        setMeetingMinuteDetail(structuredClone(initialMeetingMinuteDetail));
        notification.success({
          message: id
            ? "Lưu meeting minutes thành công"
            : "Tạo meeting minutes thành công",
        });
      },
    }
  );

  const handleChangeDescription = (value: string) => {
    setMeetingMinuteDetail((prev) => ({
      ...prev,
      infor: value,
    }));
  };

  const handleSaveMeetingMinute = () => {
    saveOrCreateMeetingMinute.mutate({
      ...meetingMinuteDetail,
      customerID: customerId as string,
      id,
    });
  };

  useEffect(() => {
    if (id && getDetailMeetingMinute?.data) {
      setMeetingMinuteDetail((prev) => ({
        ...prev,
        name: getDetailMeetingMinute?.data?.name,
        infor: getDetailMeetingMinute?.data?.infor || "",
      }));
    } else {
      setMeetingMinuteDetail(structuredClone(initialMeetingMinuteDetail));
    }
  }, [getDetailMeetingMinute.data, id]);

  return (
    <AppModal
      className="modal-detail-application-japan"
      open={open}
      footer={null}
      onCancel={onCloseModal}
      width={768}
      title={id ? meetingMinuteDetail?.name : customerName}
    >
      <div className="min-h-[60vh] overflow-auto -mt-2">
        <div className="min-h-[500px]">
          <AppCkEditor
            containerclassname="mt-2"
            value={meetingMinuteDetail.infor}
            handleChange={handleChangeDescription}
            isReadOnly={role === EnumRoleMeeting.DETAIL}
            height={role === EnumRoleMeeting.DETAIL ? "600px" : "500px"}
          />
        </div>
        {role !== EnumRoleMeeting.DETAIL && (
          <div className="flex justify-center mt-6">
            <AppButton
              typebutton="primary"
              classrow="rounded-full w-[290px]"
              onClick={handleSaveMeetingMinute}
              loading={saveOrCreateMeetingMinute.isLoading}
            >
              {id ? "Lưu thông tin" : "Tạo thông tin"}
            </AppButton>
          </div>
        )}
      </div>
    </AppModal>
  );
}

export default ModalDetailMeeting;
