/* eslint-disable jsx-a11y/no-static-element-interactions */
import {useEffect, useState} from "react";
import {Input} from "antd";
import SearchResult, {CandidateInfo} from "./SearchResult";
import {useMutation} from "react-query";
import ApiLinkedinSearch from "@app/api/ApiLinkedinSearch";
import {logEventFirebase, useDebounce} from "@app/utils/constants/function";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import config from "@app/config";
import "./index.scss";

const {TextArea} = Input;

export default function LinkedinSearch(): JSX.Element {
  const [searchParam, setSearchParam] = useState<string>();
  const [searchResult, setSearchResult] = useState<CandidateInfo[]>([]);

  const debounceSearchParam = useDebounce(searchParam, deadTimeFastSearch);

  useEffect(() => {
    if (searchParam) {
      logEventFirebase(config.EVENT_ANALYTICS_KEY.SEARCH_LINKEDIN);
      searchLinkedin.mutate(searchParam.toUpperCase());
    }
  }, [debounceSearchParam]);

  const onChangeTextSearch = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ): void => {
    setSearchParam(e.target.value.trim());
  };

  const searchLinkedin = useMutation(
    (param: string) => {
      return ApiLinkedinSearch.searchLinkedin(param);
    },
    {
      onSuccess: (data) => {
        setSearchResult(data);
      },
      onError: () => {
        setSearchResult([]);
      },
    }
  );

  return (
    <div className="container-linkedin-search flex gap-4">
      <div className="container-linkedin-search__search w-1/4 rounded-2xl overflow-hidden">
        <div className="p-3 font-normal text-base text-[#324054]">Tìm kiếm</div>
        <div className="p-3 pt-1">
          <TextArea
            name="textSearch"
            maxLength={255}
            className="input-note"
            style={{height: 110, resize: "none"}}
            placeholder="Nhập từ khóa hoặc boolean search"
            onChange={onChangeTextSearch}
          />
        </div>
      </div>
      <div className="container-linkedin-search__result w-3/4 rounded-2xl">
        <SearchResult
          candidates={searchResult}
          isLoading={searchLinkedin.isLoading}
        />
      </div>
    </div>
  );
}
