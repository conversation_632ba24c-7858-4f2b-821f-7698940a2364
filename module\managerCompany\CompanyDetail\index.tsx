/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import {Spin, Input, notification, Tooltip} from "antd";
import Icon from "@app/components/Icon/Icon";
// eslint-disable-next-line import/namespace
import {useMutation, useQuery} from "react-query";
import {useRouter} from "next/router";
import AppAccessDenied from "@app/components/AppAccessDenied";
import {ColumnsType} from "antd/lib/table";
import AppTable from "@app/components/AppTable";
import ApiCompany, {
  IJobInformation,
  ValuesSearchJobCompany,
} from "@app/api/ApiCompany";
import {FilterJobCompany} from "../FilterJobCompany";
import {OptionSelect} from "@app/types";
import AppPagination from "@app/components/AppPagination";

interface Props {
  techStacks: OptionSelect[];
  companyId: number;
  setShowCompanyDetail?: (isShow: boolean) => void;
  reloadData?: () => void;
}

const {TextArea} = Input;

export function CompanyDetail(props: Props): JSX.Element {
  const {companyId, setShowCompanyDetail, reloadData, techStacks} = props;
  const router = useRouter();
  let id: string | undefined;
  const timeOut = useRef<any>();

  const [searchParams, setSearchParams] = useState<ValuesSearchJobCompany>({
    companyId: companyId,
    pageSize: 20,
    currentPage: 1,
  } as ValuesSearchJobCompany);
  const [noteData, setNoteData] = useState<string>("");
  const [totalCount, setTotalCount] = useState<number>(0);
  const [dataSource, setDataSource] = useState<IJobInformation[]>([]);

  if (companyId) {
    id = String(companyId);
  } else {
    if (router.query.id) {
      id = String(router.query.id);
    } else {
      const searchParams = new URLSearchParams(window.location.search);
      id = String(Object.fromEntries(searchParams)?.id);
    }
  }

  const requestJobCompanyList = useMutation(
    (data: ValuesSearchJobCompany) => {
      return ApiCompany.getListJobCompany(data);
    },
    {
      onSuccess: (data) => {
        setDataSource(data?.jobsPaging);
        setTotalCount(data?.totalCount || 0);
      },
      onError: () => {
        setDataSource([]);
        setTotalCount(0);
      },
    }
  );

  const onSearchParams = () => {
    const dataClone: ValuesSearchJobCompany = searchParams;
    Object.entries(dataClone)?.forEach(([key, value]) => {
      if ((typeof value === "string" || typeof value === "object") && !value) {
        delete dataClone[key as keyof ValuesSearchJobCompany];
      }

      if (Array.isArray(value) && value?.length === 0) {
        delete dataClone[key as keyof ValuesSearchJobCompany];
      }
    });
    requestJobCompanyList.mutate(dataClone);
  };

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  useEffect(() => {
    setSearchParams((pre) => ({
      ...pre,
      companyId: companyId,
    }));
  }, [companyId]);

  useEffect(() => {
    onSearchParams();
  }, [searchParams]);

  const dataCompanyDetail = useQuery(
    ["companyDetail", id],
    // eslint-disable-next-line consistent-return
    () => {
      return ApiCompany.getDetailCompany(Number(id));
    },
    {
      enabled: !!companyId,
      onError: (error: any): void => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            if (!companyId) {
              router.back();
            } else {
              window.history.pushState({}, "", router.route);
              setShowCompanyDetail?.(false);
            }
          }, 4000);
        }
      },
    }
  );
  const dataCompany = dataCompanyDetail.data;

  const handleEditNoteCompany = useMutation(
    (note: string) => {
      return ApiCompany.editNoteCompany(companyId, note);
    },
    {
      onSuccess: () => {
        notification.success({message: "Thêm ghi chú thành công"});
        reloadData?.();
        dataCompanyDetail.refetch();
        setNoteData("");
      },
    }
  );

  const saveNote = (): void => {
    if (!noteData) return;
    handleEditNoteCompany.mutate(noteData);
  };

  if (dataCompanyDetail?.error?.errorCode === 400) {
    return (
      <div className="p-12">
        <AppAccessDenied />
      </div>
    );
  }

  const columnsJob: ColumnsType<IJobInformation> = [
    {
      title: "Tiêu đề",
      dataIndex: "jobName",
      key: "jobName",
      align: "left",
      render: (_, {jobName}: IJobInformation): JSX.Element => {
        return (
          <div className="line-clamp-2 w-full">
            <Tooltip
              title={jobName}
              placement="bottomLeft"
              overlayStyle={{maxWidth: "300px"}}
            >
              {jobName}
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: "Mức Lương",
      dataIndex: "jobSalary",
      key: "jobSalary",
    },
    {
      title: "Kinh nghiệm",
      dataIndex: "yearOfExp",
      key: "yearOfExp",
    },
    {
      title: "Vị trí",
      dataIndex: "jobLocation",
      key: "jobLocation",
      render: (_, {jobLocation}: IJobInformation): JSX.Element => {
        return (
          <div className="line-clamp-2 w-full">
            <Tooltip
              title={jobLocation}
              placement="bottomLeft"
              overlayStyle={{maxWidth: "300px"}}
            >
              {jobLocation}
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: "Kỹ năng",
      dataIndex: "skillsRequired",
      key: "skillsRequired",
      width: "25%",
      align: "left",
      render: (_: string, record: IJobInformation): JSX.Element => {
        const content =
          record?.skillsRequired?.length > 0
            ? record?.skillsRequired?.join(", ")
            : "";
        return (
          <div className="line-clamp-2 w-full">
            {content ? (
              <Tooltip
                title={content}
                placement="bottomLeft"
                overlayStyle={{maxWidth: "400px"}}
              >
                {content}
              </Tooltip>
            ) : (
              ""
            )}
          </div>
        );
      },
    },
  ];

  const handleResetCurrentPage = (): void => {
    setSearchParams((prev) => ({
      ...prev,
      currentPage: 1,
    }));
  };

  const handleSearchParams = (data: ValuesSearchJobCompany): void => {
    setSearchParams(data);
  };

  const handlePagination = (page: number, pageSize: number): void => {
    setSearchParams((pre) => ({
      ...pre,
      currentPage: page,
      pageSize: pageSize,
    }));
  };

  return (
    <div className="company-information-detail">
      {dataCompanyDetail.isLoading ? (
        <Spin size="large" />
      ) : (
        <div className="w-full h-[75vh] pr-1">
          <div className="company-information-detail__info">
            <div className="company-information-detail__info-left">
              <div className="flex flex-wrap text-base gap-y-1">
                <div className="mt-1 flex w-[60%]">
                  <div className="flex items-start">
                    <Icon size={20} icon="company-building" />
                  </div>
                  <div className="ml-4 w-full">
                    {dataCompany?.companyName || "N/A"}
                  </div>
                </div>
                <div className="mt-1 flex w-[40%]">
                  <div className="flex items-start">
                    <Icon size={20} icon="group-line" />
                  </div>
                  <div className="ml-4 w-full">
                    {dataCompany?.companyScale || "N/A"}
                  </div>
                </div>
                <div className="mt-1 flex w-[60%]">
                  <div className="flex items-start">
                    <Icon size={20} icon="map-pin-line-pin-line" />
                  </div>
                  {dataCompany?.companyLocation.length ? (
                    <div className="ml-4 w-full">
                      {dataCompany?.companyLocation.join(", ")}
                    </div>
                  ) : (
                    <div className="ml-4 w-full">N/A</div>
                  )}
                </div>
              </div>
              <div className="skills-tag mt-4">
                {dataCompany?.techStacks?.map((tech, index) => (
                  <div className="skills-tag-item" key={index}>
                    {tech}
                  </div>
                ))}
              </div>
            </div>
            <div className="company-information-detail__info-right">
              <div className="note-content min-h-[100px] max-h-[150px] overflow-auto">
                {dataCompany?.notes.length
                  ? dataCompany?.notes.map((note, index) => (
                      <div key={index}>
                        <span className="font-bold">{note.noteCreator}:</span>
                        <span className="ml-1 whitespace-pre">
                          {note.noteContent}
                        </span>
                      </div>
                    ))
                  : null}
              </div>
              <div className="note-input mt-2 relative">
                <TextArea
                  value={noteData}
                  onChange={(e): void => {
                    setNoteData(e?.target?.value);
                  }}
                  placeholder="Thông tin về công ty"
                  size="small"
                  className="rounded-xl py-1 pr-9 pl-2"
                  style={{height: 50, resize: "none"}}
                />
                <div
                  className="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer flex items-center p-2"
                  onClick={saveNote}
                >
                  <Icon size={20} icon="send-line" />
                </div>
              </div>
            </div>
          </div>
          <div className="company-information-detail__search mt-8">
            <FilterJobCompany
              companyId={companyId}
              techStacks={techStacks}
              onResetCurrentPage={handleResetCurrentPage}
              onSearchParams={handleSearchParams}
            />
            <div className="job-table mt-4">
              <AppTable
                dataSource={dataSource?.map(
                  (item: IJobInformation, index: number) => ({
                    ...item,
                    key: index,
                  })
                )}
                className="cursor-pointer"
                columns={columnsJob}
                scroll={{y: "320px"}}
                onRow={(record: IJobInformation): any => {
                  return {
                    onClick: (): void => {
                      window.open(record.jobURL, "_blank");
                    },
                  };
                }}
              />
            </div>
            <div className="mt-2">
              <AppPagination
                defaultPageSize={searchParams?.pageSize}
                current={searchParams?.currentPage}
                pageSize={searchParams?.pageSize}
                total={totalCount}
                onChange={handlePagination}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
