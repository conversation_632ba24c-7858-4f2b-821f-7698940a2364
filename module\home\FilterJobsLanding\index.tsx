import React, {useEffect, useMemo, useRef, useState} from "react";
import "./index.scss";
import {Col, Popover, Row} from "antd";
import {Formik, FormikProps} from "formik";
import {TextInput} from "@app/components/TextInput";
import {
  keyHotJobs,
  listJobLabels,
  listSalaryRanges,
  optionServiceRequest,
} from "@app/utils/constants/state";
import {SelectInput} from "@app/components/SelectInput";
import {
  BREAK_POINT,
  IAccountRole,
  OptionSelect,
  SearchParamsJobFilter,
} from "@app/types";
import {useQuery} from "react-query";
import ApiJob from "@app/api/ApiJob";
import {MyFormValues, initialValuesFilterJob} from "@app/module/job/filterJob";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {sortWorkLocation} from "@app/utils/constants/function";
import {useMediaQuery} from "react-responsive";
import Desktop from "@app/components/Layout/Desktop";
import {useDispatch, useSelector} from "react-redux";
import {useRouter} from "next/router";
import {stringToArr} from "@app/module/job";
import {IRootState} from "@app/redux/store";
import AppCheckBox from "@app/components/AppCheckbox";
import ApiUser from "@app/api/ApiUser";
import {selectUser} from "@app/redux/slices/UserSlice";
import config from "@app/config";
import {
  setDataJob,
  setSearchParamJobFilter,
} from "@app/redux/slices/DataHomePageSlice";
import {setLoading} from "@app/redux/slices/SystemSlice";
import Mobile from "@app/components/Layout/Mobile";

const listSortOption = ["Ngày đăng mới nhất", "Ngày đăng cũ nhất"];

function ContentPopover(): JSX.Element {
  const dispatch = useDispatch();
  const homePageData = useSelector((state: IRootState) => state.homePageData);
  return (
    <ul>
      {listSortOption.map((item, index) => (
        // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
        <li
          className="home__sort-job cursor-pointer"
          key={index}
          onClick={(): void => {
            dispatch(
              setSearchParamJobFilter({
                ...homePageData.searchParamsJobFilter,
                sortOption: index,
              } as any)
            );
          }}
        >
          {item}
        </li>
      ))}
    </ul>
  );
}

function FilterJobsLanding(): JSX.Element {
  // const {initValueUrlParams, updateValueFilter} = props;
  const formikRef = useRef<FormikProps<MyFormValues>>(null);
  const homePageData = useSelector((state: IRootState) => state.homePageData);
  const dispatch = useDispatch();
  const router = useRouter();
  const {user} = useSelector(selectUser);
  const isHomePage = router.pathname === config.PATHNAME.HOME;
  const [hasSearch, setHasSearch] = useState(isHomePage);

  const urlSearchParams = new URLSearchParams(window.location.search);
  const searchParams = Object.fromEntries(urlSearchParams);
  const initValueUrlParams: SearchParamsJobFilter = {
    textSearch: searchParams?.textSearch || "",
    workLocations: stringToArr(searchParams?.workLocations),
    isBookmark: String(searchParams?.isBookmark) === "true",
    isSearchHotJob: String(searchParams?.isSearchHotJob) === "true",
    jobLabels: stringToArr(searchParams?.jobLabels),
    jobTypes: stringToArr(searchParams?.jobTypes),
    levels: stringToArr(searchParams?.levels),
    salaryRanges: stringToArr(searchParams?.salaryRanges),
    pageSize: Number(searchParams?.pageSize || 20),
    currentPage: Number(searchParams?.currentPage || 1),
    sortOption: Number(searchParams?.sortOption || 0),
    haveRecommendCandidate:
      String(searchParams?.haveRecommendCandidate) === "true",
    services: stringToArr(searchParams?.services),
  };

  useEffect(() => {
    dispatch(setSearchParamJobFilter(initValueUrlParams as any));
  }, []);

  useQuery(
    ["jobList", homePageData.searchParamsJobFilter],
    () => {
      dispatch(setLoading(true));
      return ApiJob.getListJob({
        ...homePageData.searchParamsJobFilter,
        initValueUrlParams,
      } as any);
    },
    {
      onSuccess: (data) => {
        dispatch(setDataJob(data));
        handleSetQueryUrl();
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      },
      onSettled() {
        dispatch(setLoading(false));
      },
      enabled: !!homePageData?.searchParamsJobFilter && hasSearch,
    }
  );

  const handleSetQueryUrl = (): void => {
    const searchParams = {
      ...homePageData.searchParamsJobFilter,
      salaryRanges: homePageData.searchParamsJobFilter?.salaryRanges?.map(
        (i: any) => {
          if (i === null) return "null";
          return i;
        }
      ),
    };

    const convertedParams: any = {};
    Object.entries(searchParams)?.forEach(([key, value]) => {
      if (Array.isArray(value) && value?.length > 0) {
        convertedParams[key] = value?.join(",") || "";
      }
      if (typeof value === "string" && value) {
        convertedParams[key] = value;
      }
      if (typeof value === "number") {
        convertedParams[key] = value;
      }
      if (typeof value === "boolean") {
        convertedParams[key] = value;
      }
    });
    const redirect = `${config.PATHNAME.HOME}?${Object.keys(convertedParams)
      .map(
        (item) =>
          encodeURIComponent(item) +
          "=" +
          encodeURIComponent((convertedParams as any)[item])
      )
      ?.join("&")}`;
    router.push(redirect);
  };

  const isMobile = useMediaQuery({maxWidth: BREAK_POINT.lg - 1});

  const jobMarketFilters = useQuery(["jobMarketFilters"], () => {
    return ApiJob.getJobMarketFilters();
  });

  const findInputValues = (
    values: string[],
    dataSearch: OptionSelect[]
  ): OptionSelect[] => {
    const defaultData: OptionSelect[] = [];
    if (values?.length === 0 || dataSearch?.length === 0) return defaultData;
    return dataSearch?.filter((item) => values?.includes(item?.key || ""));
  };

  const dataForm = useMemo(() => {
    const workLocations: OptionSelect[] =
      jobMarketFilters?.data?.workLocations?.map((item) => ({
        label: item.name,
        value: item?.name,
        key: item?.workLocationId,
      })) || [];
    const levels: OptionSelect[] =
      jobMarketFilters.data?.levels?.map((item) => ({
        label: item?.name,
        value: item?.name,
        key: item?.levelId,
      })) || [];
    const jobTypes: OptionSelect[] =
      jobMarketFilters.data?.jobTypes?.map((item) => ({
        label: item?.name,
        value: item?.name,
        key: item?.workTypeId,
      })) || [];
    return {
      workLocations,
      levels,
      jobTypes,
    };
  }, [jobMarketFilters.data]);

  const initialValues: MyFormValues = {
    textSearch: initValueUrlParams.textSearch || "",
    salaryRanges: findInputValues(
      initValueUrlParams?.salaryRanges.map((i) => i.toString()),
      listSalaryRanges
    ),
    jobTypes: findInputValues(initValueUrlParams?.jobTypes, dataForm?.jobTypes),
    levels: findInputValues(initValueUrlParams?.levels, dataForm?.levels),
    workLocations: findInputValues(
      initValueUrlParams?.workLocations,
      dataForm?.workLocations
    ),
    jobLabels: findInputValues(initValueUrlParams?.jobLabels, listJobLabels),
    isSearchHotJob: initValueUrlParams.isSearchHotJob,
    services: findInputValues(
      initValueUrlParams.services,
      optionServiceRequest
    ),
  };

  const mapDataSearch = (values: OptionSelect[]): string[] => {
    if (!values || values?.length === 0) return [];
    return values?.map((i) => i?.key || "") || [];
  };

  const handleSearch = () => {
    if (formikRef.current?.values && initValueUrlParams) {
      const values = formikRef.current?.values;
      const valueUpdate: SearchParamsJobFilter = {
        currentPage: 1,
        pageSize: Number(initValueUrlParams?.pageSize || 20),
        jobLabels: mapDataSearch(values?.jobLabels),
        jobTypes: mapDataSearch(values?.jobTypes),
        levels: mapDataSearch(values?.levels),
        salaryRanges: mapDataSearch(values?.salaryRanges || [])?.map((i) => {
          if (i === "null") {
            return null as any;
          }
          return Number(i);
        }),
        workLocations: mapDataSearch(values?.workLocations),
        textSearch: values?.textSearch?.trim() || "",
        isSearchHotJob: !!values.isSearchHotJob,
        isBookmark: false,
        haveRecommendCandidate: initValueUrlParams?.haveRecommendCandidate,
        services: mapDataSearch(values?.services),
      };

      setHasSearch(true);

      dispatch(setSearchParamJobFilter(valueUpdate));
    }
  };

  useEffect(() => {
    // eslint-disable-next-line no-unused-expressions
    formikRef?.current && formikRef.current.setValues(initialValues);
  }, [initialValues]);

  const handleSearchKeyHotJob = (value: string): void => {
    setHasSearch(true);
    dispatch(
      setSearchParamJobFilter({
        ...homePageData.searchParamsJobFilter,
        textSearch: value,
        isSearchHotJob: false,
        currentPage: 1,
      } as any)
    );
  };

  const isLogin = ApiUser.isLogin();
  const isVisibleCheckboxRecommendJob = useMemo(
    () =>
      isLogin &&
      [
        IAccountRole.CSL,
        IAccountRole.CST,
        IAccountRole.CTV,
        IAccountRole.ADMIN,
      ].some((item) => user?.role?.includes(item)),
    [isLogin, user]
  );

  return (
    <div className="filter-jobs w-full">
      <Formik
        innerRef={formikRef}
        onSubmit={handleSearch}
        initialValues={initialValuesFilterJob}
      >
        {({values, handleSubmit}): JSX.Element => (
          <form className="filter-jobs__form" onSubmit={handleSubmit}>
            <div
              className={
                !isMobile
                  ? "filter-jobs__hight-light"
                  : "filter-jobs__hight-light-mobile"
              }
            >
              <Desktop>
                <h1 className="text24 filter-jobs__title">
                  Tìm kiếm việc làm phù hợp
                </h1>
                <Row gutter={[32, 32]}>
                  <Col xs={12}>
                    <TextInput
                      name="textSearch"
                      label="Từ khóa"
                      placeholder="Nhập từ khóa"
                      value={values?.textSearch}
                      free={!values?.textSearch}
                    />
                  </Col>
                  <Col xs={6}>
                    <SelectInput
                      mode="multiple"
                      name="workLocations"
                      labelselect="Địa điểm làm việc"
                      data={sortWorkLocation(dataForm.workLocations, "key")}
                      value={values?.workLocations}
                      free={values?.workLocations?.length === 0}
                      allowClear
                    />
                  </Col>
                  <Col xs={3}>
                    <AppButton
                      typebutton="primary"
                      htmlType="submit"
                      onClick={handleSubmit}
                    >
                      Tìm kiếm
                    </AppButton>
                  </Col>
                </Row>
              </Desktop>

              <Mobile>
                <Row gutter={[8, 8]}>
                  <Col xs={16}>
                    <TextInput
                      name="textSearch"
                      label="Từ khóa"
                      placeholder="Nhập từ khóa"
                      value={values?.textSearch}
                      free={!values?.textSearch}
                    />
                  </Col>

                  <Col xs={8}>
                    <AppButton
                      typebutton="primary"
                      htmlType="submit"
                      onClick={handleSubmit}
                    >
                      Tìm kiếm
                    </AppButton>
                  </Col>
                </Row>
              </Mobile>

              <div className="flex flex-wrap list-none">
                {keyHotJobs?.map((job, index) => (
                  // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
                  <div
                    key={index}
                    className="filter-jobs__tag mt-4 cursor-pointer list-none"
                    onClick={(): void => {
                      handleSearchKeyHotJob(job);
                    }}
                    role="button"
                    tabIndex={0}
                  >
                    {job}
                  </div>
                ))}
              </div>
            </div>
            <Desktop>
              <Row>
                <Col xs={24}>
                  <Row gutter={[16, 16]} className="mt-4">
                    <Col xs={4}>
                      <div
                        className={`w-full hot-job ${
                          homePageData.data.isSearchHotJob ? "active" : ""
                        }}`}
                      >
                        <AppButton
                          typebutton="secondary"
                          className={
                            homePageData.data.isSearchHotJob ? "active" : ""
                          }
                          onClick={(): void => {
                            dispatch(
                              setSearchParamJobFilter({
                                ...homePageData.searchParamsJobFilter,
                                currentPage: 1,
                                isSearchHotJob:
                                  !homePageData.searchParamsJobFilter
                                    ?.isSearchHotJob,
                              } as any)
                            );
                          }}
                        >
                          <Icon
                            size={20}
                            icon="fire_icon"
                            className="mr-2"
                            color={
                              homePageData.data.isSearchHotJob
                                ? "#ffffff"
                                : "rgb(193, 0, 0)"
                            }
                          />
                          Hot job
                        </AppButton>
                      </div>
                    </Col>
                    <Col xs={4}>
                      <SelectInput
                        mode="multiple"
                        name="levels"
                        labelselect="Cấp bậc"
                        data={dataForm.levels}
                        value={values?.levels}
                        free={values?.levels?.length === 0}
                        allowClear
                      />
                    </Col>
                    <Col xs={4}>
                      <SelectInput
                        mode="multiple"
                        name="salaryRanges"
                        labelselect="Mức lương"
                        data={listSalaryRanges}
                        value={values?.salaryRanges}
                        free={values?.salaryRanges?.length === 0}
                        allowClear
                      />
                    </Col>
                    <Col xs={4}>
                      <SelectInput
                        mode="multiple"
                        name="jobLabels"
                        labelselect="Trạng thái"
                        data={listJobLabels}
                        value={values?.jobLabels}
                        free={values?.jobLabels?.length === 0}
                        allowClear
                      />
                    </Col>
                    <Col xs={4}>
                      <SelectInput
                        mode="multiple"
                        name="jobTypes"
                        labelselect="Job type"
                        data={dataForm.jobTypes}
                        value={values?.jobTypes}
                        free={values?.jobTypes?.length === 0}
                        allowClear
                      />
                    </Col>
                    <Col xs={4}>
                      <SelectInput
                        mode="multiple"
                        name="services"
                        labelselect="Dịch vụ"
                        data={optionServiceRequest}
                        value={values?.services || []}
                        free={values?.services?.length === 0}
                        allowClear
                      />
                    </Col>
                  </Row>
                </Col>
              </Row>
              <Row className="mt-4 items-center">
                {isVisibleCheckboxRecommendJob && (
                  <AppCheckBox
                    className="mr-8"
                    name="recommendCandidate"
                    onChange={(e): void => {
                      const value: any = {
                        ...homePageData.searchParamsJobFilter,
                        haveRecommendCandidate: e.target.checked,
                      };
                      dispatch(setSearchParamJobFilter(value));
                    }}
                    checked={
                      homePageData.searchParamsJobFilter?.haveRecommendCandidate
                    }
                  >
                    Jobs có gợi ý
                  </AppCheckBox>
                )}
                <Popover placement="bottomRight" content={ContentPopover()}>
                  Sắp xếp:{" "}
                  <span className="cursor-pointer">
                    {homePageData.searchParamsJobFilter?.sortOption === 1
                      ? listSortOption[1]
                      : "Mặc định"}
                    <Icon
                      size={10}
                      icon="arrow-drop-down-line"
                      color="#324054"
                      className="ml-2"
                    />
                  </span>
                </Popover>
              </Row>
            </Desktop>
          </form>
        )}
      </Formik>
    </div>
  );
}

export default FilterJobsLanding;
