import {OptionSelect} from "@app/types";
import {fetcher} from "./Fetcher";
import {Moment} from "moment";

export interface ValuesSearchCompany {
  skills?: string[];
  companyLocation?: string[];
  companyName?: string;
  currentPage: number;
  pageSize: number;
}

export interface DataFormSearchCompany {
  companyName: string;
  skills: OptionSelect[];
  companyLocation: OptionSelect[];
}

export interface CompanyColumns {
  companyName: string;
  companyScale: string;
  techStacks: string[];
  notes: {
    createdDate: string;
    noteContent: string;
    noteCreator: string;
  }[];
  companyId: number;
}

export interface IJobInformation {
  jobName: string;
  jobSalary: number;
  yearOfExp: string;
  jobLocation: string;
  skillsRequired: string[];
  jobId: number;
  jobURL: string;
}

export interface ValuesSearchJobCompany {
  techStacks?: string[];
  startDate?: string | Moment;
  endDate?: string | Moment;
  companyId?: number;
  currentPage: number;
  pageSize: number;
}

export interface DataFormSearchJobCompany {
  techStacks: OptionSelect[];
  startDate: string | Moment;
  endDate: string | Moment;
  rangeDate: [string | Moment | null, string | Moment | null] | null;
}

export interface ICompanyInformationDetail {
  companyName: string;
  companyLocation: string[];
  companyScale: string;
  techStacks: string[];
  notes: {
    createdDate: string;
    noteContent: string;
    noteCreator: string;
  }[];
  companyId: number;
}

const path = {
  listCompany: "/api/CrawlData/FilterCrawlCompanies",
  listJob: "/api/CrawlData/FilterCrawlJobs",
  detailCompany: "/api/CrawlData/GetCompanyDetail",
  editNote: "/api/CrawlData/AddNote",
};

function getListCompany(data: ValuesSearchCompany): Promise<any> {
  return fetcher({
    url: path.listCompany,
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/json-patch+json",
    },
  });
}

function getListJobCompany(data: ValuesSearchJobCompany): Promise<any> {
  return fetcher({
    url: path.listJob,
    method: "post",
    data: data,
    headers: {
      "Content-Type": "application/json-patch+json",
    },
  });
}

function getDetailCompany(
  companyId: number
): Promise<ICompanyInformationDetail> {
  return fetcher({
    url: path.detailCompany,
    method: "get",
    params: {companyId},
  });
}

function editNoteCompany(companyId: number, note: string): Promise<any> {
  return fetcher({
    url: path.editNote,
    method: "post",
    params: {noteContent: note, companyId: companyId},
  });
}

export default {
  getListCompany,
  getListJobCompany,
  getDetailCompany,
  editNoteCompany,
};
