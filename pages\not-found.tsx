import React from "react";
import {Result} from "antd";
import {useRouter} from "next/router";
import Config from "../config";
import AppButton from "@app/components/AppButton";

export default function Custom404(): JSX.Element {
  const router = useRouter();

  return (
    <Result
      status="404"
      title="404"
      subTitle="Xin lỗi, trang này không tồn tại"
      extra={
        <div className="flex w-full items-center justify-center">
          <AppButton
            classrow="w-64"
            typebutton="primary"
            onClick={(): void => {
              router.push(Config.PATHNAME.HOME);
            }}
          >
            Quay về trang chủ
          </AppButton>
        </div>
      }
    />
  );
}
