import {createSlice, PayloadAction} from "@reduxjs/toolkit";
// eslint-disable-next-line import/no-cycle
import {IAccountInfo, IQueryParamsPages, IUserIsLogin} from "../../types";
// eslint-disable-next-line import/no-cycle
import {IRootState} from "../store";
// eslint-disable-next-line import/no-cycle
import {CheckboxValueType} from "antd/lib/checkbox/Group";

const colInitialRequestJob: string[] = [
  "name",
  "requestType",
  "positionName",
  "customerName",
  "statusName",
  "partnerRateValue",
];

export const initListColShowApplication = [
  "candidatePhone",
  "candidateEmail",
  "statusName",
  "timeProcess",
  "rate",
  "createDate",
  "creatorName",
  "managerName",
  "summary",
  "commissionFlag",
];

export const initColumnRecommendationBonus: string[] = [
  "userFullName",
  "applicationCreator",
  "candidateName",
  "amount",
  "paymentStatus",
  "paymentDate",
];

export const initListColShowBonusApplication: string[] = [
  "positionName",
  "statusApplication",
  "statusName",
  "paymentDate",
];

export const initColumnHotBonus: string[] = [
  "positionName",
  "stageName",
  "hotBonusAmount",
  "paymentBonusStatus",
  "paymentDate",
];

export const initColShowManagerRequestRoleAm: string[] = [
  "name",
  "requestType",
  "comments",
  "customerName",
  "rank",
  "managers",
  "statusName",
  "services",
  "workLocationNameCombined",
  "label",
  "cvSent",
  "action",
];

export const initListColShowCustomer: string[] = [
  "contact",
  "phone",
  "email",
  "customerStatus",
  "address",
  "countRequestJob",
];

export const initListColShowCustomerPayment: string[] = [
  "candidateName",
  "positionName",
  "salesTransacted",
  "amountPaid",
  "statusName",
];

const initialState: IAccountInfo = {
  user: null,
  listColShowCollaborator: [
    "phoneNumber",
    "consultantName",
    "recomenndationPersonName",
    "status",
    "createdDate",
  ],
  listColShowRequest: colInitialRequestJob,
  listColShowCandidate: [
    "email",
    "phoneNumber",
    "skill",
    "statusName",
    "createdByName",
  ],
  listColShowApplication: initListColShowApplication,
  listColShowRecommendationBonus: initColumnRecommendationBonus,
  listColShowBonusApplication: initListColShowBonusApplication,
  lisColShowHotBonus: initColumnHotBonus,
  listColShowManagerRequestRoleAm: initColShowManagerRequestRoleAm,
  listColShowCustomer: initListColShowCustomer,
  listColShowCustomerPayment: initListColShowCustomerPayment,
  groupByApplication: "",
  queryParamsPages: {
    managerRequest: "",
    managerApplication: "",
  },
};

const UserSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state: IAccountInfo, action: PayloadAction<IUserIsLogin>) => {
      state.user = action.payload;
    },
    logoutUser: () => {
      return initialState;
    },
    changeListColShowCollaborator: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowCollaborator = action.payload;
    },
    changeListColShowRequest: (
      state: IAccountInfo,
      action: PayloadAction<CheckboxValueType[]>
    ) => {
      const newData = ["name", ...action.payload];
      state.listColShowRequest = newData as any;
    },
    changeListColShowCandidate: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowCandidate = action.payload;
    },
    changeListColShowApplication: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowApplication = action.payload;
    },
    changeListColShowRecommendationBonus: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowRecommendationBonus = action.payload;
    },
    changeListColShowBonusApplication: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowBonusApplication = action.payload;
    },
    changeColShowHotBonus: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.lisColShowHotBonus = action.payload;
    },
    changeColManagerRequestRoleAm: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowManagerRequestRoleAm = [
        ...initColShowManagerRequestRoleAm,
        ...action.payload,
      ];
    },
    changeColCustomer: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowCustomer = action.payload;
    },
    changeColCustomerPayment: (
      state: IAccountInfo,
      action: PayloadAction<string[]>
    ) => {
      state.listColShowCustomerPayment = action.payload;
    },
    setGroupByApplication: (
      state: IAccountInfo,
      action: PayloadAction<string>
    ) => {
      state.groupByApplication = action.payload;
    },
    setQueryParamsPages: (
      state: IAccountInfo,
      action: PayloadAction<IQueryParamsPages>
    ) => {
      state.queryParamsPages = {...state.queryParamsPages, ...action.payload};
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setUser,
  logoutUser,
  changeListColShowCollaborator,
  changeListColShowRequest,
  changeListColShowCandidate,
  changeListColShowApplication,
  changeListColShowRecommendationBonus,
  changeListColShowBonusApplication,
  changeColShowHotBonus,
  changeColManagerRequestRoleAm,
  changeColCustomer,
  changeColCustomerPayment,
  setGroupByApplication,
  setQueryParamsPages,
} = UserSlice.actions;

export const selectUser = (state: IRootState): IAccountInfo => state.user;
export default UserSlice.reducer;
