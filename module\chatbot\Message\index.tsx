import React from "react";
import "./index.scss";
import {Avatar, Image} from "antd";
import {MessageChatbot} from "@app/types";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import {UserOutlined} from "@ant-design/icons";

export default function ChatMessage(props: MessageChatbot): JSX.Element {
  const {content, isBot} = props;
  const {user} = useSelector(selectUser);

  const urlify = (text: string) => {
    const urlRegex =
      // eslint-disable-next-line no-useless-escape
      /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim;
    return text?.replace(
      urlRegex,
      (url) =>
        `<a target="_blank" style="text-decoration: underline !important" href="${url}">${url}</a>`
    );
  };

  return (
    <div className="ui-chatbot-message">
      {!isBot ? (
        <>
          <div className="ui-chatbot-message-avatar">
            <Avatar
              icon={<UserOutlined />}
              className="w-24 flex items-center justify-center"
              src={user?.pathImage || ""}
              size={40}
            />{" "}
          </div>
          <div
            className="ui-chatbot-message-text"
            dangerouslySetInnerHTML={{__html: urlify(content)}}
          />
        </>
      ) : (
        <>
          <div className="ui-chatbot-message-avatar">
            <Image
              src="/img/chatbot-icon.jpg"
              alt="logo"
              height={40}
              width={40}
              className="rounded-full"
              preview={false}
            />
          </div>

          {content ? (
            <div
              className="ui-chatbot-message-text"
              dangerouslySetInnerHTML={{__html: urlify(content)}}
            />
          ) : (
            <div className="ui-chatbot-message-loader">
              <span className="chatbot-loader" />
            </div>
          )}
        </>
      )}
    </div>
  );
}
