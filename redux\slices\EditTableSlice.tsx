import {PayloadAction, createSlice} from "@reduxjs/toolkit";

interface EditTableState {
  editingKeyEducation: string;
  editingKeyWorkHistories: string;
  isAddRowEducation: boolean;
  isAddRowWorkHistories: boolean;
}

export const initialStateTable: EditTableState = {
  editingKeyEducation: "",
  editingKeyWorkHistories: "",
  isAddRowEducation: false,
  isAddRowWorkHistories: false,
};

export const EditTableSlice = createSlice({
  name: "EditTable",
  initialState: initialStateTable,
  reducers: {
    setStateTable: (state, actions: PayloadAction<EditTableState>) => {
      return {
        ...state,
        ...actions?.payload,
      };
    },
  },
});

export const {setStateTable} = EditTableSlice.actions;

export default EditTableSlice.reducer;
