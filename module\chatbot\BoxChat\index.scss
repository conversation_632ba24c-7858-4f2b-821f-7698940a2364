.ui-chatbot-box {
  position: fixed;
  z-index: 999;
  background-color: #fff;
  overflow: hidden;
  width: 460px;
  height: 600px;
  bottom: 0;
  right: 2px;
  top: auto;
  left: auto;
  box-shadow: 0 4px 24px -4px rgba(16, 24, 40, 0.22),
    0px 2px 8px -4px rgba(16, 24, 40, 0.03);
  border-radius: 8px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: $link-color;
    height: 54px;
    max-height: 54px;
    min-height: 54px;
    padding: 0 16px;
  }

  &__header-title {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 5px;
  }

  &__header-action {
    display: flex;
    align-items: center;
    gap: 20px;

    &--delete,
    &--close {
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }

  &__content {
    height: calc(100% - 160px);
  }

  &__content-body {
    overflow-x: hidden;
    overflow-y: auto;
    height: calc(100%);
    .icon-scroll-bottom {
      margin-top: -64px;
      width: 30px;
      min-width: 30px;
      height: 30px;
      min-height: 30px;
      transition: all 0.3s ease 0s;
      cursor: pointer;
      position: sticky;
      z-index: 99;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      opacity: 1;
      display: inline-block;
      border: 1px solid #ccc;
      border-radius: 50%;
      text-align: center;
      background-color: $white-color;
      box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px,
        rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
      svg {
        height: 28px;
      }
    }
  }

  &__content-messages {
    padding: 16px;
    padding-bottom: 0;
  }

  &__actions {
    padding: 0 16px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
