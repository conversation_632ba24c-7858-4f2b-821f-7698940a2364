.request-job-detail {
  &__header {
    padding: 12px;
  }
  .status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  &__hot-bonus {
    color: $green_color;
  }

  margin-right: 4px;
  &__container-first,
  &__customer,
  &__overall,
  &__chatting,
  &__table,
  &__assign {
    border: 1px dashed $header_tf02;
    border-radius: 8px;
    padding: 12px;
  }

  &__container-first {
    margin-right: 4px;
  }

  &__container-describe,
  &__pv {
    ol {
      list-style-type: decimal;
      list-style: decimal;
    }

    ul,
    menu {
      list-style-type: disc;
      list-style: disc;
    }

    ol,
    ul,
    menu {
      margin-left: 1rem;
    }
  }

  .status-cv {
    font-size: 12px;
    color: $white-color;
    padding: 4px 8px;
    background-color: $primary-color;
    border-radius: 8px;
    font-weight: 400;
  }

  .website {
    color: $primary-color;
    text-decoration: underline;
  }

  &__chatting {
    height: 300px;
  }

  &__chatting-content {
    max-height: 180px;
    overflow-y: auto;
  }
  .address-detail {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
