# Tạo Validation

Validation được dùng để validate dữ liệu trước khi gửi lên sever

L<PERSON>u ý: <PERSON><PERSON>i sử dụng FormItem trong thư mục components để có phần dịch message lỗi

Ví dụ: Tạo 1 file check validate cho form login

```tsx
import * as Yup from "yup";
import {SchemaOf} from "yup";

export interface ILoginForm {
  email: string;
  password: string;
}  // Tạo interface cho form

export function getValidationSchema(): SchemaOf<ILoginForm> {
  return Yup.object().shape({
    email: Yup.string()
      .email("common_validation.email_is_not")  //Khai báo các string dịch ở file translation.json
      .max(255, "common_validation.email_longer")
      .required("common_validation.email_empty"),
    password: Yup.string()
      .min(6, "common_validation.pass_shorter")
      .max(50, "common_validation.pass_longer")
      .required("common_validation.pass_empty"),
  });
}
```

# <PERSON>. Cách sử dụng

```tsx
import {
  getValidationSchema,
  ILoginForm,
} from "@app/module/login/NewPassword/form-config";  // Import interface và validation

export default function Login() {
  const [loginValidation] = useValidation(LoginValidation);

  return (
    <Formik
      validationSchema={getValidationSchema()}
      // ...
    >
      <FormItem name="email" label="Tài khoản" required>  // Giúp hiển thị thông báo lỗi
        <Input name="email" placeholder="Nhập tài khoản" />
      </FormItem>
      <FormItem
        className="pt-20"
        name="password"
        label="Mật khẩu"
        required
      >
        <Input.Password name="password" placeholder="Nhập mật khẩu" />
      </FormItem>
    </Formik>
  );
}
```
