.div-time {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}

.background-abc {
  align-items: center;
  background: $primary-color;
  border-radius: 16px;
  margin-top: 10px;
  justify-content: space-between;
}

.border-dash-bonus {
  border: 1px dashed $header_tf;
  border-radius: 16px;
  padding: 20px;
  width: 100%;
  list-style-type: disc;

  .text-xs {
    line-height: 16px;
  }
}

ol,
ul,
menu {
  list-style: inside;
  margin: 0;
  padding: 0;
}

.container-manager-bonus {
  .row-link-intro {
    button {
      height: 36px;
      border-radius: 0px 8px 8px 0px;
      font-size: 12px;
      border: 1px solid $primary-color;
    }
  }

  .div-link {
    border: 1px solid $header_tf;
    height: 36px;
    align-items: center;
    padding-left: 12px;
    border-radius: 8px 0px 0px 8px;
  }

  .link-intro {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: $header_tf;
  }
}
