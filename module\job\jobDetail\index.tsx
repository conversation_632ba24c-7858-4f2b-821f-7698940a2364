import {Avatar, List, Tabs, Tooltip, notification} from "antd";
import {useEffect, useMemo, useRef, useState} from "react";
import "./index.scss";
import {CardViewJob} from "@app/components/CardViewJob";
import {UseQueryResult, useMutation, useQuery} from "react-query";
import ApiRequestJob from "@app/api/ApiRequestJob";
import {useRouter} from "next/router";
import Icon from "@app/components/Icon/Icon";
import {Formik} from "formik";
import {FormikProps} from "formik/dist/types";
import {TextInput} from "@app/components/TextInput";
import {CardViewCandidate} from "@app/components/CardViewCandidate";
import TabPane from "antd/lib/tabs/TabPane";
import ApiCandidate, {
  FileUpload,
  ICandidateObject,
  IDataCvCandidate,
  IListCandidate,
} from "@app/api/ApiCandidate";
import ApiJob, {IStatusJob} from "@app/api/ApiJob";
import {inviteBonus, salaryRange} from "@app/utils/constants/function";
import AppUploadCv from "@app/components/AppUploadCv";
import AddCandidate from "@app/module/managerCandidate/addCandidate";
import AppLoading from "@app/components/AppLoading";
import ApiApplication, {
  IInfoPersonIntroduced,
  IListApplication,
  IParamsCreateApplication,
} from "@app/api/ApiApplication";
import {messageIntroApplication} from "@app/utils/constants/message";
import AppPagination from "@app/components/AppPagination";
import AppModal from "@app/components/AppModal";
import {CandidateInformationDetail} from "@app/module/managerCandidate/candidateInformationDetail";
import {useDispatch, useSelector} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import ApiUploadCv from "@app/api/ApiUploadCv";
import {listJobLabels} from "@app/utils/constants/state";
import HtmlComponent from "@app/components/HtmlComponent";
import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole} from "@app/types";
import config from "@app/config";
import ListCandidateTypeTable from "@app/module/managerApplication/ListCandidateTypeTable";
import AppButton from "@app/components/AppButton";
import ConversationRequestJob from "@app/module/managerRequest/conversationRequestJob";
import ApiUser from "@app/api/ApiUser";
import Desktop from "@app/components/Layout/Desktop";
import Mobile from "@app/components/Layout/Mobile";
import LayoutJobPublicCustom from "@app/module/home/<USER>";
import JobDetailLayoutMobile from "../jobDetailLayoutMobile";
import MeetingMinute, {
  EnumRoleMeeting,
} from "@app/module/managerCustomer/CustomerDetail/MeetingMinute";
import {getLabelDurationTime} from "@app/module/managerRequest/requestJobDetail";
import BooleanSearch from "./BooleanSearch";

interface MyFromCandidate {
  textSearch: string;
  positionName: string;
}

const initialValues: MyFromCandidate = {
  textSearch: "",
  positionName: "",
};

function getApplicationOfJob(
  currentPage: number,
  pageSize: number,
  requestJobId: number
): UseQueryResult<IListApplication, unknown> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const dataListApplications = useQuery(
    ["dataListApplications", currentPage, pageSize],
    () => {
      return ApiApplication.getListApplications({
        pageSize: pageSize,
        currentPage: currentPage,
        isAdvanceSearch: true,
        requestJobId: requestJobId,
      });
    },
    {enabled: !!requestJobId && ApiUser.isLogin()}
  );
  return dataListApplications;
}

function ApplicationOfJob({id}: {id: number}): JSX.Element {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };
  const dataListApplications = getApplicationOfJob(currentPage, pageSize, id);

  return (
    <ListCandidateTypeTable
      dataListApplications={dataListApplications}
      handlePagination={handlePagination}
      currentPage={currentPage}
      pageSize={pageSize}
      tableType="applicationForJob"
      canEdit
    />
  );
}

export const getAddress = (
  district?: string,
  city?: string,
  addressDetail?: string
): string => {
  const parts = [addressDetail, district, city].filter(Boolean);
  return parts.length > 0 ? parts.join(", ") : "N/A";
};

export default function JobDetail(): JSX.Element {
  const formikRef = useRef<FormikProps<MyFromCandidate>>(null);
  const formikRefAllCandidate = useRef<FormikProps<MyFromCandidate>>(null);
  const timeOut = useRef<any>();
  const router = useRouter();
  const [fileCvUpload, setFileCvUpload] = useState<FileUpload>(
    {} as FileUpload
  );
  const [openAddCandidate, setOpenAddCandidate] = useState(false);
  const [textSearchJob, setTextSearchJob] = useState("");
  const [prevIsBookMark, setPrevIsBookMark] = useState<boolean>();
  const [isBookMark, setIsBookMark] = useState<boolean>();
  const timeOutBookMark = useRef<any>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [currentPageAllCandidate, setCurrentPageAllCandidate] = useState(1);
  const [pageSizeAllCandidate, setPageSizeAllCandidate] = useState(20);
  const [showCandidateDetail, setShowCandidateDetail] =
    useState<boolean>(false);
  const [idCandidate, setIdCandidate] = useState<number>(-1);
  // const [candidatesPaging, setCandidatesPaging] = useState<ICandidateObject[]>(
  //   []
  // );
  const [allCandidatePaging, setAllCandidatePaging] = useState<
    ICandidateObject[]
  >([]);
  const [tabActiveCandidate, setTabActiveCandidate] = useState<string>();
  const [tabActiveInfoJob, setTabActiveInfoJob] = useState<string>();
  const params = new URLSearchParams(window.location.search);

  const hasTabSuggestCandidates = useRef<boolean>(false);
  const isInitialRender = useRef<boolean>(true);
  const [infoPersonIntroduced, setInfoPersonIntroduced] = useState<
    IInfoPersonIntroduced[]
  >([]);
  const isLogin = ApiUser.isLogin();
  let suggestCandidateIdFromUrl: string;
  if (router.query?.suggestCandidateId) {
    suggestCandidateIdFromUrl = router.query?.suggestCandidateId as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    suggestCandidateIdFromUrl =
      Object.fromEntries(searchParams)?.suggestCandidateId;
  }
  const [suggestCandidateId, setSuggestCandidateId] = useState<string>(
    suggestCandidateIdFromUrl || ""
  );
  const [showModalPotentialCandidate, setShowModalPotentialCandidate] =
    useState<boolean>(false);

  const dispatch = useDispatch();
  const {user} = useSelector(selectUser);
  const roles = user?.role || [];
  const userId = user?.userId || "";
  let id: string;
  let tabIntroCandidate: string;
  const isCtv = roles.includes(IAccountRole.CTV);

  if (router.query.id) {
    id = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    id = Object.fromEntries(searchParams)?.id;
  }
  const roleListAccess = [
    IAccountRole.ADMIN,
    IAccountRole.CSL,
    IAccountRole.CST,
    IAccountRole.CTV,
  ];

  const isRoleAccessCandidate = roleListAccess.some((item) =>
    roles.includes(item)
  );

  const isShowConversation = useMemo(() => {
    return isLogin && !user?.roleList?.includes(IAccountRole.CTV);
  }, [user, isLogin]);

  const linkJd = `${config.NETWORK_CONFIG.API_BASE_URL}/api/jobComment/export?jobId=${id}`;

  const requestJobDetail = useQuery(
    ["requestJobDetail", id],
    () => {
      return ApiRequestJob.getDetail(id);
    },
    {
      enabled: id !== "null" && id !== undefined,
      onSuccess: (data) => {
        setTextSearchJob(data.positionName ?? "");
        setIsBookMark(data?.isBookmark ?? false);
        setPrevIsBookMark(data?.isBookmark ?? false);
      },
    }
  );

  const jobList = useQuery(
    ["jobList"],
    () => {
      return ApiJob.getListJob({
        currentPage: 1,
        isSearchHotJob: false,
        jobLabels: [],
        jobTypes: [],
        levels: [],
        pageSize: 6,
        salaryRanges: [],
        textSearch: textSearchJob,
        workLocations: [],
        isBookmark: false,
        ignoreJobId: id,
      });
    },
    {
      enabled: false,
    }
  );

  const requestCandidate = useQuery(
    ["requestCandidate", currentPage, pageSize, isRoleAccessCandidate],
    () => {
      const data: any = {} as IListCandidate;
      if (!isRoleAccessCandidate) {
        return data;
      }
      return ApiCandidate.getListCandidate({
        currentPage: currentPage,
        pageSize: pageSize,
        isAdvanceSearch: false,
        textSearch: formikRef.current?.values.textSearch ?? "",
        createdBy: Number(userId),
        requestJobId: Number(id),
      });
    },
    {
      enabled: id !== "null" && id !== undefined,
      // onSuccess: (data: IListCandidate) => {
      //   // Check lần đầu vào màn nếu list suggest candidate mà có thì hiện tab gợi ý ứng viên
      //   if (isInitialRender.current && data?.candidatesPaging?.length === 0) {
      //     isInitialRender.current = false;
      //   } else if (
      //     isInitialRender.current &&
      //     data?.candidatesPaging?.length > 0
      //   ) {
      //     isInitialRender.current = false;
      //   }
      // },
    }
  );

  const suggestCandidateData = useQuery(
    ["suggestCandidates", id],
    () => {
      return ApiRequestJob.getSuggestCandidates(id);
    },
    {
      onSuccess(data) {
        // Check lần đầu vào màn nếu list suggest candidate mà có thì hiện tab gợi ý ứng viên
        if (isInitialRender.current && data?.length === 0) {
          isInitialRender.current = false;
          hasTabSuggestCandidates.current = false;
        } else if (isInitialRender.current && data.length > 0 && !isCtv) {
          isInitialRender.current = false;
          hasTabSuggestCandidates.current = true;
        }
      },
      enabled: id !== "null" && id !== undefined && ApiUser.isLogin(),
    }
  );

  const requestAllCandidate = useQuery(
    [
      "requestAllCandidate",
      currentPageAllCandidate,
      pageSizeAllCandidate,
      isRoleAccessCandidate,
    ],
    () => {
      const data: any = {} as IListCandidate;
      if (!isRoleAccessCandidate) {
        return data;
      }
      return ApiCandidate.getListCandidate({
        currentPage: currentPageAllCandidate,
        pageSize: pageSizeAllCandidate,
        isAdvanceSearch: false,
        textSearch: formikRefAllCandidate.current?.values.textSearch ?? "",
        createdBy: Number(userId),
        requestJobId: null,
      });
    }
  );

  if (
    router?.query?.tabIntroCandidate &&
    hasTabSuggestCandidates.current &&
    router.query.tabIntroCandidate === "1"
  ) {
    tabIntroCandidate = "chooseCandidate";
  } else if (
    router?.query?.tabIntroCandidate &&
    router.query.tabIntroCandidate === "2"
  ) {
    tabIntroCandidate = "allCandidate";
  } else if (
    router?.query?.tabIntroCandidate &&
    router.query.tabIntroCandidate === "3"
  ) {
    tabIntroCandidate = "booleanSearch";
  } else {
    tabIntroCandidate = "addCandidate";
  }

  const listApplicationsOfJob = getApplicationOfJob(
    currentPage,
    pageSize,
    Number(id)
  );

  const hasApplicationForJob =
    listApplicationsOfJob?.data?.applicationsPaging.length || false;

  const amountApplicationForJob = (): any =>
    hasApplicationForJob ? (
      <span className="flex items-center w-[26px] justify-center px-2 py-[5px] text-xs font-medium rounded-full bg-red-500 ml-2">
        {listApplicationsOfJob?.data?.totalCount}
      </span>
    ) : null;

  useEffect(() => {
    if (requestAllCandidate.data?.candidatesPaging) {
      setAllCandidatePaging(requestAllCandidate.data?.candidatesPaging);
    }
  }, [requestAllCandidate?.data]);

  const createApplication = useMutation(
    (param: IParamsCreateApplication) => {
      return ApiApplication.createApplications(param);
    },
    {
      onSuccess: (data) => {
        if (data.applications.length === 0) {
          setInfoPersonIntroduced(data?.duplicateDetails);
          return;
        }
        listApplicationsOfJob.refetch();
        notification.success({
          message: messageIntroApplication.success,
        });
        if (showCandidateDetail) {
          setShowCandidateDetail(false);
        }
      },
    }
  );

  useEffect(() => {
    if (requestJobDetail.isSuccess) {
      jobList.refetch();
    }
  }, [textSearchJob, requestJobDetail.isSuccess]);

  const uploadFileCv = useMutation(
    (data: FormData) => ApiCandidate.fileParser(data),
    {
      onSuccess: () => {
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
      // eslint-disable-next-line no-unused-expressions
      timeOutBookMark.current && clearTimeout(timeOutBookMark.current);
    };
  }, []);

  const uploadFileTemp = useMutation((formData: FormData) => {
    dispatch(setLoading(true));
    return ApiUploadCv.uploadFileTemp(formData);
  });

  useEffect(() => {
    if (
      uploadFileTemp.status === "success" &&
      uploadFileCv.isLoading === false
    ) {
      setOpenAddCandidate(true);
    }
  }, [uploadFileTemp.status, uploadFileCv.isLoading]);

  // const handleSearch = (): void => {
  //   setCurrentPage(1);
  //   // eslint-disable-next-line no-unused-expressions
  //   timeOut.current && clearTimeout(timeOut.current);
  //   timeOut.current = setTimeout(() => {
  //     requestCandidate.refetch();
  //   }, 1000);
  // };

  useEffect(() => {
    if (router?.query?.tabInfoJob) {
      setTabActiveInfoJob(router?.query?.tabInfoJob as string);
    }
  }, [router?.query?.tabInfoJob]);

  const handleSearchAllCandidate = (): void => {
    setCurrentPageAllCandidate(1);
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      requestAllCandidate.refetch();
    }, 1000);
  };

  const handleUploadFile = (file: File, formData: FormData): void => {
    setFileCvUpload({
      file: file,
      fileName: file.name,
    });
    uploadFileTemp.mutate(formData);
    uploadFileCv.mutate(formData);
  };

  const handleCancelFileUpload = (): void => {
    setFileCvUpload({} as FileUpload);
  };

  const cancelAddCandidate = (): void => {
    setOpenAddCandidate(false);
    handleCancelFileUpload();
  };

  const onChangeTabJob = (key: string): void => {
    if (key === "applicationJob") return;
    if (key === "similarJob") {
      setTextSearchJob(requestJobDetail.data?.positionName ?? "");
    } else {
      setTextSearchJob("");
    }
  };

  const introApplication = (
    idCandidate?: number,
    commissionFlag?: boolean
  ): void => {
    createApplication.mutate({
      requestJobId: requestJobDetail.data?.requestJobId,
      candidateIds: [
        {candidateId: idCandidate, commissionFlag: !!commissionFlag},
      ],
    });
  };

  useEffect(() => {
    dispatch(setLoading(createApplication.isLoading));
  }, [createApplication.isLoading]);

  const handleVisibleModalCandidateDetail = (): void => {
    setShowCandidateDetail(false);
  };

  const handleHideModalPotentialCandidate = (): void => {
    setShowModalPotentialCandidate(false);
    setSuggestCandidateId("");
    router.push(router.pathname + `?id=${id}&tabIntroCandidate=1`);
  };

  const showDetailCandidate = (id: number): void => {
    setIdCandidate(id);
    setShowCandidateDetail(true);
  };

  const showDetailPotentialCandidate = (id: string): void => {
    setSuggestCandidateId(id);
  };

  useEffect(() => {
    if (suggestCandidateId && router.query.tabIntroCandidate === "1") {
      setShowModalPotentialCandidate(true);
    }
  }, [suggestCandidateId, router.query.tabIntroCandidate]);

  const overlayLoading = (): JSX.Element => {
    return (
      <div className="overlay-loading flex items-center justify-center">
        <AppLoading />
      </div>
    );
  };

  const onClickItemJob = (idJob: number): void => {
    const redirect =
      router.pathname === config.PATHNAME.JOB_PUBLIC_DETAIL
        ? config.PATHNAME.JOB_PUBLIC_DETAIL_PARAMS(idJob)
        : `${config.PATHNAME.JOB_DETAIL}?id=${idJob}`;
    router.push(redirect);
  };

  const changeBookMark = (): void => {
    setIsBookMark(!isBookMark);
    // eslint-disable-next-line no-unused-expressions
    timeOutBookMark.current && clearTimeout(timeOutBookMark.current);
    timeOutBookMark.current = setTimeout(() => {
      if (isBookMark === prevIsBookMark && requestJobDetail.data?.requestJobId)
        ApiJob.bookMark(requestJobDetail.data?.requestJobId)
          .then(() => {
            setPrevIsBookMark(!isBookMark);
          })
          .catch(() => {
            setIsBookMark(prevIsBookMark);
          });
    }, 1000);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handlePaginationAllCandidate = (
    page: number,
    pageSize: number
  ): void => {
    setCurrentPageAllCandidate(page);
    setPageSizeAllCandidate(pageSize);
  };

  useEffect(() => {
    dispatch(setLoading(requestJobDetail.isLoading));
  }, [requestJobDetail.isLoading]);

  const tagJob = (): string => {
    const tagList = [];
    if (requestJobDetail.data?.label) {
      const labelName = listJobLabels.find(
        (e) => e.value === String(requestJobDetail.data?.label)
      )?.label;
      if (labelName) {
        tagList.push(labelName);
      }
    }

    if (requestJobDetail.data?.levelIds) {
      tagList.push(requestJobDetail.data?.levelIds);
    }

    if (requestJobDetail.data?.workType) {
      tagList.push(requestJobDetail.data?.workType);
    }

    if (requestJobDetail.data?.workLocationNameCombined) {
      tagList.push(requestJobDetail.data?.workLocationNameCombined);
    }

    return tagList.join(" | ");
  };

  // useEffect(() => {
  //   if (requestCandidate.data?.candidatesPaging) {
  //     setCandidatesPaging(requestCandidate.data?.candidatesPaging);
  //   }
  // }, [requestCandidate?.data]);

  const handleRefreshCandidatePaging = (id: number): void => {
    if (tabActiveCandidate === "allCandidate") {
      setAllCandidatePaging((preValue) =>
        preValue.map((candidate: ICandidateObject) => {
          if (candidate.candidateId === id) {
            return {...candidate, isSuggestMarked: true};
          }
          return candidate;
        })
      );
    }
    // else {
    //   setCandidatesPaging((preValue) =>
    //     preValue.map((candidate: ICandidateObject) => {
    //       if (candidate.candidateId === id) {
    //         return {...candidate, isSuggestMarked: true};
    //       }
    //       return candidate;
    //     })
    //   );
    // }
  };

  const onChangeTabCandidate = (key: string): void => {
    setTabActiveCandidate(key);
    let tabIntroCandidate = router.query?.tabIntroCandidate;
    if (key === "addCandidate") {
      tabIntroCandidate = "0";
    }
    if (key === "chooseCandidate") {
      tabIntroCandidate = "1";
    }
    if (key === "allCandidate") {
      tabIntroCandidate = "2";
    }
    if (key === "booleanSearch") {
      tabIntroCandidate = "3";
    }

    params.set("tabIntroCandidate", tabIntroCandidate as string);

    router.push(
      {
        pathname: router.pathname, // Giữ nguyên pathname
        query: {...router.query, tabIntroCandidate}, // Truyền đầy đủ query
      },
      undefined,
      {shallow: true}
    );
  };

  const onChangeTabInfoJob = (key: string): any => {
    setTabActiveInfoJob(key);
    let tabInfoJob = router.query?.tabInfoJob;
    tabInfoJob = key;

    params.set("tabInfoJob", tabInfoJob as string);

    router.push(
      {
        pathname: router.pathname, // Giữ nguyên pathname
        query: {...router.query, tabInfoJob}, // Truyền đầy đủ query
      },
      undefined,
      {shallow: true}
    );
  };

  const handleRefetchData = (): void => {
    listApplicationsOfJob.refetch();
  };

  const listCandidateReferences = (
    loading: boolean,
    data: ICandidateObject[],
    isSuggested = true
  ): any => {
    return (
      <List
        className="mt-2.5"
        loading={loading}
        grid={{
          gutter: 16,
          xs: 1,
          sm: 1,
          md: 1,
          lg: 1,
          xl: 1,
          xxl: 1,
        }}
        dataSource={data}
        renderItem={(item: ICandidateObject): any => (
          <List.Item>
            <div className="cursor-pointer relative">
              {!item.isSuggestMarked && item.isSuggestMarked !== null && (
                <div className="text-xs text-[#cb2131] font-semibold absolute top-4 right-4 z-50">
                  NEW
                </div>
              )}
              <CardViewCandidate
                candidate={item}
                isSmallSize
                onClick={(): void => {
                  if (tabActiveCandidate === "allCandidate") {
                    showDetailCandidate(Number(item?.candidateId));
                  } else {
                    showDetailPotentialCandidate(item.candidateId.toString());
                  }

                  if (router.query.tabIntroCandidate === "1") {
                    router.push(
                      router.pathname +
                        `?id=${id}&tabIntroCandidate=1&suggestCandidateId=${item.candidateId}`
                    );
                  }
                }}
                loadingIntro={createApplication.isLoading}
                isSuggested={isSuggested}
              />
            </div>
          </List.Item>
        )}
      />
    );
  };

  const isJobExpired = useMemo(
    () =>
      [
        IStatusJob.CLOSED,
        IStatusJob.PENDING_FROM_AM,
        IStatusJob.PENDING_FROM_KH,
      ].includes(Number(requestJobDetail?.data?.status)),
    [requestJobDetail?.data?.status]
  );

  const tabFormIntroCandidate = (tab: string): any => {
    if (tab === "allCandidate") {
      return (
        <TabPane
          tab="Chọn từ danh sách ứng viên"
          key="allCandidate"
          disabled={isJobExpired}
        >
          <Formik
            initialValues={initialValues}
            innerRef={formikRefAllCandidate}
            onSubmit={handleSearchAllCandidate}
          >
            {({values, handleSubmit}): JSX.Element => (
              <form onSubmit={handleSubmit}>
                <TextInput
                  label="Tìm kiếm nhanh"
                  placeholder="Nhập họ tên, email hoặc số điện thoại để tìm kiếm"
                  onChange={() => handleSubmit()}
                  name="textSearch"
                />
              </form>
            )}
          </Formik>
          <div className="list-candidate">
            {listCandidateReferences(
              requestAllCandidate.isLoading,
              allCandidatePaging
            )}
            <AppPagination
              className="mb-4"
              defaultCurrent={1}
              defaultPageSize={20}
              current={currentPageAllCandidate}
              pageSize={pageSizeAllCandidate}
              total={requestAllCandidate.data?.totalCount}
              onChange={handlePaginationAllCandidate}
            />
          </div>
        </TabPane>
      );
    }

    return (
      <TabPane
        tab="Gợi ý ứng viên"
        key="chooseCandidate"
        disabled={isJobExpired}
      >
        {/* <Formik
          initialValues={initialValues}
          innerRef={formikRef}
          onSubmit={handleSearch}
        >
          {({values, handleSubmit}): JSX.Element => (
            <form onSubmit={handleSubmit}>
              <TextInput
                label="Tìm kiếm nhanh"
                placeholder="Nhập họ tên, email hoặc số điện thoại để tìm kiếm"
                onChange={() => handleSubmit()}
                name="textSearch"
              />
            </form>
          )}
        </Formik> */}
        <div className="list-candidate">
          {listCandidateReferences(
            suggestCandidateData.isLoading,
            suggestCandidateData.data?.slice(0, 20) ?? [],
            false
          )}
          {/* <AppPagination
            className="mb-4"
            defaultCurrent={1}
            defaultPageSize={20}
            current={currentPage}
            pageSize={pageSize}
            total={requestCandidate.data?.totalCount}
            onChange={handlePagination}
          /> */}
        </div>
      </TabPane>
    );
  };

  const onCloseModalPersonIntroduced = (): void => {
    setInfoPersonIntroduced([]);
  };

  const renderContentPersonIntroduced = () => {
    return (
      <div>
        <div className="mb-2 text-base">
          <strong>Tên người giới thiệu: </strong>
          <span>{infoPersonIntroduced[0]?.creatorName}</span>
        </div>
        <div className="mb-2 text-base">
          <strong>Email người giới thiệu: </strong>
          <span>{infoPersonIntroduced[0]?.creatorEmail}</span>
        </div>
        <div className="mb-2 text-base">
          <strong>Thời gian giới thiệu: </strong>
          <span>{infoPersonIntroduced[0]?.createdDate}</span>
        </div>
        <div className="flex justify-center mt-6">
          <AppButton
            classrow="w-32 btn-cancel"
            label="Ok"
            typebutton="primary"
            onClick={onCloseModalPersonIntroduced}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col p-2 container-job-detail text-color-primary text14 break-words">
      <Desktop>
        {(uploadFileCv.isLoading || requestJobDetail.isLoading) &&
          overlayLoading()}
        <div className="flex ml-4">
          <Avatar className="mt-0.5" size={64} src="/img/reco-avatar.png" />
          <div className="flex flex-col justify-between ml-9">
            <span className="font-normal text-2xl">
              {requestJobDetail.data?.name || "N/A"}
            </span>
            {/* Nhãn - Cấp bậc - Job type - Địa điểm */}
            <span className="font-normal text-xs my-1">{tagJob()}</span>
            <div className="justify-center">
              <Icon icon="redeem" size={20} color="green" />
              <span className="text-lime-700 ml-2.5 font-normal text-xs">
                {`Thưởng giới thiệu ${inviteBonus(
                  !!requestJobDetail?.data?.partnerRateType,
                  requestJobDetail?.data?.partnerRateValue || 0,
                  requestJobDetail?.data?.partnerCurrencyTypeId || ""
                )}`}
              </span>
              {Number(requestJobDetail?.data?.status) === IStatusJob.CLOSED && (
                <span className="bg-[#DC2323] inline-flex rounded-full gap-2 items-center justify-center px-4 py-1 ml-5">
                  <Icon icon="info" size={16} />
                  <span className="text-base text-white">
                    Hết hạn ứng tuyển
                  </span>
                </span>
              )}
              {[
                IStatusJob.PENDING_FROM_AM,
                IStatusJob.PENDING_FROM_KH,
              ].includes(Number(requestJobDetail?.data?.status)) && (
                <span className="bg-[#FDEF74] inline-flex rounded-full gap-2 items-center justify-center px-4 py-1 ml-5">
                  <Icon icon="info" color="#324054" size={16} />
                  <span className="text-base text-[#324054]">
                    Tạm dừng nhận CV
                  </span>
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col">
          <div className="flex justify-between">
            <div className=" w-7/12 p-2">
              <div className="p-5 border-dash">
                <div className="flex justify-between">
                  <span className="font-bold text-base">Mô tả vị trí</span>
                  <div className="flex items-center">
                    <a href={linkJd} target="_blank" rel="noreferrer">
                      <Icon
                        icon="file-download-line"
                        size={20}
                        className="mr-2"
                      />
                    </a>
                    <span onClick={changeBookMark} role="button" tabIndex={0}>
                      {isBookMark ? (
                        <Icon
                          size={20}
                          icon="ic_bookmarked"
                          color="#324054"
                          className="icon-book-mark"
                        />
                      ) : (
                        <Icon icon="bookmark" size={20} color="#324054" />
                      )}
                    </span>
                  </div>
                </div>
                <div className="flex">
                  <div className="flex flex-col w-6/12 pl-2.5 pr-2.5">
                    <div className="flex items-center">
                      <Icon icon="apartment" size={20} />
                      <span className="text-base ml-2">
                        {requestJobDetail.data?.customerName || "N/A"}
                      </span>
                    </div>
                    <div className="flex items-center mt-1">
                      <Icon icon="briefcase-2-line" size={20} />
                      <span className="text-base  ml-2">
                        {requestJobDetail.data?.experienceYearFrom
                          ? ` Từ ${requestJobDetail.data?.experienceYearFrom} năm
                      kinh nghiệm`
                          : "Không yêu cầu kinh nghiệm "}
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col w-6/12 pl-2.5 pr-2.5">
                    <div className="flex items-center">
                      <Icon icon="group-line" size={20} />
                      <span className="text-base  ml-2">
                        {requestJobDetail.data?.headcount || 1} headcount
                      </span>
                    </div>
                    <div className="flex items-center mt-1">
                      <Icon icon="circle-dolar" size={20} />
                      <span className="text-base  ml-2">
                        {salaryRange(
                          requestJobDetail?.data?.salaryFrom,
                          requestJobDetail?.data?.salaryTo,
                          requestJobDetail?.data?.currencyTypeId
                        )}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex">
                  <div className="flex w-1/2 mt-1 pl-2.5 pr-2.5">
                    <Icon icon="map-pin-line-pin-line" size={20} />
                    <Tooltip
                      title={getAddress(
                        requestJobDetail.data?.districtNameCombined,
                        requestJobDetail.data?.workLocationNameCombined,
                        requestJobDetail.data?.detailAddress
                      )}
                    >
                      <p className="text-base ml-2 container-job-detail__address pr-8">
                        {getAddress(
                          requestJobDetail.data?.districtNameCombined,
                          requestJobDetail.data?.workLocationNameCombined,
                          requestJobDetail.data?.detailAddress
                        )}
                      </p>
                    </Tooltip>
                  </div>
                  {requestJobDetail.data?.durationValue &&
                    requestJobDetail.data?.durationUnit && (
                      <div className="flex w-1/2 mt-1 pl-2.5 pr-2.5">
                        <Icon icon="time-line" size={20} />
                        <p className="text-base ml-2 container-job-detail__address pr-8">
                          {`Thời gian: ${
                            requestJobDetail.data?.durationValue
                          } ${getLabelDurationTime(
                            requestJobDetail.data?.durationUnit
                          )}`}
                        </p>
                      </div>
                    )}
                </div>
                <span className="font-bold text-base my-2">
                  Mô tả công việc
                </span>
                <HtmlComponent
                  htmlString={
                    requestJobDetail.data?.description || "Không có dữ liệu"
                  }
                />
                <span className="flex font-bold text-base my-2">
                  Yêu cầu công việc
                </span>
                <HtmlComponent
                  htmlString={
                    requestJobDetail.data?.requestDetail || "Không có dữ liệu"
                  }
                />

                <span className="flex font-bold text-base my-2">
                  Phúc lợi và chính sách
                </span>
                <HtmlComponent
                  htmlString={
                    requestJobDetail.data?.benefit || "Không có dữ liệu"
                  }
                />
              </div>
            </div>
            <div className=" w-5/12 p-2 flex flex-col">
              {isRoleAccessCandidate && (
                <div className="p-5 border-dash">
                  <span className="font-bold text-base flex">
                    Giới thiệu ứng viên
                  </span>
                  <div className="mt-2 w-full custom-ant-tab-active">
                    <Tabs
                      activeKey={tabIntroCandidate}
                      defaultActiveKey="addCandidate"
                      onChange={onChangeTabCandidate}
                      type="card"
                    >
                      <TabPane
                        tab="Thêm ứng viên"
                        key="addCandidate"
                        disabled={isJobExpired}
                      >
                        <div className="job-detail-upload-file">
                          <AppUploadCv
                            id="file"
                            onChangeInput={handleUploadFile}
                            disabled={isJobExpired}
                          />
                        </div>
                      </TabPane>
                      {hasTabSuggestCandidates.current
                        ? tabFormIntroCandidate("chooseCandidate")
                        : null}
                      {tabFormIntroCandidate("allCandidate")}
                      <TabPane tab="Boolean search" key="booleanSearch">
                        <BooleanSearch
                          booleanSearch={requestJobDetail.data?.booleanSearch}
                        />
                      </TabPane>
                    </Tabs>
                  </div>
                </div>
              )}
              <div className="mt-2 w-full custom-ant-tab-active p-5 border-dash">
                <Tabs
                  activeKey={tabActiveInfoJob}
                  defaultActiveKey="process"
                  onChange={onChangeTabInfoJob}
                  type="card"
                >
                  <TabPane
                    tab="Quy trình phỏng vấn"
                    key="process"
                    disabled={isJobExpired}
                  >
                    <HtmlComponent
                      htmlString={
                        requestJobDetail.data?.interviewProcess ||
                        "Không có dữ liệu"
                      }
                    />
                  </TabPane>
                  <TabPane
                    tab="Lưu ý CTV"
                    key="noteCTV"
                    disabled={isJobExpired}
                  >
                    <ConversationRequestJob
                      requestJobId={Number(id)}
                      className="!border-none !pt-0"
                      showTitle={false}
                      noteCTV
                    />
                  </TabPane>
                  {isShowConversation && (
                    <>
                      <TabPane
                        tab="Lịch sử trao đổi"
                        key="history"
                        disabled={isJobExpired}
                      >
                        <ConversationRequestJob
                          requestJobId={Number(id)}
                          className="!border-none !pt-0"
                          showTitle={false}
                        />
                      </TabPane>
                      <TabPane
                        tab="Meeting minute"
                        key="meeting"
                        disabled={isJobExpired}
                      >
                        <MeetingMinute
                          idCustomer={
                            requestJobDetail?.data?.customerId as string
                          }
                          customerName={requestJobDetail?.data?.customerName}
                          role={EnumRoleMeeting.DETAIL}
                        />
                      </TabPane>
                    </>
                  )}
                </Tabs>
              </div>
            </div>
          </div>
          <div className=" w-ful p-2">
            <div className="p-5 custom-ant-tab-active border-dash">
              <Tabs
                defaultActiveKey="similarJob"
                type="card"
                onChange={onChangeTabJob}
              >
                {[
                  {key: "similarJob", title: "Job tương tự"},
                  {key: "otherRecoJob", title: "Job khác của Reco"},
                  ...(hasApplicationForJob
                    ? [
                        {
                          key: "applicationJob",
                          title: `Vị trí ứng tuyển của Job ${amountApplicationForJob()}`,
                        },
                      ]
                    : []),
                ].map((item: {key: string; title: any}) => (
                  <TabPane
                    tab={
                      item.key === "applicationJob" ? (
                        <span className="flex items-center">
                          Vị trí ứng tuyển của Job {amountApplicationForJob()}
                        </span>
                      ) : (
                        item.title
                      )
                    }
                    key={item.key}
                  >
                    {item.key === "applicationJob" ? (
                      <ApplicationOfJob id={Number(id)} />
                    ) : (
                      <List
                        loading={jobList.isFetching}
                        grid={{
                          gutter: 16,
                          xs: 1,
                          sm: 1,
                          md: 1,
                          lg: 2,
                          xl: 2,
                          xxl: 3,
                        }}
                        dataSource={jobList.data?.jobs ?? []}
                        renderItem={(item) => (
                          <List.Item>
                            <CardViewJob
                              requestJob={item}
                              onClick={(): void =>
                                onClickItemJob(item.requestJobId)
                              }
                            />
                          </List.Item>
                        )}
                      />
                    )}
                  </TabPane>
                ))}
              </Tabs>
            </div>
          </div>
        </div>
      </Desktop>

      <Mobile>
        <LayoutJobPublicCustom>
          <JobDetailLayoutMobile data={requestJobDetail?.data as any} />
        </LayoutJobPublicCustom>
      </Mobile>

      <AddCandidate
        isModalVisible={openAddCandidate}
        handleCancel={cancelAddCandidate}
        cvCandidateData={
          {
            ...uploadFileCv?.data,
            fileCVPath: uploadFileTemp?.data,
          } as IDataCvCandidate
        }
        fileUpload={fileCvUpload}
        type="introCandidate"
        reloadData={handleRefetchData}
      />

      {/* Modal candidate of tab name 'allCandidate */}
      <AppModal
        className="modal-detail-candidate"
        centered
        footer={null}
        open={showCandidateDetail}
        onCancel={handleVisibleModalCandidateDetail}
        title="Chi tiết ứng viên"
        width="70%"
      >
        <CandidateInformationDetail
          idCandidate={idCandidate}
          requestJobId={
            tabActiveCandidate === "allCandidate" ? undefined : Number(id)
          }
          setShowCandidateDetail={setShowCandidateDetail}
          reloadData={requestCandidate.refetch}
          introNow={(commissionFlag: boolean): void =>
            introApplication(idCandidate, commissionFlag)
          }
          onFetchSuccessCandidateDetail={handleRefreshCandidatePaging}
        />
      </AppModal>

      {/* Modal candidate of tab name chooseCandidate */}
      <AppModal
        className="modal-detail-pick-candidate"
        centered
        footer={null}
        open={showModalPotentialCandidate}
        onCancel={handleHideModalPotentialCandidate}
        title="Chi tiết ứng viên"
        width="70%"
        maskClosable={false}
      >
        <CandidateInformationDetail
          setShowCandidateDetail={setShowModalPotentialCandidate}
          idPotentialCandidate={suggestCandidateId}
          tab="potentialCandidate"
          isShowModalEvaluation
          requestJobId={Number(id)}
          refetchListCandidate={suggestCandidateData.refetch}
        />
      </AppModal>

      <AppModal
        open={!!infoPersonIntroduced?.length}
        onCancel={onCloseModalPersonIntroduced}
        centered
        footer={null}
        onOk={onCloseModalPersonIntroduced}
        title="Ứng viên đã được giới thiệu"
      >
        {renderContentPersonIntroduced()}
      </AppModal>
    </div>
  );
}
