// eslint-disable-next-line import/namespace, import/no-cycle
import {fetcher} from "./Fetcher";
import {IMultiSelect} from "@app/types";
// eslint-disable-next-line import/no-cycle
import {IListStatistical, IParamsStatistical} from "./ApiStaff";

export interface IEducations {
  degreeType?: string;
  endDate?: string;
  fieldOfStudy?: string;
  schoolName?: string;
  startDate?: string;
  key?: string;
}

export interface IWorkHistories {
  companyName?: string;
  endDate?: string;
  position?: string;
  startDate?: string;
  key?: string;
}

export interface IListSkill {
  createdBy: number;
  createdDate: string;
  deleteFlag: boolean;
  description: string;
  name: string;
  skillId: string;
}

export interface IWorkLocationList {
  country: string;
  countryId: string;
  createdBy: number;
  createdDate: string;
  deleteFlag: boolean;
  description: string;
  name: string;
  sortOrder: number;
  workLocationId: string;
}

export interface ICandidateObject {
  candidateId: number | string;
  countryId: string;
  currentSalary: number;
  email: string;
  experienceYear: string;
  fileCVName: string;
  fileCVPath: string;
  manager: string;
  managerId: number;
  name: string;
  note: string;
  onboardDate: string;
  phoneNumber: string;
  salaryExpected: number;
  skills: string;
  status: number;
  statusName: string;
  totalApplication: number;
  workLocationIds: string;
  workLocationNames: string[];
  fileAvatarPath?: string | null;
  currencyTypeId?: string;
  languages: string[];
  positionExpected: string;
  workHistories: IWorkHistories[];
  createdDate: string;
  createdByName: string;
  isSuggestMarked?: boolean | null;
  isSuitable?: boolean | null;
  rule_based?: boolean;
}

interface IParamsCandidate {
  isAdvanceSearch?: boolean;
  managers?: IMultiSelect[];
  currentPage: number;
  pageSize: number;
  textSearch?: string;
  from?: string;
  to?: string;
  experienceYear?: string;
  hasNext?: boolean;
  hasPrevious?: boolean;
  isBackFromChildrenView?: boolean;
  skills?: IMultiSelect[];
  statuses?: IMultiSelect[];
  workLocations?: IMultiSelect[];
  totalCount?: number;
  totalPages?: number;
  partnerId?: number;
  createdBy?: number;
  requestJobId?: number | null;
}

export interface IListCandidate {
  candidatesPaging: ICandidateObject[];
  currentPage: number;
  experienceYear: string;
  from: string;
  hasNext: boolean;
  hasPrevious: boolean;
  isAdvanceSearch: boolean;
  managers: IMultiSelect[];
  pageSize: number;
  skills: IMultiSelect[];
  statuses: IMultiSelect[];
  textSearch: string;
  to: string;
  totalCount: number;
  totalPages: number;
  workLocations: IMultiSelect[];
}

export interface ICandidateInfo {
  candidateId: number;
  countryId: string;
  createdBy: number;
  createdDate: string;
  currencyTypeId: string;
  currentSalary: number;
  currentSalaryType: string;
  deleteFlag: boolean;
  educations: IEducations[];
  email: string;
  experienceYear: string;
  fileCVName: string;
  fileCVPath: string;
  githubURL: string;
  isUploadFile: boolean;
  isUploadFileAvatar: boolean;
  languages: string[];
  linkedinURL: string;
  listCandidateSkillId: string[];
  managerId: number;
  name: string;
  note: string;
  onboardDate: string;
  phoneNumber: string;
  salaryExpected: number;
  salaryExpectedType: number;
  skypeURL: string;
  source: string;
  status: number;
  tags: string[];
  workHistories: IWorkHistories[];
  workLocationIds: string[];
  birthday: string;
  managerName: string;
  positionExpected: string | null;
}

interface ApplicationHistory {
  createdDate: string;
  customerName: string;
  positionName: string;
  stage: string;
  workingLocation: string;
  status: string;
  applicationId: number;
}

export interface ISuitableJobForCandidate {
  ratePartner: string;
  requestJobId: number;
  requestJobName: string;
  salary: string;
}

export interface ICandidateDetail {
  candidateSkills: string[];
  countryId: string;
  countryName: string;
  createdDate: string;
  email: string;
  experienceYear: string;
  fileCVName: string;
  fileCVPath: string;
  githubURL: string;
  isUploadFile: boolean;
  isUploadFileAvatar: boolean;
  languages: string[];
  listCandidateSkillId: string[];
  managerId: number;
  onboardDate: string;
  phoneNumber: string;
  salaryExpectedType: number;
  source: string;
  tags: string[];
  workLocationIds: string[];
  name: string;
  status: number;
  statusName: string;
  workHistories: IWorkHistories[];
  fileAvatarPath: string | null;
  workLocationNames: string[];
  note: string | null;
  currencyTypeId: string;
  salaryExpected: number;
  skypeURL: string | null;
  currentSalary: number;
  linkedinURL: string | null;
  facebookURL: string | null;
  applicationHistory: ApplicationHistory[];
  educations: IEducations[];
  positionName: string;
  onboardDateString: string;
  workLocationName: string;
  candidateId: number;
  birthday: string;
  managerName: string;
  positionExpected: string | null;
  birthdayString: string | null;
  currentSalaryType: number;
  recommendJobs: ISuitableJobForCandidate[];
}

export enum StatusRecruitment {
  onboard = "onboard",
  cancel = "cancel",
  fail = "fail",
  pending = "pending",
  pass = "pass",
  processing = "processing",
  onboardDone = "onboard done",
}

interface IEducationCvCandidate {
  degree: string;
  endTime: string;
  majors: string;
  organization: string;
  startTime: string;
}

interface IExperienceCVCandidate {
  endTime: string;
  level: string;
  organization: string;
  startTime: string;
}

export interface IDataCvCandidate {
  address?: string;
  country?: string | null;
  currencyTypeId?: string | null;
  currentSalaryType?: number | null;
  education?: IEducationCvCandidate[];
  experience?: IExperienceCVCandidate[];
  email?: string | null;
  facebook_URL?: string | null;
  current_Salary?: string | null;
  full_name?: string;
  github_URL?: string | null;
  languages?: string[];
  linkedin_URL?: string | null;
  location?: string | null;
  note?: string | null;
  onboardDate?: string | null;
  phone_number?: string | null;
  profile_Image_Local_Path?: string | null;
  skills?: string[];
  skype_Account?: string | null;
  tag?: string[];
  yoe?: string;
  facebookURL?: string;
  birthday: string;
  positionName: string;
  website: string;
  workLocationNames: string[];
  salaryExpected: number | null;
  currentSalary: number | null;
  workHistories: IWorkHistories[];
  educations: IEducations[];
  experienceYear: string;
  fileCVName: string;
  fileCVPath: string;
  salaryExpectedType: number;
}

export interface IDataCandidateUpdate {
  candidateId?: number;
  name: string;
  phoneNumber: string;
  email: string;
  birthday: string;
  currencyTypeId: string;
  currentSalary: number;
  languages: string;
  facebookURL: string;
  onboardDate: string;
  salaryExpected: number;
  workHistories: IWorkHistories[];
  educations: IEducations[];
  workLocationName: string[];
  note: string;
}

export interface FileUpload {
  fileName: string;
  file: File;
}

export interface IDataCreateCandidate {
  candidateId?: number;
  name: string;
  phoneNumber: string;
  email: string;
  birthday: string;
  currencyTypeId: string;
  currentSalary: number;
  languages: string;
  facebookURL: string;
  onboardDate: string;
  salaryExpected: number;
  workHistories: IWorkHistories[];
  educations: IEducations[];
  workLocationName: string[];
  note: string;
  tags: string[];
}

export enum IModeViewCandidate {
  view = "view",
  create = "create",
  edit = "edit",
}

export enum TypeEditModal {
  add = "add",
  edit = "edit",
}

export interface IDataFormDataCandidateSubmit {
  name: string;
  candidateId?: number;
  email: string;
  phoneNumber?: string;
  experienceYear?: string;
  countryId?: string;
  workLocationIds?: string;
  note?: string;
  managerId?: number;
  listCandidateSkillId?: string[];
  currentSalary?: number;
  currencyTypeId?: string;
  onboardDate?: string;
  source: string;
  languages: string[];
  tags: string[];
  fileCVName: string;
  educations?: IEducations[];
  workHistories?: IWorkHistories[];
  fileAvatarPath?: string;
  githubURL?: string;
  status?: number;
  birthday?: string;
  facebookURL?: string;
  fileCV?: string;
  fileAvatar?: string;
  salaryExpected?: number;
  isUploadFile: boolean;
  fileCVPath?: string;
  positionExpected?: string;
  linkedinURL?: string;
  salaryExpectedType?: number;
  currentSalaryType?: number;
}

export interface ICandidateJapan {
  currentLocation: string;
  currentPositionId: string;
  email: string;
  experienceYear: string;
  fileCVName: string;
  fileCVPath: string;
  fullName: string;
  japaneseLevel: string;
  note: string;
  phoneNumber: string;
  requestJobId: number;
  createdDate: string;
  currentPosition: string;
  requestName: string;
  requestPosition: string;
  selfRegisterId: number;
}
export interface IResCandidateJapan {
  hasNext: boolean;
  hasPrevious: boolean;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  currentPage: number;
  dataPaging: ICandidateJapan[];
}

export interface IReqHistoryApplyReco {
  email?: string;
  phoneNumber?: string;
}

export interface IResHistoryApplyReco {
  applicationId: number;
  position: string;
  userName: string;
  email: string;
  createdAt: string;
}

const path = {
  list: "/api/candidate/filter",
  skill: "/api/Skill/getAll",
  work: "/api/getWorkLocationList",
  detail: "/api/candidate/getByIdEdit",
  detailInformation: "/api/candidate/detail",
  fileParser: "/api/candidate/fileParser",
  deleteCandidate: "/api/candidate/delete",
  update: "/api/candidate/update",
  create: "/api/candidate/create",
  editNote: "/api/candidate/editNote",
  getManagerByConsultantLeaderId: "api/user/getManagerByConsultantLeaderId",
  exportCandidate: "api/candidate/export",
  listCandidateJapan: "/api/selfRegister/filter",
  getCreatorStatistics: "/api/candidate/creatorStatistics",
  checkLimitDownloadCVCandidate: "/api/Candidate/CheckDownLoad",
  getHistoryApplyReco: "/api/candidate/checkExistedCandidate",
};

export interface IManagerByConsultantList {
  name: string;
  roleID: string;
  roleName: string;
  userGroupId: number;
  userId: number;
}

function getListCandidate(body?: IParamsCandidate): Promise<IListCandidate> {
  return fetcher({url: path.list, method: "post", data: body});
}

function getListSkill(): Promise<IListSkill[]> {
  return fetcher({url: path.skill, method: "get"});
}

function getWorkLocationList(): Promise<IWorkLocationList[]> {
  return fetcher({url: path.work, method: "get"});
}

function getHistoryApplyReco(
  body: IReqHistoryApplyReco
): Promise<IListSkill[]> {
  return fetcher({url: path.getHistoryApplyReco, method: "post", data: body});
}

function getCandidateDetail(params?: {
  id: string | string[] | undefined;
}): Promise<ICandidateInfo> {
  return fetcher({
    url: path.detail,
    method: "get",
    params: params,
  });
}

function getCandidateDetailInformation(
  id?: number,
  requestJobId?: number
): Promise<ICandidateDetail> {
  return fetcher({
    url: path.detailInformation,
    method: "get",
    params: {id: id, requestJobId: requestJobId},
  });
}

function fileParser(data: FormData): Promise<IDataCvCandidate> {
  return fetcher({
    url: path.fileParser,
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

function deleteCandidate(id?: number): Promise<any> {
  return fetcher({
    url: path.deleteCandidate,
    method: "post",
    params: {id: id},
  });
}

function updateInformationCandidate(
  params: IDataCandidateUpdate
): Promise<any> {
  return fetcher({
    url: path.update,
    method: "post",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    timeout: 5000,
  });
}

function createCandidateFromCv(params: FormData): Promise<any> {
  return fetcher({
    url: path.create,
    method: "post",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    timeout: 5000,
  });
}

function editNoteCandidate(candidateId: number, note: string): Promise<any> {
  return fetcher({
    url: path.editNote,
    method: "post",
    data: {note: note, candidateId: candidateId},
  });
}

function getAllManagerByConsultantLeaderId(): Promise<
  IManagerByConsultantList[]
> {
  return fetcher({
    url: path.getManagerByConsultantLeaderId,
    method: "post",
    data: {
      pageSize: 10000,
    },
  });
}

function exportCandidate(body: IListCandidate): Promise<any> {
  return fetcher({
    url: path.exportCandidate,
    method: "post",
    data: body,
    responseType: "blob",
    headers: {
      "Content-type": "application/json",
      "Accept":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    },
  });
}

function getListCandidateJapan(params: {
  pageSize: number;
  currentPage: number;
  textSearch: string;
}): Promise<IResCandidateJapan> {
  return fetcher({
    url: path.listCandidateJapan,
    method: "post",
    data: params,
  });
}

function getCreatorStatistics(
  body?: IParamsStatistical
): Promise<IListStatistical> {
  return fetcher({url: path.getCreatorStatistics, method: "post", data: body});
}

function checkLimitDownloadCVCandidate(
  candidateId: string
): Promise<IListStatistical> {
  return fetcher({
    url: path.checkLimitDownloadCVCandidate,
    params: {candidateId: candidateId},
  });
}

export default {
  getListCandidate,
  getListSkill,
  getWorkLocationList,
  getCandidateDetailInformation,
  getCandidateDetail,
  fileParser,
  deleteCandidate,
  updateInformationCandidate,
  createCandidateFromCv,
  editNoteCandidate,
  getAllManagerByConsultantLeaderId,
  exportCandidate,
  path,
  getListCandidateJapan,
  getCreatorStatistics,
  checkLimitDownloadCVCandidate,
  getHistoryApplyReco,
};
