import React, {useMemo, useState} from "react";
import "./index.scss";
import ApiPaymentBonus, {
  IDataHotBonus,
  IDataPagingHotBonus,
} from "@app/api/ApiPaymentBonus";
import AppTable from "@app/components/AppTable";
import {useQuery} from "react-query";
import {useSelector} from "react-redux";
import AppPagination from "@app/components/AppPagination";
import {
  findApplicationStatus,
  formatMoney,
  getPaymentStatus,
} from "@app/utils/constants/function";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {selectUser} from "@app/redux/slices/UserSlice";
import {ColumnType} from "antd/lib/table";
import FilterHotBonus from "./FilterHotBonus";
import ModalHotBonusDetail from "./ModalHotBonusDetail";
import ModalUpdatePayment from "../ModalUpdatePayment";
import {IAccountRole} from "@app/types";

function HotBonus(): JSX.Element {
  const [searchParams, setSearchParams] = useState<IDataHotBonus>({
    currentPage: 1,
    isFirstInitialization: true,
    pageSize: 20,
  });
  const [showModalDetail, setShowModalDetail] = useState<boolean>(false);
  const [selectHotBonus, setSelectHotBonus] = useState<IDataPagingHotBonus>(
    {} as IDataPagingHotBonus
  );
  const [showModalPayment, setShowModalPayment] = useState<boolean>(false);
  const {lisColShowHotBonus, user} = useSelector(selectUser);
  const isCSL =
    user?.role?.includes(IAccountRole.CSL) ||
    user?.role?.includes(IAccountRole.ADMIN);

  const handleShowModalPayment = (): void => {
    setShowModalDetail(false);
    setShowModalPayment(true);
  };

  const getHostBonus = useQuery(["getHotBonus", searchParams], () => {
    return ApiPaymentBonus.getHotBus(searchParams);
  });

  const allColumn: ColumnType<IDataPagingHotBonus>[] = [
    {
      title: "CST quản lý",
      dataIndex: "consultantName",
      key: "consultantName",
      align: "left",
    },
    {
      title: "Cộng tác viên",
      dataIndex: "creatorName",
      key: "creatorName",
      align: "left",
    },
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
      align: "left",
    },
    {
      title: "Vị trí",
      dataIndex: "positionName",
      key: "positionName",
    },
    {
      title: "Trạng thái ứng tuyển",
      dataIndex: "stageName",
      key: "stageName",
      render: (_: string, record: IDataPagingHotBonus): JSX.Element => {
        return (
          <span
            className="recruitment-status"
            style={{
              backgroundColor: findApplicationStatus(
                String(record?.status || "")
              ).color,
            }}
          >
            {`${record?.stageName ? record?.stageName : ""} ${
              record?.statusName ? record?.statusName : ""
            }`}
          </span>
        );
      },
    },
    {
      title: "Số tiền",
      dataIndex: "hotBonusAmount",
      key: "hotBonusAmount",
      render: (_: string, record: IDataPagingHotBonus): JSX.Element => {
        return <span>{formatMoney(record.hotBonusAmount)}</span>;
      },
    },
    {
      title: "Trạng thái thanh toán",
      dataIndex: "paymentBonusStatus",
      key: "paymentBonusStatus",
      render(_: string, record: IDataPagingHotBonus): JSX.Element {
        return (
          <span
            style={{
              backgroundColor: getPaymentStatus(record?.paymentBonusStatus)
                .color,
            }}
            className="status-payment"
          >
            {getPaymentStatus(record?.paymentBonusStatus).label}
          </span>
        );
      },
    },
    {
      title: "Ngày thanh toán",
      dataIndex: "paymentDate",
      key: "paymentDate",
      render: (_: string, record: IDataPagingHotBonus): JSX.Element => {
        return (
          <span>
            {record?.paymentDate
              ? moment(record?.paymentDate).format(DATE_FORMAT)
              : ""}
          </span>
        );
      },
    },
    {
      title: "Ngày tạo application",
      dataIndex: "createdDate",
      key: "createdDate",
      render: (_: string, record: IDataPagingHotBonus): JSX.Element => {
        return (
          <span>
            {record?.createdDate
              ? moment(record?.createdDate).format(DATE_FORMAT)
              : ""}
          </span>
        );
      },
    },
  ];

  const currentColumns = useMemo(() => {
    return allColumn.filter((col) =>
      [
        isCSL ? "consultantName" : "",
        "creatorName",
        "candidateName",
        ...lisColShowHotBonus,
      ]?.some((i) => i === col.key)
    );
  }, [lisColShowHotBonus]);

  const handlePagination = (page: number, pageSize: number): void => {
    setSearchParams((prev) => ({
      ...prev,
      pageSize: pageSize,
      currentPage: page,
    }));
  };
  return (
    <div className="hot-bonus">
      <div className="hot-bonus__search">
        <FilterHotBonus
          pageSize={searchParams.pageSize}
          setSearchParams={setSearchParams}
          allColumn={allColumn}
          isCSL={!!isCSL}
          currentColumns={currentColumns}
        />
      </div>
      <div className="hot-bonus__table mt-4">
        <AppTable
          dataSource={
            getHostBonus.data?.dashboardPagingDatas?.map(
              (item: IDataPagingHotBonus, index: number) => ({
                ...item,
                key: index,
              })
            ) || []
          }
          columns={currentColumns}
          style={{
            maxHeight: "60vh",
            overflowY: "auto",
          }}
          loading={getHostBonus.isLoading}
          rowClassName="cursor-pointer"
          onRow={(record: IDataPagingHotBonus): any => {
            return {
              onClick: (): void => {
                setSelectHotBonus(record);
                setShowModalDetail(true);
              },
            };
          }}
        />
      </div>
      <div className="hot-bonus__paging mt-2">
        <AppPagination
          defaultPageSize={searchParams.pageSize}
          defaultCurrent={1}
          pageSize={searchParams.pageSize}
          current={searchParams.currentPage}
          total={getHostBonus?.data?.totalCount ?? 0}
          onChange={handlePagination}
        />
      </div>
      <ModalHotBonusDetail
        open={showModalDetail}
        title="Chi tiết Hot bonus"
        dataHotBonus={selectHotBonus}
        onCancel={(): void => setShowModalDetail(false)}
        onOpenModalPayment={handleShowModalPayment}
      />
      <ModalUpdatePayment
        open={showModalPayment}
        dataPayment={{
          amount: selectHotBonus?.hotBonusUnpaid || "",
          note: selectHotBonus?.note || "",
          paymentBonusId: selectHotBonus.paymentBonusId,
          paymentDate: selectHotBonus?.paymentDate
            ? moment(selectHotBonus?.paymentDate)
            : "",
          paymentType: "",
        }}
        onCancel={(): void => setShowModalPayment(false)}
        title="Chi tiết thanh toán"
        refreshList={(): void => {
          getHostBonus.refetch();
        }}
      />
    </div>
  );
}

export default React.memo(HotBonus);
