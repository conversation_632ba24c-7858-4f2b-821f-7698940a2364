// hoàn thiện rồi mở comment

import AppLoading from "@app/components/AppLoading";
import dynamic from "next/dynamic";
import React from "react";

const HomePageComponent = dynamic(() => import("@app/module/home"), {
  loading: () => <AppLoading classNameContainer="h-screen" />,
});

export default function Home(): JSX.Element {
  return <HomePageComponent />;
}

// import config from "@app/config";
// import {useRouter} from "next/router";
// import React from "react";

// export default function Home(): JSX.Element {
//   const route = useRouter();

//   route.replace(config.PATHNAME.MANAGER_APPLICATION);

//   return <div />;
// }
