.form-team-container {
  .card-information {
    border: 1px dashed $header_tf;
    padding: 20px 30px;
    border-radius: 8px;
    margin-top: 16px;
  }

  .add-btn {
    span {
      color: $status-reject;
      margin-left: 4px;
      text-decoration: underline;
    }

    .ant-btn {
      background-color: $white-color;
      border: 0;
    }
  }

  .ant-table-body {
    height: 32vh;
  }

  .status {
    color: $white-color;
    padding: 4px 14px;
    border-radius: 8px;
  }

  .btn-delete {
    justify-content: center;

    button {
      font-size: 0.875rem;
      font-weight: 400;
      border: none;
      display: flex;
      height: auto;
    }
    button:hover,
    button:focus {
      background: none;
      border: none;
    }
  }

  .ant-table-placeholder {
    height: 32vh;
  }
}
