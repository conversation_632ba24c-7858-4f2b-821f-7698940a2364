const PASSWORD_REGEX =
  // eslint-disable-next-line no-useless-escape
  /^(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9@$!%*#?&^?~_\.-]{8,20}$/;

const REGEX_PHONE_NUMBER =
  // eslint-disable-next-line no-useless-escape
  /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/;

const REGEX_CHARACTER = /^[A-Za-z]+$/;

const REGEX_ONLY_NUMBER = /^\d*\.?\d*$/;

const CHARACTER_SPECIALS = [
  "*",
  "!",
  "~",
  "@",
  "#",
  "$",
  ":",
  ";",
  ",",
  "=",
  "^",
  "&",
  "_",
  "%",
  "'",
  "\\",
  '"',
  "|",
  "<",
  ">",
  "?",
  "/",
  "{",
  "}",
  "[",
  "]",
];

const REGEX_EMAIL = // eslint-disable-next-line no-useless-escape
  /^[A-Za-z0-9]+([\_.-]\w{1,})*@[A-Za-z0-9]+([\.-]?[A-Za-z0-9]+)*(\.[A-Za-z0-9]{2,6})$/;

const REGEX_WEBSITE =
  // eslint-disable-next-line no-useless-escape
  /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g;

const REGEX_VIETNAMESE = // eslint-disable-next-line no-useless-escape
  /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;

export {
  PASSWORD_REGEX,
  REGEX_PHONE_NUMBER,
  REGEX_CHARACTER,
  REGEX_EMAIL,
  REGEX_ONLY_NUMBER,
  CHARACTER_SPECIALS,
  REGEX_WEBSITE,
  REGEX_VIETNAMESE,
};
