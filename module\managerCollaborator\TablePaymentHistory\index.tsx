import {IPayments} from "@app/api/ApiCollaborator";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/lib/table";
import React from "react";

interface TablePaymentHistoryProps {
  payment?: IPayments[];
}

export default function TablePaymentHistory(
  props: TablePaymentHistoryProps
): JSX.Element {
  const {payment} = props;
  const columns: ColumnsType<IPayments> = [
    {
      title: "STT",
      dataIndex: "",
      key: "stt",
      render: (_, record: IPayments, index: number) => (
        <span className="text-base text12">{index + 1}</span>
      ),
      width: "70px",
    },
    {
      title: "Application",
      dataIndex: "applicationName",
      key: "applicationName",
      className: "cursor-pointer",
      render: (_, record: IPayments) => (
        <span className="text-base text12">
          {record.application.applicationName}
        </span>
      ),
      onCell: (record: IPayments): any => {
        return {
          onClick: (): void => {
            // showModalDetail(record?.applicationId);
          },
        };
      },
    },
    {
      title: "Khách hàng",
      dataIndex: "customer",
      key: "customer",
    },
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
      render: (_, record: IPayments) => (
        <span className="text-base text12">
          {record.application.candidate.name}
        </span>
      ),
    },
    {
      title: "CST Quản lý",
      dataIndex: "ManagerCST",
      key: "ManagerCST",
      render: (_, record: IPayments) => (
        <span className="text-base text12">
          {record.application.candidate.name}
        </span>
      ),
    },
    {
      title: "Loại Bonus",
      dataIndex: "typeBonus",
      key: "typeBonus",
      render: (_, record: IPayments) => (
        <span className="text-base text12">
          {record.application.candidate.name}
        </span>
      ),
    },
    {
      title: "Ngày thanh toán",
      dataIndex: "paymentDateExpectedString",
      key: "paymentDateExpectedString",
    },
  ];

  return (
    <div>
      <AppTable
        dataSource={payment?.map((item: IPayments, index: number) => ({
          ...item,
          key: index,
        }))}
        columns={columns}
        scroll={{y: "25vh"}}
      />
    </div>
  );
}
