/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, {useEffect, useMemo, useState} from "react";
import "./index.scss";
import BoxChat from "./BoxChat";
import {Image} from "antd";
import {useRouter} from "next/router";
import ApiUser from "@app/api/ApiUser";
import {IAccountRole} from "@app/types";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import config from "@app/config";

export default function Chatbot(): JSX.Element | null {
  const [isVisibleChatbot, setVisibleChatbot] = useState<boolean>(false);
  const routerNext = useRouter();
  const isLogin = ApiUser.isLogin();
  const {user} = useSelector(selectUser);
  const isVisible = useMemo(
    () =>
      !user?.role?.includes(IAccountRole.CTV) &&
      isLogin &&
      !routerNext.pathname.includes(config.PATHNAME.PREVIEW_CV),
    [isLogin, user]
  );
  const handleShowChatbot = (): void => {
    setVisibleChatbot(true);
  };

  useEffect(() => {
    setVisibleChatbot(false);
    const sidebarElement = document.querySelector(".sidebar");
    const chatbotElement = document.querySelector(".ui-chatbot");
    if (sidebarElement) {
      chatbotElement?.classList.add("side-left-chatbot");
    } else {
      chatbotElement?.classList.remove("side-left-chatbot");
    }
  }, [routerNext.pathname]);

  return isVisible ? (
    <div className="ui-chatbot">
      <span
        className="ui-chatbot-icon"
        id="chatbot-icon"
        onClick={handleShowChatbot}
      >
        <Image
          src="/img/chatbot-icon.jpg"
          alt="logo"
          height={70}
          width={70}
          className="rounded-full"
          preview={false}
        />
      </span>
      {isVisibleChatbot && (
        <BoxChat handleHide={(): void => setVisibleChatbot(false)} />
      )}
    </div>
  ) : null;
}
