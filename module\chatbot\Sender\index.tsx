/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, {useEffect, useRef} from "react";
import {useDispatch, useSelector} from "react-redux";
import {
  setNewMessagesChatbot,
  setStateSendingMessage,
} from "@app/redux/slices/ChatbotSlice";
import {IRootState} from "@app/redux/store";
import {Button} from "antd";
import ApiMessageChatbot from "@app/api/ApiMessageChatbot";
import "./index.scss";

interface Props {
  onTextMessageChange: (scrollHeight: number) => void;
}

export default function ChatSender(props: Props): JSX.Element {
  const {onTextMessageChange} = props;
  const textareaRef = useRef<HTMLTextAreaElement | any>();
  const {isSendingMessage} = useSelector((state: IRootState) => state.chatbot);
  const stripHtmlTag = (value: string) => value.replace(/(<([^>]+)>)/gi, "");
  const dispatch = useDispatch();
  const TEXTAREA_MAX_HEIGHT = 86;

  const handleResizeTextarea = (): void => {
    textareaRef.current.style.height = "auto";
    textareaRef.current.style.maxHeight = `${TEXTAREA_MAX_HEIGHT}px`;
    textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    onTextMessageChange(textareaRef.current.scrollHeight);
  };

  const clearTextarea = (): void => {
    textareaRef.current.value = null;
  };

  const onTextareaChange = (): void => {
    handleResizeTextarea();
  };

  const sendMessage = async (): Promise<any> => {
    if (!(textareaRef.current.value || "").trim()) {
      textareaRef.current.value = "";
      return;
    }
    const messageValue = stripHtmlTag(textareaRef.current.value.trim());
    textareaRef.current?.focus();

    dispatch(
      setNewMessagesChatbot({
        content: messageValue,
        isBot: false,
      })
    );
    clearTextarea();
    handleResizeTextarea();
    dispatch(setStateSendingMessage(true));
    dispatch(
      setNewMessagesChatbot({
        content: "",
        isBot: true,
      })
    );

    ApiMessageChatbot.sendMessageChatbot(messageValue)
      .then((response) => response.body?.getReader())
      .then((reader) => {
        // Read and process the JSON response
        return reader?.read().then(function processResult(result): any {
          if (result.done) {
            dispatch(setStateSendingMessage(false));
            return;
          }
          // Massage and parse the chunk of data
          const decoder = new TextDecoder("utf-8");
          const chunk = decoder.decode(result.value);
          const lines = chunk.split("\n\n");
          const parsedLines = lines
            .map((line) => line.replace(/^data: /, "").trim()) // Remove the "data: " prefix
            .filter((line) => line !== "" && line !== "[DONE]") // Remove empty lines and "[DONE]"
            .map((line) => JSON.parse(line)); // Parse the JSON string

          for (const parsedLine of parsedLines) {
            const {choices} = parsedLine;
            const {delta} = choices[0];
            const {content} = delta;
            if (content) {
              dispatch(setStateSendingMessage(false));
              dispatch(
                setNewMessagesChatbot({
                  content: content,
                  isBot: true,
                  isFetchSuccess: true,
                })
              );
            }
          }
          // eslint-disable-next-line consistent-return
          return reader?.read()?.then(processResult);
        });
      })
      .catch((error) => {
        dispatch(
          setNewMessagesChatbot({
            content: "Đã có lỗi xảy ra. Vui lòng thử lại",
            isBot: true,
            isFetchSuccess: true,
          })
        );
        dispatch(setStateSendingMessage(false));
      });
  };

  useEffect(() => {
    const handleSubmit = (event: any): void => {
      const keyCode = event.which || event.keyCode;
      if (keyCode === 13 && !event.shiftKey && !isSendingMessage) {
        event.preventDefault();
        sendMessage();
      }
    };
    window.addEventListener("keypress", handleSubmit);
    return () => window.removeEventListener("keypress", handleSubmit);
  }, [textareaRef.current, isSendingMessage]);

  return (
    <>
      <div className="ui-chatbot-sender__line" />
      <div className="ui-chatbot-sender">
        <div className="ui-chatbot-sender__input">
          <textarea
            ref={textareaRef}
            cols={20}
            rows={1}
            placeholder="Nhập nội dung chat"
            onChange={onTextareaChange}
            name="inputText"
          />
        </div>
        <Button
          className="ui-chatbot-sender__button"
          type="primary"
          danger
          disabled={isSendingMessage}
          onClick={sendMessage}
        >
          Gửi
        </Button>
      </div>
    </>
  );
}
