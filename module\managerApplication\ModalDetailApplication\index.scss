.modal-detail-application {
  top: 50px;

  .ant-modal-content {
    height: 90vh;
  }

  .ant-modal-body {
    padding: 1rem;
  }

  .container-item-detail-modal {
    border: 1px dashed $header_tf;
    border-radius: 16px;
    overflow: hidden;
    height: 100%;
  }

  .container-name-application {
    flex-direction: column;
    display: flex;
    flex: 1;
    padding-left: 24px;
    padding-right: 18px;
  }

  .card-info {
    padding: 24px;
    overflow-y: auto;
    height: 100%;
  }

  .name-application {
    font-weight: 700;
    color: $text-color-input;
  }

  .positionName-application {
    font-weight: 400;
    color: $text-color-input;
  }

  .dot-status {
    height: 12px;
    width: 12px;
    border-radius: 12px;
  }

  .text-property-candidate {
    color: $text-color-input;
    font-size: 16px;
    font-weight: 400;
    margin-left: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }

  .line {
    height: 0.5px;
    width: 70%;
    background-color: $header_tf05;
    margin: 16px 15%;
  }

  .title-job {
    color: $text-color-input;
    font-size: 16px;
    font-weight: 700;
  }

  .status-application-title {
    color: $green_color;
    font-size: 14px;
    font-weight: 400;
    align-items: center;
  }

  .dot-content {
    display: flex;
    height: 25px;
    width: 25px;
    border-radius: 25px;
    align-items: center;
    justify-content: center;
  }

  .line-step {
    height: 0.5px;
    background-color: $header_tf05;
    width: "100%";
  }

  .title-history {
    margin-right: 14px;
    font-size: 16px;
    font-weight: 400;
    color: $text-color-input;
  }

  .ant-steps {
    margin-left: -30px;
    margin-top: 20px;

    .ant-steps-item {
      width: 25%;
    }

    .ant-steps-item-tail {
      padding: 3.5px 12px;
    }
  }

  .current-step {
    font-size: 12px;
    font-weight: 400;
    color: $white-color;
  }

  .next-step {
    font-size: 12px;
    font-weight: 400;
    color: $text-color-input;
  }

  .ant-steps-item-title {
    font-size: 12px !important;
    font-weight: 400 !important;
    color: $text-color-input !important;
  }

  .history-item {
    font-size: 12px;
    font-weight: 400;
    color: $text-color-input;
  }

  .dot-history {
    height: 8px;
    width: 8px;
    border-radius: 8px;
    background-color: $header_tf;
  }

  .btn-cancel {
    button {
      background-color: $status-reject;

      &:focus {
        background-color: $status-reject;
      }
    }

    .ant-btn[disabled] {
      background-color: $status-reject;
      opacity: 0.5;
      color: $white-color;
    }
  }

  .btn-edit {
    .ant-btn[disabled] {
      background-color: $primary-color;
      opacity: 0.5;
      color: $white-color;
    }
  }

  .a-detail-job {
    border: none;
    font-size: 10px;
    font-weight: 400;
    color: $primary-color;
    text-decoration: underline;
  }

  .form-edit-candidate-info {
    .input-salary {
      input {
        border-radius: 8px 0px 0px 8px;
      }
    }
  }

  .history-description {
    color: $header_tf;
  }

  .ant-checkbox {
    .ant-checkbox-inner {
      height: 18px;
      width: 18px;
      border: 2px solid $text-color-input;
    }
  }
}
