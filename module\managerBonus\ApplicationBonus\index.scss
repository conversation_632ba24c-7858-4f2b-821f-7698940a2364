.application-bonus-container {
  .btn-filter {
    button {
      font-size: 0.875rem;
      font-weight: 400;
      border: 1px dashed $header_tf;
      border-radius: 8px;
      display: flex;
      align-items: center;
      align-self: center;
      color: $text-color-input;
      height: auto;
    }
    button:hover,
    button:focus {
      background: none;
      color: $text-color-input;
      border: 1px dashed $header_tf;
    }
  }

  .ant-table-thead .ant-table-cell {
    color: $text-color-input;
  }

  .row-table {
    td {
      color: $text-color-input;
    }
  }

  .status-payment,
  .status-application {
    background-color: $primary-color;
    color: $white-color;
    padding: 1px;
    border-radius: 8px;
  }

  .ant-table-cell {
    padding: 6px 4px !important;
  }
}

.content-filter-advance {
  background-color: $white-color;
  width: 312px;

  .title-filter {
    font-size: 1.5rem;
    font-weight: 400;
    color: $text-color-input;
  }

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .btn-close-popover {
    button {
      border: none;
    }
  }
}

.group-check-box-list-col {
  width: 420px;
}
