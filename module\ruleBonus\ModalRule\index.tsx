import ApiPaymentBonus, {
  ICreateOrUpdatePolicy,
  IPolicy,
} from "@app/api/ApiPaymentBonus";
import AppModal from "@app/components/AppModal";
import {Formik, FormikProps} from "formik";
import moment, {Moment} from "moment";
import React, {useEffect, useRef} from "react";
import "./index.scss";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {Row, notification} from "antd";
import {InputNumber} from "formik-antd";
import AppButton from "@app/components/AppButton";
import {UseQueryResult, useMutation} from "react-query";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";

interface ModalRuleProps {
  open: boolean;
  handleClose: () => void;
  policy?: IPolicy;
  lastTime: Moment;
  getAllPaymentBonusPolicy: UseQueryResult<IPolicy[], unknown>;
}

interface IInitValue extends IPolicy {
  startDateMoment: Moment;
  endDateMoment: Moment;
}

export default function ModalRule(props: ModalRuleProps): JSX.Element {
  const {open, handleClose, policy, lastTime, getAllPaymentBonusPolicy} = props;
  const dispatch = useDispatch();
  const isTypeCreate = !policy?.paymentBonusPolicyId;
  const refForm = useRef<FormikProps<IInitValue>>(null);
  const maxLength = 3;

  const createPolicy = useMutation(
    (data: ICreateOrUpdatePolicy) => {
      return ApiPaymentBonus.createPolicy(data);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Tạo chính sách mới thành công",
        });
        getAllPaymentBonusPolicy.refetch();
        refForm.current?.resetForm();
        dispatch(setLoading(false));
        handleClose();
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const updatePolicy = useMutation(
    (data: ICreateOrUpdatePolicy) => {
      return ApiPaymentBonus.updatePolicy(data);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Cập nhật chính sách thành công",
        });
        getAllPaymentBonusPolicy.refetch();
        refForm.current?.resetForm();
        dispatch(setLoading(false));
        handleClose();
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const save = (): any => {
    const values = refForm.current?.values;
    if (
      !(
        values?.startDateMoment &&
        values?.endDateMoment &&
        values?.cvReviewed &&
        values?.cvInterviewed &&
        values?.cvOnboardDone &&
        values?.bonusOnboardDone &&
        values?.bonusReviewed &&
        values?.bonusInterviewed
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng điền đầy đủ thông tin.",
      });
      return;
    }

    if (moment(values.startDateMoment).isAfter(moment(values.endDateMoment))) {
      notification.error({
        message: "Thông báo",
        description: "Ngày kết thúc sau ngày bắt đầu.",
      });
      return;
    }

    dispatch(setLoading(true));
    if (isTypeCreate) {
      createPolicy.mutate({
        ...values,
        startDate: moment(values?.startDateMoment).format(DATE_FORMAT),
        endDate: moment(values?.endDateMoment).format(DATE_FORMAT),
      });
    } else {
      updatePolicy.mutate({
        ...values,
        startDate: moment(values?.startDateMoment).format(DATE_FORMAT),
        endDate: moment(values?.endDateMoment).format(DATE_FORMAT),
      });
    }
  };

  const disableStartDate = (current: any): boolean => {
    let result = false;
    if (isTypeCreate) {
      result = current && current < lastTime.startOf("day");
    } else {
      result =
        current && current < moment(new Date(policy.startDate)).startOf("day");
    }
    return result;
  };

  const initValue: IInitValue = {
    ...(policy || ({} as IPolicy)),
    startDateMoment: policy?.startDate
      ? moment(new Date(policy.startDate))
      : lastTime,
    endDateMoment: policy?.startDate
      ? moment(new Date(policy.endDate))
      : lastTime,
    cvReviewed: policy?.cvReviewed || 5,
    cvInterviewed: policy?.cvInterviewed || 3,
    cvOnboardDone: policy?.cvOnboardDone || 2,
    bonusReviewed: policy?.bonusReviewed || 1,
    bonusInterviewed: policy?.bonusInterviewed || 2,
    bonusOnboardDone: policy?.bonusOnboardDone || 10,
  };

  useEffect(() => {
    refForm.current?.setValues(initValue);
  }, [JSON.stringify(initValue)]);

  return (
    <AppModal
      className="modal-form-rule"
      open={open}
      footer={null}
      onCancel={handleClose}
      width="60%"
      title={`${!isTypeCreate ? "Chỉnh sửa" : "Tạo "} quy định thưởng`}
    >
      <div>
        <div className="content">
          <Formik
            initialValues={initValue}
            innerRef={refForm}
            onSubmit={(): void => {
              //
            }}
          >
            {({values, setFieldValue}): JSX.Element => {
              const disableEndDate = (current: any): boolean => {
                return (
                  current &&
                  current < moment(values.startDateMoment).startOf("day")
                );
              };

              return (
                <div>
                  <div className="text16 font-bold">Thời gian áp dụng</div>
                  <Row className="justify-between mt-4">
                    <AppDatePicker
                      classNameContainer="w-2/5"
                      name="startDateMoment"
                      label="Ngày bắt đầu"
                      format={DATE_FORMAT}
                      valueAppDatePicker={values?.startDateMoment}
                      required
                      status={values?.startDateMoment ? undefined : "error"}
                      disabledDate={disableStartDate}
                      onChange={(): void => {
                        setFieldValue("endDateMoment", "");
                      }}
                    />
                    <AppDatePicker
                      classNameContainer="w-2/5"
                      name="endDateMoment"
                      label="Ngày kết thúc"
                      format={DATE_FORMAT}
                      valueAppDatePicker={values?.endDateMoment}
                      free={!values?.endDateMoment}
                      required
                      status={values?.endDateMoment ? undefined : "error"}
                      disabledDate={disableEndDate}
                    />
                  </Row>
                  <div className="text16 font-bold mt-6">Quy định thưởng</div>
                  <div className="mt-2">
                    <ul>
                      <li>
                        {"Cứ "}
                        <InputNumber
                          name="cvReviewed"
                          value={values.cvReviewed}
                          status={values?.cvReviewed ? undefined : "error"}
                          maxLength={maxLength}
                          min={0}
                          controls={false}
                        />
                        {" CV được gửi lịch PV khách hàng của RECO => Thưởng "}
                        <InputNumber
                          name="bonusReviewed"
                          value={values.bonusReviewed}
                          status={values?.bonusReviewed ? undefined : "error"}
                          maxLength={maxLength}
                          min={0}
                          controls={false}
                        />
                        {" M"}
                      </li>
                      <li>
                        {"Cứ "}
                        <InputNumber
                          name="cvInterviewed"
                          value={values.cvInterviewed}
                          status={values?.cvInterviewed ? undefined : "error"}
                          maxLength={maxLength}
                          min={0}
                          controls={false}
                        />
                        {" Interviewed => Thưởng "}
                        <InputNumber
                          name="bonusInterviewed"
                          value={values.bonusInterviewed}
                          status={
                            values?.bonusInterviewed ? undefined : "error"
                          }
                          maxLength={maxLength}
                          min={0}
                          controls={false}
                        />
                        {" M"}
                      </li>
                      <li>
                        {"Cứ "}
                        <InputNumber
                          name="cvOnboardDone"
                          value={values.cvOnboardDone}
                          status={values?.cvOnboardDone ? undefined : "error"}
                          maxLength={maxLength}
                          min={0}
                          controls={false}
                        />
                        {" ứng viên đi làm bảo hành thành công=> Thưởng "}
                        <InputNumber
                          name="bonusOnboardDone"
                          value={values.bonusOnboardDone}
                          status={
                            values?.bonusOnboardDone ? undefined : "error"
                          }
                          maxLength={maxLength}
                          min={0}
                          controls={false}
                        />
                        {" M"}
                      </li>
                    </ul>
                  </div>
                </div>
              );
            }}
          </Formik>
        </div>
        <Row className="justify-center mt-6">
          <AppButton
            classrow="w-48 mr-10 btn-delete"
            label="Hủy"
            typebutton="secondary"
            onClick={(): void => {
              handleClose();
              refForm.current?.resetForm();
            }}
          />
          <AppButton
            classrow="w-48 ml-10"
            label="Lưu"
            typebutton="primary"
            onClick={save}
          />
        </Row>
      </div>
    </AppModal>
  );
}
