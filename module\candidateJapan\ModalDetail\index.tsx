import AppModal from "@app/components/AppModal";
import {Ava<PERSON>, Col, Row} from "antd";
import React from "react";
import "./index.scss";
import {ICandidateJapan, IModeViewCandidate} from "@app/api/ApiCandidate";
import ViewCvCandidate from "@app/components/viewCvCandidate";
import {getAbbreviatedName, salaryRange} from "@app/utils/constants/function";
import Icon from "@app/components/Icon/Icon";
import config from "@app/config";
import ApiRequestJob from "@app/api/ApiRequestJob";
import {useQuery} from "react-query";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";

interface ModalDetailProps {
  open: boolean;
  onCloseModal: () => void;
  candidate: ICandidateJapan;
}

export default function ModalDetail(props: ModalDetailProps): JSX.Element {
  const {open, onCloseModal, candidate} = props;
  const idRequestJob = candidate?.requestJobId;

  const requestJobDetail = useQuery(
    ["requestJobDetail", idRequestJob],
    () => {
      return ApiRequestJob.getDetail(idRequestJob);
    },
    {
      enabled: !!idRequestJob,
    }
  );

  const requestJob = requestJobDetail.data;

  const listInfo = [
    {icon: "mail-line", value: candidate?.email},
    {icon: "map-pin-line", value: candidate?.currentLocation},
    {icon: "phone-line", value: candidate?.phoneNumber},
    {icon: "global-line", value: candidate?.japaneseLevel},
    {icon: "briefcase-2-line", value: candidate?.experienceYear},
  ];

  const listJobInfo: {nameIcon: string; value: any}[] = [
    {
      nameIcon: "apartment",
      value: requestJob?.customerName,
    },
    {
      nameIcon: "coins-line",
      value: salaryRange(
        requestJob?.salaryFrom,
        requestJob?.salaryTo,
        requestJob?.currencyTypeId
      ),
    },
    {
      nameIcon: "briefcase-2-line",
      value: requestJob?.positionName,
    },
    {
      nameIcon: "category",
      value: requestJob?.workType,
    },
    {
      nameIcon: "map-pin-line-pin-line",
      value: requestJob?.workLocationNameCombined,
    },
  ];

  return (
    <AppModal
      className="modal-detail-application-japan"
      open={open}
      footer={null}
      onCancel={onCloseModal}
      width="80%"
      title="Chi tiết ứng tuyển"
    >
      <Row>
        <Col className="pr-2" span={12}>
          <div className="container-item-detail-modal">
            <div className="card-info">
              <Row>
                <Col span={4}>
                  <Avatar size={64} className="avatar">
                    {getAbbreviatedName(candidate?.fullName)}
                  </Avatar>
                </Col>
                <Col span={20} className="pl-3">
                  <p className="font-bold text24">
                    {candidate?.fullName || "N/A"}
                  </p>
                  <p className="font-normal text16">
                    {candidate?.currentPosition || "N/A"}
                  </p>
                </Col>
              </Row>
              <Row className="mt-4">
                {listInfo.map((item, index) => (
                  <Row className="flex items-center w-1/2 mt-2" key={index}>
                    <Icon size={20} icon={item.icon} />
                    <span className="ml-2 flex-1 break-all text-color-primary">
                      {item.value || "N/A"}
                    </span>
                  </Row>
                ))}
              </Row>
              <div className="line" />
              <Row className="flex items-center justify-between">
                <span className="text-color-primary text16 font-bold">
                  Thông tin công việc
                </span>
                <a
                  className="a-detail-job"
                  target="_blank"
                  href={`${config.PATHNAME.JOB_DETAIL}?id=${candidate?.requestJobId}`}
                  rel="noreferrer"
                >
                  Chi tiết
                </a>
              </Row>
              <Row>
                {listJobInfo.map((item, index) => (
                  <Row className="flex items-center w-1/2 mt-2" key={index}>
                    <Icon
                      size={20}
                      icon={item.nameIcon}
                      color="#324054"
                      className=""
                    />
                    <span className="font-normal text-color-primary ml-2">
                      {item.value || " N/A"}
                    </span>
                  </Row>
                ))}
              </Row>
            </div>
          </div>
        </Col>
        <Col className="pl-2" span={12}>
          <div className="container-item-detail-modal h-[72vh] p-6">
            <ViewCvCandidate
              docs={{
                filePathBase64: candidate?.fileCVPath,
                fileName: candidate?.fileCVName,
              }}
              modeViewCandidate={IModeViewCandidate.view}
              createdDate={
                candidate?.createdDate
                  ? String(moment(candidate.createdDate, DATE_FORMAT))
                  : ""
              }
            />
          </div>
        </Col>
      </Row>
    </AppModal>
  );
}
