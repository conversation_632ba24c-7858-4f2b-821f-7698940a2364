import {Spin} from "antd";
import React from "react";
import "./index.scss";

interface IAppLoadingSystemProp {
  isLoading: boolean;
}
export default function AppLoadingSystem(
  props: IAppLoadingSystemProp
): JSX.Element {
  const {isLoading} = props;
  return (
    isLoading &&
    ((
      <div className="overlay-loading flex items-center justify-center">
        <Spin size="large" />
      </div>
    ) as any)
  );
}
