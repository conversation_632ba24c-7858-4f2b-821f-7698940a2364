import {SelectInput} from "@app/components/SelectInput";
import "./index.scss";
import {TextInput} from "@app/components/TextInput";
import {Checkbox, Col, Popover, Row} from "antd";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {Formik, FormikProps} from "formik";
import React, {FormEvent, useEffect, useRef, useState} from "react";
import {deadTimeFastSearch, paymentStatus} from "@app/utils/constants/state";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment, {Moment} from "moment";
import {useSelector, useDispatch} from "react-redux";
import {changeColShowHotBonus, selectUser} from "@app/redux/slices/UserSlice";
import {CheckboxValueType} from "antd/lib/checkbox/Group";
import AppCheckBox from "@app/components/AppCheckbox";
import {IDataHotBonus, IDataPagingHotBonus} from "@app/api/ApiPaymentBonus";
import {ColumnType} from "antd/lib/table";
import {OptionSelect} from "@app/types";

interface Props {
  setSearchParams: (value: IDataHotBonus) => void;
  allColumn: ColumnType<IDataPagingHotBonus>[];
  pageSize: number;
  isCSL: boolean;
  currentColumns: ColumnType<IDataPagingHotBonus>[];
}

interface DataFormSearchHotBonus {
  consultantName: string;
  creatorName: string;
  candidateName: string;
  positionName: string;
  paymentBonusStatus: OptionSelect;
  paymentEndDate: string | Moment | undefined;
  paymentStartDate: string | Moment | undefined;
}

const listCheckBoxLeft: OptionSelect[] = [
  {
    label: "Vị trí",
    value: "positionName",
  },
  {
    label: "Trạng thái ứng tuyển",
    value: "stageName",
  },
  {
    label: "Ngày tạo application",
    value: "createdDate",
  },
];

const listCheckBoxRight: OptionSelect[] = [
  {
    label: "Số tiền",
    value: "hotBonusAmount",
  },
  {
    label: "Trạng thái thanh toán",
    value: "paymentBonusStatus",
  },
  {
    label: "Ngày thanh toán",
    value: "paymentDate",
  },
];

function FilterHotBonus(props: Props): JSX.Element {
  const {setSearchParams, allColumn, pageSize, isCSL, currentColumns} = props;
  const {lisColShowHotBonus} = useSelector(selectUser);
  const lengthAllColumns = allColumn?.length || 0;

  const totalColumn = isCSL ? lengthAllColumns : lengthAllColumns - 1;
  const dispatch = useDispatch();

  const [showFilterAdvance, setShowFilterAdvance] = useState<boolean>(false);
  const formikRef = useRef<FormikProps<DataFormSearchHotBonus>>(null);
  const timeOut = useRef<any>();

  const initialValue: DataFormSearchHotBonus = {
    consultantName: "",
    candidateName: "",
    positionName: "",
    paymentBonusStatus: {} as OptionSelect,
    paymentStartDate: "",
    paymentEndDate: "",
    creatorName: "",
  };

  const handleSearch = () => {
    const valueRef = formikRef?.current?.values;
    const paramValue = {
      ...valueRef,
      currentPage: 1,
      paymentStartDate: valueRef?.paymentStartDate
        ? moment(valueRef?.paymentStartDate).format(DATE_FORMAT)
        : "",
      paymentEndDate: valueRef?.paymentEndDate
        ? moment(valueRef?.paymentEndDate).format(DATE_FORMAT)
        : "",
      pageSize: pageSize,
      paymentBonusStatus: valueRef?.paymentBonusStatus?.key
        ? Number(valueRef?.paymentBonusStatus?.key)
        : null,
      consultantName: valueRef?.consultantName?.trim() || "",
      creatorName: valueRef?.creatorName?.trim() || "",
    };
    setSearchParams(paramValue);
  };

  const handleTextSearch = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const handleAdvanceSearch = (): void => {
    handleSearch();
  };

  const onResetForm = (): void => {
    formikRef?.current?.resetForm();
    setSearchParams({
      currentPage: 1,
      isFirstInitialization: true,
      pageSize: pageSize,
    });
  };

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  function renderFormSearch(values: DataFormSearchHotBonus): JSX.Element {
    return (
      <div className="ui-form-search">
        <TextInput
          label="Tên ứng viên"
          name="candidateName"
          containerclassname="mt-2"
          value={values?.candidateName}
          free={!values?.candidateName}
        />
        <TextInput
          name="positionName"
          label="Vị trí"
          containerclassname="mt-2"
          value={values?.positionName}
          free={!values?.positionName}
        />
        <SelectInput
          name="paymentBonusStatus"
          labelselect="Trạng thái thanh toán"
          data={paymentStatus.filter((item) => item.id !== "1")}
          containerclassname="mt-2"
          value={values?.paymentBonusStatus?.value}
          free={!values?.paymentBonusStatus?.key}
          allowClear
        />
        <p className="mt-2 text16">Ngày thanh toán</p>
        <Row className="mt-2" gutter={[16, 16]}>
          <Col xs={12}>
            <AppDatePicker
              label="Từ"
              name="paymentStartDate"
              format={DATE_FORMAT}
              valueAppDatePicker={
                values?.paymentStartDate
                  ? moment(values?.paymentStartDate, DATE_FORMAT)
                  : undefined
              }
              allowClear
            />
          </Col>
          <Col xs={12}>
            <AppDatePicker
              label="Đến"
              name="paymentEndDate"
              format={DATE_FORMAT}
              valueAppDatePicker={
                values?.paymentEndDate
                  ? moment(values?.paymentEndDate, DATE_FORMAT)
                  : undefined
              }
              allowClear
            />
          </Col>
        </Row>
        <Row className="mt-2" gutter={[16, 16]}>
          <Col xs={12}>
            <AppButton
              label="Xoá tất cả"
              typebutton="secondary"
              onClick={onResetForm}
            />
          </Col>
          <Col xs={12}>
            <AppButton
              label="Tìm kiếm"
              typebutton="primary"
              onClick={handleAdvanceSearch}
            />
          </Col>
        </Row>
      </div>
    );
  }

  const onChangeColumns = (newSelectedRowKeys: CheckboxValueType[]): void => {
    dispatch(changeColShowHotBonus(newSelectedRowKeys as string[]));
  };

  const renderListCheckbox = (
    <Checkbox.Group
      className="list-checkbox"
      value={lisColShowHotBonus ?? []}
      onChange={onChangeColumns}
    >
      <Row>
        <Col xs={12}>
          {listCheckBoxLeft.map((itemCheck, index) => (
            <AppCheckBox
              className="w-full mt-1"
              key={index}
              value={itemCheck.value}
            >
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col xs={12}>
          {listCheckBoxRight.map((itemCheck, index) => (
            <AppCheckBox
              className="w-full mt-1"
              key={index}
              value={itemCheck.value}
            >
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );
  return (
    <Formik
      initialValues={initialValue}
      innerRef={formikRef}
      onSubmit={() => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <form
            onSubmit={(e: FormEvent<HTMLFormElement>): void => {
              e.preventDefault();
            }}
          >
            <div className="search-form-recommendation">
              <Row
                gutter={[16, 16]}
                justify="space-between"
                className="w-full flex-row items-center"
              >
                <Col xs={12}>
                  <Row gutter={[16, 16]} className="items-center">
                    {isCSL && (
                      <Col xs={12}>
                        <TextInput
                          name="consultantName"
                          label="CST quản lý"
                          placeholder="Nhập CST quản lý"
                          containerclassname="mt-2 w-full"
                          value={values?.consultantName}
                          free={!values?.consultantName}
                          onChange={handleTextSearch}
                        />
                      </Col>
                    )}
                    <Col xs={12}>
                      <TextInput
                        label="Cộng tác viên"
                        name="creatorName"
                        placeholder="Nhập cộng tác viên"
                        containerclassname="mt-2 w-full"
                        value={values?.creatorName}
                        free={!values?.creatorName}
                        onChange={handleTextSearch}
                      />
                    </Col>
                  </Row>
                </Col>
                <Col>
                  <Row gutter={[16, 16]} className="items-center">
                    <Col>
                      <Popover
                        content={renderFormSearch(values)}
                        trigger={["click"]}
                        placement="bottomLeft"
                        open={showFilterAdvance}
                        onOpenChange={setShowFilterAdvance}
                        getPopupContainer={(trigger): any =>
                          trigger.parentElement
                        }
                      >
                        <AppButton typebutton="normal" classrow="btn-filter">
                          <Icon
                            className="mr-1"
                            icon="filter-line"
                            size={12}
                            color="#324054"
                          />
                          Tìm kiếm nâng cao
                        </AppButton>
                      </Popover>
                    </Col>
                    <Col>
                      <Popover
                        placement="bottom"
                        trigger="click"
                        content={renderListCheckbox}
                        getPopupContainer={(trigger): any =>
                          trigger.parentElement
                        }
                      >
                        <AppButton typebutton="normal" classrow="btn-filter">
                          <Icon
                            className="mr-1"
                            icon="eye"
                            size={16}
                            color="#324054"
                          />
                          Hiển thị {currentColumns?.length}/{totalColumn}
                        </AppButton>
                      </Popover>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </div>
          </form>
        );
      }}
    </Formik>
  );
}

export default React.memo(FilterHotBonus);
