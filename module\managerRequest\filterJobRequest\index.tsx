import {FormEvent, useCallback, useEffect, useRef, useState} from "react";
import "./index.scss";
import {Formik, FormikProps} from "formik";
import {
  GroupByRequestJob,
  IDataSearchFormAdvance,
  IParamsListRequestJob,
  ISearchParamsRequestJob,
  IWorkLocationFilters,
} from "@app/api/ApiRequestJob";
import {Checkbox, Col, Popover, Row} from "antd";
import {TextInput} from "@app/components/TextInput";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {useSelector, useDispatch} from "react-redux";
import {SelectInput} from "@app/components/SelectInput";
import AppCheckBox from "@app/components/AppCheckbox";
import {CheckboxValueType} from "antd/lib/checkbox/Group";
import {
  changeColManagerRequestRoleAm,
  selectUser,
  setQueryParamsPages,
} from "@app/redux/slices/UserSlice";
import {
  deadTimeFastSearch,
  optionServiceRequest,
} from "@app/utils/constants/state";
import {IAccountRole, OptionSelect} from "@app/types";
import {useRouter} from "next/router";
import {
  convertToOptionSelect,
  formatDateTimeValue,
  onlyUnique,
} from "@app/utils/constants/function";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import AppGroupBy from "@app/components/AppGroupBy";
import _ from "lodash";

interface Props {
  dataForm: IDataSearchFormAdvance;
  initialValueSearch: IParamsListRequestJob;
  setSearchFormAdvance: (values: IParamsListRequestJob) => void;
  allRequestJob: string[];
  setVisibleGroupBy?: (val: boolean) => void;
  groupBySearchParams: GroupByRequestJob;
  onChangeGroupBySearchParams: (data: GroupByRequestJob) => void;
  isGroupBy: boolean;
  onAdvanceSearchGroupBy: (dataForm: IParamsListRequestJob) => Promise<void>;
}

interface DataSelectInput {
  label: string;
  value: string;
}

const mapFilterWorkLocation = (
  filters: IWorkLocationFilters[]
): {label: string; value: string}[] => {
  const newData = filters.map((item) => ({
    value: item?.label,
    label: item?.label,
    key: item?.id,
  }));
  return newData;
};

// const listCheckBoxLeft: DataSelectInput[] = [
//   {
//     label: "Loại request",
//     value: "requestType",
//   },
//   {
//     label: "Vị trí làm việc",
//     value: "positionName",
//   },
//   {
//     label: "Ngày request",
//     value: "requestDate",
//   },
//   {
//     label: "Khách hàng",
//     value: "customerName",
//   },
//   {
//     label: "Khu vực",
//     value: "workLocationNameCombined",
//   },
//   {
//     label: "Nhãn job",
//     value: "label",
//   },
//   {
//     label: "Dịch vụ",
//     value: "services",
//   },
// ];

const listCheckBoxLeftRoleAm: DataSelectInput[] = [
  {
    label: "Ngày request",
    value: "requestDate",
  },
  {
    label: "Vị trí",
    value: "positionName",
  },
  {
    label: "Ngày kết thúc",
    value: "expiryDate",
  },
];

// const listCheckBoxRight: DataSelectInput[] = [
//   {
//     label: "Trạng thái",
//     value: "statusName",
//   },
//   {
//     label: "CV Review",
//     value: "cvReview",
//   },
//   {
//     label: "CV Interview",
//     value: "cvInterview",
//   },
//   {
//     label: "CV Offer",
//     value: "cvOffer",
//   },
//   {
//     label: "Rate KH",
//     value: "customerRateValue",
//   },
//   {
//     label: "Rate CTV (%)",
//     value: "partnerRateValue",
//   },
// ];

const listCheckBoxRightRoleAm: DataSelectInput[] = [
  {
    label: "CV Interview",
    value: "cvInterview",
  },
  {
    label: "CV Offer",
    value: "cvOffer",
  },
  {
    label: "Rate KH",
    value: "customerRateValue",
  },
  {
    label: "Rate CTV",
    value: "partnerRateValue",
  },
];

const groupByOption: OptionSelect[] = [
  {
    label: "Khách hàng",
    value: "1",
    key: "1",
  },
  {
    label: "Vị trí",
    value: "2",
    key: "2",
  },
];

function FilterJobRequest(props: Props): JSX.Element {
  const {
    dataForm,
    initialValueSearch,
    setSearchFormAdvance,
    allRequestJob,
    setVisibleGroupBy,
    groupBySearchParams,
    onChangeGroupBySearchParams,
    isGroupBy,
    onAdvanceSearchGroupBy,
  } = props;
  const {user, listColShowManagerRequestRoleAm} = useSelector(selectUser);

  const isRoleAm = [
    IAccountRole.AML,
    IAccountRole.AMG,
    IAccountRole.ADMIN,
    IAccountRole.BD,
    IAccountRole.BDL,
  ].some((item) => user?.role?.includes(item));

  const currentCol = listColShowManagerRequestRoleAm;

  const router = useRouter();
  const navigatorAddRequest = () => {
    router.push("/manager-request/add");
  };
  const dispatch = useDispatch();
  const [showFilterAdvance, setShowFilterAdvance] = useState<boolean>(false);

  const formikAdvancedRef = useRef<FormikProps<IParamsListRequestJob>>(null);
  const timeOut = useRef<any>();

  const resetFormAdvance = () => {
    const initParams = {
      // isAdvanceSearch: false,
      isFirstInitialization: true,
      textSearch: undefined,
      pageSize: initialValueSearch.pageSize,
      currentPage: 1,
    };
    formikAdvancedRef?.current?.setValues(initParams);
    setSearchFormAdvance(initParams);

    if (isGroupBy) {
      onAdvanceSearchGroupBy(initParams);
    }
    dispatch(setQueryParamsPages({managerRequest: ""}));
  };

  const handleSearch = () => {
    const valueForm = formikAdvancedRef.current?.values;

    const valueSearch: ISearchParamsRequestJob = {
      ...valueForm,
      pageSize: initialValueSearch.pageSize || 20,
      currentPage: 1,
      countries:
        valueForm?.countries?.map((item: any) => ({
          label: item.label,
          id: item?.key,
        })) || [],
      customers:
        valueForm?.customers?.map((item: any) => ({
          label: item.label,
          id: item?.key,
        })) || [],
      isFirstInitialization: true,
      labels:
        valueForm?.labels?.map((item: any) => ({
          label: item.label,
          id: item?.key,
        })) || [],
      name: valueForm?.name || "",
      positionName: valueForm?.positionName || "",
      priorities:
        valueForm?.priorities?.map((item: any) => ({
          label: item.label,
          id: item?.key,
        })) || [],
      requestTypes:
        valueForm?.requestTypes?.map((item: any) => ({
          label: item.label,
          id: item?.key,
        })) || [],
      statuses:
        valueForm?.statuses?.map((item: any) => ({
          label: item.label,
          id: item?.key,
        })) || [],
      workLocations:
        valueForm?.workLocations?.map((item: any) => ({
          countryId: item?.value,
          id: item?.key,
          label: item?.label,
        })) || [],
      services:
        valueForm?.services?.map((i) => (i?.key ? i?.key : i) as string) || [],
      from: valueForm?.from ? moment(valueForm?.from).format(DATE_FORMAT) : "",
      to: valueForm?.to ? moment(valueForm?.to).format(DATE_FORMAT) : "",
      textSearch: valueForm?.textSearch?.trim() || "",
    };

    setSearchFormAdvance(valueSearch as any);

    if (isGroupBy) {
      onAdvanceSearchGroupBy(valueSearch as any);
    }
  };

  const handleSearchDebounce = useCallback(
    _.debounce(() => {
      handleSearch();
    }, deadTimeFastSearch),
    [handleSearch]
  );

  useEffect(() => {
    setVisibleGroupBy?.(true);
    onChangeGroupBySearchParams({
      ...groupBySearchParams,
      groupByValue: "",
      groupBy: Number(groupByOption[0].value),
    });
  }, []);

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  const handleSearchFormAdvance = () => {
    handleSearch();
  };

  function searchForm(
    dataForm: IDataSearchFormAdvance,
    values: IParamsListRequestJob
  ): JSX.Element {
    const validateTime = (): boolean => {
      const {from, to} = values;
      return !from || !to || moment(from).isSameOrBefore(moment(to));
    };

    return (
      <div className="search-form-filter-job">
        <div className="flex justify-between">
          <div>Tất cả bộ lọc</div>
          <div
            onClick={() => {
              setShowFilterAdvance(false);
            }}
            role="button"
            tabIndex={0}
          >
            <Icon className="" icon="close-circle-line" size={20} />
          </div>
        </div>
        <TextInput
          name="name"
          label="Tên request"
          placeholder="Nhập tên request"
          containerclassname="mt-2 w-full"
          free={!values?.name}
          value={values?.name}
          onChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="requestTypes"
          labelselect="Loại request"
          data={convertToOptionSelect(dataForm?.requestTypeFilters) ?? []}
          value={values?.requestTypes}
          free={values?.requestTypes?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <TextInput
          name="positionName"
          label="Vị trí làm việc"
          placeholder="Nhập vị trí làm việc"
          containerclassname="mt-2 w-full"
          value={values?.positionName}
          free={!values?.positionName}
          onChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="countries"
          labelselect="Quốc gia"
          data={convertToOptionSelect(dataForm?.countryFilters) ?? []}
          value={values?.countries || []}
          free={values?.countries?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="workLocations"
          labelselect="Tỉnh thành"
          data={mapFilterWorkLocation(dataForm?.workLocationFilters) ?? []}
          value={values?.workLocations || []}
          free={values?.workLocations?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="statuses"
          labelselect="Trạng thái"
          data={convertToOptionSelect(dataForm?.statusFilters) ?? []}
          value={values?.statuses}
          free={values?.statuses?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="priorities"
          labelselect="Độ ưu tiên"
          data={convertToOptionSelect(dataForm?.priorityFilters) ?? []}
          value={values?.priorities || []}
          free={values?.priorities?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="customers"
          labelselect="Khách hàng"
          data={convertToOptionSelect(dataForm?.customerFilters) ?? []}
          value={values?.customers || []}
          free={values?.customers?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="labels"
          labelselect="Nhãn job"
          data={convertToOptionSelect(dataForm?.labelFilters) ?? []}
          value={values?.labels || []}
          free={values?.labels?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          containerclassname="mt-2 w-full"
          mode="multiple"
          name="services"
          labelselect="Dịch vụ"
          data={optionServiceRequest}
          value={values?.services || []}
          free={values?.services?.length === 0}
          allowClear
          handleChange={handleSearchDebounce}
        />
        <Row className="mt-2 div-time">
          <AppDatePicker
            name="from"
            label="Từ"
            format={DATE_FORMAT}
            free={!values.from}
            status={!validateTime() ? "error" : ""}
            allowClear
            valueAppDatePicker={
              values?.from ? moment(values?.from, DATE_FORMAT) : undefined
            }
            onChange={handleSearchDebounce}
          />
          <AppDatePicker
            name="to"
            label="Đến"
            format={DATE_FORMAT}
            free={!values.to}
            status={!validateTime() ? "error" : ""}
            allowClear
            valueAppDatePicker={
              values?.to ? moment(values?.to, DATE_FORMAT) : undefined
            }
            onChange={handleSearchDebounce}
          />
        </Row>
        <div className="mt-6">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={resetFormAdvance}
          />
          {/* <AppButton
              label="Tìm kiếm"
              typebutton="primary"
              onClick={handleSearchFormAdvance}
            /> */}
        </div>
      </div>
    );
  }

  const onChangeColumns = (newSelectedRowKeys: CheckboxValueType[]): void => {
    const newValue = newSelectedRowKeys.filter(onlyUnique as any);
    dispatch(changeColManagerRequestRoleAm(newValue as string[]));
  };

  const renderListCheckbox = (
    <Checkbox.Group
      className="list-checkbox"
      value={listColShowManagerRequestRoleAm}
      onChange={onChangeColumns}
    >
      <Row>
        <Col xs={12}>
          {listCheckBoxLeftRoleAm.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col xs={12}>
          {listCheckBoxRightRoleAm.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );

  return (
    <Formik
      initialValues={initialValueSearch}
      innerRef={formikAdvancedRef}
      onSubmit={() => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <form
            onSubmit={(e: FormEvent<HTMLFormElement>): void => {
              e.preventDefault();
            }}
          >
            <div className="w-full filter-job-form">
              <Row
                gutter={[16, 16]}
                justify="space-between"
                className="items-center"
              >
                <Col xs={8}>
                  <div className="w-full">
                    <TextInput
                      label="Nhập tên request, tên khách hàng, vị trí"
                      name="textSearch"
                      onChange={handleSearchDebounce}
                      value={values.textSearch}
                      disabled={showFilterAdvance}
                    />
                  </div>
                </Col>
                <Col>
                  <Row gutter={[16, 16]} className="flex flex-nowrap">
                    <Col>
                      <AppGroupBy
                        options={groupByOption}
                        defaultValue={groupByOption[0].value}
                        onChange={(value) => {
                          if (value) {
                            setVisibleGroupBy?.(true);
                            onChangeGroupBySearchParams({
                              ...groupBySearchParams,
                              groupByValue: "",
                              groupBy: Number(value),
                            });
                          } else {
                            setVisibleGroupBy?.(false);
                          }
                        }}
                      />
                    </Col>
                    {isRoleAm && (
                      <Col>
                        <AppButton
                          typebutton="normal"
                          classrow="add-btn"
                          onClick={navigatorAddRequest}
                        >
                          <Icon icon="add-line" size={16} />
                          Thêm mới request
                        </AppButton>
                      </Col>
                    )}
                    <Col>
                      <Popover
                        content={searchForm(dataForm, values)}
                        trigger={["click"]}
                        placement="bottom"
                        open={showFilterAdvance}
                        onOpenChange={setShowFilterAdvance}
                        getPopupContainer={(trigger): any =>
                          trigger.parentElement
                        }
                      >
                        <AppButton typebutton="normal" classrow="btn-filter">
                          <Icon
                            className="mr-1"
                            icon="filter-line"
                            size={12}
                            color="#324054"
                          />
                          Tìm kiếm nâng cao
                        </AppButton>
                      </Popover>
                    </Col>
                    <Col>
                      <Popover
                        placement="bottom"
                        trigger="click"
                        content={renderListCheckbox}
                        getPopupContainer={(trigger): any =>
                          trigger.parentElement
                        }
                      >
                        <AppButton typebutton="normal" classrow="btn-filter">
                          <Icon
                            className="mr-1"
                            icon="eye"
                            size={16}
                            color="#324054"
                          />
                          Hiển thị {currentCol?.length}/{allRequestJob.length}
                        </AppButton>
                      </Popover>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </div>
          </form>
        );
      }}
    </Formik>
  );
}

export default FilterJobRequest;
