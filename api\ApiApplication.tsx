import {Moment} from "moment";
// eslint-disable-next-line import/no-cycle
import {fetcher} from "./Fetcher";
import {DataGroupByField, IMultiSelect, OptionSelect} from "@app/types";

export interface IApplication {
  applicationId: number;
  applicationName: string;
  candidateEmail: string;
  candidateName: string;
  candidatePhone: string;
  commissionFlag: boolean;
  createdDate: string;
  creatorName: string;
  creatorPhone: string;
  customerName: string;
  isFinished: boolean;
  managerName: string;
  positionName: string;
  requestJobCode: string;
  stage?: string | number;
  stageName: string;
  status?: string | number;
  statusName?: string;
  summary?: string;
  modifiedDate: string;
  salaryExpected?: number;
  salaryExpectedType: number;
  matching: {
    rate: number | null;
    suitable: string;
    unsuitable: string;
  };
  services: Array<string>;
}

export interface IParamsListApplication {
  isAdvanceSearch?: boolean;
  isFirstInitialization?: boolean;
  userId?: number;
  role?: string;
  textSearch?: string;
  customerName?: string;
  positionName?: string;
  stages?: OptionSelect[];
  statuses?: OptionSelect[];
  requestJobCode?: string;
  requestJobName?: string;
  from?: string;
  to?: string;
  candidateName?: string;
  candidateEmail?: string;
  candidatePhone?: string;
  applicationType?: boolean;
  currentPage: number;
  pageSize: number;
  workingLocationIds?: string[];
  partnerId?: number;
  roleFilter?: string | null;
  creators?: OptionSelect[];
  requestJobId?: number;
  managerId?: number;
  services?: OptionSelect[] | any;
}

export interface IListApplication {
  stages: IMultiSelect[];
  statuses: IMultiSelect[];
  creators: IMultiSelect[];
  managers: IMultiSelect[];
  consultantKeys: number[];
  creatorFilters: IMultiSelect[];
  managerFilters: IMultiSelect[];
  applicationsPaging: IApplication[];
  totalPages: number;
  currentPage: number;
  totalCount: number;
  pageSize: number;
  hasPrevious: boolean;
  hasNext: boolean;
}

interface IManager {
  userId: number;
  name: string;
  email: string;
  image: string;
  role: string;
  roleId: string;
  userGroupId: number;
}
export interface ICandidate {
  candidateId: number;
  name: string;
  email: string;
  phoneNumber: number;
  experienceYear: string;
  countryId: string;
  workLocationId: string;
  fileCVPath: string;
  fileCVName: string;
  note: string;
  managerId: number;
  deleteFlag: boolean;
  manager: IManager[];
  createdDate: string;
  currentSalary: number;
  salaryExpected: number;
  currentSalaryType: string;
  salaryExpectedType: number;
  onboardDate: string;
  currencyTypeId?: string;
  fileAvatarPath?: string | null;
  status?: number;
}

interface ICreator {
  email: string;
  image: string;
  name: string;
  role: string;
  roleId?: number;
  userGroupId: number;
  userId: number;
  roleUsers: {
    roleId: string;
    userId: number;
    userGroupId: number;
  }[];
  phoneNumber: string;
}

interface IAppHistories {
  applicationId: number;
  applicationName: string;
  candidate: string;
  commissionFlag: boolean;
  contractTerminationDate: string;
  currencyTypeId: string;
  currentSalary: string;
  currentSalaryType: string;
  desctiption: string;
  managerId: number;
  onboardDate: string;
  requestJob: string;
  requestJobCode: string;
  salaryExpected: string;
  salaryExpectedType: string;
  salaryOffered: string;
  salaryOfferedType: number;
  stage: number;
  stageDateStarted: string;
  stageNote: string;
  stagePersonCare: string;
  status: string;
  summary: string;
  trailWorkTime: string;
  userId: number;
  modifiedDate: string;
}
export interface IApplicationDetail {
  applicationId: number;
  applicationName: string;
  appHistories: IAppHistories[];
  candidate: ICandidate;
  commissionFlag: boolean;
  contractTerminationDate?: string;
  createdDate: string;
  createdDateDisp?: string;
  manager: IManager;
  currencyTypeId?: string;
  currentSalary?: number;
  currentSalaryType?: number;
  customerId: string;
  customerName: string;
  description?: string;
  hasCustomerPayment: boolean;
  hasPartnerPayment: boolean;
  managerId: number;
  onboardDate?: string;
  partnersManaging: number[];
  positionName: string;
  requestJobCode: string;
  requestJobId: number;
  requestJobName: string;
  salaryExpected?: number;
  salaryExpectedType?: number;
  salaryOffered?: number;
  salaryOfferedType?: number;
  stage?: number;
  stageDateStarted: string;
  stageNote?: string;
  stagePersonCare?: string;
  status?: number;
  summary?: string;
  trailWorkTime?: number;
  creator: ICreator;
  workingLocation?: string;
  workingLocationId?: string;
  workType?: string;
  salaryFrom?: number;
  salaryTo?: number;
  jobCurrencyTypeId?: string;
  matching: {
    rate: number | null;
    suitable: string;
    unsuitable: string;
  };
}
export interface IParamsUpdateApplication {
  applicationName?: string;
  currencyTypeId?: string;
  currentSalaryType?: number;
  salaryExpectedType?: number;
  salaryOfferedType?: number;
  stage?: number;
  consultantId?: number;
  applicationId?: number;
  candidateName?: string;
  candidateEmail?: string;
  candidatePhone?: number;
  salaryExpected?: number;
  currentSalary?: number;
  onboardDate?: string | Moment | null; // DD/MM/YYYY
  status?: number;
  ctvNote?: string;
  stagePersonCare?: string;
  stageDateStarted?: string | Moment;
  salaryOffered?: number;
  trailWorkTime?: number;
  summary?: string;
  description?: string;
  commissionFlag?: boolean;
}

export interface IParamsCreateApplication {
  requestJobId?: number;
  candidateIds: {candidateId?: number; commissionFlag: boolean}[];
}

export interface IInfoPersonIntroduced {
  candidateName: string;
  createdDate: string;
  creatorEmail: string;
  creatorName: string;
  requestJobName: string;
}
export interface INewApplication {
  totalAppCreated: number;
  totalCandidateCreated: number;
  applications: IApplicationDetail[];
  candidateDuplicateHasApplied: ICandidate[];
  duplicateDetails: IInfoPersonIntroduced[];
}

export interface IPaymentByApplicationId {
  paymentId: number;
  applicationId: number;
  requestJobId: number;
  onboardDate: string;
  paymentDateExpected: string;
  salaryOffered: number;
  currencyOfferedType: string;
  salaryOfferedExchangeRate: number;
  paymentRate: number;
  paymentRateType: boolean;
  paymentRateUnit: string;
  monthWarranty: string;
  totalAmount: number;
  listCurrency: {currencyTypeId: string; name: string}[];
}

export interface IParamPayment {
  applicationId: number;
  currencyOfferedType: string;
  monthWarranty: string;
  onboardDate: string;
  paymentDateExpected: string;
  paymentId: number;
  paymentRate?: number;
  paymentRateType: boolean; // ngược với trả về
  paymentRateUnit?: string;
  requestJobId?: number;
  salaryOffered?: number;
  salaryOfferedExchangeRate: string | number;
}

interface ICustomerPaymentByApplicationId {
  amount: number;
  applicationId: number;
  currencyOfferedType: string;
  hasVAT: number;
  monthTrailWork: string;
  onboardDate: string;
  paymentDateExpected: string;
  paymentId: number;
  paymentRate: number;
  paymentRateType: boolean; // false là tỷ lệ , true là số tiền
  paymentRateUnit: string;
  requestJobId: number;
  salaryOffered: number;
  salaryOfferedExchangeRate: number;
  salesTransacted: number;
  vat: number;
  listCurrency: {currencyTypeId: string; name: string}[];
}

export interface IParamCustomerPayment {
  amount: number;
  amountRemain: string | null;
  applicationId: number;
  currencyOfferedType: string;
  hasVAT: number; // 0 là ko có , 1 là có
  monthTrailWork: number;
  onboardDate: string;
  paymentDateExpected: string;
  paymentId: number;
  paymentRate: number;
  paymentRateType: boolean; // true là tỷ lệ , false là số tiền
  paymentRateUnit: string;
  requestJobId: number;
  salaryOffered: number;
  salaryOfferedExchangeRate: number;
  salesTransacted: number;
  vat: number | null;
}

export enum GroupByApplication {
  Stage = 1,
  CreatedBy = 2,
  RequestJob = 3,
  Customer = 4,
  None = 0,
}

export interface GroupByRequest {
  groupBy: GroupByApplication | string;
  groupByValue: string | number;
  pageNumber: number;
  pageSize: number;
}

export interface GroupByFilterParams {
  candidateInformation: string;
  customerName: string;
  requestJobName: string;
  workingLocationIds: Array<string>;
  stages: OptionSelect[];
  statuses: OptionSelect[];
  creators: OptionSelect[];
  from: string;
  to: string;
  isAdvanceSearch: boolean;
  currentPage?: number;
  roleFilterSelected?: any;
  pageSize?: number;
  roleFilter?: any;
  managerId?: number | null;
}

export interface ParamsGroupByApplication {
  groupByRequest: GroupByRequest;
  applicationSearch: GroupByFilterParams;
}

export interface DataListApplicationGroupBy {
  applicationSearchDetail: Array<IApplication>;
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

interface ManagerData {
  managerId: number;
  managerName: string;
}

const path = {
  list: "/api/application/filter",
  detailApplication: "/api/application",
  updateApplication: "/api/application/edit",
  createApplication: "/api/application/createAppFromList",
  checkPartnerPaymentExist: "/api/payment/CheckPartnerPaymentExist",
  getPaymentByApplicationId: "/api/payment/GetPaymentByApplicationId",
  createPartnerPayment: "api/payment/CreatePartnerPayment",
  checkCustomerPaymentExist: "api/payment/CheckCustomerPaymentExist",
  getCustomerPaymentByApplicationId:
    "/api/payment/GetCustomerPaymentByApplicationId",
  createCustomerPayment: "api/payment/CreateCustomerPayment",
  updateStage: "/api/application/editStage",
  getListGroupBy: "/api/Application/getListGroupBy",
  listApplicationGroupBy: "/api/application/getListApplicationByGroupBy",
  getListManager: "/api/application/getListManager",
};

function getListApplications(
  body?: IParamsListApplication
): Promise<IListApplication> {
  return fetcher({url: path.list, method: "post", data: body});
}

function getDetailApplications(id?: number): Promise<IApplicationDetail> {
  return fetcher({url: path.detailApplication + "/" + id, method: "get"});
}

function updateApplication(body?: IParamsUpdateApplication): Promise<any> {
  return fetcher({
    url: path.updateApplication,
    method: "post",
    data: body,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

function createApplications(
  body?: IParamsCreateApplication
): Promise<INewApplication> {
  return fetcher({url: path.createApplication, method: "post", data: body});
}

function checkPartnerPaymentExist(applicationId: number): Promise<boolean> {
  return fetcher({
    url: `${path.checkPartnerPaymentExist}?applicationId=${applicationId}`,
    method: "get",
  });
}

function getPaymentByApplicationId(
  applicationId: number
): Promise<IPaymentByApplicationId> {
  return fetcher({
    url: `${path.getPaymentByApplicationId}?applicationId=${applicationId}`,
    method: "get",
  });
}

function createPartnerPayment(body: IParamPayment): Promise<boolean> {
  return fetcher({
    url: path.createPartnerPayment,
    method: "post",
    data: body,
  });
}

function checkCustomerPaymentExist(applicationId: number): Promise<boolean> {
  return fetcher({
    url: `${path.checkCustomerPaymentExist}?applicationId=${applicationId}`,
    method: "get",
  });
}

function getCustomerPaymentByApplicationId(
  applicationId: number
): Promise<ICustomerPaymentByApplicationId> {
  return fetcher({
    url: `${path.getCustomerPaymentByApplicationId}?applicationId=${applicationId}`,
    method: "get",
  });
}

function createCustomerPayment(body: IParamCustomerPayment): Promise<boolean> {
  return fetcher({
    url: path.createCustomerPayment,
    method: "post",
    data: body,
  });
}

function updateStageApplication(body: {
  applicationId: number;
  stage: number;
  status: number;
}): Promise<any> {
  return fetcher({
    url: path.updateStage,
    method: "post",
    data: body,
  });
}

function filterGroupByApplication(
  body: ParamsGroupByApplication
): Promise<DataGroupByField> {
  return fetcher({
    url: path.getListGroupBy,
    method: "post",
    data: body,
  });
}

function getListApplicationGroupBy(
  body: ParamsGroupByApplication
): Promise<DataListApplicationGroupBy> {
  return fetcher({
    url: path.listApplicationGroupBy,
    method: "post",
    data: body,
  });
}

function getDataManagers(): Promise<Array<ManagerData>> {
  return fetcher({
    url: path.getListManager,
    method: "get",
  });
}

export default {
  getListApplications,
  getDetailApplications,
  updateApplication,
  createApplications,
  checkPartnerPaymentExist,
  getPaymentByApplicationId,
  createPartnerPayment,
  checkCustomerPaymentExist,
  getCustomerPaymentByApplicationId,
  createCustomerPayment,
  updateStageApplication,
  filterGroupByApplication,
  getListApplicationGroupBy,
  getDataManagers,
};
