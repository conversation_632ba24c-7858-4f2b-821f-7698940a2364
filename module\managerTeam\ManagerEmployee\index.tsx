import AppButton from "@app/components/AppButton";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import Icon from "@app/components/Icon/Icon";
import {TextInput} from "@app/components/TextInput";
import {Popover, Row} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import ApiTeam, {IEmployeeFilter, IUsersRecoPaging} from "@app/api/ApiTeam";
import {ColumnsType} from "antd/lib/table";
import {SelectInput} from "@app/components/SelectInput";
import {OptionSelect} from "@app/types";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import {useQuery} from "react-query";
import {getStatusEmployee} from "@app/utils/constants/function";
import {deadTimeFastSearch, statusEmployee} from "@app/utils/constants/state";
import ModalFormEmployee from "../ModalFormEmployee";
import ModalDetailEmployee from "../ModalDetailEmployee";

interface IFormFilter extends IEmployeeFilter {
  titleSelected: OptionSelect[];
  statusSelected: OptionSelect[];
}

export default function ManagerEmployee(): JSX.Element {
  const [valuesSearch, setValuesSearch] = useState<IEmployeeFilter>({
    textSearch: "",
    currentPage: 1,
    pageSize: 20,
    isAdvanceSearch: false,
    name: "",
    email: "",
    phoneNumber: "",
    title: [],
    status: [],
    from: "",
    to: "",
  });
  const [isShowFilterAdvance, setIsShowFilterAdvance] =
    useState<boolean>(false);
  const timeOut = useRef<any>();
  const filterAdvancedRef = useRef<FormikProps<IFormFilter>>(null);
  const [isOpenFormEmployee, setIsOpenFormEmployee] = useState<boolean>(false);
  const [idEmployee, setIdEmployee] = useState(-1);

  const getListEmployee = useQuery(["getListEmployee", valuesSearch], () => {
    return ApiTeam.getListEmployee(valuesSearch);
  });

  const getUserTitles = useQuery(["getUserTitles"], () => {
    return ApiTeam.getUserTitles();
  });

  const optionTitle: OptionSelect[] =
    getUserTitles.data?.map((item) => ({
      key: item.titleId,
      value: item.name,
      label: item.name,
    })) || [];

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesSearch({
      ...valuesSearch,
      currentPage: page,
      pageSize,
    });
  };

  const resetData = (): void => {
    filterAdvancedRef.current?.resetForm();
    setValuesSearch({
      ...valuesSearch,
      textSearch: "",
      currentPage: 1,
      isAdvanceSearch: false,
    });
  };

  const onClickSearch = (): void => {
    const values = filterAdvancedRef.current?.values;
    setValuesSearch({
      ...valuesSearch,
      currentPage: 1,
      pageSize: valuesSearch.pageSize,
      isAdvanceSearch: true,
      name: values?.name || "",
      email: values?.email || "",
      phoneNumber: values?.phoneNumber || "",
      title: values?.titleSelected?.map((item) => item.key || "") || [],
      status: values?.statusSelected?.map((item) => Number(item.value)) || [],
      from: values?.from ? moment(values.from).format(DATE_FORMAT) : "",
      to: values?.to ? moment(values.to).format(DATE_FORMAT) : "",
    });
  };

  const validateTime = (from?: string, to?: string): boolean => {
    return !from || !to || moment(from).isSameOrBefore(moment(to));
  };

  const onChangeTextSearch = (e: any): void => {
    timeOut.current = setTimeout(() => {
      setValuesSearch({
        currentPage: 1,
        pageSize: 20,
        textSearch: e.target.value?.trim() || "",
        isAdvanceSearch: false,
      });
    }, deadTimeFastSearch);
  };

  const handelCloseModalForm = (): void => {
    setIsOpenFormEmployee(false);
    setIdEmployee(-1);
  };

  function contentFilterAdvance(values: IFormFilter): JSX.Element {
    return (
      <div className="content-filter-advance">
        <Row className="flex items-center justify-between mb-4">
          <span className="title-filter">Tất cả bộ lọc</span>
          <AppButton
            classrow="btn-close-popover"
            typebutton="normal"
            onClick={(): void => setIsShowFilterAdvance(false)}
          >
            <Icon icon="close-circle-line" size={20} />
          </AppButton>
        </Row>
        <TextInput
          containerclassname="mt-2"
          label="Họ và tên"
          name="name"
          value={values.name}
          free={!values.name}
        />
        <TextInput
          containerclassname="mt-2"
          label="Email"
          name="email"
          value={values.email}
          free={!values.email}
        />
        <TextInput
          containerclassname="mt-2"
          label="Số điện thoại"
          name="phoneNumber"
          value={values.phoneNumber}
          free={!values.phoneNumber}
          isphonenumber
        />
        <SelectInput
          containerclassname="mt-2"
          name="titleSelected"
          labelselect="Chức danh"
          data={optionTitle}
          value={values.titleSelected}
          free={values?.titleSelected?.length === 0}
          allowClear
          mode="multiple"
        />
        <SelectInput
          containerclassname="mt-2"
          name="statusSelected"
          labelselect="Trạng thái"
          data={statusEmployee}
          value={values.statusSelected}
          free={values?.statusSelected?.length === 0}
          allowClear
          mode="multiple"
        />
        <div className="mt-2 text16 font-bold">Ngày tạo</div>
        <Row className="mt-2 div-time">
          <AppDatePicker
            name="from"
            label="Từ"
            format={DATE_FORMAT}
            free={!values.from}
            status={!validateTime(values?.from, values?.to) ? "error" : ""}
            allowClear
          />
          <AppDatePicker
            name="to"
            label="Đến"
            format={DATE_FORMAT}
            free={!values.to}
            status={!validateTime(values?.from, values?.to) ? "error" : ""}
            allowClear
          />
        </Row>
        <Row className="mt-6 div-time">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={resetData}
          />
          <AppButton
            label="Tìm kiếm"
            typebutton="primary"
            onClick={onClickSearch}
          />
        </Row>
      </div>
    );
  }

  const columns: ColumnsType<IUsersRecoPaging> = [
    {
      title: "Họ và tên",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "name",
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
    },
    {
      title: "Chức danh",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (_, item: IUsersRecoPaging) => (
        <span
          className="status"
          style={{backgroundColor: getStatusEmployee(item?.statusId).color}}
        >
          {getStatusEmployee(item?.statusId).label}
        </span>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      render: (_, item: IUsersRecoPaging) => (
        <span>
          {item.createdDate ? moment(item.createdDate).format(DATE_FORMAT) : ""}
        </span>
      ),
    },
  ];

  return (
    <div className="container-manager-employee">
      <Formik
        initialValues={{} as IFormFilter}
        onSubmit={(): void => {
          //
        }}
        innerRef={filterAdvancedRef}
      >
        {({values}): JSX.Element => {
          return (
            <Row className="justify-between items-center">
              <TextInput
                containerclassname="w-1/2"
                label="Tìm kiếm nhanh"
                name="textSearch"
                value={values.textSearch}
                onChange={onChangeTextSearch}
                placeholder="Nhập họ tên, email, số điện thoại"
              />

              <Row className="items-center">
                <AppButton
                  typebutton="normal"
                  classrow="add-btn"
                  onClick={(): void => setIsOpenFormEmployee(true)}
                >
                  <Icon icon="add-line" size={16} />
                  <div className="title mx-3">Tạo nhân viên</div>
                </AppButton>
                <Popover
                  className="mr-1"
                  placement="bottom"
                  trigger="click"
                  content={(): React.ReactNode => contentFilterAdvance(values)}
                  open={isShowFilterAdvance}
                  onOpenChange={setIsShowFilterAdvance}
                >
                  <AppButton typebutton="normal" classrow="btn-filter">
                    <Icon
                      className="mr-1"
                      icon="filter-line"
                      size={12}
                      color="#324054"
                    />
                    Tìm kiếm nâng cao
                  </AppButton>
                </Popover>
              </Row>
            </Row>
          );
        }}
      </Formik>
      <div className="mt-6">
        <AppTable
          rowClassName="cursor-pointer"
          dataSource={getListEmployee.data?.usersRecoPaging?.map(
            (item, index) => ({...item, key: index})
          )}
          columns={columns}
          loading={getListEmployee.isLoading}
          scroll={{y: "52vh"}}
          onRow={(item: IUsersRecoPaging): any => {
            return {
              onClick: (): void => {
                setIdEmployee(item.userId);
              },
            };
          }}
        />
      </div>
      <AppPagination
        className="mt-6"
        defaultPageSize={valuesSearch.pageSize}
        current={valuesSearch.currentPage}
        pageSize={valuesSearch.pageSize}
        total={getListEmployee.data?.totalCount}
        onChange={handlePagination}
      />
      <ModalDetailEmployee
        employeeId={idEmployee}
        handleClose={(): void => setIdEmployee(-1)}
        getListEmployee={getListEmployee}
      />
      <ModalFormEmployee
        open={isOpenFormEmployee}
        onClose={handelCloseModalForm}
        id={idEmployee}
        getListEmployee={getListEmployee}
      />
    </div>
  );
}
