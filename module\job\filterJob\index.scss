.container-filter-advance {
  width: 100%;
  justify-content: space-between;
  //background: $border-color;

  .filter-container {
    color: $text-color-input;
    display: flex;
    justify-content: flex-end;

    button {
      font-size: 12px;
      font-weight: 400;
      border: 1px dashed black;
      border-radius: 8px;
      display: flex;
      align-items: baseline;
      align-self: center;
      color: $text-color-input;
      height: auto;
    }
    button:hover,
    button:focus {
      background: none;
      color: $text-color-input;
      border: 1px dashed black;
    }

    .div-time {
      display: grid;
      gap: 8px;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .button-search {
      border-radius: 12px;
      color: white;
      background: #2f6bff;
    }

    .button-delete {
      border-radius: 12px;
      color: $primary-color;
      background: $secondary-color;
    }
  }
}

.filter-content {
  background-color: $white-color;
  border-radius: 16px;
  width: 312px;

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .btn-close-popover {
    button {
      border: none;
    }
  }
}

.check-box {
  .ant-checkbox .ant-checkbox-inner {
    height: 15px;
    width: 15px;

    &::after {
      left: 11%;
      top: 48%;
    }
  }
}
