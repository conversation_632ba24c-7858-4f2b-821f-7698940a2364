import {fetcher} from "./Fetcher";

const path = {
  getJds: "/api/jd/get-all",
  createJd: "/api/jd/create",
  getDetailJd: (id: number): string => `/api/jd/detail/${id}`,
  evaluationOfCandidate: "/api/aiSuggest/getEvaluation",
  evaluateCandidate: "/api/aiSuggest/saveEvaluation",
};

export type JD = {
  jobDescriptionId: number;
  title: string;
};

export type DetailJd = {
  jobDescriptionId: number;
  title: string;
  tags: Array<string>;
  responsibilities: Array<string>;
  requirements: Array<string>;
  language: string;
};

export type DataCreateJd = {
  tags: Array<string>;
  language: string;
};

export const createJd = (data: DataCreateJd): Promise<DetailJd> => {
  return fetcher({
    url: path.createJd,
    method: "post",
    data: data,
  });
};

export const getJd = (id: number): Promise<DetailJd> => {
  return fetcher({
    url: path.getDetailJd(id),
    method: "get",
  });
};

export const getJds = (): Promise<Array<JD>> => {
  return fetcher({
    url: path.getJds,
    method: "get",
  });
};

interface EvaluationOfCandidateRequest {
  candidateId: string;
  requestJobId: number;
}

export interface EvaluationOfCandidateData
  extends EvaluationOfCandidateRequest {
  isSuitable: boolean;
  note: string;
}

export const getEvaluationOfCandidate = (
  data: EvaluationOfCandidateRequest
): Promise<EvaluationOfCandidateData> => {
  return fetcher({
    url: path.evaluationOfCandidate,
    method: "post",
    data,
  });
};

export const saveEvaluateCandidate = (data: EvaluationOfCandidateData) => {
  return fetcher({
    url: path.evaluateCandidate,
    method: "post",
    data,
  });
};
