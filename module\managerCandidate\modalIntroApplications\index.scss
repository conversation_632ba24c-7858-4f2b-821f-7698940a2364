.container-modal-intro-application {
  .footer-modal-intro-application {
    box-shadow: 0px -4px 4px $shadow;

    .btn-intro-application {
      .ant-btn[disabled] {
        opacity: 0.5;
        color: $white-color;
        background-color: $primary-color;
      }
    }

    .btn-cancel {
      button {
        background-color: $status-reject;
        color: $white-color;

        &:focus {
          background-color: $status-reject;
          color: $white-color;
        }
      }
    }
  }

  .ant-pagination-jump-prev
    .ant-pagination-item-container
    .ant-pagination-item-ellipsis,
  .ant-pagination-jump-next
    .ant-pagination-item-container
    .ant-pagination-item-ellipsis {
    top: auto;
    right: auto;
  }
}
