# Gửi dữ liệu lên backend

Base sẽ sử dụng react-query để gửi dữ liệu lên backend dùng useMutation

```tsx
import {useMutation} from "react-query";
import ApiUser, {ILoginBody} from "@app/api/ApiUser";  // Import dưới dạng sau thay vì import thẳng những hàm gọi API cần dùng

export function SignIn({changeTab}: SignInProps): JSX.Element {
    const dispatch = useDispatch();
    const router = useRouter();
    const loginMutation = useMutation(ApiUser.login); // Khởi tạo mutation

    const handleLogin = (
        values: ILoginBody,
        {setSubmitting}: {setSubmitting: (isSubmitting: boolean) => void}
    ): void => {
        loginMutation.mutate( // Gọi API
            {email: values.email, password: values.password},  // Tham số truyền đi theo API quy định
            {
                onSuccess: (res: IAccountInfo) => {
                  // Viết hàm xử lý sau khi API gọi thành công ở đây
                  dispatch(loginUser({...res}));
                  router.push(Config.PATHNAME.HOME);
                  setSubmitting(false);
                },
                onError: (error) => {
                  // Viết hàm xử lý sau khi API trả về lỗi ở đây
                  setSubmitting(false);
                },
            }
        );
    };

    return (
        <div>
            ...
        </div>
    );
}

```
