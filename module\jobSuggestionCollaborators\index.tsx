import "./index.scss";
import React, {useEffect, useMemo, useRef, useState} from "react";
import {Col, Input, List, Row, notification} from "antd";
import {useMutation, useQuery} from "react-query";
import {CardViewJob} from "@app/components/CardViewJob";
import ApiJob, {IJobObject} from "@app/api/ApiJob";
import SearchFilter, {
  CheckboxAllProps,
  initialValuesFilterJob,
} from "./SearchFilter";
import AppButton from "@app/components/AppButton";
import ApiRequestJob, {
  IInfoPartner,
  IParamGetPartner,
  IParamSendJobToPartner,
} from "@app/api/ApiRequestJob";
import {CardInfoPartner} from "./CardInfoPartner";
import AppCheckBox from "@app/components/AppCheckbox";
import AppModal from "@app/components/AppModal";
import {Formik, FormikProps} from "formik";
import {TextInput} from "@app/components/TextInput";
import ApiCollaborator from "@app/api/ApiCollaborator";
import config from "@app/config";
import {useRouter} from "next/router";
import {inviteBonus} from "@app/utils/constants/function";

interface InfoMail {
  titleMail?: string;
}

export default function JobSuggestion(): JSX.Element {
  const [idJobs, setIdJobs] = useState<Array<number>>([]);
  const [idPartners, setIdPartners] = useState<Array<number>>([]);
  const [jobData, setJobData] = useState<Array<IJobObject>>([]);
  const [partnerData, setPartnerData] = useState<Array<IInfoPartner>>([]);
  const pageSize = 20;
  const [valuesJobFilter, setValuesJobFilter] = useState(
    initialValuesFilterJob
  );
  const [valuesPartnerFilter, setValuesPartnerFilter] = useState(
    initialValuesFilterJob
  );
  const jobContainerRef = useRef<any>(null);
  const bottomJobRef = useRef<any>(null);
  const partnerContainerRef = useRef<any>(null);
  const bottomPartnerRef = useRef<any>(null);
  const timeOut = useRef<any>();
  const partnerLength = useRef<number>(0);
  const [isReloadPartners, setIsReloadPartners] = useState<boolean>(false);
  const [contentMail, setContentMail] = useState<string>("");
  const [isVisibleEditContentMail, setIsVisibleEditContentMail] =
    useState<boolean>(false);
  const refInfoMail = useRef<FormikProps<InfoMail>>(null);
  const initialValueMail: InfoMail = {
    titleMail: "",
  };
  const {TextArea} = Input;
  const router = useRouter();

  const changeContentMail = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ): void => {
    setContentMail(e.target.value);
  };

  const handleLoadMoreJob = (): void => {
    setValuesJobFilter({
      ...valuesJobFilter,
      currentPageJob: valuesJobFilter.currentPageJob + 1,
    });
  };

  const handleLoadMorePartner = (): void => {
    setIsReloadPartners(false);
    setValuesPartnerFilter({
      ...valuesPartnerFilter,
      currentPagePartner: Math.floor(partnerLength.current / 20) + 1,
      pageSizePartner: 20,
    });
  };

  const sendJobInfoToPartnerMail = useMutation(
    (data: IParamSendJobToPartner) => {
      return ApiRequestJob.sendJobInfoToPartnerMail(data);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Đã gửi email thành công",
        });
        onCloseModalEditMail();
      },
    }
  );

  const handleSendMail = (): void => {
    if (!idJobs.length || !idPartners.length) return;
    const valueInfoMail = refInfoMail?.current?.values;
    const param: IParamSendJobToPartner = {
      requestJobIds: idJobs,
      partnerIds: idPartners,
      title: valueInfoMail?.titleMail,
      content: contentMail,
    };
    sendJobInfoToPartnerMail.mutate(param);
  };

  const jobList = useQuery(
    ["jobList", valuesJobFilter],
    () => {
      const valueSearch = {
        currentPage: valuesJobFilter.currentPageJob,
        pageSize: valuesJobFilter.pageSizeJob,
        isExternal: true,
        textSearch: valuesJobFilter.jobSearch.trim() ?? "",
      };
      return ApiJob.getListJob(valueSearch as any);
    },
    {
      onSuccess: (data) => {
        setJobData((pre) => [...pre, ...data.jobs]);
      },
    }
  );

  const partnersList = useQuery(
    ["getListPartner", valuesPartnerFilter],
    () => {
      const valueSearch: IParamGetPartner = {
        currentPage: valuesPartnerFilter.currentPagePartner || 1,
        pageSize: valuesPartnerFilter.pageSizePartner,
        textSearch: valuesPartnerFilter.partnersSearch.trim() ?? "",
        tags: valuesPartnerFilter.partnerTags ?? [],
      };
      return ApiRequestJob.getListPartners(valueSearch);
    },
    {
      onSuccess: (data) => {
        if (data?.partnersPaging && !isReloadPartners) {
          setPartnerData((pre) => [...pre, ...data.partnersPaging]);
        }
        if (data?.partnersPaging && isReloadPartners) {
          setPartnerData(data.partnersPaging);
        }
      },
    }
  );

  useEffect(() => {
    partnerLength.current = partnerData.length || 20;
  }, [partnerData]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setValuesPartnerFilter((preValue) => ({
        ...preValue,
        currentPagePartner: 1,
        pageSizePartner:
          partnerLength.current > pageSize ? partnerLength.current : pageSize,
      }));
      setIsReloadPartners(true);
      partnersList.refetch();
    }, 15000);
    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    const handleScrollJob = () => {
      const targetElement: any = bottomJobRef.current;
      if (!targetElement) return;
      const rect = targetElement.getBoundingClientRect();
      const isElementVisible = rect.top <= window.innerHeight;
      if (isElementVisible) {
        handleLoadMoreJob();
      }
    };
    jobContainerRef.current?.addEventListener("scroll", handleScrollJob);
    return () => {
      jobContainerRef.current?.removeEventListener("scroll", handleScrollJob);
    };
  }, [jobList]);

  useEffect(() => {
    const handleScrollPartner = () => {
      const targetElement: any = bottomPartnerRef.current;
      if (!targetElement) return;
      const rect = targetElement.getBoundingClientRect();
      const isElementVisible = rect.top <= window.innerHeight;
      if (isElementVisible) {
        // eslint-disable-next-line no-unused-expressions
        timeOut.current && clearTimeout(timeOut.current);
        timeOut.current = setTimeout(() => {
          handleLoadMorePartner();
        }, 150);
      }
    };
    partnerContainerRef.current?.addEventListener(
      "scroll",
      handleScrollPartner
    );
    return () => {
      partnerContainerRef.current?.removeEventListener(
        "scroll",
        handleScrollPartner
      );
    };
  }, [partnersList]);

  const handleChooseJobs = (idJob: number, checked: boolean): void => {
    if (checked) {
      setIdJobs((preIds) => [...preIds, idJob]);
    } else {
      setIdJobs((preIds) => preIds.filter((id) => id !== idJob));
    }
  };

  const handleChoosePartners = (idPartners: number, checked: boolean): void => {
    if (checked) {
      setIdPartners((preIds) => [...preIds, idPartners]);
    } else {
      setIdPartners((preIds) => preIds.filter((id) => id !== idPartners));
    }
  };

  const handleChooseAllItem = (
    value: CheckboxAllProps,
    isJob: boolean
  ): void => {
    if (value.isAllJobs && isJob) {
      setIdJobs(jobData.map((job) => job.requestJobId));
    } else if (!value.isAllJobs && isJob) {
      setIdJobs([]);
    }
    if (value.isAllPartners && !isJob) {
      setIdPartners(partnerData.map((partner) => partner.partnerId));
    } else if (!value.isAllPartners && !isJob) {
      setIdPartners([]);
    }
  };

  const isShowMoreJob = useMemo(
    () =>
      !jobList.isFetching &&
      jobList.data?.jobs &&
      jobData.length >= 20 &&
      jobList.data?.totalJob > jobData.length,
    [jobList, jobData]
  );

  const isShowMorePartner = useMemo(
    () =>
      !partnersList.isFetching &&
      partnersList.data?.partnersPaging &&
      partnerData.length >= 20 &&
      partnersList.data?.totalCount > partnerData.length,
    [partnersList, partnerData]
  );

  const onCloseModalEditMail = (): void => {
    setIsVisibleEditContentMail(false);
    refInfoMail.current?.resetForm();
    setContentMail("");
  };

  const getAllTagsOfCollaborator = useQuery(
    ["getAllTagsOfCollaborator"],
    () => {
      return ApiCollaborator.getAllTagsOfCollaborator();
    }
  );

  const onClickItemJob = (idJob: number): void => {
    router.push(`${config.PATHNAME.JOB_DETAIL}?id=${idJob}`);
  };

  const handleCopyClipboardJobsChecked = () => {
    const idJobsSet = new Set(idJobs);

    const jobsChecked = jobData.filter((job) =>
      idJobsSet.has(job.requestJobId)
    );

    const textResult = jobsChecked
      .map((job) => {
        const experience =
          job?.experienceYearFrom > 0
            ? `Từ ${job?.experienceYearFrom} năm kinh nghiệm`
            : "Không yêu cầu kinh nghiệm ";

        return `[${job?.workAddress}] ${job?.name}\n${experience}\nLương: ${
          job?.salary
        }\nThưởng: ${inviteBonus(
          job?.partnerRateType,
          job?.partnerRateValue,
          job?.partnerCurrencyType
        )}\nhttps://app.reco-vn.com/job-public/${job?.requestJobId}/detail\n`;
      })
      .join("\n");
    navigator.clipboard.writeText(textResult);
  };

  return (
    <div className="job-suggestion-container">
      <Row justify="space-evenly">
        <Col span={15} className="list-job-suggestion">
          <SearchFilter
            updateValueFilter={(value): void => {
              setJobData([]);
              setValuesJobFilter(value as any);
            }}
            typeFilter="jobs"
            jobAmount={idJobs.length}
            checkedAllValues={{
              isAllJobs: !!(jobData.length && idJobs.length === jobData.length),
              isAllPartners: !!(
                partnerData.length && idPartners.length === partnerData.length
              ),
            }}
            updateCheckboxAll={handleChooseAllItem}
            onCopyClipboardJobsChecked={handleCopyClipboardJobsChecked}
          />
          <div className="list-job-suggestion-container" ref={jobContainerRef}>
            <List
              loading={jobList.isFetching}
              grid={{
                gutter: 16,
                xs: 1,
                sm: 1,
                md: 1,
                lg: 2,
                xl: 2,
                xxl: 2,
              }}
              dataSource={jobData ?? []}
              renderItem={(item) => (
                <List.Item>
                  <CardViewJob
                    requestJob={item}
                    onCheckBoxSuggestion={handleChooseJobs}
                    checked={idJobs.includes(item.requestJobId)}
                    isJobSuggestionsPartner
                    onClick={() => onClickItemJob(item.requestJobId)}
                  />
                </List.Item>
              )}
            />
            {isShowMoreJob && <div ref={bottomJobRef} />}
          </div>
        </Col>

        <Col span={8} className="list-partner">
          <SearchFilter
            updateValueFilter={(value): void => {
              setPartnerData([]);
              setIsReloadPartners(false);
              setValuesPartnerFilter(value as any);
            }}
            typeFilter="partners"
            partnerAmount={idPartners.length}
            checkedAllValues={{
              isAllPartners: !!(
                partnerData.length && idPartners.length === partnerData.length
              ),
              isAllJobs: !!(jobData.length && idJobs.length === jobData.length),
            }}
            updateCheckboxAll={handleChooseAllItem}
            tagsOfCollaborator={getAllTagsOfCollaborator?.data}
          />
          <div className="list-partner-container relative">
            <div
              className="list-partner-container__content"
              ref={partnerContainerRef}
            >
              <List
                loading={partnersList.isFetching}
                grid={{
                  gutter: 16,
                  xs: 1,
                  sm: 1,
                  md: 1,
                  lg: 1,
                  xl: 1,
                  xxl: 1,
                }}
                dataSource={partnerData ?? []}
                renderItem={(item) => (
                  <List.Item>
                    <CardInfoPartner
                      partnerInfo={item}
                      checked={idPartners.includes(item.partnerId)}
                      onCheckBox={handleChoosePartners}
                    />
                  </List.Item>
                )}
              />
              {isShowMorePartner && <div ref={bottomPartnerRef} />}
            </div>
            <div className="absolute bottom-0 left-0 bg-white w-[97%] p-4 pb-0 shadow-button-mail">
              <AppCheckBox
                className="mb-1"
                checked={isVisibleEditContentMail}
                disabled={!idJobs.length || !idPartners.length}
                onChange={(e): any => {
                  if (e.target.checked) {
                    setIsVisibleEditContentMail(true);
                  } else {
                    setIsVisibleEditContentMail(false);
                  }
                }}
              >
                Gửi kèm nội dung
              </AppCheckBox>
              <AppButton
                typebutton="primary"
                classrow="justify-center rounded-md w-4/5 mx-auto"
                onClick={handleSendMail}
                disabled={!idJobs.length || !idPartners.length}
              >
                Gửi email
              </AppButton>
            </div>
          </div>
        </Col>
      </Row>

      <AppModal
        title="Thông tin email"
        open={isVisibleEditContentMail}
        onOk={handleSendMail}
        footer={null}
        onCancel={onCloseModalEditMail}
        centered
      >
        <Formik
          innerRef={refInfoMail}
          initialValues={initialValueMail}
          onSubmit={(): void => {
            //
          }}
        >
          {({values}): JSX.Element => {
            return (
              <div className="text-color-primary px-4">
                <Row className="justify-center mt-1">
                  <TextInput
                    containerclassname="flex-1 mb-2"
                    label="Tiêu đề"
                    name="titleMail"
                    value={values.titleMail}
                    free={!values?.titleMail}
                    maxLength={50}
                  />
                </Row>
                <Row className="justify-center mt-1">
                  <TextArea
                    name="contentMail"
                    maxLength={1000}
                    className="textarea-note"
                    style={{height: 110, resize: "none"}}
                    placeholder="Nội dung"
                    value={contentMail}
                    onChange={changeContentMail}
                  />
                </Row>
                <Row className="flex justify-center items-center mt-5">
                  <AppButton
                    classrow="mr-2 w-36 btn-cancel"
                    label="Hủy"
                    typebutton="secondary"
                    onClick={onCloseModalEditMail}
                  />
                  <AppButton
                    classrow="ml-2 w-36 btn-edit"
                    label="Gửi email"
                    typebutton="primary"
                    onClick={handleSendMail}
                  />
                </Row>
              </div>
            );
          }}
        </Formik>
      </AppModal>
    </div>
  );
}
