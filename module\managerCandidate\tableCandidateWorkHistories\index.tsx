import {
  InputNumber,
  Form,
  Input,
  Popconfirm,
  Typography,
  DatePicker,
  Tooltip,
  Row,
} from "antd";
import React, {useEffect, useState} from "react";
import Icon from "@app/components/Icon/Icon";
import "./index.scss";
// eslint-disable-next-line import/namespace
import {IEducations, IWorkHistories} from "@app/api/ApiCandidate";
// eslint-disable-next-line import/no-cycle
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import {formatTimeToTableEdit} from "@app/utils/constants/function";
import {useDispatch, useSelector} from "react-redux";
import {IRootState} from "@app/redux/store";
import {setStateTable} from "@app/redux/slices/EditTableSlice";
import AppTable from "@app/components/AppTable";

const {RangePicker} = DatePicker;

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  inputType: "number" | "text" | "date";
  index: number;
  children: React.ReactNode;
  record: IEducations;
}

interface Prop {
  dataSource: IWorkHistories[];
  updateDataCandidateWorkHistories?: (data: IWorkHistories[]) => void;
  experienceString?: string;
  isVisibleModal?: boolean;
  isDisabledAddRow?: boolean;
}

// eslint-disable-next-line react/function-component-definition
const TableCandidateWorkHistories: React.FC<Prop> = ({...props}) => {
  const {
    dataSource,
    updateDataCandidateWorkHistories,
    experienceString,
    isDisabledAddRow,
  } = props;
  const [form] = Form.useForm();
  const [data, setData] = useState<IWorkHistories[]>([]);
  const [count, setCount] = useState(data.length);
  const stateTable = useSelector((state: IRootState) => state.editTableSlice);
  const dispatch = useDispatch();

  const onChangeDate = (dates: any) => {
    form.setFieldValue("timeWorkHistories", dates);
  };

  useEffect(() => {
    if (dataSource && dataSource?.length > 0) {
      const newData = dataSource?.map((item, index) => ({
        ...item,
        startDate: item?.startDate ? item?.startDate : "",
        endDate: item?.endDate ? item?.endDate : "",
        key: String(index),
      }));
      setData(newData as IWorkHistories[]);
      setCount(newData?.length);
    } else {
      setData([]);
      setCount(0);
    }
  }, [dataSource]);

  // eslint-disable-next-line react/no-unstable-nested-components, react/function-component-definition
  const EditableCell: React.FC<EditableCellProps> = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "number" ? (
        <Form.Item name={dataIndex}>
          <InputNumber />
        </Form.Item>
      ) : inputType === "date" ? (
        <Form.Item name={dataIndex}>
          <RangePicker
            format={DATE_FORMAT}
            onChange={onChangeDate}
            disabledDate={(current) => {
              return current && current > moment().endOf("day");
            }}
          />
        </Form.Item>
      ) : (
        <Form.Item name={dataIndex}>
          <Input />
        </Form.Item>
      );

    return <td {...restProps}>{editing ? inputNode : children}</td>;
  };

  const isEditing = (record: IWorkHistories) =>
    record.key === stateTable.editingKeyWorkHistories;

  const edit = (record: IWorkHistories) => {
    if (record) {
      const initialValueForm = {
        ...record,
        timeWorkHistories: [
          formatTimeToTableEdit(record?.startDate ? record?.startDate : ""),
          formatTimeToTableEdit(record?.endDate ? record?.endDate : ""),
        ],
      };
      form.setFieldsValue(initialValueForm);
      dispatch(
        setStateTable({
          ...stateTable,
          editingKeyWorkHistories: String(record?.key),
        })
      );
    }
  };

  const cancel = (key: string) => {
    if (
      !(
        data[Number(key)]?.companyName &&
        data[Number(key)]?.position &&
        data[Number(key)]?.startDate &&
        data[Number(key)]?.endDate
      )
    ) {
      handleDelete(key);
    }
    dispatch(
      setStateTable({
        ...stateTable,
        editingKeyWorkHistories: "",
        isAddRowWorkHistories: false,
      })
    );
  };

  const handleDelete = (key: string) => {
    const newData: IWorkHistories[] = data.filter((item: IWorkHistories) => {
      return item.key !== key;
    });
    setData(newData);
    setCount(newData?.length || 0);
    updateDataCandidateWorkHistories?.(newData);
  };

  const handleAdd = () => {
    const newData = {
      key: count.toString(),
      position: "",
      companyName: "",
      startDate: "",
      endDate: "",
    };
    setData([...data, newData]);
    updateDataCandidateWorkHistories?.([...data, newData]);
    setCount(count + 1);
    edit(newData);
    setStateTable({
      ...stateTable,
      isAddRowWorkHistories: true,
    });
  };

  const save = async (key: React.Key) => {
    try {
      const row = await form.validateFields();
      const dataChange: IWorkHistories = {
        startDate:
          row?.timeWorkHistories && row?.timeWorkHistories[0]
            ? row?.timeWorkHistories[0]?.format(DATE_FORMAT)
            : "",
        endDate:
          row?.timeWorkHistories && row?.timeWorkHistories[1]
            ? row?.timeWorkHistories[1]?.format(DATE_FORMAT)
            : "",
        key: String(key),
        companyName: row?.companyName ? row?.companyName : "",
        position: row?.position ? row?.position : "",
      };

      const newData = [...data];
      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, {
          ...item,
          ...dataChange,
        });
        setData(newData as IWorkHistories[]);
      } else {
        newData.push(dataChange);
        setData(newData as IWorkHistories[]);
      }
      updateDataCandidateWorkHistories?.(newData);
      dispatch(
        setStateTable({
          ...stateTable,
          isAddRowWorkHistories: false,
          editingKeyWorkHistories: "",
        })
      );
      form.resetFields();
    } catch (errInfo) {
      //
    }
  };

  const columnsWorkHistories = [
    {
      title: "Tên công ty",
      dataIndex: "companyName",
      width: "30%",
      editable: true,
      ellipsis: true,
      render: (_: string, record: IWorkHistories) => {
        return record?.companyName ? (
          <Tooltip title={record?.companyName} placement="bottomLeft">
            <span>{record?.companyName}</span>
          </Tooltip>
        ) : (
          <span />
        );
      },
    },
    {
      title: "Vị trí",
      dataIndex: "position",
      width: "25%",
      editable: true,
      ellipsis: true,
      render: (_: string, record: IWorkHistories) => {
        return record?.position ? (
          <Tooltip title={record?.position} placement="bottomLeft">
            <span>{record?.position}</span>
          </Tooltip>
        ) : (
          <span />
        );
      },
    },
    {
      title: "Thời gian",
      dataIndex: "timeWorkHistories",
      width: "35%",
      editable: true,
      ellipsis: true,
      render: (_: any, record: IWorkHistories) => {
        const timeDisplay = (time: string): string => {
          if (!time) return "";
          const timeSplit = time.split("/");
          timeSplit.shift();
          return timeSplit.join("/");
        };
        return (
          <span>
            {record?.startDate && record?.endDate
              ? `${timeDisplay(record?.startDate)} - ${timeDisplay(
                  record?.endDate
                )}`
              : ""}
          </span>
        );
      },
    },
    {
      title: "",
      dataIndex: "operation",
      width: "10%",
      render: (_: any, record: IWorkHistories) => {
        const editable = isEditing(record);
        return editable ? (
          <span>
            <Typography.Link
              onClick={() => save(record.key as any)}
              style={{marginRight: 8}}
            >
              <span className="cursor-pointer">
                <Icon icon="check-line" size={12} />
              </span>
            </Typography.Link>
            <Popconfirm
              title="Sure to cancel?"
              onConfirm={() => cancel(record.key as any)}
            >
              <span className="cursor-pointer">
                <Icon icon="close-line" size={12} />
              </span>
            </Popconfirm>
          </span>
        ) : (
          <div className="flex items-center justify-center">
            <div className="mr-4">
              <Typography.Link
                disabled={
                  stateTable.editingKeyWorkHistories !== "" || isDisabledAddRow
                }
                onClick={() => handleDelete(record.key || "")}
              >
                <Icon icon="delete-bin-6-line" size={12} />
              </Typography.Link>
            </div>
            <div>
              <Typography.Link
                disabled={
                  stateTable.editingKeyWorkHistories !== "" || isDisabledAddRow
                }
                onClick={() => {
                  edit(record as any);
                }}
              >
                <Icon icon="edit-line" size={12} />
              </Typography.Link>
            </div>
          </div>
        );
      },
    },
  ];

  const mergedColumns = columnsWorkHistories.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: IWorkHistories) => ({
        record,
        inputType:
          col.dataIndex === ""
            ? "number"
            : col.dataIndex === "timeWorkHistories"
            ? "date"
            : "text",
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  return (
    <Form form={form} component={false}>
      <Row className="text16 font-bold mt-4 mb-1 items-baseline">
        Kinh nghiệm{" "}
        {experienceString && (
          <span className="text16">({experienceString})</span>
        )}
        {!(
          stateTable.isAddRowWorkHistories || stateTable.editingKeyWorkHistories
        ) &&
          !isDisabledAddRow && (
            // eslint-disable-next-line jsx-a11y/no-static-element-interactions
            <span onClick={handleAdd} className="ml-2 cursor-pointer flex">
              <Icon icon="user-add-line" size={14} />
            </span>
          )}
      </Row>
      <AppTable
        className="table-work-education"
        pagination={false}
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        bordered
        dataSource={data ?? []}
        columns={mergedColumns ?? []}
        rowClassName="editable-row"
        rowKey="key"
      />
    </Form>
  );
};

export default React.memo(TableCandidateWorkHistories);
