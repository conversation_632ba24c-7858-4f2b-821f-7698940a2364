import {IAccountRole} from "@app/types";
import {fetcher} from "./Fetcher";
import {IRequestJob} from "./ApiRequestJob";

export interface IParamsListCustomer {
  isAdvanceSearch?: boolean;
  isFirstInitialization?: boolean;
  pageSize?: number;
  currentPage: number;
  textSearch?: string;
  name?: string;
  companyTypes?: string[];
  customerId?: string;
  customerStatuses?: string[];
  managers?: string[];
}

export interface ICustomer {
  assignTo: number;
  companyType: string;
  contact: string;
  customerId: string;
  customerStatus: number;
  deleteFlag: boolean;
  email: string;
  groupId: number;
  manager: string;
  managerId: number;
  managerName: string;
  name: string;
  phone: string;
  address: string;
  isOwner: boolean;
}

interface IManagerFilters {
  email: string;
  image: string;
  name: string;
  phoneNumber: string;
  role: IAccountRole[];
  roleId: string;
  roleList: string;
  roleUsers: string;
  userGroupId: number;
  userId: number;
}

export interface IResCustomer {
  companyTypes: string[];
  currentPage: number;
  customerId: string;
  customerStatuses: string[];
  companyTypeFilters: {id: number; name: string}[];
  groups: string[];
  hasNext: boolean;
  hasPrevious: boolean;
  isAdvanceSearch: boolean;
  isFirstInitialization: boolean;
  listRole: string[];
  managers: string[];
  name: string;
  pageSize: number;
  role: string;
  textSearch: string;
  totalCount: number;
  totalPages: number;
  userGroupFilters: {
    name: string;
    roleTypeId: number;
    userGroupId: number;
  }[];
  userId: number;
  customersPaging: ICustomer[];
  managerFilters: IManagerFilters[];
}

export interface ICustomerDetail {
  address: string;
  assignName: string;
  assignTo: number;
  benefit: string;
  companyType: string;
  contact: string;
  country: string;
  countryName: string;
  createdBy: number;
  createdDate: string;
  customerId: string;
  customerStatus: number;
  customerStatusName: string;
  email: string;
  field: string;
  interviewProcess: string;
  name: string;
  note: string;
  phone: string;
  profile: string;
  size: string;
  tags: string;
  website: string;
  workingTime: string;
  isOwner: boolean;
  rank: string;
}
export interface IResInitialize {
  countries: {
    countryId: string;
    name: string;
    orderId: number;
    workLocations: any[];
  }[];
  customer: ICustomerDetail;
  managers: IManagerFilters[];
  user: IManagerFilters;
}

export interface IParamsCreateCustomer {
  assignTo: number;
  benefit: string;
  companyType: string;
  country: string;
  customerId: string;
  customerStatus: number;
  email: string;
  field: string;
  name: string;
  phone: string;
  profile: string;
  size: string;
  tags: string;
  address: string;
  contact: string;
  interviewProcess: string;
  note: string;
  website: string;
  workingTime: string;
}

export interface IMeetingMinute {
  id?: string;
  name: string;
  infor: string;
  customerID?: string;
  createdby: number;
  createdByName: string;
  updatedby: number;
  createdDate: string;
  updatedDate: string;
}

export interface IDataPostMeeting {
  id?: string;
  name?: string;
  infor: string;
  customerID: string;
}

const path = {
  filter: "/api/customer/filter",
  initialize: "/api/customer/initialize",
  getRequestJob: "/api/requestjob/getByCustomer",
  editCustomer: "api/customer/edit",
  createCustomer: "/api/customer/create",
  getListMeetingMinute: "/api/customer/getListInforCustomer",
  saveOrCreateMeetingMinute: "/api/customer/saveInfo",
  getDetailMeetingMinute: "/api/customer/getInfoDetail",
};

function getListCustomer(body?: IParamsListCustomer): Promise<IResCustomer> {
  return fetcher({url: path.filter, method: "post", data: body});
}

function getDetailCustomer(id?: string): Promise<IResInitialize> {
  return fetcher({
    url: id ? `${path.initialize}?customerId=${id}` : path.initialize,
    method: "get",
  });
}

function getRequestJob(id: string): Promise<IRequestJob[]> {
  return fetcher({
    url: path.getRequestJob,
    method: "get",
    params: {customerId: id},
  });
}

function editCustomer(data: ICustomerDetail): Promise<any> {
  return fetcher({
    url: path.editCustomer,
    method: "post",
    data,
  });
}

function createCustomer(data: IParamsCreateCustomer): Promise<any> {
  return fetcher({
    url: path.createCustomer,
    method: "post",
    data,
  });
}

function getListInfoMeetingMinute(id?: string): Promise<IMeetingMinute[]> {
  return fetcher({
    url: id
      ? `${path.getListMeetingMinute}?customerId=${id}`
      : path.getListMeetingMinute,
  });
}

function getDetailMeetingMinute(id: string): Promise<IMeetingMinute> {
  return fetcher({
    url: `${path.getDetailMeetingMinute}?id=${id}`,
  });
}

function saveOrCreateMeetingMinute(
  body: IDataPostMeeting
): Promise<IMeetingMinute[]> {
  return fetcher({
    url: path.saveOrCreateMeetingMinute,
    method: "post",
    data: body,
  });
}

export default {
  getListCustomer,
  getDetailCustomer,
  getRequestJob,
  editCustomer,
  createCustomer,
  getListInfoMeetingMinute,
  saveOrCreateMeetingMinute,
  getDetailMeetingMinute,
};
