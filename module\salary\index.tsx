import "./index.scss";
import React, {useRef} from "react";
import {Formik} from "formik";
import {TextInput} from "@app/components/TextInput";
import {Avatar, Card, List} from "antd";
import {BookOutlined, FilterOutlined} from "@ant-design/icons";
import {useQuery} from "react-query";
import ApiRequestJob from "@app/api/ApiRequestJob";
import {SelectInput} from "@app/components/SelectInput";
import config from "@app/config";
import {IMultiSelect} from "@app/types";
import {sortWorkLocation} from "@app/utils/constants/function";

interface MyFormValues {
  textSearch: string;
  workLocation: string;
  jobLabel: string;
}

const initialValues: MyFormValues = {
  textSearch: "",
  workLocation: "",
  jobLabel: "",
};

export function Salary(): JSX.Element {
  const searchParamsRef = useRef({});

  const handleSearch = (values: MyFormValues) => {
    searchParamsRef.current = values;
    requestJobList.refetch();
  };

  const getListRequestJob = () => {
    return ApiRequestJob.getListRequestJob({
      currentPage: 1,
      pageSize: 20,
      ...searchParamsRef.current,
    });
  };

  const requestJobList = useQuery("requestJobList", getListRequestJob, {
    // getNextPageParam: (lastPage) => {
    //   if (lastPage?.currentPage <= lastPage?.totalPages) {
    //     return lastPage.currentPage + 1;
    //   }
    //   return undefined;
    // },
    // getPreviousPageParam: (lastPage) => {
    //   if (lastPage?.currentPage > 1) {
    //     return lastPage.currentPage - 1;
    //   }
    //   return undefined;
    // },
  });

  const mapFilterData = (filters: IMultiSelect[]) =>
    filters.map((item) => ({
      value: item.id,
      label: item.label,
    }));

  return (
    <div>
      <Formik initialValues={initialValues} onSubmit={handleSearch}>
        {({values, handleChange, handleBlur, handleSubmit}): JSX.Element => (
          <form className="flex flex-col" onSubmit={handleSubmit}>
            <div className="container-sign-in flex w-full items-center justify-between p-1.5">
              <TextInput
                containerclassname="w-4/12"
                className="w-full"
                label="Tìm kiếm nhanh"
                name="textSearch"
                placeholder="Tìm kiếm theo việc làm, công ty, kỹ năng"
              />
              <SelectInput
                containerclassname="w-3/12"
                name="workLocation"
                labelselect="Địa điểm làm việc"
                data={sortWorkLocation(
                  mapFilterData(requestJobList.data?.workLocationFilters ?? [])
                )}
              />
              <SelectInput
                containerclassname="w-3/12"
                name="jobLabel"
                labelselect="Nhãn job"
                data={mapFilterData(requestJobList.data?.labelFilters ?? [])}
              />
              <div className="items-center flex ">
                <FilterOutlined />
                <span>Tìm kiếm nâng cao</span>
              </div>
            </div>
            <button type="submit">Tìm kiếm</button>
          </form>
        )}
      </Formik>

      <div>
        <List
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 2,
            lg: 2,
            xl: 3,
            xxl: 3,
          }}
          dataSource={requestJobList.data?.requestJobPagging ?? []}
          renderItem={(item) => (
            <List.Item>
              <Card>
                <div>
                  <div className="flex w-full justify-between">
                    <div className="flex items-center w-full">
                      <Avatar
                        size={80}
                        src={`${config.NETWORK_CONFIG.API_BASE_URL}/image/layout/header/logo-icon.svg`}
                      />
                      <div className="flex flex-col ml-2.5">
                        <span className="font-bold text-base my-1.5">
                          {item.positionName}
                        </span>
                        <span>
                          1000- 1200$ | {item.workLocationNameCombined}
                        </span>
                        <span>Số lượng tuyển 4 | Đang xử lý 2</span>
                        <span>
                          Thưởng giới thiệu{" "}
                          {item.partnerRateValue.replace(/\.\d+$/, "%")}
                        </span>
                      </div>
                    </div>
                    <BookOutlined />
                  </div>
                  <div>
                    <span>Test chữ rất dài</span>
                  </div>
                </div>
              </Card>
            </List.Item>
          )}
        />
      </div>
    </div>
  );
}
