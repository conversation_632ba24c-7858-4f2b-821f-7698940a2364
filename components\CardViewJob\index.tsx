import "./index.scss";

import React, {
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {Card, CardProps, Image, notification, Tooltip} from "antd";
import Icon from "@app/components/Icon/Icon";
import ApiJob, {IJobObject} from "@app/api/ApiJob";
import AppCheckBox from "../AppCheckbox";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import {formatMoney, inviteBonus} from "@app/utils/constants/function";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import {messageRemind} from "@app/utils/constants/message";
import config from "@app/config";
import {BREAK_POINT} from "@app/types";
import ApiUser from "@app/api/ApiUser";
import {useMediaQuery} from "react-responsive";
import classNames from "classnames";

interface CardViewJobProps extends CardProps {
  requestJob: IJobObject;
  isHasCheckBox?: boolean;
  onCheckBox?: (id: number | null) => void;
  onCheckBoxSuggestion?: (id: number, checked: boolean) => void;
  checked?: boolean;
  isJobSuggestionsPartner?: boolean;
}

export function CardViewJob(props: CardViewJobProps): JSX.Element {
  const {
    requestJob,
    isHasCheckBox,
    onCheckBox,
    checked,
    isJobSuggestionsPartner,
    onCheckBoxSuggestion,
  } = props;
  const [prevIsBookMark, setPrevIsBookMark] = useState(requestJob.isBookmark);
  const [isBookMark, setIsBookMark] = useState(requestJob.isBookmark);
  const timeOut = useRef<any>();
  const {user} = useSelector(selectUser);
  const isLogin = ApiUser.isLogin();
  const divFef = useRef<HTMLDivElement>(null);
  const aTagRef = useRef<HTMLAnchorElement>(null);
  // const isVisibleCountCandidateSuitable = useMemo(
  //   () =>
  //     isLogin &&
  //     [
  //       IAccountRole.CSL,
  //       IAccountRole.CST,
  //       IAccountRole.CTV,
  //       IAccountRole.ADMIN,
  //     ].some((item) => user?.role?.includes(item)),
  //   [isLogin, user]
  // );

  const isAssignedJob = useMemo(() => {
    return (
      isLogin &&
      (requestJob?.userAssign || []).includes(Number(user?.userId || -1))
    );
  }, [isLogin, user, requestJob.userAssign]);

  useEffect(() => {
    setIsBookMark(requestJob.isBookmark);
    setPrevIsBookMark(requestJob.isBookmark);
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [requestJob.isBookmark]);

  const changeBookMark = (): void => {
    if (!user) {
      notification.error({
        message: "Thông báo",
        description: messageRemind,
      });
    } else {
      setIsBookMark(!isBookMark);
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
      timeOut.current = setTimeout(() => {
        if (isBookMark === prevIsBookMark)
          ApiJob.bookMark(requestJob.requestJobId)
            .then(() => {
              setPrevIsBookMark(!isBookMark);
            })
            .catch(() => {
              setIsBookMark(prevIsBookMark);
            });
      }, 1000);
    }
  };

  const onChangeCheckBox = (e: CheckboxChangeEvent): void => {
    if (e.target.checked) {
      onCheckBox?.(requestJob.requestJobId);
    } else {
      onCheckBox?.(null);
    }
  };

  const onChangeCheckBoxSuggestion = (e: CheckboxChangeEvent): void => {
    if (e.target.checked) {
      onCheckBoxSuggestion?.(requestJob.requestJobId, true);
    } else {
      onCheckBoxSuggestion?.(requestJob.requestJobId, false);
    }
  };

  const address = useMemo(() => {
    const hasDistrict = !!requestJob.district;
    const hasWorkAddress = !!requestJob.workAddress;

    return `${requestJob?.district || ""}${
      hasWorkAddress
        ? hasDistrict
          ? ", " + requestJob.workAddress
          : requestJob.workAddress
        : ""
    }`;
  }, [requestJob]);

  const isMobile = useMediaQuery({maxWidth: BREAK_POINT.lg - 1});

  const listLevelName = useMemo(() => {
    if (!requestJob?.listLevelName || !requestJob?.listLevelName?.length) {
      return [];
    }

    return requestJob?.listLevelName;
  }, [requestJob?.listLevelName]);

  const deleteChildNode = () => {
    if (aTagRef.current && divFef.current) {
      const widthATag = aTagRef.current?.offsetWidth;
      const widthDiv = divFef.current?.offsetWidth;

      if (widthDiv > widthATag) {
        if (divFef.current.children?.length > 0) {
          const promise = new Promise((resolve, reject) => {
            resolve(divFef.current?.removeChild(divFef.current.children[0]));
          });

          promise.catch((e) => {
            throw Error(e);
          });
        }
        deleteChildNode();
      }
    }
  };

  useLayoutEffect(() => {
    deleteChildNode();
  }, [requestJob, aTagRef.current, divFef.current]);

  return (
    <div className="relative container-card overflow-hidden">
      <Card
        className="container-card__ui radius-card-view hover-pointer card-padding"
        {...props}
      >
        <a
          href={`${config.PATHNAME.JOB_DETAIL}?id=${requestJob.requestJobId}`}
          // eslint-disable-next-line react/jsx-no-bind
          onClick={(e: any): void => e.preventDefault()}
          className="hover:text-inherit"
          ref={aTagRef}
        >
          <div className="flex">
            <div className="ml-0.5 w-full">
              <div className={isMobile ? "w-12/12" : "w-10/12"}>
                <div className="flex items-center">
                  <Tooltip title={requestJob.name} placement="top">
                    <p
                      className={classNames(
                        "my-1.5 truncate font-bold container-card__name",
                        isMobile ? "text16" : "text20"
                      )}
                    >
                      {requestJob.name}
                    </p>
                  </Tooltip>
                  {isAssignedJob && (
                    <span className="ml-2 text12 text-white container-card__assigned">
                      Chỉ định
                    </span>
                  )}
                </div>

                {user && (
                  <p className="truncate text-xs font-normal text14">
                    {requestJob.customer}
                  </p>
                )}
                <p className="truncate text-xs font-normal text14 mt-2">
                  {`Mức lương: ${requestJob.salary}`}
                </p>
                <p className="truncate text-xs font-normal mt-2 text14">
                  {`Địa điểm: ${address}`}
                </p>
              </div>
              <div className="container-card__bonus">
                <span className="text-xs color-text mt-2 text14">
                  Thưởng giới thiệu{" "}
                  <span className="font-bold">
                    {inviteBonus(
                      requestJob?.partnerRateType,
                      requestJob?.partnerRateValue,
                      requestJob?.partnerCurrencyType
                    )}
                  </span>
                </span>
                <span className="text-xs color-text mt-2 text14 container-card__line-height">
                  {requestJob?.hotBonusAmountDisplay > 0 ? (
                    <span className="color-text">
                      {`${isMobile ? " " : " ,"} Thưởng phỏng vấn `}
                      {/* {" "} */}
                      <span className="font-bold">
                        {formatMoney(
                          requestJob.hotBonusAmountDisplay,
                          requestJob.hotBonusPaymentUnitDisplay
                        )}
                      </span>
                    </span>
                  ) : (
                    <p />
                  )}
                </span>
              </div>
            </div>
          </div>
          <div className="flex justify-between mt-3 pr-2">
            <div className="ml-0.5 flex gap-y-3" ref={divFef}>
              {listLevelName?.map((item, index) => (
                <div key={index} className="container-card__keyword">
                  {item}
                </div>
              ))}
              {requestJob?.workType && (
                <div className="container-card__keyword">
                  {requestJob?.workType}
                </div>
              )}
            </div>
          </div>
        </a>
      </Card>
      {isJobSuggestionsPartner ? (
        <AppCheckBox
          name="chooseJob"
          onChange={onChangeCheckBoxSuggestion}
          checked={checked}
          className="container-icon-checkbox"
        />
      ) : isLogin ? (
        <span
          onClick={changeBookMark}
          role="button"
          tabIndex={0}
          className="container-icon-book-mark"
        >
          <Icon
            icon={isBookMark ? "ic_bookmarked" : "bookmark"}
            size={20}
            color={isBookMark ? "#dc2323" : "#324054"}
            className="icon-book-mark"
          />
        </span>
      ) : (
        ""
      )}

      {/* tạm thời ẩn đi */}
      {/* {!isJobSuggestionsPartner &&
      isVisibleCountCandidateSuitable &&
      requestJob?.numberOfUnmarkedRecommendCandidate &&
      requestJob?.numberOfUnmarkedRecommendCandidate > 0 ? (
        <div className="container-icon-count-candidate">
          {requestJob?.numberOfUnmarkedRecommendCandidate > 99
            ? "99+"
            : requestJob?.numberOfUnmarkedRecommendCandidate}
        </div>
      ) : null} */}
      {isHasCheckBox && (
        <AppCheckBox
          className="job-check-box"
          onChange={onChangeCheckBox}
          checked={checked}
        />
      )}
      <div className="container-card__label">
        {requestJob?.labelCode === 2 ? (
          <Image
            src="/img/urgent-label.svg"
            preview={false}
            width={60}
            height={60}
            alt="label"
          />
        ) : requestJob?.labelCode === 3 ? (
          <Image
            src="/img/bonus-label.svg"
            preview={false}
            width={60}
            height={60}
            alt="label"
          />
        ) : (
          ""
        )}
      </div>
    </div>
  );
}
