.ui-chatbot-sender {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 0;

  &__line {
    margin: 0 -16px;
    border-top: 1px solid #d9dbe9;
  }

  &__input {
    display: flex;
    flex-basis: 80%;
    min-height: 44px;
    textarea {
      padding: 10px 24px 10px 14px;
      border: solid 1px #cacaca;
      border-radius: 16px;
      outline: none;
      resize: none;
      overflow: hidden;
      width: 100%;
      overflow-y: auto;
      min-height: 44px;

      &::-webkit-scrollbar {
        width: 0;
      }
    }
  }

  &__button {
    flex-basis: 15%;
    text-align: center;
    border: 1px solid #d9dbe9;
    border-radius: 24px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    height: 42px;
  }
}
