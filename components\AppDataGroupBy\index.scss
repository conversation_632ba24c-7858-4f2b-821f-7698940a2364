.app-data-group-by {
  &__header {
    .ant-table-tbody {
      height: 0;
      overflow: hidden;
      visibility: collapse;
    }
  }

  .app-collapse .ant-collapse-header {
    padding: 12px 16px !important;
  }

  .ant-collapse-borderless > .ant-collapse-item {
    border: 1px solid #d9d9d9;
    border-bottom: 0;
  }

  .ant-collapse-borderless > .ant-collapse-item:last-child {
    border-bottom: 1px solid #d9d9d9;
  }

  .container-app-table {
    border-radius: 8px 8px 0 0;
  }

  .ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {
    border: 1px solid #d9d9d9;
  }

  .ant-collapse-borderless
    > .ant-collapse-item
    > .ant-collapse-content
    > .ant-collapse-content-box {
    padding-top: 0px;
  }

  // td {
  //   width: 180px !important;
  //   min-width: 180px !important;
  //   max-width: 181px !important;
  // }

  // th {
  //   width: 180px !important;
  //   min-width: 180px !important;
  //   max-width: 181px !important;
  // }

  // colgroup col {
  //   width: 180px !important;
  //   min-width: 180px !important;
  //   max-width: 181px !important;
  // }

  // .ant-table-cell {
  //   width: 180px !important;
  //   min-width: 180px !important;
  //   max-width: 181px !important;
  // }

  .ant-collapse-header:hover {
    background-color: #e6e6e6;
  }

  tr {
    &:hover {
      background-color: #e6e6e6;
      cursor: pointer;
    }
  }

  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }
}
