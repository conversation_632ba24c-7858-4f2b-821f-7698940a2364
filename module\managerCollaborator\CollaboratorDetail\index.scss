.container-collaborator-detail {
  .avatar-collaborator {
    background-color: $primary-color;
    span {
      font-size: 50px;
      font-weight: bold;
    }
  }

  .line {
    height: 0.5px;
    width: 70%;
    background-color: $header_tf05;
    margin-top: 12px;
  }

  .container-note {
    border: 1px dashed $header_tf05;
    border-radius: 16px;
    padding: 15px;

    textarea {
      border: 1px solid $header_tf05;
      border-radius: 8px;
      color: $text-color-input;
    }
  }

  .btn-save {
    button {
      border-radius: 16px;
      height: fit-content;
      padding: 4px 20px;
    }
  }

  .custom-ant-tab-active {
    .ant-tabs {
      .ant-tabs-nav {
        .ant-tabs-tab-active {
          background: $green_color;
          color: $white-color;
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
          .ant-tabs-tab-btn {
            color: $white-color;
            font-size: 1rem;
            line-height: 20.24px;
          }
        }

        .ant-tabs-tab {
          border-start-start-radius: 12px;
          border-start-end-radius: 12px;
          .ant-tabs-tab-btn {
            font-size: 1rem;
            line-height: 20.24px;
          }
        }
      }
    }
  }

  .row-table-applications {
    td {
      color: $text-color-input;
      font-size: 0.75rem;
    }
  }

  .ant-table-thead .ant-table-cell {
    color: $text-color-input;
    font-size: 1rem;
  }

  .ant-table-cell {
    color: $text-color-input;
    font-size: 0.75rem;
  }

  .dot {
    height: 12px;
    width: 12px;
    border-radius: 12px;
  }
}
