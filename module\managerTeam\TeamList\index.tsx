import ApiTeam, {IUserGroups, ParamFilter} from "@app/api/ApiTeam";
import AppButton from "@app/components/AppButton";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import Icon from "@app/components/Icon/Icon";
import {TextInput} from "@app/components/TextInput";
import config from "@app/config";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {Row} from "antd";
import {ColumnsType} from "antd/lib/table";
import {Formik} from "formik";
import moment from "moment";
import {useRouter} from "next/router";
import React, {useEffect, useRef, useState} from "react";
import {useQuery} from "react-query";
import "./index.scss";

interface TeamListProps {
  isAdmin?: boolean;
}

export default function TeamList(props: TeamListProps): JSX.Element {
  const {isAdmin} = props;
  const [valuesSearch, setValuesSearch] = useState<ParamFilter>({
    currentpage: 1,
    numberShowItem: 20,
    keywords: "",
  } as ParamFilter);
  const router = useRouter();
  const timeOut = useRef<any>();
  const getListTeam = useQuery(["getListTeam", valuesSearch], () => {
    return ApiTeam.getListTeam(valuesSearch);
  });

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const onChangeTextSearch = (e: any): void => {
    timeOut.current = setTimeout(() => {
      setValuesSearch({
        currentpage: 1,
        numberShowItem: 20,
        keywords: e.target.value?.trim() || "",
      });
    }, deadTimeFastSearch);
  };

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesSearch({
      ...valuesSearch,
      currentpage: page,
      numberShowItem: pageSize,
    });
  };

  const columns: ColumnsType<IUserGroups> = [
    {
      title: "Loại nhóm",
      dataIndex: "roleTypeName",
      key: "roleTypeName",
    },
    {
      title: "Tên nhóm",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Trưởng nhóm",
      dataIndex: "groupLeaderName",
      key: "groupLeaderName",
    },
    {
      title: "Số Lượng nhân viên",
      dataIndex: "memberCount",
      key: "memberCount",
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      render: (_, item: IUserGroups) => (
        <span>
          {item.createdDate ? moment(item.createdDate).format(DATE_FORMAT) : ""}
        </span>
      ),
    },
    {
      title: "Ngày cập nhật",
      dataIndex: "modifiedDate",
      key: "modifiedDate",
      render: (_, item: IUserGroups) => (
        <span>
          {item.modifiedDate
            ? moment(item.modifiedDate).format(DATE_FORMAT)
            : ""}
        </span>
      ),
    },
  ];

  return (
    <div className="container-manager-team-list">
      <Row className="justify-between items-center">
        <Formik
          initialValues={{keywords: ""}}
          onSubmit={(): void => {
            //
          }}
        >
          {({values}): JSX.Element => {
            return (
              <TextInput
                containerclassname="w-1/2"
                label="Tìm kiếm nhanh"
                name="keywords"
                value={values.keywords}
                onChange={onChangeTextSearch}
                placeholder="Nhập tên nhóm, loại nhóm, tên trưởng nhóm để tìm kiếm"
              />
            );
          }}
        </Formik>
        {!!isAdmin && (
          <AppButton
            typebutton="normal"
            classrow="add-btn"
            onClick={(): void => {
              router.push(config.PATHNAME.TEAM_CREATE);
            }}
          >
            <Icon icon="add-line" size={16} />
            <div className="title mx-6">Tạo nhóm</div>
          </AppButton>
        )}
      </Row>
      <div className="mt-6">
        <AppTable
          rowClassName="cursor-pointer"
          dataSource={getListTeam.data?.userGroups?.map(
            (item: IUserGroups, index: number) => ({
              ...item,
              key: index,
            })
          )}
          columns={columns}
          loading={getListTeam.isLoading}
          scroll={{y: isAdmin ? "50vh" : "60vh"}}
          onRow={(item: IUserGroups): any => {
            return {
              onClick: (): void => {
                router.push({
                  pathname: config.PATHNAME.TEAM_DETAIL,
                  query: {
                    id: item.userGroupId,
                  },
                });
              },
            };
          }}
        />
      </div>
      <AppPagination
        className="mt-6"
        defaultPageSize={valuesSearch.numberShowItem}
        current={valuesSearch.currentpage}
        pageSize={valuesSearch.numberShowItem}
        total={getListTeam.data?.totalCount}
        onChange={handlePagination}
      />
    </div>
  );
}
