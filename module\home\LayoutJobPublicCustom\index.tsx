import React, {ReactNode} from "react";
import FilterJobsLanding from "../FilterJobsLanding";

interface Props {
  children: ReactNode;
}

function LayoutJobPublicCustom(props: Props) {
  return (
    <div className="layout-public-custom w-full">
      <FilterJobsLanding />
      <div
        className="layout-public-custom__content"
        style={{padding: "8px 12px"}}
      >
        {props.children}
      </div>
    </div>
  );
}

export default LayoutJobPublicCustom;
