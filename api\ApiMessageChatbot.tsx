import {MessageChatbot} from "@app/types";
import {fetcher} from "./Fetcher";
import Config from "../config";

const path = {
  getMessage: "/api/Chat/getData",
  sendMessage: "/api/Chat/SendAsync",
  deleteMessage: "/api/Chat/clear",
};

function getMessageChatbot(): Promise<Array<MessageChatbot>> {
  return fetcher({
    url: path.getMessage,
    method: "get",
  });
}

const sendMessageUrl = Config.NETWORK_CONFIG.API_BASE_URL + path.sendMessage;

const sendMessageChatbot = (message: string) => {
  return fetch(sendMessageUrl, {
    method: "POST",
    redirect: "follow",
    credentials: "include",
    headers: {
      "Content-Type": "application/json-patch+json",
    },
    body: JSON.stringify(message),
  });
};

function deleteMessageChatbot(): Promise<any> {
  return fetcher({
    url: path.deleteMessage,
    method: "get",
  });
}

export default {
  getMessageChatbot,
  sendMessageChatbot,
  deleteMessageChatbot,
};
