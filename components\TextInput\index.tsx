import "./index.scss";
import {Input, InputProps} from "formik-antd";
import {useMemo, useRef, useState} from "react";
import classNames from "classnames";
// eslint-disable-next-line import/namespace
import {
  inputCurrency,
  moneyToNumber,
  onlyInputNumber,
  onlyPhoneNumber,
} from "@app/utils/constants/function";

interface TextInputProps extends InputProps {
  label: string;
  name: string;
  required?: boolean;
  containerclassname?: string;
  defaultValue?: string;
  value?: any;
  free?: boolean;
  onlynumber?: boolean;
  typeInput?: "salary" | "";
  isphonenumber?: boolean;
  iscurrency?: boolean;
}

export function TextInput(props: TextInputProps): JSX.Element {
  const {
    label,
    containerclassname,
    placeholder,
    required,
    value,
    free,
    onlynumber,
    typeInput,
    isphonenumber,
    iscurrency,
  } = props;
  const [focus, setFocus] = useState(false);
  const refInput = useRef({input: {value: ""}});

  const isOccupied =
    focus || (free ? false : !!refInput.current?.input?.value || value);

  const labelClass = isOccupied ? "label as-label" : "label as-placeholder";

  const requiredMark = required ? (
    <span className="text-required">*</span>
  ) : null;

  const newValue = useMemo(() => {
    // Check if the value is a valid number
    const isNumberValid =
      /^\d+(\.\d*)?$/.test(String(value).replace(/,/g, "")) &&
      !/\.$/.test(value);
    if (typeInput === "salary" && isNumberValid) {
      if (!value) {
        return "";
      }
      return Intl.NumberFormat("en-US", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 5,
      }).format(moneyToNumber(String(value)));
    }
    return value;
  }, [value]);

  return (
    <div
      className={classNames("text-input-container", containerclassname)}
      onBlur={() => setFocus(false)}
      onFocus={() => setFocus(true)}
    >
      <Input
        ref={refInput}
        {...props}
        placeholder={focus ? placeholder : ""}
        onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
          if (onlynumber && iscurrency) {
            inputCurrency(e);
          }
          if (onlynumber) {
            onlyInputNumber(e);
          }
          if (isphonenumber) {
            onlyPhoneNumber(e);
          }
        }}
        value={newValue}
      />
      <span className={labelClass}>
        {label} {requiredMark}
      </span>
    </div>
  );
}
