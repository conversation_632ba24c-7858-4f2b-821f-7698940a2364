import AppModal from "@app/components/AppModal";
import React, {useEffect, useState} from "react";
import "./index.scss";
import {Input, Row} from "antd";
import AppButton from "@app/components/AppButton";
import {UseMutationResult} from "react-query";
// eslint-disable-next-line import/no-cycle
import {FormUpdate} from "../ModalDetailApplication";
import {ICandidate} from "@app/api/ApiApplication";

const {TextArea} = Input;
interface ModalCancelApplication {
  open?: boolean;
  //   applicationId?: number;
  closeModal: () => void;
  updateApplication: UseMutationResult<
    any,
    unknown,
    FormUpdate | undefined,
    unknown
  >;
  candidate?: ICandidate;
}
export default function ModalCancelApplication(
  props: ModalCancelApplication
): JSX.Element {
  const {open, closeModal, updateApplication, candidate} = props;
  const [isError, setIsError] = useState<boolean>(false);
  const [note, setNote] = useState("");

  const changeNote = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
    setNote(e.target.value);
    setIsError(false);
  };

  const resetData = (): void => {
    setIsError(false);
    setNote("");
  };

  const handleClose = (): void => {
    resetData();
    closeModal();
  };

  const handleCancelApplication = (): void => {
    if (note.trim().length === 0) {
      setIsError(true);
    } else {
      updateApplication.mutate({
        name: candidate?.name,
        email: candidate?.email,
        phoneNumber: candidate?.phoneNumber,
        status: 2,
        ctvNote: note,
      });
    }
  };

  useEffect(() => {
    if (updateApplication.status === "success") {
      handleClose();
    }
  }, [updateApplication.status]);

  return (
    <AppModal
      className="modal-cancel-application"
      open={open}
      footer={null}
      onCancel={handleClose}
      width={500}
      title="Hủy giới thiệu ứng viên"
    >
      <span>
        Lý do hủy <span className="text-red-600">*</span>
      </span>
      <TextArea
        maxLength={255}
        className="input-note"
        style={{height: 110, resize: "none"}}
        placeholder="Nhập lý do hủy giới thiệu ứng viên"
        onChange={changeNote}
        value={note}
      />

      {isError && <span className="text-red-600">Bạn cần nhập lý do</span>}
      <Row>
        {["Ứng viên đã có việc", "Công việc không phù hợp"].map((i, index) => (
          <AppButton
            onClick={() => {
              setNote(i);
            }}
            classrow="item-reason"
            typebutton="normal"
            key={index}
            label={i}
          />
        ))}
      </Row>
      <Row className="mt-6 justify-center flex">
        <AppButton
          classrow="btn-close"
          typebutton="secondary"
          label="Đóng"
          onClick={handleClose}
        />
        <AppButton
          classrow="btn-cancel-application"
          typebutton="primary"
          label="Hủy giới thiệu"
          onClick={handleCancelApplication}
        />
      </Row>
    </AppModal>
  );
}
