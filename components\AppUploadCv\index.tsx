import React, {useRef} from "react";
import Icon from "../Icon/Icon";
import "./index.scss";
import {fileTypeAllow} from "@app/utils/constants/state";
import {Row, notification} from "antd";
import {messageErrorUploadCv} from "@app/utils/constants/message";

interface Props {
  id: string;
  onChangeInput?: (file: File, formData: FormData) => void;
  type?: "btn" | "view";
  disabled?: boolean;
}

function AppUploadCv(props: Props): JSX.Element {
  const {id, onChangeInput, type, disabled} = props;
  const fileUploadRef = useRef() as React.MutableRefObject<HTMLInputElement>;
  const handleChangeInput = (file: File) => {
    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      const fileType = file?.name.split(".").pop();
      const fileSize = file.size / 1024 / 1024;
      if (file && fileType && fileSize) {
        if (!fileTypeAllow.includes(fileType?.toLowerCase())) {
          notification.error({
            message: messageErrorUploadCv.errFileType,
          });
          return "";
        }

        if (fileSize > 5) {
          notification.error({
            message: messageErrorUploadCv.errFileSize,
          });
          return "";
        }
        onChangeInput?.(file, formData);
      }
    }
    return "";
  };

  return (
    <label
      htmlFor={id}
      className={`cursor-pointer ${disabled && "pointer-events-none"}`}
    >
      {type === "btn" ? (
        <div className="flex items-center h-full">
          <Row className="add-candidate-btn items-center flex mr-2">
            <Icon size={16} color="white" icon="upload-cloud-2-line" />
            <span className="add-candidate-btn text14">Thêm ứng viên</span>
          </Row>
        </div>
      ) : (
        <div className="cv-form">
          <Icon size={48} color="#324054" icon="upload-cloud-2-line" />
          <div className="flex flex-col items-center ml-6">
            <span className="text24 app-upload-title">
              Bấm vào khung để tải lên CV ứng viên
            </span>
            <span className="text-base mt-1 app-upload-desc text16">
              Chỉ có thể tải lên file pdf, docx, xls, xlsx, csv tối đa 5mb
            </span>
          </div>
        </div>
      )}
      <input
        type="file"
        name={id}
        id={id}
        accept=".docx, .pdf, .xls, .csv, .xlsx"
        ref={fileUploadRef}
        className="hidden"
        onChange={(e: any): void => {
          handleChangeInput(e.target.files[0]);
          fileUploadRef.current.value = "";
        }}
      />
    </label>
  );
}

export default AppUploadCv;
