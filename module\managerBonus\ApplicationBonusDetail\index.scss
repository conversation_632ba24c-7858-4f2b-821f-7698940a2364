.modal-detail-application-bonus {
  color: $text-color-input;
  top: 50px;

  .container-item-detail-modal {
    border: 1px dashed $header_tf;
    border-radius: 16px;
    overflow: hidden;
  }

  .dot-status {
    height: 12px;
    width: 12px;
    border-radius: 12px;
  }

  .line {
    height: 0.5px;
    background-color: $header_tf05;
    margin: 16px 0px;
  }

  .title-info {
    color: $header_tf;
  }

  .amount-application {
    background-color: $primary-color01;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .payment-card {
    border: 1px dashed $header_tf;
    padding: 12px;
    overflow: hidden;
    border-radius: 5px;
    margin-top: 10px;
  }

  .btn-cancel {
    button {
      background-color: $status-reject;

      &:focus {
        background-color: $status-reject;
      }
    }

    .ant-btn[disabled] {
      background-color: $status-reject;
      opacity: 0.5;
      color: $white-color;
    }
  }

  .btn-edit {
    .ant-btn[disabled] {
      background-color: $primary-color;
      opacity: 0.5;
      color: $white-color;
    }
  }
}
