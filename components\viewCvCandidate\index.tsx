/* eslint-disable jsx-a11y/no-static-element-interactions */
import Icon from "@app/components/Icon/Icon";
import "./index.scss";
import React, {useEffect, useMemo} from "react";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import ApiCandidate, {IModeViewCandidate} from "@app/api/ApiCandidate";
import config from "@app/config";
import {fileExcelAllow} from "@app/utils/constants/state";
import {Tooltip, notification} from "antd";
import AppPdfPreview from "../AppPdfPreview";
import {IAccountRole} from "@app/types";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import {useRouter} from "next/router";
import {useMutation} from "react-query";

export interface Uri {
  filePathBase64?: string;
  fileName?: string;
}

export interface PotentialDoc {
  urlPotential?: string;
  urlDownloadPotential?: string;
}

interface Props {
  docs: Uri;
  modeViewCandidate: IModeViewCandidate;
  createdDate: string;
  tab?: "candidate" | "potentialCandidate";
  potential?: PotentialDoc;
  idCandidate?: number | string;
  isShowFormatFile?: boolean;
}

function ViewCvCandidate(props: Props): JSX.Element {
  const {
    docs,
    modeViewCandidate,
    createdDate,
    tab,
    potential,
    idCandidate,
    isShowFormatFile,
  } = props;

  // const officeDocument = `https://view.officeapps.live.com/op/embed.aspx?src=${config.NETWORK_CONFIG.API_BASE_URL}/api/file/previewFile?fileName=`;
  const officeDocument = `https://view.officeapps.live.com/op/embed.aspx?src=`;
  const linkDownload =
    config.NETWORK_CONFIG.API_BASE_URL + "/api/file/downloadFile?fileName=";
  const {user} = useSelector(selectUser);
  const conditionalShowFormatFile = [
    IAccountRole.ADMIN,
    IAccountRole.AMG,
    IAccountRole.AML,
    IAccountRole.CSL,
    IAccountRole.CST,
  ].some((item) => user?.role?.includes(item));

  const isRoleAdmin = user?.role?.includes(IAccountRole.ADMIN);

  const router = useRouter();

  const isHideDownload = router.pathname === config.PATHNAME.DATA_POOL;

  const checkExpiredTokenCandidatePath = useMutation(
    (candidateId: any) => {
      return ApiCandidate.checkLimitDownloadCVCandidate(candidateId);
    },
    {
      onSuccess(data) {
        if (data) {
          if (!docs?.filePathBase64) {
            downloadCV(dataOverall?.linkDownloadResult, dataOverall.fileName);
          }
        } else {
          notification.warn({
            message: "Thông báo",
            description:
              "Tài khoản đã tải quá 10 cv ứng viên tiềm năng trong ngày, vui lòng thử lại sau",
          });
        }
      },
    }
  );

  const dataOverall = useMemo(() => {
    let fileName: string = docs?.fileName ? docs.fileName : "";
    let convertFilePath: string = docs?.filePathBase64
      ? window.atob(decodeURIComponent(docs.filePathBase64))
      : "";
    let linkDownloadResult = docs?.filePathBase64
      ? linkDownload + docs.filePathBase64
      : "";
    let urlResult = "";

    if (tab && tab === "potentialCandidate") {
      fileName = potential?.urlPotential ? potential.urlPotential : "";
      convertFilePath = potential?.urlPotential ? potential?.urlPotential : "";
      linkDownloadResult = potential?.urlDownloadPotential
        ? potential?.urlDownloadPotential
        : "";
    }
    const fileType = fileName ? fileName.split(".").pop()?.toLowerCase() : "";
    if (fileType === "pdf") {
      urlResult =
        config.NETWORK_CONFIG.API_BASE_URL +
        "/" +
        convertFilePath +
        "#toolbar=0";

      if (tab && tab === "potentialCandidate") {
        urlResult = convertFilePath + "#toolbar=0";
      }
    }

    if (fileExcelAllow.includes(fileType || "") || fileType === "docx") {
      urlResult =
        officeDocument +
        config.NETWORK_CONFIG.API_BASE_URL +
        "/" +
        convertFilePath;
    }

    return {
      linkDownloadResult,
      fileName,
      urlResult,
      fileType,
    };
  }, [docs, potential]);

  function renderFile(url: string, fileName: string): JSX.Element {
    return <iframe src={url} title={fileName} className="w-full h-full" />;
  }

  const linkExportCv = `${config.NETWORK_CONFIG.API_BASE_URL}/api/candidate/${
    tab === "potentialCandidate" ? "exportCVFromAI" : "exportCV"
  }?candidateId=${idCandidate}`;

  const downloadCV = async (
    url: string,
    fileName = "Document"
  ): Promise<any> => {
    const splitFileName = fileName.split("/");
    const fileNameConvert = splitFileName[splitFileName.length - 1];
    await fetch(url, {
      method: "GET",
    })
      .then((response) => response.blob())
      .then((blob) => {
        const blobData = new Blob([blob]);
        const url = window.URL.createObjectURL(blobData);
        const linkDownload = document.createElement("a");
        linkDownload.href = url;
        linkDownload.download = fileNameConvert;
        linkDownload.click();
        window.URL.revokeObjectURL(url);
        linkDownload.remove();
      });
  };

  useEffect(() => {
    const formatCvRef = document.getElementById("formatCv");
    const showNoticeFormatCV = (): void => {
      return notification.success({
        message: "Thông báo",
        description:
          "Hệ thống đang tiến hành format CV, vui lòng chờ trong giây lát",
      });
    };
    formatCvRef?.addEventListener("click", showNoticeFormatCV);
    return () => formatCvRef?.removeEventListener("click", showNoticeFormatCV);
  }, []);

  const handleDownloadCV = (e: any): void => {
    const isPotentialCandidatePath =
      router.pathname === config.PATHNAME.MANAGER_POTENTIAL_CANDIDATE;

    if (isPotentialCandidatePath && !isRoleAdmin) {
      e.preventDefault();
      checkExpiredTokenCandidatePath.mutate(idCandidate);
      return;
    }

    if (!docs?.filePathBase64) {
      e.preventDefault();
      downloadCV(dataOverall?.linkDownloadResult, dataOverall.fileName);
    }
  };

  return (
    <div className="view-cv-ui flex justify-center items-center">
      {dataOverall?.urlResult && dataOverall?.urlResult?.includes("http") ? (
        <div className="w-full h-full">
          <div className="mb-4 flex justify-between items-center">
            <div className="flex">
              {isHideDownload ? (
                ""
              ) : (
                <Tooltip title="Download">
                  <span onClick={handleDownloadCV}>
                    <a
                      className="mr-4 cursor-pointer"
                      target="_blank"
                      rel="noreferrer"
                      href={dataOverall?.linkDownloadResult}
                      download={dataOverall.fileName || "Document"}
                    >
                      <Icon size={16} icon="file-download-line" />
                    </a>
                  </span>
                </Tooltip>
              )}

              <div>
                <Tooltip title="View">
                  <a
                    target="_blank"
                    href={
                      dataOverall.fileType === "pdf"
                        ? `/preview-cv?url=${dataOverall.urlResult}`
                        : dataOverall.urlResult
                    }
                    title={docs?.fileName}
                    rel="noreferrer"
                  >
                    <Icon size={16} icon="external-link-line" />
                  </a>
                </Tooltip>
              </div>
              {isShowFormatFile && conditionalShowFormatFile && (
                <Tooltip title="Format">
                  <a
                    className="ml-4"
                    href={linkExportCv}
                    download
                    id="formatCv"
                  >
                    <Icon size={16} icon="format-icon" />
                  </a>
                </Tooltip>
              )}
            </div>
            <div className="flex items-center gap-1">
              <Icon size={16} icon="calendar-2-line" />
              <span>
                {" "}
                Ngày tạo:{" "}
                {modeViewCandidate === IModeViewCandidate.create
                  ? moment(new Date()).format(DATE_FORMAT)
                  : createdDate
                  ? moment(new Date(createdDate)).format(DATE_FORMAT)
                  : ""}
              </span>
            </div>
          </div>
          <div className="w-full h-full">
            {dataOverall.fileType === "pdf" ? (
              <AppPdfPreview
                url={dataOverall.urlResult}
                classNameContainer="h-[62vh] w-full"
              />
            ) : (
              renderFile(
                dataOverall.urlResult,
                dataOverall?.fileName || "Document"
              )
            )}
          </div>
        </div>
      ) : (
        "File not found"
      )}
    </div>
  );
}

export default React.memo(ViewCvCandidate);
