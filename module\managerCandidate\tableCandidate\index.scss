.table-candidate {
  .row-table-candidate {
    td {
      color: $text-color-input;
      font-size: 0.75rem;
    }
  }

  .ant-table-thead .ant-table-cell {
    color: $text-color-input;
    font-size: 1rem;
  }

  .ant-table-tbody > tr.ant-table-row-selected:hover > td {
    background: $white-color;
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background: $white-color;
  }

  .ant-checkbox-indeterminate .ant-checkbox-inner::after {
    background-color: $white-color;
  }

  .ant-table-selection-column {
    border-right: 0px !important;
  }
  .status-candidate {
    color: $white-color;
    border-radius: 8px;
    padding: 2px 10px;
  }

  .list-skill {
    cursor: default;

    .ant-select-selector {
      background: $white-color !important;
      cursor: default;
    }

    .ant-select-selection-overflow {
      cursor: default;
    }

    .ant-select-disabled.ant-select:not(.ant-select-customize-input)
      .ant-select-selector {
      cursor: default;
    }

    .ant-select-multiple.ant-select-disabled.ant-select:not(
        .ant-select-customize-input
      )
      .ant-select-selector {
      cursor: default;
    }

    .ant-select-disabled.ant-select:not(.ant-select-customize-input)
      .ant-select-selector {
      cursor: default;
    }

    .ant-select-selection-overflow-item {
      cursor: default;
    }
  }
  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector
    input {
    cursor: default;
  }
  .ant-select-disabled.ant-select-multiple .ant-select-selection-item {
    border: none;
    background-color: $primary-color;
    color: $white-color;
    border-radius: 8px;
  }
  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border: none;
  }
  .ant-select-disabled.ant-select-multiple .ant-select-selection-item {
    cursor: default;
  }
}

.filter-candidate-container {
  .btn-filter {
    button {
      font-size: 0.875rem;
      font-weight: 400;
      border: 1px dashed $header_tf;
      border-radius: 8px;
      display: flex;
      align-items: center;
      align-self: center;
      color: $text-color-input;
      height: auto;
    }
    button:hover,
    button:focus {
      background: none;
      color: $text-color-input;
      border: 1px dashed $header_tf;
    }
  }
}

.group-check-box-list-col {
  width: 380px;
}

.ant-popover-inner {
  border-radius: 16px;
}

.content-filter-advance-candidate {
  background-color: $white-color;
  width: 312px;

  .title-filter {
    font-size: 1.5rem;
    font-weight: 400;
    color: $text-color-input;
  }

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .btn-close-popover {
    button {
      border: none;
    }
  }
}

.btn-export {
  margin-top: 10px;

  .ant-btn {
    border: none;
    padding: 0;
  }
}

.statistical-btn {
  .primary-button {
    height: 33px !important;
    border-radius: 8px;
  }
  &__content {
    color: $white-color;
    background-color: $primary-color;
    border-radius: 8px;
  }
}
