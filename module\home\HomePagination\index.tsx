import {LeftOutlined, RightOutlined} from "@ant-design/icons";
import classNames from "classnames";
import React, {useMemo} from "react";
import "./index.scss";

interface Props {
  total: number;
  pageSize: number;
  pageNumber: number;
  onPageChange?: (page: number, size: number) => void;
}

function HomePagination(props: Props) {
  const {total, pageNumber, pageSize, onPageChange} = props;
  const totalPage = useMemo(() => {
    const count = Math.ceil(total / pageSize);
    return count;
  }, [total, pageSize]);
  const handlePageChange = (typeButton: "prev" | "next") => {
    const isPrev = typeButton === "prev";
    if (isPrev) {
      if (pageNumber > 1) {
        const pageDesired = pageNumber - 1;
        onPageChange?.(pageDesired, pageSize);
      }
    } else {
      if (pageNumber < totalPage) {
        const pageDesired = pageNumber + 1;
        onPageChange?.(pageDesired, pageSize);
      }
    }
  };
  return totalPage > 0 ? (
    <div className="flex text4 items-center home-pagination">
      <div
        className={classNames(
          "home-pagination__button cursor-pointer flex items-center justify-center",
          pageNumber === 1 && "home-pagination__button-disable"
        )}
        role="button"
        tabIndex={0}
        // eslint-disable-line jsx-a11y/no-static-element-interactions
        onClick={() => {
          handlePageChange("prev");
        }}
      >
        <LeftOutlined />
      </div>
      <div className="mx-4">{`${pageNumber}/${totalPage} trang`}</div>
      <div
        className={classNames(
          "home-pagination__button cursor-pointer flex items-center justify-center",
          pageNumber === totalPage && "home-pagination__button-disable"
        )}
        role="button"
        tabIndex={0}
        // eslint-disable-line jsx-a11y/no-static-element-interactions
        onClick={() => {
          handlePageChange("next");
        }}
      >
        <RightOutlined />
      </div>
    </div>
  ) : (
    <div />
  );
}

export default HomePagination;
