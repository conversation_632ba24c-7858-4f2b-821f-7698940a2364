import AppModal from "@app/components/AppModal";
import {Avatar, Col, Row, Tabs, notification} from "antd";
import React, {useEffect, useMemo, useRef, useState} from "react";
import "./index.scss";
import {UseQueryResult, useMutation, useQuery} from "react-query";
import ApiTeam, {
  ICandidateInfo,
  IResListEmployee,
  IResTeamDetail,
} from "@app/api/ApiTeam";
import {useRouter} from "next/router";
import AppAccessDenied from "@app/components/AppAccessDenied";
import AppLoading from "@app/components/AppLoading";
import {
  getAbbreviatedName,
  getStatusEmployee,
} from "@app/utils/constants/function";
import Icon from "@app/components/Icon/Icon";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/lib/table";
import TabPane from "antd/lib/tabs/TabPane";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole} from "@app/types";
import AppButton from "@app/components/AppButton";
import ModalFormEmployee from "../ModalFormEmployee";
import AppModalConfirm from "@app/components/AppModalConfirm";
import Link from "next/link";
import config from "@app/config";
import {getStatusCandidate} from "@app/utils/constants/state";

interface DetailEmployeeProps {
  handleClose: () => void;
  employeeId: number;
  groupId?: string;
  detailTeam?: IResTeamDetail;
  getDetailTeam?: UseQueryResult<IResTeamDetail, any>;
  getListEmployee?: UseQueryResult<IResListEmployee, unknown>;
}

export default function ModalDetailEmployee(
  props: DetailEmployeeProps
): JSX.Element {
  const {
    handleClose,
    employeeId,
    groupId,
    detailTeam,
    getDetailTeam,
    getListEmployee,
  } = props;
  const router = useRouter();
  const timeOut = useRef<any>();
  const {user} = useSelector(selectUser);
  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);
  const [tabActive, setTabActive] = useState("customer");
  const [isShowModelEdit, setIsShowModelEdit] = useState<boolean>(false);
  const [isShowModalConfirmPassword, setIsShowModalConfirmPassword] =
    useState<boolean>(false);

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const getRecoProfileDetail = useQuery(
    ["getRecoProfileDetail", employeeId, groupId],
    () => {
      return ApiTeam.getRecoProfileDetail(employeeId, groupId);
    },
    {
      enabled: employeeId !== -1,
      onError: (error: any): void => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            router.back();
          }, 4000);
        }
      },
    }
  );

  const isCS =
    !!getRecoProfileDetail?.data?.accountTypeIds &&
    getRecoProfileDetail?.data?.accountTypeIds?.filter((item) =>
      [IAccountRole.CSL, IAccountRole.CST].some((i) => i === item)
    )?.length > 0;

  const isAM =
    !!getRecoProfileDetail?.data?.accountTypeIds &&
    getRecoProfileDetail?.data?.accountTypeIds?.filter((item) =>
      [IAccountRole.AMG, IAccountRole.AML].some((i) => i === item)
    )?.length > 0;

  const isBD =
    !!getRecoProfileDetail?.data?.accountTypeIds &&
    getRecoProfileDetail?.data?.accountTypeIds?.filter((item) =>
      [IAccountRole.BD, IAccountRole.BDL].some((i) => i === item)
    )?.length > 0;

  const isAdminRole =
    !!getRecoProfileDetail?.data?.accountTypeIds &&
    getRecoProfileDetail?.data?.accountTypeIds?.filter((item) =>
      [IAccountRole.ADMIN].some((i) => i === item)
    )?.length > 0;

  const isOnlyCS = useMemo(
    () => isCS && !(isAM || isBD || isAdminRole),
    [getRecoProfileDetail?.data]
  );

  useEffect(() => {
    if (isOnlyCS) {
      setTabActive("candidate");
    } else {
      setTabActive("customer");
    }
  }, [getRecoProfileDetail?.data]);

  const resetPassword = useMutation(
    (employeeId: string) => {
      return ApiTeam.resetPassword(employeeId);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Đặt lại mật khẩu thành công.",
        });
      },
    }
  );

  const employeeDetail = getRecoProfileDetail.data;

  const listInfo = [
    {icon: "mail-line", value: employeeDetail?.email},
    {
      icon: "calendar-check-line",
      value: employeeDetail?.dateOfBirth,
    },
    {icon: "phone-line", value: employeeDetail?.phoneNumber},
    {icon: "shield-user-line", value: employeeDetail?.accountType},
  ];

  const listOtherInfo = groupId
    ? [
        {
          title: "Thông tin công việc",
          info: [
            {title: "Chức danh", value: employeeDetail?.titleName},
            {title: "Ngày vào công ty", value: employeeDetail?.startWorkDate},
            {title: "Ngày tạo tài khoản", value: employeeDetail?.createdDate},
          ],
        },
        {
          title: "Nhóm nhân viên",
          info: [
            {title: "Loại nhóm", value: detailTeam?.roleTypeName},
            {title: "Tên nhóm", value: detailTeam?.name},
            {title: "Trưởng nhóm", value: detailTeam?.groupLeaderName},
          ],
        },
      ]
    : [
        {
          title: "Thông tin công việc",
          info: [
            {title: "Chức danh", value: employeeDetail?.titleName},
            {title: "Ngày vào công ty", value: employeeDetail?.startWorkDate},
            {title: "Ngày tạo tài khoản", value: employeeDetail?.createdDate},
          ],
        },
      ];

  const renderTableCustomer = (): JSX.Element => {
    const columns: ColumnsType<{
      customerCode: string;
      customerName: string;
      totalRequestFollow: number;
      statusName: string;
    }> = [
      {
        title: "Tên khách hàng",
        dataIndex: "customerName",
        key: "customerName",
        render: (_, item: any) => (
          <Link
            href={`${config.PATHNAME.CUSTOMER_DETAIL}?id=${item.customerCode}`}
            className="status cursor-pointer hover:text-inherit"
          >
            {item.customerName}
          </Link>
        ),
      },
      {
        title: "Mã khách hàng",
        dataIndex: "customerCode",
        key: "customerCode",
      },
      {
        title: "Trạng thái",
        dataIndex: "statusName",
        key: "statusName",
      },
      {
        title: "Số lượng request",
        dataIndex: "totalRequestFollow",
        key: "totalRequestFollow",
      },
    ];

    return (
      <div className="flex-1 overflow-y-auto h-full mt-4">
        <AppTable
          bordered={false}
          dataSource={employeeDetail?.listCustomerInfo?.map(
            (item, index: number) => ({
              ...item,
              key: index,
            })
          )}
          scroll={{y: "42vh"}}
          columns={columns}
        />
      </div>
    );
  };

  const renderTableCandidate = (): JSX.Element => {
    const columns: ColumnsType<ICandidateInfo> = [
      {
        title: "Tên ứng viên",
        dataIndex: "name",
        key: "name",
        render: (_, {name, candidateId}: ICandidateInfo) => (
          <Link
            href={`${config.PATHNAME.MANAGER_CANDIDATE}?id=${candidateId}`}
            className="status cursor-pointer hover:text-inherit"
          >
            {name}
          </Link>
        ),
      },
      {
        title: "Email",
        dataIndex: "email",
        key: "email",
      },
      {
        title: "Số điện thoại",
        dataIndex: "phoneNumber",
        key: "phoneNumber",
      },
      {
        title: "Trạng thái",
        dataIndex: "status",
        key: "status",
        render: (_, {status}: ICandidateInfo): JSX.Element => {
          return (
            <span
              className="status-cv"
              style={{
                backgroundColor: getStatusCandidate(Number(status)).color,
              }}
            >
              {getStatusCandidate(status).label}
            </span>
          );
        },
      },
    ];

    return (
      <div className="flex-1 overflow-y-auto h-full mt-4">
        <AppTable
          bordered={false}
          dataSource={employeeDetail?.listCandidateInfo?.map(
            (item, index: number) => ({
              ...item,
              key: index,
            })
          )}
          scroll={{y: "42vh"}}
          columns={columns}
        />
      </div>
    );
  };

  const handelCloseModalEdit = (): void => {
    setIsShowModelEdit(false);
  };

  const handleResetPass = (): void => {
    resetPassword.mutate(String(employeeId));
    setIsShowModalConfirmPassword(false);
  };

  const renderContentRight = (): JSX.Element => {
    // if (!isAdmin) {
    //   return renderTableCustomer();
    // }

    if (isCS && (isAM || isBD)) {
      return (
        <div className="custom-ant-tab-active">
          <Tabs
            defaultActiveKey="customer"
            activeKey={tabActive}
            type="card"
            onChange={setTabActive}
          >
            {[
              {
                key: "customer",
                title: "Khách hàng",
                component: renderTableCustomer(),
              },
              {
                key: "candidate",
                title: "Ứng viên",
                component: renderTableCandidate(),
              },
            ].map(
              (item: {key: string; title: string; component: JSX.Element}) => (
                <TabPane tab={item.title} key={item.key}>
                  {item.component}
                </TabPane>
              )
            )}
          </Tabs>
        </div>
      );
    }

    if (isAM || isBD) {
      return renderTableCustomer();
    }

    if (isCS) {
      return renderTableCandidate();
    }

    return <div />;
  };

  const renderBodyModal = (): JSX.Element => {
    if (getRecoProfileDetail.isLoading) {
      return (
        <div className=" h-[65vh]">
          <AppLoading />
        </div>
      );
    }

    if (getRecoProfileDetail?.error?.errorCode === 400) {
      return (
        <div className="p-12">
          <AppAccessDenied />
        </div>
      );
    }

    return (
      <div>
        <Row className="mb-6">
          <Col className="pr-2" span={12}>
            <div className="container-item-detail-modal text-color-primary">
              <div className="p-5 h-[65vh] overflow-auto text16">
                <Row className="items-start">
                  <Row className="flex-1 items-center">
                    <Avatar size={64} className="avatar">
                      {getAbbreviatedName(employeeDetail?.name)}
                    </Avatar>
                    <span className="text24 font-bold flex-1 ml-4">
                      {employeeDetail?.name}
                    </span>
                  </Row>
                  <Row className="items-center">
                    <div
                      className="dot"
                      style={{
                        backgroundColor: getStatusEmployee(
                          employeeDetail?.statusId
                        ).color,
                      }}
                    />
                    <span className="text16 ml-1">
                      {getStatusEmployee(employeeDetail?.statusId).label}
                    </span>
                  </Row>
                </Row>
                <Row className="mt-4">
                  {listInfo.map((item, index) => (
                    <Row className="flex items-center w-1/2 mt-2" key={index}>
                      <Icon size={20} icon={item.icon} />
                      <span className="ml-2 flex-1 break-all text-color-primary">
                        {item.value || "N/A"}
                      </span>
                    </Row>
                  ))}
                </Row>
                <div className="line" />
                {listOtherInfo.map(
                  (item, index): JSX.Element => (
                    <div key={index} className={index === 1 ? "mt-10" : ""}>
                      <span className="text16 font-bold ">{item.title}</span>
                      {item.info.map(
                        (i, ind): JSX.Element => (
                          <Row className="items-center mt-3" key={ind}>
                            <Col span={12} className="font-light">
                              {i.title}
                            </Col>
                            <Col className="flex-1 break-all text14 font-medium">
                              {i.value || "N/A"}
                            </Col>
                          </Row>
                        )
                      )}
                    </div>
                  )
                )}
                <div className="mt-6 text14">Ghi chú</div>
                <span className="text12 font-light">
                  {employeeDetail?.note || "N/A"}
                </span>
              </div>
            </div>
          </Col>
          <Col className="pl-2" span={12}>
            <div className="container-item-detail-modal h-[65vh] flex flex-col p-4">
              <span className="text24 text-color-primary">
                {` Danh sách ${
                  tabActive === "customer" ? "khách hàng" : "ứng viên"
                } `}
                <span className="text14">
                  (
                  {tabActive === "customer"
                    ? `${
                        employeeDetail?.listCustomerInfo?.length || 0
                      } khách hàng`
                    : `${
                        employeeDetail?.listCandidateInfo?.length || 0
                      } ứng viên`}
                  )
                </span>
              </span>
              {renderContentRight()}
            </div>
          </Col>
        </Row>
        {isAdmin && (
          <Row className="justify-center items-center mt-6">
            <AppButton
              classrow="w-48 mr-4"
              label="Reset password"
              typebutton="secondary"
              onClick={(): void => setIsShowModalConfirmPassword(true)}
            />
            <AppButton
              classrow="w-48 ml-4˝"
              label="Cập nhật"
              typebutton="primary"
              onClick={(): void => setIsShowModelEdit(true)}
            />
          </Row>
        )}
      </div>
    );
  };

  return (
    <div>
      <AppModal
        className="modal-employee-detail-container"
        open={employeeId !== -1}
        footer={null}
        onCancel={handleClose}
        width="80%"
        title="Chi tiết nhân viên"
      >
        {renderBodyModal()}
      </AppModal>
      <ModalFormEmployee
        open={isShowModelEdit}
        onClose={handelCloseModalEdit}
        id={employeeId}
        getRecoProfileDetail={getRecoProfileDetail}
        getDetailTeam={getDetailTeam}
        getListEmployee={getListEmployee}
      />
      <AppModalConfirm
        open={isShowModalConfirmPassword}
        content="Nếu xác nhận, hệ thống sẽ tự động tạo mật khẩu mới và gửi đến email của tài khoản."
        title="Xác nhận đặt lại mật khẩu"
        onCancel={(): void => setIsShowModalConfirmPassword(false)}
        onOk={handleResetPass}
      />
    </div>
  );
}
