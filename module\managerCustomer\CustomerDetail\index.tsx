import ApiCustomer from "@app/api/ApiCustomer";
import AppAccessDenied from "@app/components/AppAccessDenied";
import AppBreadcrumb from "@app/components/AppBreadcrumb";
import config from "@app/config";
import {useRouter} from "next/router";
import React, {useEffect, useRef} from "react";
import {useQuery} from "react-query";
import "./index.scss";
import {Col, Row} from "antd";
import AppLoading from "@app/components/AppLoading";
import {getStatusCustomer} from "@app/utils/constants/function";
import Icon from "@app/components/Icon/Icon";
import HtmlComponent from "@app/components/HtmlComponent";
import AppTable from "@app/components/AppTable";
import {IRequestJob} from "@app/api/ApiRequestJob";
import {ColumnsType} from "antd/lib/table";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {getStatusRequestJob} from "@app/utils/constants/state";
import AppButton from "@app/components/AppButton";
import {selectUser} from "@app/redux/slices/UserSlice";
import {useSelector} from "react-redux";
import {IAccountRole} from "@app/types";
import AppTag from "@app/components/AppTag";
import ApiUser from "@app/api/ApiUser";
import MeetingMinute from "./MeetingMinute";

const listBreadcrumb = [
  {
    breadcrumb: "Danh sách khách hàng",
    href: config.PATHNAME.MANAGER_CUSTOMER,
  },
  {
    breadcrumb: "Thông tin chi tiết khách hàng",
    href: config.PATHNAME.CUSTOMER_DETAIL,
  },
];

export default function CustomerDetail(): JSX.Element {
  const router = useRouter();
  const timeOut = useRef<any>(null);
  let id: string;
  const {user} = useSelector(selectUser);
  const isAML = user?.role?.includes(IAccountRole.AML);
  const isBD = [IAccountRole.BD, IAccountRole.BDL].some((item) =>
    user?.role?.includes(item)
  );

  if (router.query.id) {
    id = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    id = Object.fromEntries(searchParams)?.id;
  }

  const getDetailCustomer = useQuery(
    ["getDetailCustomer", id],
    () => {
      return ApiCustomer.getDetailCustomer(id);
    },
    {
      enabled: !!id,
      onError: (error: any) => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            router.back();
          }, 4000);
        }
      },
    }
  );

  const getRequestJob = useQuery(
    ["getRequestJob"],
    () => {
      return ApiCustomer.getRequestJob(id);
    },
    {
      enabled: !!id,
    }
  );

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const routerEdit = (): void => {
    router.push(`/manager-customer/${id}/edit`);
  };

  const navigatorJobDetail = (idJob: number): void => {
    router.push(`${config.PATHNAME.JOB_DETAIL}?id=${idJob}`);
  };

  if (getDetailCustomer?.error?.errorCode === 400) {
    return <AppAccessDenied />;
  }

  function renderItemLeft(): JSX.Element {
    if (getDetailCustomer.isLoading) {
      return <AppLoading />;
    }
    const customer = getDetailCustomer.data?.customer;
    const status = getStatusCustomer(String(customer?.customerStatus));

    const listInfo: {nameIcon: string; value: any}[] = [
      {
        value: customer?.contact,
        nameIcon: "user-star-line",
      },
      {
        value: customer?.address,
        nameIcon: "map-pin-line-pin-line",
      },
      {
        value: customer?.email,
        nameIcon: "mail-line",
      },
      {
        value: customer?.website,
        nameIcon: "global-line",
      },
      {
        value: customer?.phone,
        nameIcon: "phone-line",
      },
      {
        value: customer?.workingTime,
        nameIcon: "time-line",
      },
      {
        value: customer?.field,
        nameIcon: "building-community",
      },
      {
        value: customer?.assignName,
        nameIcon: "team-line",
      },
      {
        value: customer?.size,
        nameIcon: "grommet-icons-user-manager",
      },
    ];

    const infoJD: {title: string; value: string}[] = [
      {
        title: "Giới thiệu công ty",
        value: customer?.profile || "",
      },
      {
        title: "Phúc lợi chính sách",
        value: customer?.benefit || "",
      },
      {
        title: "Quy trình phỏng vấn",
        value: customer?.interviewProcess || "",
      },
      {
        title: "Ghi chú",
        value: customer?.note || "",
      },
    ];

    return (
      <div>
        <Row className="items-center justify-between">
          <Col className="font-bold text24 " span={14}>
            {customer?.name || "N/A"}
          </Col>
          <Col span={10}>
            <Row className="items-center">
              <div className="text12 px-4 py-0.5 text-white rounded-xl mr-5 company-type">
                {customer?.companyType}
              </div>
              <Row className="items-center">
                <div
                  className="dot-status mr-1"
                  style={{backgroundColor: status.color}}
                />
                <span>{status.label}</span>
              </Row>
            </Row>
          </Col>
        </Row>
        <span className=" mt-1">{customer?.customerId}</span>
        <Row>
          {listInfo.map((item, index) => (
            <Row className="flex items-center w-1/2 mt-2" key={index}>
              <Icon size={20} icon={item.nameIcon} />
              <HtmlComponent
                classNameContainer="ml-2 text16 flex-1 break-all"
                htmlString={item.value}
              />
            </Row>
          ))}
        </Row>
        {infoJD.map((item, index) => (
          <div key={index}>
            <div className="line my-4" />
            <span className="font-bold tex16">{item.title}</span>
            <HtmlComponent
              classNameContainer="mt-2 text14"
              htmlString={item.value}
            />
          </div>
        ))}
      </div>
    );
  }

  function renderItemRight(): JSX.Element {
    const listRequest = getRequestJob?.data || [];
    const isLogin = ApiUser.isLogin();
    const customer = getDetailCustomer.data?.customer;

    const allRoleWithoutCTV =
      isLogin && !user?.roleList?.includes(IAccountRole.CTV);

    const columns: ColumnsType<IRequestJob> = [
      {
        title: "Tên request",
        dataIndex: "name",
        key: "name",
        render: (_, item: IRequestJob) => (
          // eslint-disable-next-line jsx-a11y/no-static-element-interactions
          <span
            className="cursor-pointer"
            onClick={(): void => {
              navigatorJobDetail(item.requestJobId);
            }}
          >
            {item?.name || ""}
          </span>
        ),
      },
      {
        title: "Vị trí",
        dataIndex: "positionName",
        key: "positionName",
      },
      {
        title: "Ngày request",
        dataIndex: "requestDate",
        key: "requestDate",
        render: (_, item: IRequestJob) => (
          <span>
            {item?.requestDate
              ? moment(item?.requestDate).format(DATE_FORMAT)
              : ""}
          </span>
        ),
      },
      {
        title: "Trạng thái",
        dataIndex: "status",
        key: "status",
        render: (_, item: IRequestJob) => {
          const status = getStatusRequestJob(item?.status);
          return <AppTag color={status.color}>{status.label}</AppTag>;
        },
      },
      {
        title: "Ngày kết thúc",
        dataIndex: "expiryDate",
        key: "expiryDate",
        render: (_, item: IRequestJob) => <span>{item?.expiryDate || ""}</span>,
      },
    ];

    return (
      <div>
        <div>
          <div className="text24 text-color-primary mb-4">
            Danh sách request
            <span className="text16">{` (${listRequest.length} request)`}</span>
          </div>
          <AppTable
            rowKey="requestJobId"
            dataSource={listRequest}
            columns={columns}
            loading={getRequestJob.isLoading}
            scroll={{y: "55vh"}}
          />
        </div>
        {allRoleWithoutCTV && (
          <MeetingMinute
            idCustomer={id}
            customerName={`${customer?.name}_${moment().format("DDMMYYYY")}`}
          />
        )}
      </div>
    );
  }

  return (
    <div className="container-customer-detail text-color-primary text16">
      <AppBreadcrumb separator=">" items={listBreadcrumb} />
      <Row className="mt-3">
        <Col span={12} className="">
          <div className="container-customer-detail__left mr-2">
            <div className="p-4 overflow-auto h-[76vh]">{renderItemLeft()}</div>
          </div>
        </Col>
        <Col span={12}>
          <div className="container-customer-detail__right ml-2">
            <div className="p-4 h-[76vh] overflow-auto">
              {renderItemRight()}
            </div>
          </div>
        </Col>
      </Row>

      <Row className="justify-center items-center mt-5">
        {!isBD && (
          <AppButton
            classrow={`w-64 ${isAML ? "mr-2" : ""}`}
            label="Tạo mới request"
            typebutton="secondary"
            onClick={(): void => {
              router.push({
                pathname: config.PATHNAME.MANAGER_REQUEST_ADD,
                query: {
                  customerId: id,
                },
              });
            }}
          />
        )}
        {(!!isAML || !!isBD) && !!getDetailCustomer.data?.customer?.isOwner && (
          <AppButton
            classrow="ml-2 w-64"
            label="Chỉnh sửa thông tin"
            typebutton="primary"
            onClick={routerEdit}
          />
        )}
      </Row>
    </div>
  );
}
