import React, {useImperative<PERSON>andle, useMemo, useRef, useState} from "react";
import "./index.scss";
import AppTable from "@app/components/AppTable";
import type {ColumnsType} from "antd/es/table";
import ApiApplication, {
  IApplication,
  IListApplication,
  ParamsGroupByApplication,
} from "@app/api/ApiApplication";
import moment from "moment";
import {UseQueryResult, useMutation} from "react-query";
import {
  findApplicationStatus,
  setQueryUrl,
  timeSince,
} from "@app/utils/constants/function";
import {DATE_FORMAT, TIME_FORMAT} from "@app/utils/constants/formatDateTime";
import AppPagination from "@app/components/AppPagination";
import {useSelector, useDispatch} from "react-redux";
import {
  initListColShowApplication,
  selectUser,
} from "@app/redux/slices/UserSlice";
import HtmlComponent from "@app/components/HtmlComponent";
import AppCheckBox from "@app/components/AppCheckbox";
import {IAccountRole, OptionSelect} from "@app/types";
import {
  getColorCandidateRate,
  stagesApplication,
  statusesApplication,
} from "@app/utils/constants/state";
import {Popover, Tooltip} from "antd";
import config from "@app/config";
import Icon from "@app/components/Icon/Icon";
import {SelectInput} from "@app/components/SelectInput";
import {Formik, FormikProps} from "formik";
import {FormUpdate} from "../ModalDetailApplication";

import {setLoading} from "@app/redux/slices/SystemSlice";
import AppDataGroupBy from "@app/components/AppDataGroupBy";
import {IRootState} from "@app/redux/store";
import {
  setDataGroupByValue,
  setGroupByFieldApplication,
  setSelectGroupByValue,
} from "@app/redux/slices/GroupByApplication";

interface ListCandidateTypeTableProps {
  dataListApplications: UseQueryResult<IListApplication, unknown>;
  currentPage?: number;
  handlePagination?: (page: number, pageSize: number) => void;
  pageSize?: number;
  setApplicationData?: (id: number) => void;
  tableType: "application" | "applicationForJob";
  canEdit?: boolean;
}

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
function ListCandidateTypeTable(
  {
    dataListApplications,
    currentPage,
    handlePagination,
    pageSize,
    setApplicationData,
    tableType,
    canEdit = false,
  }: ListCandidateTypeTableProps,
  ref: any
) {
  const {listColShowApplication, user} = useSelector(selectUser);
  const {groupByFieldApplication, dataGroupApplication, isLoadingApplication} =
    useSelector((state: IRootState) => state.groupByApplication);
  const isCTV = user?.role?.includes(IAccountRole.CTV);

  const isCS =
    user?.role &&
    user.role?.filter((item) =>
      [IAccountRole.CSL, IAccountRole.CST].some((i) => i === item)
    )?.length > 0;
  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);
  const [selectCandidate, setSelectCandidate] = useState<IApplication>(
    {} as IApplication
  );
  const infoUpdateRef = useRef<FormikProps<FormUpdate>>(null);
  const dispatch = useDispatch();

  const handleSelectCandidate = (data: IApplication): void => {
    setSelectCandidate(data);
  };

  const resetSelectCandidate = (): void => {
    setSelectCandidate({} as IApplication);
  };

  const onCellClick = (item: IApplication): void => {
    if (tableType === "application") return;

    window.open(
      `${window.location.origin}/${config.PATHNAME.MANAGER_APPLICATION}?id=${item.applicationId}`,
      "_blank"
    );
  };

  // chi co user co role cst, csl, admin moi co filter nguoi quan ly
  const listColShowByCSL = (list: Array<string>): Array<string> =>
    !(isCS || isAdmin) ? list.filter((col) => col !== "managerName") : list;

  // khi khong co khoi tao listColShowApplication do website dang su dung
  const listColShow =
    listColShowByCSL(listColShowApplication) ||
    listColShowByCSL(initListColShowApplication);

  useImperativeHandle(
    ref,
    () => ({
      columnsAll,
    }),
    []
  );

  const getStage = (state: number): OptionSelect | undefined => {
    let stage = stagesApplication?.find((i) => Number(i.id) === state);

    if (stage) {
      stage = {...stage, key: stage.id as string | null};
    }
    return stage;
  };

  const getStatus = (statusJob: number): OptionSelect | undefined => {
    if (!statusJob) {
      return {
        id: null,
        label: "Chưa hoàn thành",
        value: "Chưa hoàn thành",
        key: null,
      };
    }
    let status = statusesApplication?.find((i) => Number(i.id) === statusJob);
    if (status) {
      status = {...status, key: status.id as string | null};
    }
    return status;
  };

  const disableStatus = (
    record: IApplication,
    idStatus?: string | null,
    idStage?: string | null
  ): boolean => {
    if (!idStatus && Number(idStage) === record?.stage && !!record?.status) {
      return true;
    }

    if (idStatus === "5" && !(record?.stage === 3 && record?.status === 3)) {
      return true;
    }

    return false;
  };

  const {mutate: updateStage} = useMutation(
    (data: {applicationId: number; stage: number; status: number}) => {
      return ApiApplication.updateStageApplication(data);
    },
    {
      onSuccess: (data) => {
        setSelectCandidate({} as IApplication);
      },
      onSettled: () => {
        dispatch(setLoading(false));
        dataListApplications.refetch();
      },
    }
  );

  const handleUpdateStage = (): void => {
    const value = {
      applicationId: selectCandidate.applicationId,
      stage: Number(
        (infoUpdateRef.current?.values?.stageSelected as any).key || 0
      ),
      status: Number(
        (infoUpdateRef.current?.values?.statusSelected as any).key
      ),
    };
    updateStage(value);
  };

  const {mutateAsync} = useMutation(
    (data: ParamsGroupByApplication) => {
      return ApiApplication.getListApplicationGroupBy(data);
    },
    {
      onSuccess(data) {
        dispatch(setDataGroupByValue(data));
      },
    }
  );

  const onClickGroupBy = async (id: string, currentPage: number) => {
    try {
      dispatch(setLoading(true));
      dispatch(setSelectGroupByValue(id));
      const params: ParamsGroupByApplication = {
        ...groupByFieldApplication,
        groupByRequest: {
          ...groupByFieldApplication.groupByRequest,
          groupByValue: id,
          pageSize: 20,
          pageNumber: currentPage,
        },
      };

      await mutateAsync(params);
    } catch (e) {
      console.log(e);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const columnsAll: ColumnsType<IApplication> = [
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
      className: "cursor-pointer column-candidate",
      width: "180px",
      fixed: "left",
      onCell: (record: IApplication): any => {
        return {
          onClick: (): void => {
            setQueryUrl({id: String(record.applicationId)});
            setApplicationData?.(record?.applicationId);
          },
        };
      },
      align: "center",
      render: (_, {candidateName, applicationId}: IApplication) => (
        <span>{`${applicationId} - ${candidateName}`}</span>
      ),
    },
    {
      title: "Vị trí",
      dataIndex: "positionName",
      key: "positionName",
      width: "220px",
      align: "center",
    },
    {
      title: "Công ty",
      dataIndex: "customerName",
      width: "140px",
      key: "customerName",
      align: "center",
    },
    {
      title: "Số điện thoại",
      dataIndex: "candidatePhone",
      key: "candidatePhone",
      width: "130px",
      align: "center",
    },
    {
      title: "Email",
      dataIndex: "candidateEmail",
      key: "candidateEmail",
      width: "130px",
      align: "center",
    },
    {
      title: "Trạng thái",
      key: "statusName",
      dataIndex: "statusName",
      width: "150px",
      render: (
        _,
        record: // status,
        IApplication
      ): JSX.Element => {
        const isEdit = selectCandidate.applicationId === record.applicationId;
        const disabledEdit = record.stage === 3 && record.status === 5;

        let statusResult = "";
        if (record?.stageName) {
          statusResult += record.stageName;
        }

        if (record?.statusName) {
          statusResult += " " + record.statusName;
        }

        return (
          <div>
            {isEdit ? (
              <Formik
                innerRef={infoUpdateRef}
                onSubmit={(): void => {
                  //
                }}
                initialValues={{
                  stageSelected: getStage(record?.stage as any),
                  statusSelected: getStatus(record?.status as any),
                }}
              >
                {({values}): JSX.Element => {
                  return (
                    <div>
                      <div>
                        <SelectInput
                          // containerclassname="mt-2"
                          name="stageSelected"
                          labelselect="Giai đoạn"
                          data={
                            stagesApplication?.map((i: OptionSelect) => ({
                              ...i,
                              disabled:
                                Number(i.id) < Number(record?.stage || 0),
                            })) || []
                          }
                          value={values.stageSelected}
                          disabled={disabledEdit}
                        />
                      </div>
                      <div>
                        <SelectInput
                          containerclassname="mt-2"
                          name="statusSelected"
                          labelselect="Trạng thái"
                          data={
                            statusesApplication?.map((i: OptionSelect) => ({
                              ...i,
                              disabled: disableStatus(
                                record,
                                i.id as string | null,
                                values.stageSelected?.key
                              ),
                            })) || []
                          }
                          value={values.statusSelected}
                          disabled={disabledEdit}
                        />
                      </div>
                    </div>
                  );
                }}
              </Formik>
            ) : (
              <span
                className="status-cv"
                style={{
                  backgroundColor: findApplicationStatus(
                    String(record?.statusName),
                    true
                  ).color,
                }}
              >
                {statusResult}
              </span>
            )}
          </div>
        );
      },
      align: "center",
    },
    {
      title: "Dịch vụ",
      dataIndex: "services",
      key: "services",
      width: "130px",
      render: (value) => {
        return <span>{value?.join(", ")}</span>;
      },
    },
    {
      title: "Thời gian xử lý",
      dataIndex: "timeProcess",
      key: "timeProcess",
      render: (_, {modifiedDate}: IApplication) => (
        <span>{timeSince(new Date(modifiedDate))}</span>
      ),
      width: "130px",
      align: "center",
    },
    {
      title: "Tỷ lệ phù hợp",
      dataIndex: "rate",
      key: "rate",
      render: (_, {matching}: IApplication): JSX.Element => {
        const content = (): JSX.Element => (
          <div className="max-w-xs">
            <div className="text-xs font-normal text-[#324054] mb-1">
              <span className="font-semibold">Mức độ phù hợp: </span>
              <span style={{color: getColorCandidateRate(matching.rate)}}>
                {matching.rate !== null ? `${matching.rate}%` : null}
              </span>
            </div>
            <div className="text-xs font-normal text-[#324054] mb-1">
              <span className="font-semibold">Phù hợp: </span>
              {matching.suitable || "N/A"}
            </div>
            <div className="text-xs font-normal text-[#324054] mb-1">
              <span className="font-semibold">Chưa phù hợp: </span>
              {matching.unsuitable || "N/A"}
            </div>
          </div>
        );

        const title = (): JSX.Element => (
          <div className="text-base font-bold text-[#324054]">Đánh giá</div>
        );
        return (
          <Popover content={content} title={title} placement="leftTop">
            <span
              style={{color: getColorCandidateRate(matching?.rate || 0)}}
              className="cursor-pointer"
            >
              {matching?.rate !== null ? `${matching?.rate}%` : null}
            </span>
          </Popover>
        );
      },

      width: "130px",
      align: "center",
    },
    {
      title: "Thời gian ứng tuyển",
      dataIndex: "createDate",
      key: "createDate",
      render: (_, {createdDate}: IApplication) => (
        <span>
          {createdDate ? (
            <>
              {moment(createdDate).format(TIME_FORMAT)}
              <br />
              {moment(createdDate).format(DATE_FORMAT)}
            </>
          ) : (
            ""
          )}
        </span>
      ),
      width: "110px",
      align: "center",
    },
    {
      title: "Người tạo",
      dataIndex: "creatorName",
      key: "creatorName",
      width: "130px",
      align: "center",
    },
    {
      title: "Người quản lý",
      dataIndex: "managerName",
      width: "130px",
      key: "managerName",
      align: "center",
    },
    {
      title: "Ủy thác",
      dataIndex: "commissionFlag",
      key: "commissionFlag",
      width: "80px",
      render: (_, {commissionFlag}: IApplication) => (
        <AppCheckBox checked={!!commissionFlag} disabled />
      ),
      align: "center",
    },
    {
      title: "Đánh giá",
      dataIndex: "summary",
      key: "summary",
      render: (_, {summary}: IApplication) => (
        <Tooltip
          title={
            <HtmlComponent
              classNameContainer="application-table__summary-tooltip"
              htmlString={summary || ""}
            />
          }
          placement="leftTop"
          overlayStyle={{maxWidth: "400px"}}
        >
          <span>
            <HtmlComponent
              classNameContainer="line-clamp-5 cursor-pointer"
              htmlString={summary || ""}
            />
          </span>
        </Tooltip>
      ),
      align: "center",
      width: "130px",
    },
    {
      title: "Action",
      dataIndex: "editStatus",
      key: "editStatus",
      className: "cursor-pointer",
      align: "center",
      render: (_, record: IApplication): JSX.Element => {
        const isEdit = selectCandidate.applicationId === record.applicationId;
        return (
          <span>
            {isEdit ? (
              <span>
                <Icon
                  icon="check-line"
                  size={12}
                  onClick={handleUpdateStage}
                  className="p-2"
                />
                <Icon
                  icon="close-line"
                  size={12}
                  onClick={resetSelectCandidate}
                  className="ml-2 p-2"
                />
              </span>
            ) : (
              <Icon
                icon="edit-line"
                size={12}
                onClick={(): void => {
                  handleSelectCandidate(record);
                }}
                className="p-3"
              />
            )}
          </span>
        );
      },
    },
  ];

  const columnsForJob: ColumnsType<IApplication> = [
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
      className: "cursor-pointer",
      render: (_, {candidateName, applicationId}: IApplication) => (
        <span>{`${applicationId} - ${candidateName}`}</span>
      ),
      onCell: (record: IApplication) => {
        return {
          onClick: (): void => {
            onCellClick(record);
          },
        };
      },
    },

    {
      title: "Số điện thoại",
      dataIndex: "candidatePhone",
      key: "candidatePhone",
      className: "cursor-pointer",
      onCell: (record: IApplication) => {
        return {
          onClick: (): void => {
            onCellClick(record);
          },
        };
      },
    },
    {
      title: "Email",
      dataIndex: "candidateEmail",
      key: "candidateEmail",
      className: "cursor-pointer",
      onCell: (record: IApplication) => {
        return {
          onClick: (): void => {
            onCellClick(record);
          },
        };
      },
    },
    {
      title: "Trạng thái",
      key: "statusName",
      dataIndex: "statusName",
      className: "cursor-pointer",
      render: (
        _,
        record: // status,
        IApplication
      ): JSX.Element => {
        const isEdit = selectCandidate.applicationId === record.applicationId;
        const disabledEdit = record.stage === 3 && record.status === 5;

        let statusResult = "";
        if (record?.stageName) {
          statusResult += record.stageName;
        }

        if (record?.statusName) {
          statusResult += " " + record.statusName;
        }

        const handleChangeStatus = (
          statusSelected: OptionSelect,
          values: FormUpdate
        ): void => {
          const currentStageIndex = stagesApplication?.findIndex(
            (item) => Number(item.key) === Number(values?.stageSelected?.key)
          );

          const nextStageIndex = currentStageIndex + 1;

          const nextStage = stagesApplication?.[nextStageIndex];

          // statusesApplication value Pass = "3"
          if (
            statusSelected.key === "3" &&
            currentStageIndex < stagesApplication.length - 1
          ) {
            infoUpdateRef.current?.setValues({
              stageSelected: nextStage,
              statusSelected: statusesApplication[0],
            });
            return;
          }

          infoUpdateRef.current?.setValues({
            stageSelected: values.stageSelected,
            statusSelected: statusSelected,
          });
        };

        return (
          <div>
            {isEdit ? (
              <Formik
                innerRef={infoUpdateRef}
                onSubmit={(): void => {
                  //
                }}
                initialValues={{
                  stageSelected: getStage(record?.stage as any),
                  statusSelected: getStatus(record?.status as any),
                }}
              >
                {({values}): JSX.Element => {
                  const isDisabledStage = (itemStage: OptionSelect) => {
                    return (
                      Number(itemStage.order) <
                      (stagesApplication.find(
                        (item) =>
                          Number(item.key || item.id) === Number(record?.stage)
                      )?.order || 0)
                    );
                  };

                  return (
                    <div>
                      <div>
                        <SelectInput
                          // containerclassname="mt-2"
                          name="stageSelected"
                          labelselect="Giai đoạn"
                          data={
                            stagesApplication?.map((i: OptionSelect) => ({
                              ...i,
                              disabled: isDisabledStage(i),
                            })) || []
                          }
                          value={values.stageSelected}
                          disabled={disabledEdit}
                        />
                      </div>
                      <div>
                        <SelectInput
                          containerclassname="mt-2"
                          name="statusSelected"
                          labelselect="Trạng thái"
                          data={
                            statusesApplication?.map((i: OptionSelect) => ({
                              ...i,
                              disabled: disableStatus(
                                record,
                                i.id as string | null,
                                values.stageSelected?.key
                              ),
                            })) || []
                          }
                          value={values.statusSelected}
                          disabled={disabledEdit}
                          handleChange={(value) =>
                            handleChangeStatus(value, values)
                          }
                        />
                      </div>
                    </div>
                  );
                }}
              </Formik>
            ) : (
              <span
                className="status-cv"
                style={{
                  backgroundColor: findApplicationStatus(
                    String(record?.statusName),
                    true
                  ).color,
                }}
              >
                {statusResult}
              </span>
            )}
          </div>
        );
      },
    },
    {
      title: "Thời gian ứng tuyển",
      dataIndex: "createDate",
      key: "createDate",
      className: "cursor-pointer",
      render: (_, {createdDate}: IApplication) => (
        <span>
          {createdDate ? (
            <>
              {moment(createdDate).format(TIME_FORMAT)}
              <br />
              {moment(createdDate).format(DATE_FORMAT)}
            </>
          ) : (
            ""
          )}
        </span>
      ),
      onCell: (record: IApplication) => {
        return {
          onClick: (): void => {
            onCellClick(record);
          },
        };
      },
    },
    {
      title: "Action",
      dataIndex: "editStatus",
      key: "editStatus",
      className: "cursor-pointer",
      width: "8%",
      render: (_, record: IApplication): JSX.Element => {
        const isEdit = selectCandidate.applicationId === record.applicationId;
        return (
          <span>
            {isEdit ? (
              <span>
                <Icon
                  icon="check-line"
                  size={12}
                  onClick={handleUpdateStage}
                  className="p-2"
                />
                <Icon
                  icon="close-line"
                  size={12}
                  onClick={resetSelectCandidate}
                  className="ml-2 p-2"
                />
              </span>
            ) : (
              <Icon
                icon="edit-line"
                size={12}
                onClick={(): void => {
                  handleSelectCandidate(record);
                }}
                className="p-3"
              />
            )}
          </span>
        );
      },
    },
  ];

  const columnsTable = useMemo(() => {
    const colNotShowRoleCTV = ["customerName", "commissionFlag"];
    const initialColumn = ["candidateName", "positionName", ...listColShow];

    if (!isCTV) {
      initialColumn.push(...colNotShowRoleCTV);
    }

    const columns = columnsAll.filter((item) =>
      initialColumn.some((i) => i === item.key)
    );

    const tableOverall = tableType === "application" ? columns : columnsForJob;

    return canEdit && !isCTV
      ? tableOverall
      : tableOverall.filter((item) => item.key !== "editStatus");
  }, [canEdit, isCTV, selectCandidate, listColShow]);

  const onPageChangeGroupBy = (page: number, size: number) => {
    const dataChange = {
      ...groupByFieldApplication,
      groupByRequest: {
        ...groupByFieldApplication.groupByRequest,
        pageSize: size,
        pageNumber: page,
      },
    };

    dispatch(setGroupByFieldApplication(dataChange));
  };

  return (
    <div className="table-list-applications">
      {Number(groupByFieldApplication.groupByRequest.groupBy) > 0 &&
      tableType === "application" ? (
        <AppDataGroupBy
          column={columnsTable}
          dataGroupBy={dataGroupApplication.data}
          rowTableClassName="row-table-applications"
          onClickItem={onClickGroupBy}
          pageNumber={groupByFieldApplication.groupByRequest.pageNumber}
          pageSize={groupByFieldApplication.groupByRequest.pageSize}
          totalCount={dataGroupApplication.totalCount}
          onPageChangeGroupBy={onPageChangeGroupBy}
          dataQueryGroupBy={groupByFieldApplication}
          isLoadingGroupBy={isLoadingApplication}
        />
      ) : (
        <div>
          <AppTable
            dataSource={dataListApplications?.data?.applicationsPaging?.map(
              (item: IApplication, index: number) => ({
                ...item,
                key: index,
              })
            )}
            rowClassName="row-table-applications"
            columns={columnsTable}
            scroll={{y: "60vh", x: "max-content"}}
          />
          <AppPagination
            defaultPageSize={pageSize}
            current={currentPage}
            pageSize={pageSize}
            total={dataListApplications.data?.totalCount}
            onChange={handlePagination}
          />
        </div>
      )}
    </div>
  );
}

export default React.forwardRef(ListCandidateTypeTable);
