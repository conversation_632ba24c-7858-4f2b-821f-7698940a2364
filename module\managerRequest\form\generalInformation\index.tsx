import AppDatePicker from "@app/components/AppDatePicker";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {OptionSelect} from "@app/types";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {
  durationExpertTime,
  labelOption,
  optionServiceRequest,
  priorities,
  requestTypes,
} from "@app/utils/constants/state";
import {Col, Row} from "antd";
import {Input} from "formik-antd";
import React, {useEffect, useState} from "react";
// eslint-disable-next-line import/no-cycle
import {listPartnerRateType} from "..";
// import {useSelector} from "react-redux";
// import {selectUser} from "@app/redux/slices/UserSlice";
import AppCkEditor from "@app/components/AppCkEditor";
import {BonusTypeOptionSelect} from "@app/api/ApiRequestJob";

interface Props {
  values: any;
  setFieldInput: (name: string, value: any) => void;
  amList: OptionSelect[];
  hotBonus: BonusTypeOptionSelect[];
  partnerRateType: "0" | "1";
  interviewProcess: string;
  handleChangeInterviewProcess: (value: string) => void;
  touched?: any;
}

const {TextArea} = Input;

function GeneralInformation(props: Props): JSX.Element {
  const {
    values,
    setFieldInput,
    amList,
    hotBonus,
    partnerRateType,
    interviewProcess,
    handleChangeInterviewProcess,
    touched,
  } = props;
  const [typeRate, setTypeRate] = useState<string>("0");
  // const {user} = useSelector(selectUser);
  // const roleUser = user?.role || [];
  // const isRoleAml = roleUser?.includes(IAccountRole.AML);

  useEffect(() => {
    setTypeRate(partnerRateType);
  }, [partnerRateType]);

  return (
    <div>
      <p className="text24">Thông tin chung</p>
      <div className="create-request__form-general-information">
        <Row gutter={[64, 32]} className="mt-2">
          <Col xs={12}>
            <p className="text16">Quản lý</p>
            <SelectInput
              name="priority"
              labelselect="Độ ưu tiên"
              data={priorities}
              value={values?.priority}
              free={!values?.priority?.value}
              allowClear
              containerclassname="mt-2"
              required
              status={
                !values?.priority?.value && touched?.priority
                  ? "error"
                  : undefined
              }
            />
            <SelectInput
              name="requestType"
              labelselect="Loại request"
              data={requestTypes}
              value={values?.requestType}
              free={!values?.requestType?.key}
              allowClear
              containerclassname="mt-2"
              required
              status={
                !values?.requestType?.value && touched?.requestType
                  ? "error"
                  : undefined
              }
            />
            <SelectInput
              name="accountManagerId"
              labelselect="AM quản lý"
              data={amList}
              value={values?.accountManagerId}
              free={!values?.accountManagerId?.value}
              allowClear
              containerclassname="mt-2"
              // disabled={!isRoleAml}
              optionFilterProp="label"
              showSearch
            />
            <SelectInput
              name="services"
              labelselect="Dịch vụ"
              data={optionServiceRequest}
              value={values?.services}
              free={!values?.services?.length}
              allowClear
              containerclassname="mt-2"
              optionFilterProp="label"
              showSearch
              mode="multiple"
              status={!values?.services?.length ? "error" : undefined}
              required
            />

            {values?.services?.some(
              (service: any) => service?.value === "EXPERT"
            ) && (
              <div className="flex input-time-expert mt-2">
                <TextInput
                  containerclassname="flex-1"
                  label="Duration hợp đồng"
                  name="durationValue"
                  value={values?.durationValue}
                  free={!values?.durationValue}
                  onlynumber
                />
                <AppSelectCurrency
                  name="durationUnit"
                  value={values?.durationUnit || "MONTH"}
                  style={{width: "90px"}}
                  options={durationExpertTime}
                />
              </div>
            )}
            <AppDatePicker
              label="Ngày kết thúc"
              name="expiryDate"
              valueAppDatePicker={values?.expiryDate}
              free={!values?.expiryDate}
              classNameContainer="mt-2"
              format={DATE_FORMAT}
            />
            <p className="text16 mt-2">Ghi chú</p>
            <TextArea
              name="note"
              maxLength={1000}
              className="input-note mt-1"
              style={{height: 240, resize: "none"}}
              placeholder="Ghi chú"
              value={values?.note}
            />
          </Col>
          <Col xs={12}>
            <p className="text16">Thưởng bonus</p>
            <div className="flex rate-ctv mt-2">
              <TextInput
                containerclassname="w-full"
                label="Rate CTV"
                name="partnerRateValue"
                onlynumber
                maxLength={values?.partnerRateType?.value === "0" ? 5 : 50}
                required
                value={values?.partnerRateValue}
                free={!values.partnerRateValue}
                status={
                  !values?.partnerRateValue && touched?.partnerRateValue
                    ? "error"
                    : values?.partnerRateType?.value === "0" &&
                      Number(values?.partnerRateValue) > 100 &&
                      touched?.partnerRateValue
                    ? "error"
                    : undefined
                }
                typeInput={
                  values?.partnerRateType?.value === "0" ? "" : "salary"
                }
                iscurrency={values?.partnerRateType?.value !== "0"}
              />
              <AppSelectCurrency
                name="partnerRateType"
                style={{width: "120px"}}
                options={listPartnerRateType}
                value={values?.partnerRateType}
                onChange={(e: any): void => {
                  if (e.value && e.value !== typeRate) {
                    setTypeRate(e.value);
                    setFieldInput("partnerRateValue", "");
                  }
                }}
              />
            </div>
            <SelectInput
              name="label"
              labelselect="Gán nhãn request"
              data={labelOption}
              value={values?.label}
              free={!values?.label?.value}
              allowClear
              containerclassname="mt-2"
              onSelect={(e: any): void => {
                const {value} = e;
                if (value !== "2") {
                  setFieldInput("hotBonusId", {});
                  setFieldInput("hotBonusAmount", "");
                }
              }}
              disabled={values?.status?.key === "4"}
            />
            <SelectInput
              name="hotBonusId"
              labelselect="Loại bonus"
              data={hotBonus}
              value={values?.hotBonusId}
              free={!values?.hotBonusId?.value}
              allowClear
              containerclassname="mt-2"
              disabled={values?.label?.value !== "2"}
              onSelect={(e: any): void => {
                const value = e?.value || "";
                const defaultCurrencyType = {label: "VND", value: "VND"};
                const findBonus =
                  hotBonus?.find((item) => item?.value === value) ||
                  ({} as BonusTypeOptionSelect);
                setFieldInput("hotBonusAmount", findBonus?.amount || "");
                setFieldInput("hotBonusUnit", defaultCurrencyType);
              }}
            />
            <div className="flex mt-2 hot-bonus-amount">
              <TextInput
                label="Số tiền thưởng phỏng vấn"
                name="hotBonusAmount"
                value={values?.hotBonusAmount}
                free={!values?.hotBonusAmount}
                containerclassname="w-full"
                onlynumber
                typeInput="salary"
                iscurrency
                disabled={
                  values?.label?.value !== "2" ||
                  values?.hotBonusId?.value !== "0"
                }
              />
              <AppSelectCurrency
                name="hotBonusUnit"
                value={values?.hotBonusUnit?.value}
                style={{width: "120px"}}
                disabled={values?.hotBonusId?.value !== "0"}
              />
            </div>

            <AppCkEditor
              label="Quy trình phỏng vấn"
              value={interviewProcess}
              handleChange={handleChangeInterviewProcess}
              containerclassname="mt-3"
            />
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default React.memo(GeneralInformation);
