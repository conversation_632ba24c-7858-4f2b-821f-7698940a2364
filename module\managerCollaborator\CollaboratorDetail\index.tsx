import React, {useState} from "react";
import "./index.scss";
import {useRouter} from "next/router";
import {useMutation, useQuery} from "react-query";
import ApiCollaborator, {IManagerFilters} from "@app/api/ApiCollaborator";
import {Avatar, Row, Tabs, notification} from "antd";
import {getAbbreviatedName} from "@app/utils/constants/function";
import Icon from "@app/components/Icon/Icon";
import TextArea from "antd/lib/input/TextArea";
import AppButton from "@app/components/AppButton";
import TabPane from "antd/lib/tabs/TabPane";
import TableCandidateList from "../TableCandidateList";
import TableApplicationHistory from "../TableApplicationHistory";
import config from "@app/config";
import AppModal from "@app/components/AppModal";
import CollaboratorEdit from "../CollaboratorEdit";
import {
  getStatusCollaborator,
  levelRecruitment,
  recruitmentLanguages,
  timeForReco,
  yearOfRecruitmentExperience,
} from "@app/utils/constants/state";
import AppLoading from "@app/components/AppLoading";
import {OptionSelect} from "@app/types";
import moment from "moment";
import {DATE_TIME} from "@app/utils/constants/formatDateTime";
// import TablePaymentHistory from "../TablePaymentHistory";

export default function CollaboratorDetail(): JSX.Element {
  const [isShowModalEdit, setIsShowModalEdit] = useState<boolean>(false);
  const router = useRouter();
  const [textNote, setTextNote] = useState("");

  let id: string | undefined;
  if (router.query.id) {
    id = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    id = Object.fromEntries(searchParams)?.id;
  }

  const requestCollaboratorDetail = useQuery(
    ["requestCollaboratorDetail", id],
    () => {
      return ApiCollaborator.getDetailCollaborator(id);
    }
  );

  const getAllCareerTypeList = useQuery(["getAllCareerTypeList"], () => {
    return ApiCollaborator.getAllCareerTypeList();
  });

  const getAllRecruitmentTypeList = useQuery(
    ["getAllRecruitmentTypeList"],
    () => {
      return ApiCollaborator.getAllRecruitmentTypeList();
    }
  );

  const getAllTagsOfCollaborator = useQuery(
    ["getAllTagsOfCollaborator"],
    () => {
      return ApiCollaborator.getAllTagsOfCollaborator();
    }
  );

  const addNote = useMutation(
    (body: {userId: number; content: string}) => {
      return ApiCollaborator.addNote(body);
    },
    {
      onSuccess: () => {
        setTextNote("");
        requestCollaboratorDetail.refetch();
        notification.success({
          message: "Thông báo",
          description: "Tạo ghi chú thành công.",
        });
      },
    }
  );

  const careerTypeList = getAllCareerTypeList.data || [];
  const recruitmentTypeList = getAllRecruitmentTypeList.data || [];
  const collaborator = requestCollaboratorDetail.data;

  const getRecruitmentType = (): OptionSelect => {
    if (!collaborator?.recruitmentTypeId) {
      return {
        value: "",
        label: "",
      };
    }

    const recruitment = recruitmentTypeList.find(
      (element) => element.recruitmentTypeId === collaborator?.recruitmentTypeId
    );

    return {
      value: recruitment?.name || "",
      label: recruitment?.name || "",
      key: String(recruitment?.recruitmentTypeId) || "",
    };
  };

  const getCareerType = (): OptionSelect => {
    if (!collaborator?.careerTypeId) {
      return {
        value: "",
        label: "",
      };
    }

    const career = careerTypeList.find(
      (element) => element.careerTypeId === collaborator?.careerTypeId
    );

    return {
      value: career?.name || "",
      label: career?.name || "",
      key: String(career?.careerTypeId) || "",
    };
  };

  const getYearOfExperience = () => {
    const result =
      yearOfRecruitmentExperience.find(
        (item) => Number(item.value) === collaborator?.yoe
      )?.label || "N/A";

    return result;
  };

  const getLevels = () => {
    const result =
      levelRecruitment
        ?.filter((item) => collaborator?.levels.includes(item.value))
        .map((item) => item.label)
        .join(", ") || "N/A";

    return result;
  };

  const getLanguages = () => {
    const result =
      recruitmentLanguages
        ?.filter((item) => collaborator?.languages.includes(item.value))
        .map((item) => item.label)
        .join(", ") || "N/A";

    return result;
  };

  const getTimeWorkForReco = () => {
    return (
      timeForReco.find(
        (item) => Number(item.value) === collaborator?.timeForReco
      )?.label || "N/A"
    );
  };

  const dataListCollaborators = useQuery(["getListCollaborator"], () => {
    return ApiCollaborator.getListCollaborator({
      pageSize: 1,
      currentPage: 1,
      isAdvanceSearch: false,
    });
  });

  const managerFilters =
    dataListCollaborators.data?.managerFilters?.map(
      (manager: IManagerFilters) => ({
        ...manager,
        key: manager.userId ? String(manager.userId) : "",
        value: manager?.name || "",
        label: manager?.name || "",
      })
    ) || [];

  const listCollaboratorInfo: {
    nameIcon: string;
    value?: string;
    colorIcon: string;
    link?: boolean;
  }[] = [
    {
      nameIcon: "mail-line",
      value: collaborator?.email || "N/A",
      colorIcon: "#324054",
    },
    {
      nameIcon: "facebook",
      value: collaborator?.facebook || "N/A",
      colorIcon: "rgb(24, 119, 242)",
      link: true,
    },
    {
      nameIcon: "phone-line",
      value: collaborator?.phoneNumber || "N/A",
      colorIcon: "#324054",
    },
    {
      nameIcon: "skype",
      value: collaborator?.skype || "N/A",
      colorIcon: "#00B9F1",
    },

    {
      nameIcon: "calendar-check-line",
      value: collaborator?.dateOfBirth || "N/A",
      colorIcon: "#324054",
    },
    {
      nameIcon: "linkedIn",
      value: collaborator?.linkein || "N/A",
      colorIcon: "rgb(10, 102, 194)",
      link: true,
    },
  ];

  const payInfo: string[] = [
    `CCCD/CMND: ${collaborator?.identifyCardNo || "N/A"}`,
    `Ngày cấp: ${collaborator?.issuedOn || "N/A"}`,
    `Nơi cấp: ${collaborator?.issuedAt || "N/A"}`,
    `Mã số thuế: ${collaborator?.taxCode || "N/A"}`,
    `STK: ${collaborator?.bank_AccountNumber || "N/A"}`,
    `Ngân hàng: ${collaborator?.bank_Name || "N/A"}`,
    `Chi nhánh: ${collaborator?.bank_Branch || "N/A"}`,
  ];

  const jobInfo: string[] = [
    `Nghề nghiệp: ${collaborator?.job || "N/A"}`,
    `Địa chỉ: ${collaborator?.addressName || "N/A"}`,
    `Ngành nghề tuyển dụng: ${getRecruitmentType().label}`,
    `Hình thức làm việc: ${getCareerType().label}`,
    `Người quản lý: ${collaborator?.consultantName || "N/A"}`,
    `Ngày đăng ký: ${collaborator?.createdDate || "N/A"}`,
    `Người giới thiệu: ${collaborator?.recommendationPerson || "N/A"}`,
  ];

  const jobInfo2: string[] = [
    `Số năm kinh nghiệm: ${getYearOfExperience()}`,
    `Vị trí thường tuyển: ${collaborator?.positions.join(", ") || "N/A"}`,
    `Khu vực tuyển: ${collaborator?.workLocations.join(", ") || "N/A"} `,
    `Level: ${getLevels()}`,
    `Ngoại ngữ: ${getLanguages()}`,
    `Thời gian: ${getTimeWorkForReco()}`,
  ];

  const onClose = (): void => {
    setIsShowModalEdit(false);
  };

  const saveNote = (): void => {
    addNote.mutate({
      userId: Number(id),
      content: textNote,
    });
  };

  if (requestCollaboratorDetail.isLoading) {
    return (
      <div className="h-[70vh] flex items-center justify-center">
        <AppLoading />
      </div>
    );
  }

  return (
    <div className="container-collaborator-detail text-color-primary">
      <Row>
        {collaborator?.image ? (
          <Avatar
            size={170}
            src={config.NETWORK_CONFIG.API_BASE_URL + "/" + collaborator.image}
          />
        ) : (
          <Avatar size={170} className="avatar-collaborator">
            {getAbbreviatedName(collaborator?.name)}
          </Avatar>
        )}
        <div className="w-8/12 ml-11">
          <Row className=" flex justify-between items-center text-base">
            <span className="text32 font-bold">{collaborator?.name}</span>
            <Row className="flex items-center">
              <div
                className="dot"
                style={{
                  backgroundColor: getStatusCollaborator(collaborator?.statusId)
                    .color,
                }}
              />
              <span className="text16 ml-1">
                {getStatusCollaborator(collaborator?.statusId).label}
              </span>
            </Row>
          </Row>
          <Row className="mt-7">
            {listCollaboratorInfo.map((item, index) => (
              <Row className="flex items-center w-1/2 mt-3" key={index}>
                <Icon size={14} icon={item.nameIcon} color={item.colorIcon} />
                {item.link && item.value !== "N/A" ? (
                  <a
                    className="flex-1 ml-4 text-base break-all"
                    href={item.value}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {item.value}
                  </a>
                ) : (
                  <span className="flex-1 ml-4 text-base break-all">
                    {item.value}
                  </span>
                )}
              </Row>
            ))}
          </Row>
          <div className="line" />
          <Row>
            {[
              {title: "Thông tin thanh toán", info: payInfo},
              {title: "Thông tin công việc", info: jobInfo},
              {
                title: <div className="opacity-0">Thông tin công việc</div>,
                info: jobInfo2,
              },
            ].map((item, index) => (
              <div className="w-1/3 flex flex-col" key={index}>
                <div className="mb-6 text-base text16 font-bold mt-3">
                  {item.title}
                </div>
                {item.info.map((i, indx) => (
                  <span
                    className="text-base text16 font-normal mt-3 leading-[1.5] text-justify"
                    key={indx}
                  >
                    {i}
                  </span>
                ))}
              </div>
            ))}
          </Row>
        </div>
      </Row>
      <div className="container-note mt-8">
        <div className="text-base font-bold">Ghi chú</div>
        {requestCollaboratorDetail?.data?.notes.map((item, index) => (
          <Row key={index} className="justify-between mt-1">
            <Row className="items-baseline flex-1">
              <Icon size={14} icon="history" color="#324054" />
              <span className="text-base ml-3 flex-1">{item.content}</span>
            </Row>
            <span className="text-base">
              {moment(item.createdDate).format(DATE_TIME)}
            </span>
          </Row>
        ))}
        <Row className="mt-4 flex">
          <TextArea
            maxLength={255}
            value={textNote}
            className="flex-1 mr-4"
            style={{height: 68, resize: "none"}}
            placeholder="Nhập ghi chú tại đây"
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>): void => {
              setTextNote(e.target.value);
            }}
          />
          <AppButton
            label="Lưu"
            typebutton="primary"
            classrow="btn-save"
            onClick={saveNote}
            disabled={addNote.isLoading || !textNote}
          />
        </Row>
      </div>
      <div className="mt-8 custom-ant-tab-active">
        <Tabs defaultActiveKey="listCandidate" type="card">
          {[
            {
              key: "listCandidate",
              title: "Danh sách ứng viên",
              component: <TableCandidateList collaborator={collaborator} />,
            },
            {
              key: "historyApplication",
              title: "Lịch sử ứng tuyển",
              component: (
                <TableApplicationHistory collaborator={collaborator} />
              ),
            },
            // {
            //   key: "historyPayment",
            //   title: "Lịch sử thanh toán",
            //   component: (
            //     <TablePaymentHistory payment={collaborator?.payments} />
            //   ),
            // },
          ].map(
            (item: {key: string; title: string; component: JSX.Element}) => (
              <TabPane tab={item.title} key={item.key}>
                {item.component}
              </TabPane>
            )
          )}
        </Tabs>
      </div>
      <Row className="flex justify-center mt-10 items-center">
        <AppButton
          classrow="ml-2 w-80 btn-edit"
          label="Sửa thông tin CTV"
          typebutton="primary"
          onClick={(): void => {
            setIsShowModalEdit(true);
          }}
        />
      </Row>

      <AppModal
        className="modal-edit-candidate"
        centered
        footer={null}
        open={isShowModalEdit}
        onCancel={onClose}
        title="Chỉnh sửa thông tin CTV"
        width="60%"
      >
        <CollaboratorEdit
          careerTypeList={careerTypeList}
          getCareerType={getCareerType}
          recruitmentTypeList={recruitmentTypeList}
          getRecruitmentType={getRecruitmentType}
          requestCollaboratorDetail={requestCollaboratorDetail}
          onCancel={onClose}
          managerFilters={managerFilters}
          tagsOfCollaborator={getAllTagsOfCollaborator?.data || []}
        />
      </AppModal>
    </div>
  );
}
