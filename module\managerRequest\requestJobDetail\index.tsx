import ApiRe<PERSON>Job, {
  AssignJobData,
  DataUpdateStatusJob,
  IDataCandidateRequestJob,
} from "@app/api/ApiRequestJob";
import AppBreadcrumb from "@app/components/AppBreadcrumb";
import AppLoading from "@app/components/AppLoading";
import config from "@app/config";
import {getStatusRequestJob, priorities} from "@app/utils/constants/state";
import {Avatar, Col, notification, Row, Tooltip, TreeSelect} from "antd";
import {useRouter} from "next/router";
import React, {useEffect, useMemo, useRef, useState} from "react";
import {useMutation, useQuery} from "react-query";
import "./index.scss";
import {newLabels} from "..";
import Icon from "@app/components/Icon/Icon";
import {
  compareTwoArrayNumber,
  formatMoney,
  inviteBonus,
  onlyUnique,
  salaryRange,
  timeSince,
} from "@app/utils/constants/function";
import HtmlComponent from "@app/components/HtmlComponent";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/es/table";
import AppButton from "@app/components/AppButton";
import {IAccountRole, RequestType, StatusRequestJob} from "@app/types";
import AppAccessDenied from "@app/components/AppAccessDenied";
import {IRootState} from "@app/redux/store";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {useSelector, useDispatch} from "react-redux";
import {Formik, FormikProps} from "formik";
import AppTreeSelect, {TreeSelectDataItem} from "@app/components/AppTreeSelect";
import ConversationRequestJob from "../conversationRequestJob";

const {SHOW_PARENT} = TreeSelect;

const extraKeyParent = "parent";

function makeId(length: number, parentId: string) {
  let result = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return "_" + parentId + result;
}

const numberOfChar = 2;

const clearMakeId = (data: string) => {
  if (!data) {
    return "";
  }
  const splitData = data.split("_");

  return splitData[0];
};

export const getLabelDurationTime = (durationUnit?: string) => {
  switch (durationUnit) {
    case "MONTH":
      return "Tháng";
    case "YEAR":
      return "Năm";
    default:
      return "N/A";
  }
};

interface CTSForm {
  groupCSTs: Array<string>;
}

function RequestJobDetail(): JSX.Element {
  const router = useRouter();
  const idRequestJob: string = (router?.query?.id || "") as string;
  const linkJd = `${config.NETWORK_CONFIG.API_BASE_URL}/api/jobComment/export?jobId=${idRequestJob}`;
  const {user} = useSelector((state: IRootState) => state.user);
  const dispatch = useDispatch();
  const [groupCStsData, setGroupCSTsData] = useState<Array<TreeSelectDataItem>>(
    []
  );
  const formikCSTRef = useRef<FormikProps<CTSForm>>(null);

  const isRoleCSL = useMemo(() => {
    return user?.role?.includes(IAccountRole.CSL);
  }, [user]);

  const items = [
    {
      breadcrumb: "Danh sách request",
      href: config.PATHNAME.MANAGER_REQUEST,
    },
    {
      href: config.PATHNAME.MANAGER_REQUEST_DETAIL,
      breadcrumb: "Thông tin chi tiết request",
    },
  ];

  const getDetail = useQuery(
    ["getDetailRequestJob", idRequestJob],
    () => {
      return ApiRequestJob.getDetail(idRequestJob);
    },
    {
      enabled: idRequestJob !== "",
    }
  );

  const getByRequestJobId = useQuery(
    ["getByRequestJobId", idRequestJob],
    () => {
      return ApiRequestJob.getByRequestJobId(idRequestJob);
    },
    {
      enabled: idRequestJob !== "",
    }
  );
  const {data, refetch: refreshDetail} = getDetail;

  const isStatusReview = useMemo(() => {
    return data?.status === StatusRequestJob.Review;
  }, [data?.status]);

  const findLabelJob = (label: number): string => {
    const result = newLabels.find((item) => item.value === label)?.label || "";
    return ` | ${result}`;
  };

  const jobInformation = {
    level: data?.levelIds || "",
    label: findLabelJob(data?.label || 0),
    workType: data?.workType ? `| ${data?.workType}` : "",
    workLocation:
      data?.workLocationNameCombined || data?.countryNameCombined
        ? ` | ${data?.workLocationNameCombined || ""}`
        : "",
    country: data?.countryNameCombined || "",
  };

  const describeRequestJob = [
    {
      label: "Mô tả công việc",
      value: data?.description || "",
    },
    {
      label: "Yêu cầu công việc",
      value: data?.requestDetail || "",
    },
    {
      label: "Phúc lợi và chính sách",
      value: data?.benefit || "",
    },
  ];

  const describePositionLeft = [
    {
      icon: "apartment",
      value: data?.customerName || "",
    },
    {
      icon: "briefcase-2-line",
      value: `${
        data?.experienceYearFrom
          ? ` Từ ${data?.experienceYearFrom} năm kinh nghiệm`
          : "Không yêu cầu kinh nghiệm "
      }`,
    },
    {
      icon: "map-pin-line-pin-line",
      key: "location",
      value:
        data?.detailAddress ||
        data?.workLocationNameCombined ||
        data?.countryNameCombined
          ? [
              data?.detailAddress,
              data?.workLocationNameCombined,
              data?.countryNameCombined,
            ]
              .filter(Boolean)
              .join(" - ")
          : "",
    },
  ];

  const describePositionRight = [
    {
      icon: "group-line",
      value: `${data?.remainHeadcount || 0}/${data?.headcount || 0}`,
    },
    {
      icon: "circle-dolar",
      value: salaryRange(
        data?.salaryFrom,
        data?.salaryTo,
        data?.currencyTypeId
      ),
    },
    {
      icon: "calendar-check-line",
      value: data?.monthWarranty ? data.monthWarranty + " tháng" : "N/A",
    },
  ];

  const findPriority = (priority: number): string => {
    if (!priority) return "N/A";
    return priorities.find((item) => item.key === String(priority))
      ?.label as string;
  };

  const findTypeRequest = (type: number): string => {
    if (type === RequestType.external) return "External";
    return "Internal";
  };

  const columns: ColumnsType<IDataCandidateRequestJob> = [
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
      align: "left",
    },
    {
      title: "Số điện thoại",
      dataIndex: "candidatePhone",
      key: "candidatePhone",
    },
    {
      title: "Email",
      dataIndex: "candidateEmail",
      key: "candidateEmail",
    },
    {
      title: "Trạng thái ứng tuyển",
      dataIndex: "candidateEmail",
      key: "candidateEmail",
      render: (
        _: string,
        {stageName, statusName}: IDataCandidateRequestJob
      ): JSX.Element => {
        let status = "";
        if (stageName) {
          status += stageName;
        }

        if (statusName) {
          status += " " + statusName;
        }

        return (
          <span className={`status-cv ${status.length === 0 ? "hidden" : ""}`}>
            {status}
          </span>
        );
      },
    },
    {
      title: "Thời gian xử lý",
      dataIndex: "modifiedDate",
      key: "modifiedDate",
      render: (_: string, record: IDataCandidateRequestJob): JSX.Element => (
        <span>{timeSince(new Date(record?.modifiedDate))}</span>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdDate",
      key: "createdDate",
      render: (_: string, record: IDataCandidateRequestJob): JSX.Element => (
        <span>
          {record?.createdDate
            ? moment(new Date(record?.createdDate)).format(DATE_FORMAT)
            : ""}
        </span>
      ),
    },
  ];

  const goToEditRequestJob = (): void => {
    const redirect = config.PATHNAME.MANAGER_REQUEST_EDIT_PARAMS(idRequestJob);
    router.push(redirect);
  };

  const informationCustomer = [
    {
      label: "Tên khách hàng: ",
      value: data?.customerName || "N/A",
    },
    {
      label: "Người đại diện: ",
      value: data?.proxyCustomer || "N/A",
    },
    {
      label: "Ngày request: ",
      value: data?.requestDate || "N/A",
    },
    {
      label: "Website: ",
      value: data?.website || "N/A",
    },
    {
      label: "Rate thanh toán khách hàng: ",
      value: inviteBonus(
        !!data?.customerRateType,
        data?.customerRateValue || 0,
        data?.customerCurrencyTypeId || "VND"
      ).replace("%", " lần"),
    },
  ];

  const informationOverall = [
    {
      label: "Độ ưu tiên: ",
      value: findPriority(data?.priority || 0),
    },
    {
      label: "Loại request: ",
      value: findTypeRequest(data?.requestType || 0),
    },
    {
      label: "AM quản lý: ",
      value: data?.accountManagerName || "N/A",
    },
    {
      label: "Dịch vụ: ",
      value: data?.services?.join(", ") || "N/A",
    },
    {
      label: "Thời gian: ",
      value: `${data?.durationValue} ${getLabelDurationTime(
        data?.durationUnit
      )}`,
    },
    {
      label: "Ngày kết thúc: ",
      value: data?.expiryDate
        ? moment(data?.expiryDate, DATE_FORMAT).format(DATE_FORMAT)
        : "N/A",
    },
  ];

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const {mutate: handleUpdateStatusJob} = useMutation(
    (data: DataUpdateStatusJob) => ApiRequestJob.updateStatusJob(data),
    {
      onSettled() {
        dispatch(setLoading(false));
      },
      onSuccess() {
        refreshDetail();
      },
    }
  );

  const onUpdateStatusJob = (data: DataUpdateStatusJob) => {
    if (isRoleCSL && isStatusReview) {
      dispatch(setLoading(true));

      handleUpdateStatusJob(data);
    }
  };

  useQuery(
    ["getGroupCSTs"],
    () => {
      return ApiRequestJob.getGroupCSTs();
    },
    {
      onSuccess(data) {
        const cloneData: any = data?.map((i) => ({
          label: i.name,
          value: String(i.groupId) + extraKeyParent,
          children:
            i.users.map((item) => ({
              label: item.userName,
              // Because maybe user can join in groups so generate extra 2 character to make different value
              value:
                String(item.userId) + makeId(numberOfChar, String(i.groupId)),
            })) || [],
        }));
        setGroupCSTsData(cloneData);
      },
    }
  );

  const initialCSTValue = useMemo(() => {
    let result = [];

    if (!data?.userAssign || data?.userAssign.length === 0) {
      return [];
    }
    let dataCSTs: Array<{label: string; value: string}> = [];

    groupCStsData.reduce((_: any, curr) => {
      const dataChildren = curr?.children || [];
      dataCSTs = dataCSTs.concat(dataChildren);
      return dataCSTs;
    }, dataCSTs);

    result = dataCSTs.filter((item) =>
      data?.userAssign.some((i) => {
        const id = clearMakeId(item.value);
        return id === i.toString();
      })
    );

    result = result.map((i) => i.value);

    return result;
  }, [groupCStsData, data?.userAssign]);

  useEffect(() => {
    formikCSTRef.current?.setFieldValue("groupCSTs", initialCSTValue);

    return () => {
      formikCSTRef.current?.resetForm();
    };
  }, [initialCSTValue]);

  const {mutate: onAssignJob} = useMutation(
    (data: AssignJobData) => {
      dispatch(setLoading(true));
      return ApiRequestJob.handleAssignJob(data);
    },
    {
      onSuccess() {
        notification.success({
          message: "Success",
          description: "Assign job successfully",
        });
      },
      onSettled() {
        refreshDetail();
        dispatch(setLoading(false));
      },
    }
  );

  const handleAssignJob = (value: Array<string>) => {
    let result: Array<string> = [];
    const groupCSTsValue = value;

    const parentIds = groupCSTsValue.filter((id: string) =>
      id.includes(extraKeyParent)
    );
    parentIds.reduce((_: any, currentValue: string) => {
      const dataParent = groupCStsData.find((i) => i.value === currentValue);

      result = result.concat(
        (dataParent?.children?.map((i) => i.value) as any) || []
      );
      return result;
    }, result);

    const childrenIds = groupCSTsValue.filter(
      (id: string) => !id.includes(extraKeyParent)
    );
    result = result.concat(childrenIds).map((i) => clearMakeId(i));

    result = result.filter(onlyUnique);
    const cloneResult = result.map((i) => Number(i));

    const isChange = compareTwoArrayNumber(cloneResult, data?.userAssign || []);

    if (isChange) {
      const assignJobData: AssignJobData = {
        requestJobId: Number(idRequestJob),
        assignUsers: cloneResult,
      };
      onAssignJob(assignJobData);
    }
  };

  if ((getDetail.error as any)?.response?.status === 400) {
    return <AppAccessDenied />;
  }

  return (
    <div className="request-job-detail">
      {getDetail.isLoading ? (
        <AppLoading classNameContainer="h-screen" />
      ) : (
        <div>
          <AppBreadcrumb separator=">" items={items} />
          <div className="request-job-detail__header flex justify-between items-center">
            <div className="flex text12">
              <Avatar className="mt-0.5" size={64} src="/img/reco-avatar.png" />
              <div className="ml-3">
                <h3 className="text24">{data?.name || "N/A"}</h3>
                <div className="mt-1 text-color-primary">
                  <span className="mr-1">{jobInformation.level}</span>
                  <span className="mr-1">{jobInformation.label}</span>
                  <span className="mr-1">{jobInformation.workType}</span>
                  <span>{`${jobInformation?.workLocation} - ${jobInformation?.country}`}</span>
                </div>
                <div className="mt-2 flex items-center text12 hot-bonus request-job-detail__hot-bonus">
                  <Icon icon="redeem" size={20} color="green" />
                  <p className="ml-2 mt-1">
                    {`Thưởng giới thiệu ${inviteBonus(
                      !!data?.partnerRateType,
                      data?.partnerRateValue || 0,
                      data?.partnerCurrencyTypeId || "VND"
                    )}/ ứng viên`}
                  </p>
                  <div className="flex items-center text12 ml-2">
                    <div
                      className="status"
                      style={{
                        backgroundColor: getStatusRequestJob(data?.status || "")
                          .color,
                      }}
                    />
                    <p className="ml-3">
                      {getStatusRequestJob(data?.status || "")?.label}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="request-job-detail__container flex">
            <div className="flex-1 request-job-detail__container-first">
              <div className="flex justify-between">
                <h3 className="text16 font-bold">Mô tả vị trí</h3>
                <div>
                  <a href={linkJd} target="_blank" rel="noreferrer">
                    <Icon
                      icon="file-download-line"
                      size={20}
                      className="mr-2"
                    />
                  </a>
                </div>
              </div>
              <div className="flex">
                <div className="flex-1 mt-1">
                  {describePositionLeft.map((item, index) => (
                    <div
                      key={index}
                      className={`flex mt-1 ${item.value ? "none" : ""}`}
                    >
                      <Icon icon={item.icon} size={16} />
                      <Tooltip
                        title={item.key === "location" ? item.value : null}
                      >
                        <p className="ml-2 address-detail">{item.value}</p>
                      </Tooltip>
                    </div>
                  ))}
                </div>
                <div className="flex-1 mt-1">
                  {describePositionRight.map((item, index) => (
                    <div
                      key={index}
                      className={`flex mt-1 ${item.value ? "none" : ""}`}
                    >
                      <Icon icon={item.icon} size={16} />
                      <p className="ml-2">{item.value}</p>
                    </div>
                  ))}
                </div>
              </div>
              <div className="request-job-detail__container-describe mt-2">
                {describeRequestJob.map((item, index) => (
                  <div key={index} className="mt-2">
                    <h3 className="text16 font-bold">{item.label}</h3>
                    <div className="mt-1 ml-4">
                      <HtmlComponent htmlString={item.value} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex-1 request-job-detail__container-second">
              <div className="request-job-detail__customer text14 w-full">
                <h3 className="request-job-detail__customer-text text16 font-bold">
                  Thông tin khách hàng
                </h3>
                <div className="text14 ml-4">
                  {informationCustomer?.map((item, index) => (
                    <p key={index} className="mt-2">
                      {item.label}
                      {item.label === "Website: " ? (
                        item?.value === "N/A" ? (
                          <span>{item?.value}</span>
                        ) : (
                          <a
                            className="font-bold ml-2 website"
                            href={item.value}
                            target="_blank"
                            rel="noreferrer"
                          >
                            {item.value}
                          </a>
                        )
                      ) : (
                        <span className="font-bold ml-2">{item.value}</span>
                      )}
                    </p>
                  ))}
                </div>
              </div>
              <div className="request-job-detail__overall mt-2 text14">
                <div className="flex">
                  <div className="flex-1">
                    <h3 className="font-bold text16">Thông tin chung</h3>
                    <div className="ml-4 mt-2">
                      {informationOverall?.map((item, index) => (
                        <p className="mt-1" key={index}>
                          {item.label}
                          <span className="font-bold ml-2">{item.value}</span>
                        </p>
                      ))}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text16">Thưởng bonus</h3>
                    <div className="ml-4 mt-2">
                      <p className="mt-1">
                        Loại bonus:{" "}
                        <span className="font-bold ml-2">
                          {data?.hotBonusName || "N/A"}
                        </span>
                      </p>
                      <p className="mt-1">
                        Số tiền thưởng phỏng vấn:{" "}
                        <span className="font-bold ml-2">
                          {formatMoney(
                            data?.hotBonusAmount,
                            data?.hotBonusUnit
                          )}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>

                <h3 className="font-bold text16 mt-2">Quy trình phỏng vấn</h3>
                <div className="request-job-detail__pv pl-4 mt-2">
                  <HtmlComponent htmlString={data?.interviewProcess || ""} />
                </div>
              </div>
              {isRoleCSL && (
                <div className="request-job-detail__assign mt-2">
                  <h3 className="font-bold text16">Consultant phụ trách</h3>
                  <div>
                    <Formik
                      initialValues={{groupCSTs: initialCSTValue}}
                      onSubmit={() => {
                        //
                      }}
                      innerRef={formikCSTRef}
                    >
                      {({values}) => {
                        return (
                          <Row gutter={[16, 16]} className="items-center">
                            <Col xs={20}>
                              <AppTreeSelect
                                name="groupCSTs"
                                labelselect="Chọn CST"
                                data={groupCStsData}
                                showCheckedStrategy={SHOW_PARENT}
                                treeDefaultExpandAll={false}
                                containerclassname="mt-2"
                                treeNodeFilterProp="label"
                                allowClear
                                value={values.groupCSTs}
                                showSearch
                              />
                            </Col>
                            <Col xs={4}>
                              <AppButton
                                typebutton="primary"
                                style={{marginTop: "6px"}}
                                onClick={() => {
                                  handleAssignJob(values.groupCSTs);
                                }}
                              >
                                Assign
                              </AppButton>
                            </Col>
                          </Row>
                        );
                      }}
                    </Formik>
                  </div>
                </div>
              )}
              <ConversationRequestJob
                requestJobId={Number(idRequestJob)}
                className="mt-2"
              />
            </div>
          </div>
          <div className="request-job-detail__table mt-2">
            <h3 className="text24 font-bold">Vị trí ứng tuyển</h3>
            <div className="mt-2">
              <AppTable
                columns={columns}
                dataSource={getByRequestJobId?.data ?? []}
                scroll={{
                  y: "30vh",
                }}
              />
            </div>
          </div>
          <div className="request-job-detail__action mt-4 mb-4">
            <Row gutter={[64, 32]} justify="center">
              <Col xs={6}>
                <AppButton
                  typebutton="secondary"
                  onClick={(): void => {
                    router.back();
                  }}
                >
                  Trở lại
                </AppButton>
              </Col>
              {isRoleCSL && isStatusReview && (
                <>
                  <Col xs={6}>
                    <AppButton
                      typebutton="normal"
                      onClick={(): void => {
                        onUpdateStatusJob({
                          jobId: Number(idRequestJob),
                          jobStatus: 7,
                        });
                      }}
                      type="primary"
                      danger
                    >
                      Từ chối
                    </AppButton>
                  </Col>
                  <Col xs={6}>
                    <AppButton
                      typebutton="warning"
                      onClick={(): void => {
                        onUpdateStatusJob({
                          jobId: Number(idRequestJob),
                          jobStatus: 6,
                        });
                      }}
                    >
                      Cần thêm thông tin
                    </AppButton>
                  </Col>
                  <Col xs={6}>
                    <AppButton
                      typebutton="primary"
                      onClick={(): void => {
                        onUpdateStatusJob({
                          jobId: Number(idRequestJob),
                          jobStatus: 1,
                        });
                      }}
                    >
                      Chấp nhận
                    </AppButton>
                  </Col>
                </>
              )}
              {data?.isOwner && (
                <Col xs={6}>
                  <AppButton typebutton="primary" onClick={goToEditRequestJob}>
                    Chỉnh sửa request
                  </AppButton>
                </Col>
              )}
            </Row>
          </div>
        </div>
      )}
    </div>
  );
}

export default RequestJobDetail;
