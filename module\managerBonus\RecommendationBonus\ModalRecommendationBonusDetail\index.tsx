import {DashboardPagingDatas} from "@app/api/ApiPaymentBonus";
import AppModal from "@app/components/AppModal";
import {formatMoney, getPaymentStatus} from "@app/utils/constants/function";
import React from "react";
import "./index.scss";
import {Col, Row} from "antd";
import AppButton from "@app/components/AppButton";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {OptionSelect} from "@app/types";

interface Props {
  title: string;
  open: boolean;
  dataRecommendation: DashboardPagingDatas;
  onCancel: () => void;
  onOpenModalPayment: () => void;
}

function ModalRecommendationBonusDetail(props: Props): JSX.Element {
  const {title, open, dataRecommendation, onCancel, onOpenModalPayment} = props;

  const leftInformation: OptionSelect[] = [
    {
      label: "<PERSON><PERSON> trí ứng tuyển",
      value: dataRecommendation?.positionName || "N/A",
    },
    {
      label: "Cộng tác viên giới thiệu ",
      value: dataRecommendation?.userFullName || "N/A",
    },
    {
      label: "Khách hàng",
      value: dataRecommendation?.customerName || "N/A",
    },
  ];

  const rightInformation: OptionSelect[] = [
    {
      label: "Ngày onboard",
      value: dataRecommendation?.onboardDate
        ? moment(dataRecommendation?.onboardDate).format(DATE_FORMAT)
        : "N/A",
    },
    {
      label: "Người tạo application",
      value: dataRecommendation?.newUserFullName || "N/A",
    },
  ];

  return (
    <AppModal
      title={title}
      open={open}
      footer={null}
      className="recommendation-detail-ui"
      onCancel={onCancel}
      centered
    >
      <div className="recommendation-detail-ui__detail">
        <div className="recommendation-detail-ui__border">
          <div className="recommendation-detail-ui__header flex justify-between items-center">
            <p className="text24 font-bold">
              {dataRecommendation?.candidateName || "N/A"}
            </p>
            <div className="flex items-center">
              <div
                style={{
                  backgroundColor: getPaymentStatus(
                    dataRecommendation?.paymentStatus
                  )?.color,
                }}
                className="recommendation-detail-ui__status mr-2"
              />
              <div>
                {getPaymentStatus(dataRecommendation?.paymentStatus)?.label}
              </div>
            </div>
          </div>
          <div className="recommendation-detail-ui__container mt-4 text16">
            <div className="recommendation-detail-ui__information flex justify-between">
              <div>
                {leftInformation.map((item) => (
                  <div key={item.value} className="mt-4">
                    <p className="text12 recommendation-detail-ui__label">
                      {item.label}
                    </p>
                    <p className="text14">{item.value}</p>
                  </div>
                ))}
              </div>
              <div>
                {rightInformation.map((item) => (
                  <div key={item.value} className="mt-4">
                    <p className="text12 recommendation-detail-ui__label">
                      {item.label}
                    </p>
                    <p className="text14">{item.value}</p>
                  </div>
                ))}
              </div>
            </div>
            <hr className="my-4" />
            <div className="recommendation-detail-ui__information">
              <div className="flex justify-between">
                <p className="text16">Tổng tiền</p>
                <p className="text14">
                  {dataRecommendation?.amount
                    ? formatMoney(dataRecommendation?.amount)
                    : "N/A"}
                </p>
              </div>
              <div className="flex justify-between text14 mt-1">
                <p>Ngày thanh toán</p>
                <p>
                  {dataRecommendation?.paymentDate
                    ? moment(dataRecommendation?.paymentDate).format(
                        DATE_FORMAT
                      )
                    : "N/A"}
                </p>
              </div>
              <p className="mt-4">Ghi chú</p>
              <p className="text12">{dataRecommendation?.note || "N/A"}</p>
            </div>
          </div>
        </div>
        <div className="recommendation-detail-ui__footer mt-4">
          <Row gutter={[16, 16]} justify="center">
            <Col xs={8}>
              <AppButton
                label="Đóng"
                onClick={onCancel}
                typebutton="secondary"
              />
            </Col>
            {dataRecommendation?.paymentStatus !== 2 && (
              <Col xs={8}>
                <AppButton
                  label="Cập nhật"
                  typebutton="primary"
                  onClick={onOpenModalPayment}
                />
              </Col>
            )}
          </Row>
        </div>
      </div>
    </AppModal>
  );
}

export default React.memo(ModalRecommendationBonusDetail);
