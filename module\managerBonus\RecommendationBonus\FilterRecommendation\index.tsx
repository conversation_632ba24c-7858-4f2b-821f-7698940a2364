import {SelectInput} from "@app/components/SelectInput";
import "./index.scss";
import {TextInput} from "@app/components/TextInput";
import {Checkbox, Col, Popover, Row} from "antd";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {Formik, FormikProps} from "formik";
import React, {FormEvent, useEffect, useRef, useState} from "react";
import {deadTimeFastSearch, paymentStatus} from "@app/utils/constants/state";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment, {Moment} from "moment";
import {useSelector, useDispatch} from "react-redux";
import {
  changeListColShowRecommendationBonus,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {CheckboxValueType} from "antd/lib/checkbox/Group";

import AppCheckBox from "@app/components/AppCheckbox";
import {
  DashboardPagingDatas,
  IResRecommendationBonus,
} from "@app/api/ApiPaymentBonus";
import {ColumnType} from "antd/lib/table";
import {IAccountRole, OptionSelect} from "@app/types";

interface Props {
  setSearchParams: (value: IResRecommendationBonus) => void;
  allColumn: ColumnType<DashboardPagingDatas>[];
  searchParams: IResRecommendationBonus;
  currentColumns: ColumnType<DashboardPagingDatas>[];
}

interface DataFormSearchRecommendation {
  consultantName: string;
  userFullName: string;
  candidateName: string;
  positionName: string;
  newUserFullName: string;
  paymentStatus: OptionSelect;
  from: string | Moment | undefined;
  to: string | Moment | undefined;
}

const listCheckBoxLeft: OptionSelect[] = [
  {
    label: "Tên ứng viên",
    value: "candidateName",
  },
  {
    label: "Vị trí ứng tuyển",
    value: "positionName",
  },
  {
    label: "Ngày Onboard",
    value: "onboardDate",
  },
  {
    label: "Khách hàng",
    value: "customerName",
  },
];

const listCheckBoxRight: OptionSelect[] = [
  {
    label: "Số tiền",
    value: "amount",
  },
  {
    label: "Trạng thái thanh toán",
    value: "paymentStatus",
  },
  {
    label: "Ngày thanh toán",
    value: "paymentDate",
  },
];

function FilterRecommendation(props: Props): JSX.Element {
  const {setSearchParams, allColumn, searchParams, currentColumns} = props;
  const {listColShowRecommendationBonus, user} = useSelector(selectUser);
  const dispatch = useDispatch();
  const isRoleCst = (user?.role || [])?.includes(IAccountRole.CST);
  const lengthAllColumns = allColumn?.length;
  const totalColumn = isRoleCst ? lengthAllColumns - 1 : lengthAllColumns;

  const [showFilterAdvance, setShowFilterAdvance] = useState<boolean>(false);
  const formikRef = useRef<FormikProps<DataFormSearchRecommendation>>(null);
  const timeOut = useRef<any>();

  const initialValue: DataFormSearchRecommendation = {
    consultantName: "",
    userFullName: "",
    candidateName: "",
    positionName: "",
    newUserFullName: "",
    paymentStatus: {} as OptionSelect,
    from: "",
    to: "",
  };

  const handleSearch = () => {
    const valueRef = formikRef?.current?.values;
    const paramValue = {
      ...valueRef,
      currentPage: 1,
      from: valueRef?.from ? moment(valueRef?.from).format(DATE_FORMAT) : "",
      to: valueRef?.to ? moment(valueRef?.to).format(DATE_FORMAT) : "",
      paymentStatus: valueRef?.paymentStatus?.key
        ? Number(valueRef?.paymentStatus?.key)
        : null,
      isFirstInitialization: true,
      pageSize: searchParams.pageSize,
      isHidenTextSearch: false,
      isViewDetail: false,
      consultantName: valueRef?.consultantName?.trim() || "",
      userFullName: valueRef?.userFullName?.trim() || "",
      newUserFullName: valueRef?.newUserFullName,
    };
    setSearchParams(paramValue);
  };

  const handleTextSearch = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const handleAdvanceSearch = (): void => {
    handleSearch();
  };

  const onResetForm = (): void => {
    formikRef?.current?.resetForm();
    setSearchParams({
      currentPage: 1,
      isFirstInitialization: true,
      isViewDetail: false,
      pageSize: searchParams.pageSize,
    });
  };

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  function renderFormSearch(values: DataFormSearchRecommendation): JSX.Element {
    return (
      <div className="ui-form-search">
        <TextInput
          label="Tên ứng viên"
          name="candidateName"
          containerclassname="mt-2"
          value={values?.candidateName}
          free={!values?.candidateName}
        />
        <TextInput
          name="positionName"
          label="Vị trí ứng tuyển"
          containerclassname="mt-2"
          value={values?.positionName}
          free={!values?.positionName}
        />
        <TextInput
          name="newUserFullName"
          label="Người tạo application"
          containerclassname="mt-2"
          value={values?.newUserFullName}
          free={!values?.newUserFullName}
        />
        <SelectInput
          name="paymentStatus"
          labelselect="Trạng thái thanh toán"
          data={paymentStatus}
          containerclassname="mt-2"
          value={values?.paymentStatus?.value}
          free={!values?.paymentStatus?.key}
          allowClear
        />
        <p className="mt-2 text16">Ngày thanh toán</p>
        <Row className="mt-2" gutter={[16, 16]}>
          <Col xs={12}>
            <AppDatePicker
              label="Từ"
              name="from"
              format={DATE_FORMAT}
              valueAppDatePicker={
                values?.from ? moment(values?.from, DATE_FORMAT) : undefined
              }
              allowClear
            />
          </Col>
          <Col xs={12}>
            <AppDatePicker
              label="Đến"
              name="to"
              format={DATE_FORMAT}
              valueAppDatePicker={
                values?.to ? moment(values?.to, DATE_FORMAT) : undefined
              }
              allowClear
            />
          </Col>
        </Row>
        <Row className="mt-2" gutter={[16, 16]}>
          <Col xs={12}>
            <AppButton
              label="Xoá tất cả"
              typebutton="secondary"
              onClick={onResetForm}
            />
          </Col>
          <Col xs={12}>
            <AppButton
              label="Tìm kiếm"
              typebutton="primary"
              onClick={handleAdvanceSearch}
            />
          </Col>
        </Row>
      </div>
    );
  }

  const onChangeColumns = (newSelectedRowKeys: CheckboxValueType[]): void => {
    dispatch(
      changeListColShowRecommendationBonus(newSelectedRowKeys as string[])
    );
  };

  const renderListCheckbox = (
    <Checkbox.Group
      className="list-checkbox"
      value={listColShowRecommendationBonus ?? []}
      onChange={onChangeColumns}
    >
      <Row>
        <Col xs={12}>
          {listCheckBoxLeft.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col xs={12}>
          {listCheckBoxRight.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );
  return (
    <Formik
      initialValues={initialValue}
      innerRef={formikRef}
      onSubmit={() => {
        //
      }}
    >
      {({values, handleChange, handleBlur, handleSubmit, handleReset}) => {
        return (
          <form
            onSubmit={(e: FormEvent<HTMLFormElement>): void => {
              e.preventDefault();
            }}
          >
            <div className="search-form-recommendation">
              <Row
                gutter={[16, 16]}
                justify="space-between"
                className="w-full flex-row items-center"
              >
                <Col xs={12}>
                  <Row gutter={[16, 16]} className="items-center">
                    {!isRoleCst && (
                      <Col xs={12}>
                        <TextInput
                          name="consultantName"
                          label="CST quản lí"
                          containerclassname="mt-2 w-full"
                          value={values?.consultantName}
                          free={!values?.consultantName}
                          onChange={handleTextSearch}
                          placeholder="Nhập CST quản lý"
                        />
                      </Col>
                    )}
                    <Col xs={12}>
                      <TextInput
                        label="CTV giới thiệu"
                        name="userFullName"
                        containerclassname="mt-2 w-full"
                        value={values?.userFullName}
                        free={!values?.userFullName}
                        onChange={handleTextSearch}
                        placeholder="Nhập CTV giới thiệu"
                      />
                    </Col>
                  </Row>
                </Col>
                <Col>
                  <Row gutter={[16, 16]} className="items-center">
                    <Col>
                      <Popover
                        content={renderFormSearch(values)}
                        trigger={["click"]}
                        placement="bottomLeft"
                        open={showFilterAdvance}
                        onOpenChange={setShowFilterAdvance}
                        getPopupContainer={(trigger): any =>
                          trigger.parentElement
                        }
                      >
                        <AppButton typebutton="normal" classrow="btn-filter">
                          <Icon
                            className="mr-1"
                            icon="filter-line"
                            size={12}
                            color="#324054"
                          />
                          Tìm kiếm nâng cao
                        </AppButton>
                      </Popover>
                    </Col>
                    <Col>
                      <Popover
                        placement="bottom"
                        trigger="click"
                        content={renderListCheckbox}
                        getPopupContainer={(trigger): any =>
                          trigger.parentElement
                        }
                      >
                        <AppButton typebutton="normal" classrow="btn-filter">
                          <Icon
                            className="mr-1"
                            icon="eye"
                            size={16}
                            color="#324054"
                          />
                          Hiển thị {(currentColumns || [])?.length}/
                          {totalColumn}
                        </AppButton>
                      </Popover>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </div>
          </form>
        );
      }}
    </Formik>
  );
}

export default React.memo(FilterRecommendation);
