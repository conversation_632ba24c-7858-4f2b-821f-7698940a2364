import {fetcher} from "./Fetcher";

export type LocationParams = {
  countryId: string;
  workLocationIds: Array<string>;
};

export interface AccountRegisterParams {
  name: string;
  email: string;
  phoneNumber: string;
  password: string;
  recommendationEmail: string;
  recruitmentTypeId: number;
  careerTypeId: number;
  agreeRecoPolicy: boolean;
  userName: string;
  yoe: number;
  timeForReco: number;
  needSupport: string;
  positions: Array<string>;
  locations: Array<LocationParams>;
  languages: Array<string>;
  levels: Array<string>;
}

export interface AccountChangePasswordParams {
  password: string;
  authenId: string;
  token: string;
}

export interface CheckExpiredTokenParams {
  id: string;
  token: string;
}

const path = {
  registerCollaborators: "/account/partner/register",
  forgotPassword: "/account/sendmailforgotpassword",
  checkExpiredToken: "/account/authentication/fogotpassword",
  changePassword: "/account/forgotpassword",
};

function registerCollaborators(data: AccountRegisterParams): Promise<any> {
  return fetcher({
    url: path.registerCollaborators,
    method: "post",
    data: data,
  });
}

function forgotPassword(email: string): Promise<any> {
  return fetcher({
    url: path.forgotPassword,
    method: "get",
    params: {email},
  });
}

function checkExpiredToken(params: CheckExpiredTokenParams): Promise<any> {
  return fetcher({
    url: path.checkExpiredToken,
    method: "get",
    params: params,
  });
}

function changePassword(data: AccountChangePasswordParams): Promise<any> {
  return fetcher({
    url: path.changePassword,
    method: "post",
    data: data,
  });
}

export default {
  registerCollaborators,
  forgotPassword,
  changePassword,
  checkExpiredToken,
};
