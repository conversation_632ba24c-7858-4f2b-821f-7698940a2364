// eslint-disable-next-line import/no-cycle
import {fetcher} from "./Fetcher";

export const urlGetNew =
  "https://job.reco-vn.com/reco-manpower-for-partners/wp-json/wp/v2/posts?per_page=100&lang=vi&category_name=tin-tuc";
export interface IJobTypes {
  workTypeId: string;
  name: string;
  description: string;
}

export interface ILevels {
  levelId: string;
  name: string;
  description: string;
}

export interface IWorkLocations {
  countryId: string;
  name: string;
  sortOrder: number;
  workLocationId: string;
}

export interface IJobObject {
  benefit: string;
  countryIds: string;
  countryNameCombined: string;
  currencyType: string;
  customer: string;
  customerDetails: string;
  description: string;
  district: string;
  districtIds: string;
  experienceYearFrom: number;
  experienceYearTo: number;
  expiryDate: string;
  fileJDName: string;
  fileJDPath: string;
  hotBonus: number;
  hotBonusAmount: number;
  hotBonusAmountDisplay: number;
  hotBonusPaymentUnitDisplay: string;
  interviewProcess: string | null;
  keyWords: string[];
  label: string;
  labelCode: number;
  levelIds: string;
  listLevelName: string[];
  name: string;
  note: string;
  partnerCurrencyType: string;
  partnerRateType: boolean;
  partnerRateValue: number;
  position: string;
  priority: number;
  ratePartner: string;
  requestJobCode: string;
  requestJobId: number;
  requestType: number;
  salary: string;
  salaryFrom: string;
  salaryTo: number;
  status: string;
  submitedDate: string;
  tags: string;
  time: string;
  timeAgo: string;
  workAddress: string;
  workLocation: string;
  workLocationIds: string;
  workType: string;
  isBookmark?: boolean;
  numberOfRequired?: number | null;
  numberInProcess?: number | null;
  numberOfUnmarkedRecommendCandidate?: number;
  userAssign: Array<number>;
}

export interface IJob {
  currentPage: number;
  hasNext: boolean;
  isSearchHotJob: boolean;
  jobLabels: string[];
  jobTypes: string[];
  jobs: IJobObject[];
  levels: string[];
  pageSize: number;
  salaryRanges: string[];
  sortOption: number;
  textSearch: string;
  totalJob: number;
  workLocations: string[];
}

export interface IPramJob {
  currentPage: number;
  hasNext?: boolean;
  isSearchHotJob: boolean;
  jobLabels: number[];
  jobTypes: string[];
  jobs?: IJobObject[];
  levels: string[];
  pageSize: number;
  salaryRanges: (null | number)[];
  sortOption?: number;
  textSearch: string;
  totalJob?: number;
  workLocations: string[];
  isBookmark: boolean;
  ignoreJobId?: string;
  haveRecommendCandidate?: boolean;
}

export interface IJobMarketFilters {
  jobTypes: IJobTypes[];
  levels: ILevels[];
  workLocations: IWorkLocations[];
}

export interface INews {
  id: number;
  title: string;
  link: string;
  yoast_head_json: {
    og_image: {
      url: string;
    }[];
  };
}

export enum IStatusJob {
  DRAFT = -1,
  CLOSED,
  OPEN,
  PROCESSING,
  PENDING_FROM_AM,
  PENDING_FROM_KH,
}

const path = {
  job: "/home/<USER>",
  jobMarketFilters: "/api/getJobMarketFilters",
  bookMark: "/api/requestjob/savejob?requestjobid=",
};

function getListJob(body?: IPramJob): Promise<IJob> {
  return fetcher({url: path.job, method: "post", data: body});
}

function getJobMarketFilters(): Promise<IJobMarketFilters> {
  return fetcher({url: path.jobMarketFilters, method: "get"});
}

function getListNews(): Promise<INews[]> {
  return fetcher({url: urlGetNew, method: "get"});
}

function bookMark(jobId: number): Promise<any> {
  return fetcher({url: path.bookMark + jobId, method: "post"});
}

export default {
  getListJob,
  getJobMarketFilters,
  getListNews,
  bookMark,
};
