// eslint-disable-next-line import/no-cycle
import {INews} from "@app/api/ApiJob";
import {PayloadAction, createSlice} from "@reduxjs/toolkit";
// eslint-disable-next-line import/no-cycle
import {IRootState} from "../store";

interface IInitialState {
  listNews: INews[];
}
const initialState: IInitialState = {
  listNews: [],
};

const NewsSlice = createSlice({
  name: "News",
  initialState,
  reducers: {
    setListNews: (state: IInitialState, action: PayloadAction<INews[]>) => {
      state.listNews = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const {setListNews} = NewsSlice.actions;
export const selectNews = (state: IRootState): IInitialState => state.news;
export default NewsSlice.reducer;
