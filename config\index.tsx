// NAME
const STORE_NAME = "state";

// NETWORK
const NETWORK_CONFIG = {
  HOST: process.env.NEXT_PUBLIC_APP_URL,
  API_BASE_URL: process.env.NEXT_PUBLIC_APP_URL,
  BASE_URL: process.env.NEXT_PUBLIC_WEB_URL,
  TIMEOUT: 30000,
  RETRY: false,
  DISPLAY_ERROR: process.env.NEXT_PUBLIC_DISPLAY_ERROR === "true",
  USE_TOKEN: true,
  WITH_METADATA: false,
};

// PATHNAME
const PATHNAME = {
  HOME: "/",
  LOGIN: "/login",
  NOT_FOUND: "/not-found",
  MANAGER_APPLICATION: "/manager-application",
  MANAGER_COLLABORATOR: "/manager-collaborator",
  COLLABORATOR_DETAIL: "/manager-collaborator/detail",
  MANAGER_CANDIDATE: "/candidate",
  CA<PERSON><PERSON><PERSON>E_DETAIL: "/candidate/detail",
  MANAGER_REQUEST: "/manager-request",
  REQUEST_JOB: "/request-job",
  JO<PERSON>_DETAIL: "/request-job/detail",
  JO<PERSON>: "/job/[id]/[name]",
  // NOTIFICATION: "/notification",
  MANAGER_BONUS: "/manager-bonus",
  MANAGER_POTENTIAL_CANDIDATE: "/manager-potential-candidate",
  MANAGER_CUSTOMER: "/manager-customer",
  CUSTOMER_DETAIL: "/manager-customer/detail",
  CUSTOMER_EDIT: "/manager-customer/[id]/edit",
  CUSTOMER_CREATE: "/manager-customer/create",
  CUSTOMER_PAYMENT: "/customer-payment",
  MANAGER_REQUEST_ADD: "/manager-request/add",
  MANAGER_REQUEST_EDIT: "/manager-request/[id]/edit",
  MANAGER_REQUEST_DETAIL: "/manager-request/[id]/detail",
  MANAGER_REQUEST_EDIT_PARAMS: (id: string | number): string =>
    `/manager-request/${id}/edit`,
  MANAGER_REQUEST_DETAIL_PARAMS: (id: string | number): string =>
    `/manager-request/${id}/detail`,
  MANAGER_TEAM: "/manager-team",
  TEAM_DETAIL: "/manager-team/detail",
  CANDIDATE_JAPAN: "/candidate-japan",
  JOB_PUBLIC_DETAIL: "/job-public/[id]/detail",
  JOB_PUBLIC_DETAIL_PARAMS: (id: string | number): string =>
    `/job-public/${id}/detail`,
  REGISTER: "/account/partner/register",
  RULE_BONUS: "/manager-bonus/rule-bonus",
  REGISTER_WITH_CODE: "/account/partner/recommendation/[id]",
  TEAM_EDIT: "/manager-team/[id]/edit",
  TEAM_EDIT_PARAM: (id: string | number): string => `/manager-team/${id}/edit`,
  TEAM_CREATE: "/manager-team/create",
  LINKEDIN_SEARCH: "/linkedin-search",
  FORGOT_PASSWORD: "/account/forgot-password",
  PREVIEW_CV: "/preview-cv",
  MANAGER_COMPANY: "/manager-company",
  MANAGER_JOB_SUGGESTION_COLLABORATORS: "/manager-job-suggestion-collaborators",
  DATA_POOL: "/data-pool",
  MANAGER_JD: "/manager-jd",
  MANAGER_REPORT: "/manager-report",
};

// LAYOUT
const LAYOUT_CONFIG = {
  useSidebar: true,
  useNavbar: true,
  useFooter: true,
  useBottomNavigator: true,
};

// LANGUAGE
const LANGUAGE = {
  DEFAULT: "en",
};

const HREF_NEWS = "https://job.reco-vn.com/reco-manpower-for-partners/tin-tuc";

const EVENT_ANALYTICS_KEY = {
  VIEW_DETAIL_POTENTIAL_CANDIDATE: "view_detail_potential_candidate",
  SEARCH_POTENTIAL_CANDIDATE: "search_potential_candidate",
  SEARCH_LINKEDIN: "search_linkedin",
};

export default {
  STORE_NAME,
  NETWORK_CONFIG,
  PATHNAME,
  LAYOUT_CONFIG,
  LANGUAGE,
  HREF_NEWS,
  EVENT_ANALYTICS_KEY,
};
