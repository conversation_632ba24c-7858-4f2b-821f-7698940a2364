.ui-chatbot-message {
  margin-bottom: 24px;
  display: flex;
  align-items: flex-start;
  word-break: break-all;

  .ui-chatbot-message-avatar {
    min-width: 40px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-weight: 600;
    font-size: 14px;
    line-height: 150%;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    text-align: center;
    -webkit-box-pack: center;
    justify-content: center;
    color: rgb(39, 39, 42);
  }

  .ui-chatbot-message-text {
    padding: 8px 12px;
    margin-left: 10px;
    background: rgb(245, 245, 250);
    border-radius: 16px;
    font-weight: 400;
    font-size: 14px;
    color: $text-color;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .ui-chatbot-message-loader {
    padding: 8px 12px;
    margin-left: 14px;

    .chatbot-loader {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      display: block;
      margin: 15px auto;
      position: relative;
      background: #cacaca;
      box-shadow: -12px 0 #cacaca, 12px 0 #cacaca;
      box-sizing: border-box;
      animation: shadowPulse 1s linear infinite;

      @keyframes shadowPulse {
        33% {
          background: #cacaca;
          box-shadow: -12px 0 $link-color, 12px 0 #cacaca;
        }
        66% {
          background: $link-color;
          box-shadow: -12px 0 #cacaca, 12px 0 #cacaca;
        }
        100% {
          background: #cacaca;
          box-shadow: -12px 0 #cacaca, 12px 0 $link-color;
        }
      }
    }
  }
}
