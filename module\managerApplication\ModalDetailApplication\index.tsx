/* eslint-disable react/no-unstable-nested-components */
import React, {ReactNode, useEffect, useMemo, useRef, useState} from "react";
// eslint-disable-next-line import/no-cycle
import ApiApplication, {
  IListApplication,
  IParamsUpdateApplication,
} from "@app/api/ApiApplication";
import AppModal from "@app/components/AppModal";
import {Avatar, Col, Popover, Row, Steps, notification} from "antd";
import "./index.scss";
import moment from "moment";
import Icon from "@app/components/Icon/Icon";
import AppButton from "@app/components/AppButton";
import {UseQueryResult, useMutation, useQuery} from "react-query";
import {
  getColorCandidateRate,
  getStatusCandidate,
  stagesApplication,
  statusesApplication,
} from "@app/utils/constants/state";
import {
  formatDateTime,
  formatMoney,
  getAbbreviatedName,
  moneyToNumber,
  salaryRange,
  timeSince,
} from "@app/utils/constants/function";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import AppLoading from "@app/components/AppLoading";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {TextInput} from "@app/components/TextInput";
import {Formik, FormikProps} from "formik";
import ViewCvCandidate from "@app/components/viewCvCandidate";
// eslint-disable-next-line import/no-cycle
import ModalCancelApplication from "../ModalCancelApplication";
import {IModeViewCandidate} from "@app/api/ApiCandidate";
import config from "@app/config";
import {
  messageCheckCustomerPaymentExist,
  messageCheckPartnerPaymentExist,
  messageValidateEmail,
} from "@app/utils/constants/message";
import {IAccountRole, OptionSelect} from "@app/types";
import ModalCreatePayment from "../ModalCreatePayment";
// eslint-disable-next-line import/no-cycle
import FormStatus from "../FormStatus";
import ApiNotification from "@app/api/ApiNotification";
import {useRouter} from "next/router";
import HtmlComponent from "@app/components/HtmlComponent";
import ModalCreateBill from "../ModalCreateBill";
import AppCheckBox from "@app/components/AppCheckbox";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import {REGEX_EMAIL, REGEX_PHONE_NUMBER} from "@app/utils/constants/regex";
import _ from "lodash";

interface ModalDetailApplicationProps {
  open?: boolean;
  applicationId: number;
  closeModal: () => void;
  dataListApplications: UseQueryResult<IListApplication, unknown>;
  typeRoleUsed: string;
}
interface IItemEditInfo {
  label: string;
  name: "name" | "phoneNumber" | "email";
  type: "input" | "date" | "salary";
  required?: boolean;
}

const listInfoCandidateEdit: IItemEditInfo[] = [
  {
    label: "Họ và tên",
    name: "name",
    type: "input",
    required: true,
  },
  {
    label: "Số điện thoại",
    name: "phoneNumber",
    type: "input",
  },
  {
    label: "Email",
    name: "email",
    type: "input",
  },
];
export interface FormUpdate extends IParamsUpdateApplication {
  name?: string;
  email?: string;
  phoneNumber?: number;
  salaryExpected?: number;
  currentSalary?: number;
  status?: number;
  ctvNote?: string;
  statusSelected?: OptionSelect;
  stageSelected?: OptionSelect;
  currencyTypeIdSelected?: OptionSelect;
  currentSalaryTypeSelected?: OptionSelect;
  salaryExpectedTypeSelected?: OptionSelect;
  salaryOfferedTypeSelected?: OptionSelect;
  commissionFlag?: boolean;
}

export default function ModalDetailApplication(
  props: ModalDetailApplicationProps
): JSX.Element {
  const {applicationId, open, closeModal, dataListApplications, typeRoleUsed} =
    props;

  const isRoleAM = [IAccountRole.AMG, IAccountRole.AML].includes(
    (typeRoleUsed || "") as IAccountRole
  );

  const {user} = useSelector(selectUser);
  const [typeModal, setTypeModal] = useState<"detail" | "edit">("detail");
  const [isShowModalCancel, setIsShowModalCancel] = useState(false);
  const [isShowModalPayment, setIsShowModalPayment] = useState(false);
  const [isShowModalCreateBill, setIsShowModalCreateBill] = useState(false);
  const infoUpdateRef = useRef<FormikProps<FormUpdate>>(null);
  const router = useRouter();
  const [commissionFlag, setCommissionFlag] = useState<boolean>(false);
  const isCTV = user?.role?.includes(IAccountRole.CTV);

  const detailApplication = useQuery(
    ["getDetailApplication", applicationId],
    () => {
      return ApiApplication.getDetailApplications(applicationId);
    },
    {
      enabled: applicationId !== -1,
      onSuccess: (data) => {
        setCommissionFlag(!!data.commissionFlag);
      },
    }
  );

  const updateApplication = useMutation(
    (infoCandidate: FormUpdate | undefined) =>
      ApiApplication.updateApplication({
        applicationId,
        candidateName: infoCandidate?.name,
        candidateEmail: infoCandidate?.email,
        candidatePhone: infoCandidate?.phoneNumber,
        onboardDate: infoCandidate?.onboardDate
          ? moment(infoCandidate?.onboardDate).format(DATE_FORMAT)
          : "",
        consultantId: detailApplication.data?.manager.userId,
        applicationName: detailApplication.data?.applicationName,
        currentSalaryType: detailApplication.data?.currentSalaryType,
        salaryExpectedType: detailApplication.data?.salaryExpectedType,
        salaryOfferedType: detailApplication.data?.salaryOfferedType,
        stage: detailApplication.data?.stage,
        currencyTypeId: detailApplication.data?.candidate?.currencyTypeId,
        status: infoCandidate?.status,
        ctvNote: infoCandidate?.ctvNote,
        commissionFlag: !!detailApplication?.data?.commissionFlag,
      })
  );

  const updateStatusApplication = useMutation(
    (state: FormUpdate | undefined) => {
      let param: FormUpdate = {
        applicationName: detailApplication.data?.applicationName,
        applicationId,
        consultantId: detailApplication.data?.manager.userId,
        currentSalaryType:
          state?.currentSalaryTypeSelected?.value === "0" ? 0 : 1,
        salaryExpectedType:
          state?.salaryExpectedTypeSelected?.value === "0" ? 0 : 1,
        salaryOfferedType:
          state?.salaryOfferedTypeSelected?.value === "0" ? 0 : 1,
        stage: Number(state?.stageSelected?.key),
        candidateName: candidate?.name,
        candidateEmail: candidate?.email,
        candidatePhone: candidate?.phoneNumber,
        currencyTypeId: state?.currencyTypeIdSelected?.value,
        currentSalary: moneyToNumber(String(state?.currentSalary)),
        salaryExpected: moneyToNumber(String(state?.salaryExpected)),
        salaryOffered: moneyToNumber(String(state?.salaryOffered)),
        summary: state?.summary,
        description: state?.description,
        commissionFlag,
      };

      // trường hợp status là chưa hoàn thành ko gửi lên paramStatus
      if (state?.statusSelected?.key && state?.statusSelected.key !== "null") {
        param = {...param, status: Number(state?.statusSelected?.key)};
      }

      // Trường hợp stage là review và interview có mới gửi
      if (state?.stageSelected?.key !== "3") {
        if (state?.stagePersonCare) {
          param.stagePersonCare = state?.stagePersonCare;
        }
      }

      // stage là  offer status onboard có mới gửi
      if (
        state?.stageSelected?.key === "3" &&
        state.statusSelected?.key === "5"
      ) {
        if (state?.trailWorkTime) {
          param.trailWorkTime = state?.trailWorkTime;
        }
      }

      if (state?.stageDateStarted) {
        param.stageDateStarted = moment(state?.stageDateStarted).format(
          DATE_FORMAT
        );
      }

      return ApiApplication.updateApplication(param);
    },
    {
      onSuccess: (): void => {
        detailApplication.refetch();
        ApiNotification.createNotification({
          ApplicationId: applicationId,
          ApplicationName: detailApplication.data?.applicationName,
          NotiType: "N007",
        });
        dataListApplications.refetch();
        notification.success({
          message: "Cập nhật trạng thái ứng viên thành công.",
          duration: 3,
        });
      },
    }
  );

  const changeStatus = (): void => {
    updateStatusApplication.mutate(infoUpdateRef.current?.values);
  };

  const checkPartnerPaymentExist = useQuery(
    ["checkPartnerPaymentExist", applicationId],
    () => {
      return ApiApplication.checkPartnerPaymentExist(applicationId);
    },
    {
      onSuccess: (res) => {
        if (!res) {
          setIsShowModalPayment(true);
        } else {
          notification.error({
            message: "Thông báo",
            description: messageCheckPartnerPaymentExist,
            duration: 3,
          });
        }
      },
      enabled: false,
    }
  );

  const checkCustomerPaymentExist = useQuery(
    ["checkCustomerPaymentExist", applicationId],
    () => {
      return ApiApplication.checkCustomerPaymentExist(applicationId);
    },
    {
      onSuccess: (res) => {
        if (!res) {
          setIsShowModalCreateBill(true);
        } else {
          notification.error({
            message: "Thông báo",
            description: messageCheckCustomerPaymentExist,
            duration: 3,
          });
        }
      },
      enabled: false,
    }
  );

  useEffect(() => {
    if (updateApplication.status === "success") {
      detailApplication.refetch();
      dataListApplications.refetch();
      setTypeModal("detail");
      setIsShowModalCancel(false);
    }
  }, [updateApplication.status]);

  const candidate = detailApplication.data?.candidate;
  const data = detailApplication?.data;

  const reloadData = (): void => {
    detailApplication.refetch();
    setIsShowModalPayment(false);
    router.push(config.PATHNAME.MANAGER_BONUS);
  };

  const reloadDataWhenCreateBill = (): void => {
    detailApplication.refetch();
    setIsShowModalCreateBill(false);
  };

  const onCloseModal = (): void => {
    infoUpdateRef.current?.resetForm();
    closeModal();
    setTypeModal("detail");
  };

  const handleEditInfoCandidate = (): void => {
    if (typeModal === "detail") {
      setTypeModal("edit");
    } else {
      if (infoUpdateRef.current?.values.name?.length === 0) {
        return;
      }

      if (
        infoUpdateRef.current?.values?.email &&
        !infoUpdateRef.current?.values?.email.match(REGEX_EMAIL)
      ) {
        notification.error({
          message: "Thông báo",
          description: messageValidateEmail,
        });
        return;
      }
      updateApplication.mutate(infoUpdateRef.current?.values);
    }
  };

  const handleCancelCandidate = (): void => {
    if (typeModal === "edit") {
      setTypeModal("detail");
    } else {
      setIsShowModalCancel(true);
    }
  };

  const onChangeCheckBox = (e: CheckboxChangeEvent): void => {
    setCommissionFlag(e.target.checked);
  };

  const listApplicationInfo: {nameIcon: string; value: any}[] = [
    {
      nameIcon: "mail-line",
      value: candidate?.email,
    },
    {
      nameIcon: "coins-line",
      value: `${formatMoney(
        candidate?.salaryExpected,
        candidate?.currencyTypeId
      )}/${candidate?.salaryExpectedType === 0 ? "Net" : "Gross"}`,
    },
    {
      nameIcon: "phone-line",
      value: candidate?.phoneNumber,
    },
    {
      nameIcon: "calendar-check-line",
      value: candidate?.onboardDate,
    },
  ];

  const listJobInfo: {nameIcon: string; value: any}[] = [
    {
      nameIcon: "apartment",
      value: isCTV ? "Khách hàng Reco" : data?.customerName,
    },
    {
      nameIcon: "coins-line",
      value: salaryRange(
        data?.salaryFrom,
        data?.salaryTo,
        data?.jobCurrencyTypeId
      ),
    },
    {
      nameIcon: "briefcase-2-line",
      value: data?.requestJobName,
    },
    {
      nameIcon: "category",
      value: data?.workType,
    },
    {
      nameIcon: "map-pin-line-pin-line",
      value: data?.workingLocation,
    },
  ];

  const currentStep = useMemo(() => {
    if (!data?.stage) {
      return 0;
    }

    if (data?.stage === 3 && data?.status === 5) {
      return 4;
    }

    if (data.status === 3) {
      return data.stage;
    }

    return data.stage - 0.5;
  }, [data?.stage, data?.status]);

  function RenderIcon(index: number): JSX.Element {
    return (
      <div
        className="dot-content"
        style={{
          backgroundColor:
            currentStep >= index - 0.5 ? "#2F6BFF" : "rgba(157, 157, 157, 0.5)",
        }}
      >
        {currentStep >= index ? (
          <Icon size={14} icon="check-line" color="white" className="" />
        ) : (
          <span
            className={
              index === currentStep + 0.5 ? "current-step" : "next-step"
            }
          >
            {index}
          </span>
        )}
      </div>
    );
  }

  const ListStep: {
    title: string;
    icon?: ReactNode;
  }[] = [
    {
      title: "Review",
      icon: RenderIcon(1),
    },
    {
      title: "Interview",
      icon: RenderIcon(2),
    },
    {
      title: "Offer",
      icon: RenderIcon(3),
    },
    {
      title: "Hire",
      icon: RenderIcon(4),
    },
  ];

  const contentRate = (): JSX.Element => (
    <div className="max-w-xs">
      <div className="text-xs font-normal text-[#324054] mb-1">
        <span className="font-semibold">Mức độ phù hợp: </span>
        <span style={{color: getColorCandidateRate(data?.matching.rate)}}>
          {data?.matching.rate !== null ? `${data?.matching.rate}%` : null}
        </span>
      </div>
      <div className="text-xs font-normal text-[#324054] mb-1">
        <span className="font-semibold">Phù hợp: </span>
        {data?.matching.suitable}
      </div>
      <div className="text-xs font-normal text-[#324054] mb-1">
        <span className="font-semibold">Chưa phù hợp: </span>
        {data?.matching.unsuitable}
      </div>
    </div>
  );

  const titleRate = (): JSX.Element => (
    <div className="text-base font-bold text-[#324054]">Đánh giá</div>
  );

  const renderInfoCandidate = (): JSX.Element => {
    const initFormEdit: FormUpdate = {
      name: candidate?.name,
      email: candidate?.email,
      phoneNumber: candidate?.phoneNumber,
      salaryExpected: candidate?.salaryExpected,
      currentSalary: candidate?.currentSalary,
      onboardDate: candidate?.onboardDate
        ? moment(candidate?.onboardDate, "DD/MM/YYYY")
        : null,
    };
    if (typeModal === "detail") {
      return (
        <div>
          <Row>
            {listApplicationInfo.map((item, index) => (
              <Row className="flex w-1/2 mt-2" key={index}>
                <div>
                  <Icon size={14} icon={item.nameIcon} color="#324054" />
                </div>
                <span className="text-property-candidate ml-4 pr-2">
                  {item.value}
                </span>
              </Row>
            ))}
          </Row>
          <Row>
            <Row
              className={`flex items-start mt-2 ${
                _.isNumber(data?.matching.rate) ? "w-1/2 pr-1" : "w-full"
              }`}
            >
              <AppCheckBox
                disabled={
                  typeRoleUsed !== IAccountRole.CSL || data?.status === 5
                } // role CST ,CSL mới được sửa
                checked={commissionFlag}
                onChange={onChangeCheckBox}
              />
              <span className="text-property-candidate ml-4 underline text">
                Ủy thác cho reco hỗ trợ ứng viên
              </span>
            </Row>
            {_.isNumber(data?.matching.rate) && (
              <Row className="flex items-start w-1/2 mt-2">
                <Popover
                  content={contentRate}
                  title={titleRate}
                  placement="rightTop"
                >
                  <span
                    className="text-base font-normal cursor-pointer"
                    style={{color: getColorCandidateRate(data?.matching.rate)}}
                  >
                    Tỷ lệ phù hợp {data?.matching.rate}%
                  </span>
                </Popover>
              </Row>
            )}
          </Row>
          {candidate?.note && (
            <Row className="flex items-center w-full mt-2">
              <span className="text-property-candidate !ml-0">
                <span className="font-bold">Note: </span>
                {candidate?.note}
              </span>
            </Row>
          )}
        </div>
      );
    }
    return (
      <Formik
        initialValues={initFormEdit}
        innerRef={infoUpdateRef}
        onSubmit={(): void => {
          //
        }}
      >
        {({values, handleSubmit}): JSX.Element => {
          return (
            <form className="form-edit-candidate-info" onSubmit={handleSubmit}>
              <Row>
                {listInfoCandidateEdit.map(
                  (item: IItemEditInfo, index: number) => {
                    const status = (): "error" | undefined | "warning" => {
                      if (item.name === "name" && !values?.name) {
                        return "error";
                      }

                      if (
                        item.name === "phoneNumber" &&
                        values?.phoneNumber &&
                        !REGEX_PHONE_NUMBER.test(String(values.phoneNumber))
                      ) {
                        return "error";
                      }

                      if (
                        item.name === "email" &&
                        values?.email &&
                        !REGEX_EMAIL.test(values.email)
                      ) {
                        return "error";
                      }

                      return undefined;
                    };

                    return (
                      <Row
                        className={`w-1/2 mt-2 ${
                          index % 2 === 0 ? "pr-2" : "pl-2"
                        }`}
                        key={index}
                      >
                        <TextInput
                          containerclassname="flex-1"
                          label={item.label}
                          name={item.name}
                          value={String(values[item.name] || "")}
                          required={item.required}
                          status={status()}
                          onlynumber={item.name === "phoneNumber"}
                        />
                      </Row>
                    );
                  }
                )}
              </Row>
            </form>
          );
        }}
      </Formik>
    );
  };

  const renderFooter = (): JSX.Element => {
    if (typeRoleUsed === IAccountRole.ADMIN) {
      if (data?.stage === 3 && data?.status === 5) {
        return (
          <Row className="flex justify-center items-center">
            {!data.hasPartnerPayment && (
              <AppButton
                onClick={checkPartnerPaymentExist.refetch}
                classrow="mr-2 w-64"
                label="Thanh toán cộng tác viên"
                typebutton="primary"
                disabled={checkPartnerPaymentExist.isLoading}
              />
            )}
            {!data.hasCustomerPayment && (
              <AppButton
                onClick={checkCustomerPaymentExist.refetch}
                classrow="ml-2 w-64"
                label="Thanh toán khách hàng"
                typebutton="primary"
                disabled={checkCustomerPaymentExist.isLoading}
              />
            )}
          </Row>
        );
      }
      return (
        <Row className="flex justify-center items-center">
          <AppButton
            classrow="mr-2 w-64"
            label="Đóng"
            typebutton="secondary"
            onClick={closeModal}
          />
          {!(data?.stage === 3 && data?.status === 5) && (
            <AppButton
              classrow="ml-2 w-64 btn-edit"
              label="Lưu thông tin"
              typebutton="primary"
              onClick={changeStatus}
              disabled={data?.stage === 3 && data?.status === 5}
            />
          )}
        </Row>
      );
    }

    if (isCTV) {
      return (
        <Row className="flex justify-center items-center">
          <AppButton
            classrow="mr-2 w-64 btn-cancel"
            label={typeModal === "detail" ? "Hủy ứng tuyển" : "Hủy"}
            typebutton="primary"
            onClick={handleCancelCandidate}
            disabled={typeModal === "detail" && data?.status === 2}
          />
          {/* <AppButton
            onClick={handleEditInfoCandidate}
            classrow="ml-2 w-64 btn-edit"
            label={
              typeModal === "detail" ? "Chỉnh sửa thông tin" : "Lưu thông tin"
            }
            typebutton="primary"
          /> */}
        </Row>
      );
    }

    if (typeRoleUsed === IAccountRole.CSL) {
      if (data?.stage === 3 && data?.status === 5 && !data.hasPartnerPayment) {
        return (
          <Row className="flex justify-center items-center">
            <AppButton
              classrow="mr-2 w-64"
              label="Đóng"
              typebutton="secondary"
              onClick={closeModal}
            />
            <AppButton
              onClick={checkPartnerPaymentExist.refetch}
              classrow="w-64"
              label="Thanh toán cộng tác viên"
              typebutton="primary"
              disabled={checkPartnerPaymentExist.isLoading}
            />
          </Row>
        );
      }

      return (
        <Row className="flex justify-center items-center">
          <AppButton
            classrow="mr-2 w-64"
            label="Đóng"
            typebutton="secondary"
            onClick={closeModal}
          />
          {!(data?.stage === 3 && data?.status === 5) && (
            <AppButton
              classrow="ml-2 w-64 btn-edit"
              label="Lưu thông tin"
              typebutton="primary"
              onClick={changeStatus}
              disabled={data?.stage === 3 && data?.status === 5}
            />
          )}
        </Row>
      );
    }
    const isRolePayment =
      [IAccountRole.AML, IAccountRole.BD].includes(
        typeRoleUsed as IAccountRole
      ) && typeRoleUsed !== IAccountRole.AMG;

    if (
      typeRoleUsed === IAccountRole.AML ||
      typeRoleUsed === IAccountRole.BD ||
      typeRoleUsed === IAccountRole.AMG
    ) {
      if (
        data?.stage === 3 &&
        data?.status === 5 &&
        !data.hasCustomerPayment &&
        isRolePayment
      ) {
        return (
          <Row className="flex justify-center items-center">
            <AppButton
              classrow="mr-2 w-64"
              label="Đóng"
              typebutton="secondary"
              onClick={closeModal}
            />
            <AppButton
              onClick={checkCustomerPaymentExist.refetch}
              classrow="w-64"
              label="Thanh toán khách hàng"
              typebutton="primary"
              disabled={checkCustomerPaymentExist.isLoading}
            />
          </Row>
        );
      }

      if (
        typeRoleUsed === IAccountRole.AML ||
        typeRoleUsed === IAccountRole.AMG
      ) {
        return (
          <Row className="flex justify-center items-center">
            <AppButton
              classrow="mr-2 w-64"
              label="Đóng"
              typebutton="secondary"
              onClick={closeModal}
            />
            {!(data?.stage === 3 && data?.status === 5) && (
              <AppButton
                classrow="ml-2 w-64 btn-edit"
                label="Lưu thông tin"
                typebutton="primary"
                onClick={changeStatus}
                disabled={data?.stage === 3 && data?.status === 5}
              />
            )}
          </Row>
        );
      }
    }
    return <div />;
  };

  function BodyModal(): React.ReactNode {
    const getLastOperationHistories = (value: any[]): any => {
      if (value && Array?.isArray(value) && value?.length > 0) {
        const length = value?.length;
        return value[length - 1];
      }
      return "";
    };
    if (
      detailApplication.isLoading ||
      updateApplication.isLoading ||
      updateStatusApplication.isLoading
    ) {
      return <AppLoading classNameContainer="h-[72vh]" />;
    }
    return (
      <div>
        <Row className="h-[72vh]">
          <Col className="pr-2 h-full" span={12}>
            <div className="container-item-detail-modal">
              <div className="card-info">
                <Row>
                  <Col span={4}>
                    {candidate?.fileAvatarPath ? (
                      <Avatar
                        size={64}
                        src={
                          config.NETWORK_CONFIG.API_BASE_URL +
                          candidate.fileAvatarPath
                        }
                      />
                    ) : (
                      <Avatar
                        size={64}
                        className="candidate-information-ui_avatar"
                      >
                        {getAbbreviatedName(candidate?.name)}
                      </Avatar>
                    )}
                  </Col>
                  <Col span={16} className="pr-3">
                    <p className="name-application text24">
                      {candidate?.name || "N/A"}
                    </p>
                    <p className="positionName-application text16">
                      {data?.positionName || "N/A"}
                    </p>
                  </Col>
                  <Col span={4}>
                    <Row className="items-baseline">
                      <div
                        className="dot-status mr-1"
                        style={{
                          backgroundColor: getStatusCandidate(candidate?.status)
                            .color,
                        }}
                      />
                      {getStatusCandidate(candidate?.status).label}
                    </Row>
                  </Col>
                </Row>
                {renderInfoCandidate()}
                <div className="line" />
                <Row className="flex items-center justify-between">
                  <span className="title-job">Thông tin công việc</span>
                  <a
                    className="a-detail-job"
                    target="_blank"
                    href={`${config.PATHNAME.JOB_DETAIL}?id=${data?.requestJobId}`}
                    rel="noreferrer"
                  >
                    Chi tiết
                  </a>
                </Row>
                <Row>
                  {listJobInfo.map((item, index) => (
                    <Row className="flex w-1/2 mt-2" key={index}>
                      <div>
                        <Icon
                          size={14}
                          icon={item.nameIcon}
                          color="#324054"
                          className=""
                        />
                      </div>
                      <span className="text-property-candidate ml-4">
                        {item.value}
                      </span>
                    </Row>
                  ))}
                </Row>
                {(typeRoleUsed === IAccountRole.CSL ||
                  typeRoleUsed === IAccountRole.ADMIN ||
                  typeRoleUsed === IAccountRole.BD ||
                  isRoleAM) && (
                  <FormStatus
                    refFormStatus={infoUpdateRef}
                    detailApplication={detailApplication}
                  />
                )}
                <div className="line" />
                <Row
                  className="status-application-title"
                  style={{color: "#329932"}}
                >
                  <span>
                    Reco{" "}
                    {open
                      ? stagesApplication?.find(
                          (item: OptionSelect) =>
                            item.id === String(data?.stage)
                        )?.label
                      : ""}{" "}
                    {open
                      ? statusesApplication?.find(
                          (item: OptionSelect) =>
                            item.id === String(data?.status)
                        )?.label
                      : ""}
                  </span>
                  <Icon
                    size={14}
                    icon="history"
                    color="#329932"
                    className="ml-4 mr-1"
                  />
                  <span>
                    {timeSince(
                      getLastOperationHistories(data?.appHistories || [])
                        ?.modifiedDate
                    )}
                  </span>
                </Row>
                <Steps
                  current={currentStep - 1}
                  size="small"
                  labelPlacement="vertical"
                  items={ListStep}
                />
                <Row className="items-center mt-4">
                  <span className="title-history">Lịch sử hoạt động</span>
                  <Icon size={8} icon="arrow-drop-down-line" color="#324054" />
                </Row>
                {data?.appHistories.map((item, index) => {
                  const stageItem = item?.stage
                    ? stagesApplication?.find(
                        (i) => i.id === JSON.stringify(item.stage)
                      )?.label
                    : "";
                  const statusItem = item?.status
                    ? statusesApplication?.find(
                        (i) => i.id === JSON.stringify(item.status)
                      )?.label
                    : "";

                  return (
                    <Row
                      className="items-baseline history-item mt-1"
                      key={index}
                    >
                      <Row className="items-baseline flex-1 mr-2">
                        <div className="dot-history" />
                        <Col className="flex-1 ml-1">
                          <span>{`Reco đã chuyển trạng thái ứng viên thành ${stageItem} ${statusItem}`}</span>
                          <HtmlComponent
                            classNameContainer="text12 history-description"
                            htmlString={item.summary || ""}
                          />
                        </Col>
                      </Row>
                      <span>{formatDateTime(item.modifiedDate)}</span>
                    </Row>
                  );
                })}
              </div>
            </div>
          </Col>
          <Col className="pl-2 h-full" span={12}>
            <div className="container-item-detail-modal p-6">
              <ViewCvCandidate
                docs={{
                  filePathBase64: candidate?.fileCVPath,
                  fileName: candidate?.fileCVName,
                }}
                modeViewCandidate={IModeViewCandidate.view}
                createdDate={candidate?.createdDate || ""}
                idCandidate={candidate?.candidateId}
                isShowFormatFile
              />
            </div>
          </Col>
        </Row>
        <div className="mt-4">{renderFooter()}</div>
      </div>
    );
  }

  return (
    <AppModal
      className="modal-detail-application"
      open={open}
      footer={null}
      onCancel={onCloseModal}
      width="80%"
      title="Chi tiết ứng tuyển"
    >
      {BodyModal()}
      <ModalCancelApplication
        candidate={candidate}
        updateApplication={updateApplication}
        open={isShowModalCancel}
        closeModal={(): void => setIsShowModalCancel(false)}
      />
      <ModalCreatePayment
        isShow={isShowModalPayment}
        setIsShow={setIsShowModalPayment}
        applicationId={applicationId}
        onSuccess={reloadData}
        createdDateCandidate={candidate?.createdDate || ""}
      />
      <ModalCreateBill
        isShow={isShowModalCreateBill}
        setIsShow={setIsShowModalCreateBill}
        applicationId={applicationId}
        onSuccess={reloadDataWhenCreateBill}
        createdDateCandidate={candidate?.createdDate || ""}
      />
    </AppModal>
  );
}
