import Header from "@app/components/Layout/Header";
import React from "react";
import Content from "./Content/Content";

interface LandingLayoutProps {
  children: React.ReactNode;
}

export default function LandingLayout({
  children,
}: LandingLayoutProps): JSX.Element {
  return (
    <div className="wrapper">
      <div className="landing-page">
        <div className="w-full landing-page__header">
          <Header />
        </div>
        <Content>{children}</Content>
      </div>
    </div>
  );
}
