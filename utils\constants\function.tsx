import moment, {Moment} from "moment";
import {DATE_FORMAT} from "./formatDateTime";
import {useEffect, useState} from "react";
import _ from "lodash";
import {
  listSalaryRanges,
  optionGuarantee,
  paymentStatus,
  statusCustomers,
  statusEmployee,
  statusesApplication,
} from "./state";
import {IMultiSelect, OptionSelect} from "@app/types";
import {
  CHARACTER_SPECIALS,
  PASSWORD_REGEX,
  REGEX_CHARACTER,
  REGEX_EMAIL,
  REGEX_ONLY_NUMBER,
} from "./regex";
import {logEvent} from "firebase/analytics";
import {analytics, app, firebaseConfig} from "../firebase";
// eslint-disable-next-line import/no-cycle
import {IListSkill} from "../../api/ApiCandidate";
import {localStorageKey, preferredWorkplace} from "./constants";
import {getMessaging, getToken, isSupported} from "firebase/messaging";
import {saveTokenNotification} from "@app/api/ApiFirebaseNotification";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function timeSince(date: any): string {
  if (!date) {
    return "";
  }
  return moment(date).fromNow();
}

export function getAbbreviatedName(name?: string): string {
  if (!name) {
    return "U";
  }
  const wordSplit = name.split(" ");
  let result = "";
  if (wordSplit.length === 1) {
    result = wordSplit[0].charAt(0).toUpperCase();
  } else {
    result =
      wordSplit[0].charAt(0).toUpperCase() +
      wordSplit[wordSplit.length - 1].charAt(0).toUpperCase();
  }

  return result;
}

export const formatMoney = (salary?: number, currency?: string): string => {
  if (!salary && salary !== 0) {
    return "N/A";
  }
  const currencyName = currency || "VND";
  return new Intl.NumberFormat("en-US").format(salary) + " " + currencyName;
};

export function onlyInputNumber(e: React.KeyboardEvent<HTMLInputElement>) {
  if (!REGEX_ONLY_NUMBER.test(String.fromCharCode(e.keyCode || e.which))) {
    e.preventDefault();
  }
  return true;
}

export function onlyPhoneNumber(e: React.KeyboardEvent<HTMLInputElement>) {
  if (
    REGEX_CHARACTER.test(String.fromCharCode(e.keyCode || e.which)) ||
    CHARACTER_SPECIALS.includes(e.key)
  ) {
    e.preventDefault();
  }
  return true;
}

export function inputCurrency(e: React.KeyboardEvent<HTMLInputElement>): any {
  const dotCount = (e.currentTarget.value.match(/\./g) || []).length;
  if (dotCount && dotCount >= 1 && e.key === ".") {
    e.preventDefault();
  }
  return true;
}

export const formatTimeToTableEdit = (time: string): moment.Moment | string => {
  if (!time) return "";
  if (time.toLowerCase() === "now") {
    return moment(new Date(), DATE_FORMAT);
  }
  return moment(time, DATE_FORMAT);
};

export const useDebounce = (value: any, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

export function appendFormData(
  formData: FormData,
  data: any,
  parentKey = ""
): void {
  if (Array.isArray(data)) {
    data.forEach((value, index) => {
      const key = parentKey + "[" + index + "]";
      if (typeof value === "object" && value !== null) {
        if (Array.isArray(value)) {
          appendFormData(formData, value, key);
        } else {
          Object.entries(value).forEach(([nestedKey, prop]) => {
            const nameKey = key + "." + nestedKey;
            formData.append(nameKey, value[nestedKey]);
          });
        }
      } else {
        formData.append(key, value);
      }
    });
  } else {
    Object.entries(data).forEach(([key, value]) => {
      const nameKey = parentKey ? parentKey + "[" + key + "]" : key;
      if (typeof value === "object" && value !== null) {
        appendFormData(formData, value, nameKey);
      } else {
        formData.append(nameKey, value as string);
      }
    });
  }
}

export const validatePhoneNumber = (value: string): boolean => {
  return !value.match(REGEX_CHARACTER);
};

export const salaryRange = (
  salaryFrom?: number,
  salaryTo?: number,
  currencyTypeId?: string
): string => {
  const currency = currencyTypeId || "VND";
  if (!salaryFrom && !salaryTo) {
    return "Lương thỏa thuận";
  }

  if (!salaryFrom && salaryTo) {
    return `Up to ${formatMoney(salaryTo, currency)}`;
  }

  if (salaryFrom && !salaryTo) {
    return `Min ${formatMoney(salaryFrom, currency)}`;
  }

  return `${formatMoney(salaryFrom, currency)} - ${formatMoney(
    salaryTo,
    currency
  )}`;
};

export const formatDateTime = (date?: string): string => {
  if (!date) {
    return "";
  }
  let hour = String(new Date(date).getHours());
  hour = hour.length === 1 ? "0" + hour : hour;
  let minutes = String(new Date(date).getMinutes());
  minutes = minutes.length === 1 ? "0" + minutes : minutes;

  return `${hour}:${minutes} - ${moment(date).format(DATE_FORMAT)}`;
};

export const autoFormatPhoneNumber = (value: string): string => {
  if (!value) return "";
  let result = "";
  for (let i = 0; i < value?.length; i++) {
    if (value[i] >= "0" && value[i] <= "9") {
      result += value[i];
    }
  }
  return result;
};

export const sortDateTime = (value: any[]) => {
  if (value?.length === 0) return [];
  const cloneList = [...value];
  return _.orderBy(cloneList, ["endDate"], ["asc"]);
};

export const moneyToNumber = (value?: string): number => {
  if (!value) return 0;
  let result = "";
  for (let i = 0; i < value?.length; i++) {
    if ((value[i] >= "0" && value[i] <= "9") || value[i] === ".") {
      result += value[i];
    }
  }
  return Number(result);
};

export const inviteBonus = (
  partnerRateType: boolean,
  partnerRateValue: number,
  currencyTypeId: string
): string => {
  if (partnerRateType) {
    return formatMoney(Math.round(partnerRateValue), currencyTypeId);
  }

  if (!partnerRateValue) return "0%";
  return `${partnerRateValue}%`;
};

export const upperCaseFirstChar = (value: string): string => {
  if (!value) return "";
  return value.charAt(0).toUpperCase() + value.slice(1);
};

export const getPaymentStatus = (id: number): OptionSelect => {
  return (
    paymentStatus.find((element) => element.id === String(id)) || {
      label: "N/A",
      value: "",
      color: "white",
      id: "",
      key: "",
    }
  );
};

export const getMonthWarrantySelected = (
  monthWarranty?: number | string
): OptionSelect => {
  if (!monthWarranty && monthWarranty !== 0) {
    return {
      value: "2",
      label: "2 tháng",
    };
  }

  return (
    optionGuarantee.find((i) => i.value === String(monthWarranty)) || {
      value: String(monthWarranty),
      label: monthWarranty + " tháng",
    }
  );
};

// expect date1 date2 moment
export const validateIsSameOrAfter = (date?: any, dateAfter?: any): boolean => {
  if (!date || !dateAfter) {
    return false;
  }
  return moment(dateAfter).isSameOrAfter(moment(date));
};

export const setQueryUrl = (objectQuery: object): void => {
  if (!_.isEmpty(objectQuery)) {
    const url = new URL(window.location.href);
    Object.entries(objectQuery).forEach(([key, value]): void => {
      url.searchParams.set(key, value);
    });
    window.history.pushState({}, "", url.toString());
  }
};

export const getStatusCustomer = (id: string): OptionSelect => {
  return (
    statusCustomers.find((item) => item.value === id) || {
      label: "",
      value: "",
      color: "white",
    }
  );
};

export const getStatusEmployee = (id?: string | number): OptionSelect => {
  return (
    statusEmployee.find((item) => item.value === String(id)) || {
      label: "",
      value: "",
      color: "white",
    }
  );
};

export const findApplicationStatus = (
  value: string,
  filterName = false
): OptionSelect => {
  return (statusesApplication?.find(
    (item) => (filterName ? item.label : item.key) === value
  ) || {
    label: "",
    value: "",
    key: "",
    color: "#cecece",
  }) as OptionSelect;
};

export const getFirst20Item = (list?: string[]): string[] => {
  if (!list || !Array.isArray(list)) {
    return [];
  }

  if (list.length > 20) {
    return list.slice(0, 20);
  }

  return list;
};

export const findSalaryRange = (values: string[]): string[] => {
  if (!values || values?.length === 0) return [];
  return (
    listSalaryRanges.filter((item) => values?.includes(item.value)) || []
  )?.map((item) => item.key) as string[];
};

export const validateEmail = (value: string): boolean => {
  if (!value || (value && !value.toLowerCase().match(REGEX_EMAIL)))
    return false;
  return true;
};

export const validatePassword = (value: string): boolean => {
  if (!value || (value && !value.match(PASSWORD_REGEX))) return false;
  return true;
};

export const convertToOptionSelect = (
  filters: IMultiSelect[]
): {label: string; value: string}[] => {
  const newData = filters.map((item) => ({
    value: item?.label,
    label: item?.label,
    key: item?.id,
  }));
  return newData;
};

export const logEventFirebase = (key: string, eventParams?: any): void => {
  return logEvent(analytics, key, eventParams);
};

export const getTimeZone = () =>
  new Date().getHours() - new Date().getUTCHours();

export const setAllFieldTouched = (values: any): Record<string, true> => {
  return Object.keys(values).reduce((touched, field) => {
    touched[field] = true;
    return touched;
  }, {} as Record<string, true>);
};

export const mapFilterSkillData = (filters: IListSkill[]): OptionSelect[] =>
  filters.map((item) => ({
    value: item.skillId,
    label: item.name,
    id: item.skillId,
  }));

export const convertValuesToOptionsSelect = (
  values?: string[]
): OptionSelect[] => {
  if (!values?.length) return [];
  return values.map((item) => ({
    value: item,
    label: item,
    key: item,
  }));
};

export const convertStringToArray = (
  data: string,
  mark = ","
): Array<string> => {
  if (!data) return [];
  return data.split(mark);
};

export const deleteKeyHasInvalidValue = (obj: object) => {
  const clone = obj;

  // eslint-disable-next-line no-restricted-syntax
  for (const property in clone) {
    if (!clone[property as keyof object])
      delete clone[property as keyof object];
  }

  return clone;
};

export const sortWorkLocation = (
  data: Array<OptionSelect>,
  keyCondition?: keyof OptionSelect
): OptionSelect[] => {
  const key = keyCondition || "value";
  const dataPreferredWorkplace = data.filter((i) =>
    preferredWorkplace.includes((i as any)[key])
  );

  const newData = _.uniqBy([...dataPreferredWorkplace, ...data], key);

  return newData;
};

export const mapDataOptionHasId = (
  data: Array<OptionSelect>,
  keyObjectDesire?: keyof OptionSelect
): Array<OptionSelect> => {
  if (!data || data?.length === 0) {
    return [];
  }

  const initialKeyDesire: keyof OptionSelect = keyObjectDesire || "key";

  return data.map((item) => ({
    ...item,
    id: (item[initialKeyDesire] ? item[initialKeyDesire] : item.value) as any,
  }));
};

export async function registerReady(
  scriptURL: string,
  options?: RegistrationOptions
) {
  return navigator.serviceWorker
    .register(scriptURL, options)
    .then((registration) => {
      // If there is an active worker and nothing incoming, we are done.
      const incomingSw = registration.installing || registration.waiting;

      if (registration.active && !incomingSw) {
        return Promise.resolve(registration);
      }

      // If not, wait for the newest service worker to become activated.
      /* eslint-disable consistent-return */
      return new Promise<ServiceWorkerRegistration>((fulfill, reject) => {
        if (incomingSw) {
          incomingSw.onstatechange = (evt) => {
            if ((evt.target as ServiceWorker)?.state === "activated") {
              incomingSw.onstatechange = null;
              return fulfill(registration);
            }
          };
        } else {
          reject(new Error("No incoming service worker found."));
        }
      });
    })
    .catch((err) => {
      console.error("Error registering service worker:", err);
      return Promise.reject(err);
    });
}

export const handleInitFcm = (userId?: number) => {
  if (!(userId && !Number.isNaN(userId))) {
    return;
  }

  const firebaseConfigParams = new URLSearchParams(
    firebaseConfig as any
  ).toString();

  const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPIKEY;

  registerReady(`/firebase-messaging-sw.js?${firebaseConfigParams}`, {
    scope: "/",
  })
    .then((serviceWorkerRegistration) => {
      isSupported()
        .then((isSupport) => {
          if (isSupport) {
            const messaging = getMessaging(app);
            Notification.requestPermission().then((permission) => {
              if (permission === "granted") {
                getToken(messaging, {
                  vapidKey: vapidKey,
                  serviceWorkerRegistration,
                })
                  .then((token) => {
                    const currentToken = localStorage.getItem(
                      localStorageKey.deviceToken
                    );
                    if (token && token !== currentToken) {
                      saveTokenNotification({
                        deviceToken: token,
                        userId: userId,
                      });

                      localStorage.setItem(localStorageKey.deviceToken, token);
                    }
                  })
                  .catch((e) => {
                    console.log(e);
                  });
              }
            });
          }
        })
        .catch((e) => {
          console.log(e);
        });
    })
    .catch((error) => {
      console.log(error);
    });
};

export const unRegisterServiceWorker = () => {
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker
      .getRegistrations()
      .then(function (registrations) {
        for (const registration of registrations) {
          registration.unregister().then(function (success) {
            if (success) {
              console.log("Service Worker unregistered successfully.");
              localStorage.removeItem(localStorageKey.deviceToken);
            } else {
              console.log("Service Worker unregistration failed.");
            }
          });
        }
      })
      .catch(function (error) {
        console.log("Error during unregistering Service Workers:", error);
      });
  }
};

export const compareTwoArrayNumber = (
  arr1: Array<number>,
  arr2: Array<number>
): boolean => {
  if (arr1.length !== arr2.length) return true;

  const cloneArr1 = arr1.sort();
  const cloneArr2 = arr2.sort();

  for (let i = 0; i < cloneArr1.length; i++) {
    if (cloneArr1[i] !== cloneArr2[i]) return true;
  }

  return false;
};

export function onlyUnique(value: string, index: number, array: Array<string>) {
  return array.indexOf(value) === index;
}

export const formatDateTimeValue = (
  time: Moment | Date | string,
  format = DATE_FORMAT
): string => {
  if (!time) return "";

  const isMatch = moment(time, format, true).isValid();
  if (isMatch) return time as any;

  return moment(time).format(format);
};

export const secureInformation = (text: string): string => {
  const defaultText = "***";
  if (!text) return defaultText;
  if (text.toLowerCase().match(REGEX_EMAIL)) {
    const splitText = text.split("@");
    return `${splitText[0].replace(/./g, "*")}@${splitText[1]}`;
  }

  if (text.length <= 3) {
    return defaultText;
  }
  return text.slice(0, -3) + defaultText;
};
