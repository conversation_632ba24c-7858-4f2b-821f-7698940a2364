import AppTable from "@app/components/AppTable";
import "./index.scss";
import {ColumnsType} from "antd/es/table";
import {useMutation, useQuery} from "react-query";
import {useEffect, useMemo, useState} from "react";
import AppPagination from "@app/components/AppPagination";
import ApiCandidate from "@app/api/ApiCandidate";
import {Tooltip} from "antd";
import AppModal from "@app/components/AppModal";
import {useRouter} from "next/router";
import {setQueryUrl} from "@app/utils/constants/function";
import {OptionSelect} from "@app/types";
import {FilterCompany} from "./FilterCompany";
import ApiCompany, {
  CompanyColumns,
  ValuesSearchCompany,
} from "@app/api/ApiCompany";
import {CompanyDetail} from "./CompanyDetail";

export default function ManagerCompany(): JSX.Element {
  const [searchParams, setSearchParams] = useState<ValuesSearchCompany>({
    currentPage: 1,
    pageSize: 50,
  } as ValuesSearchCompany);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [idCompany, setIdCompany] = useState<number>(0);
  const [dataSource, setDataSource] = useState<CompanyColumns[]>([]);
  const [showCompanyDetail, setShowCompanyDetail] = useState<boolean>(false);
  const router = useRouter();
  let companyId: string | undefined;
  if (router.query.id) {
    companyId = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    companyId = Object.fromEntries(searchParams)?.id;
  }

  useEffect(() => {
    if (companyId) {
      showModalDetail(Number(companyId));
    }
  }, [companyId]);

  const showModalDetail = (id: number): void => {
    setIdCompany(id);
    setShowCompanyDetail(true);
  };

  const requestSkillList = useQuery("requestSkillList", () => {
    return ApiCandidate.getListSkill();
  });

  const skills: OptionSelect[] = useMemo(() => {
    const newData: OptionSelect[] =
      requestSkillList?.data?.map((skill) => ({
        label: skill?.name,
        value: skill?.name,
        key: skill?.skillId,
      })) ?? [];
    return newData;
  }, [requestSkillList?.data]);

  const requestCompanyList = useMutation(
    (data: ValuesSearchCompany) => {
      return ApiCompany.getListCompany(data);
    },
    {
      onSuccess: (data) => {
        setDataSource(data.companiesPaging || []);
        setTotalCount(data?.totalCount || 0);
      },
    }
  );

  const onSearchParams = () => {
    const dataClone: ValuesSearchCompany = searchParams;
    Object.entries(dataClone)?.forEach(([key, value]) => {
      if (typeof value === "string" && !value) {
        delete dataClone[key as keyof ValuesSearchCompany];
      }
      if (Array.isArray(value) && value?.length === 0) {
        delete dataClone[key as keyof ValuesSearchCompany];
      }
    });
    requestCompanyList.mutate(dataClone);
  };

  useEffect(() => {
    onSearchParams();
  }, [searchParams]);

  const columns: ColumnsType<CompanyColumns> = [
    {
      title: "Tên công ty",
      key: "companyName",
      dataIndex: "companyName",
      align: "left",
      ellipsis: true,
    },
    {
      title: "Quy mô",
      key: "companyScale",
      dataIndex: "companyScale",
    },
    {
      title: "Tech stack",
      key: "techStacks",
      dataIndex: "techStacks",
      ellipsis: true,
      render: (_: string, record: CompanyColumns): JSX.Element => {
        const content =
          record?.techStacks?.length > 0 ? record?.techStacks?.join(", ") : "";
        return (
          <div className="truncate w-full text-ellipsis overflow-hidden">
            {content ? (
              <Tooltip
                title={content}
                placement="bottomLeft"
                overlayStyle={{maxWidth: "400px"}}
              >
                {content}
              </Tooltip>
            ) : (
              ""
            )}
          </div>
        );
      },
    },
    {
      title: "Note",
      dataIndex: "notes",
      key: "notes",
      align: "left",
      render: (_, {notes}: CompanyColumns) => {
        const noteContent = () => (
          <div>
            {notes.map((note: any, index: number) => (
              <div key={index} className="whitespace-pre">
                {note.noteCreator.trim()}: {note.noteContent.trim()}
              </div>
            ))}
          </div>
        );
        return (
          <Tooltip
            title={noteContent()}
            placement="leftTop"
            overlayStyle={{maxWidth: "400px"}}
          >
            <div className="line-clamp-5">{noteContent()}</div>
          </Tooltip>
        );
      },
    },
  ];

  const handlePagination = (page: number, pageSize: number): void => {
    setSearchParams((prev) => ({
      ...prev,
      currentPage: page,
      pageSize: pageSize,
    }));
  };

  const handleResetCurrentPage = (): void => {
    setSearchParams((prev) => ({
      ...prev,
      currentPage: 1,
    }));
  };

  const handleSearchParams = (data: ValuesSearchCompany): void => {
    setSearchParams(data);
  };

  const visibleDetailCandidate = (): void => {
    window.history.pushState({}, "", router.route);
    setShowCompanyDetail(false);
  };

  const requestWorkLocationList = useQuery("requestWorkLocationList", () => {
    return ApiCandidate.getWorkLocationList();
  });

  const workLocations: OptionSelect[] = useMemo(() => {
    const newData: OptionSelect[] =
      requestWorkLocationList?.data?.map((location) => ({
        label: location?.name,
        value: location?.name,
        key: location?.workLocationId,
      })) ?? [];
    return newData;
  }, [requestWorkLocationList?.data]);

  return (
    <div className="company w-full">
      <div className="company__search w-full">
        <FilterCompany
          workLocations={workLocations}
          skills={skills}
          onResetCurrentPage={handleResetCurrentPage}
          onSearchParams={handleSearchParams}
        />
      </div>
      <div className="company__table mt-2">
        <AppTable
          columns={columns}
          dataSource={dataSource}
          loading={requestCompanyList?.isLoading}
          style={{
            maxHeight: "72vh",
            overflowY: "scroll",
            cursor: "pointer",
          }}
          onRow={(record: CompanyColumns): any => {
            return {
              onClick: (): void => {
                setQueryUrl({id: String(record.companyId)});
                showModalDetail(record.companyId);
              },
            };
          }}
        />
      </div>
      <div className="company__pagination mt-2">
        <AppPagination
          defaultPageSize={searchParams?.pageSize}
          current={searchParams?.currentPage}
          pageSize={searchParams?.pageSize}
          total={totalCount}
          onChange={handlePagination}
        />
      </div>

      <AppModal
        className="modal-detail-company"
        centered
        footer={null}
        open={showCompanyDetail}
        onCancel={visibleDetailCandidate}
        title="Chi tiết công ty"
        width="80%"
      >
        <CompanyDetail
          setShowCompanyDetail={setShowCompanyDetail}
          reloadData={onSearchParams}
          techStacks={skills}
          companyId={idCompany}
        />
      </AppModal>
    </div>
  );
}
