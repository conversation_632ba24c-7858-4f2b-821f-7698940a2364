import "./index.scss";
import {Input, InputProps} from "formik-antd";
import {useRef, useState} from "react";
import classNames from "classnames";

interface TextInputProps extends InputProps {
  label: string;
  name: string;
  required?: boolean;
  containerclassname?: string;
  defaultValue?: string;
  value?: any;
  free?: boolean;
}

export function AppPassword(props: TextInputProps): JSX.Element {
  const {label, containerclassname, placeholder, required, value, free} = props;
  const [focus, setFocus] = useState(false);

  const refInput = useRef({input: {value: ""}});

  const isOccupied =
    focus || (free ? false : !!refInput.current?.input?.value || value);

  const labelClass = isOccupied ? "label as-label" : "label as-placeholder";

  const requiredMark = required ? (
    <span className="text-required">*</span>
  ) : null;

  return (
    <div
      className={classNames("text-input-password", containerclassname)}
      onBlur={(): void => setFocus(false)}
      onFocus={(): void => setFocus(true)}
    >
      <Input.Password
        ref={refInput}
        {...props}
        placeholder={focus ? placeholder : ""}
      />
      <span className={labelClass}>
        {label} {requiredMark}
      </span>
    </div>
  );
}
