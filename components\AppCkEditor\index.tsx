import dynamic from "next/dynamic";
import {TextEditorProps} from "./Editor";
import React from "react";
import {Skeleton} from "antd";

const Editor = dynamic(() => import("./Editor"), {
  ssr: false,
  loading: () => <Skeleton />,
});

function AppCkEditor(props: TextEditorProps): JSX.Element {
  return (
    <div>
      <Editor {...props} />
    </div>
  );
}

export default React.memo(AppCkEditor);
