import "./index.scss";
import {But<PERSON>, <PERSON>} from "antd";

interface ButtonSubmitProps {
  isSubmitting?: boolean;
  label: string;
  classrow?: string;
}

export function ButtonSubmit({
  isSubmitting,
  label,
  classrow,
}: ButtonSubmitProps): JSX.Element {
  return (
    <Row className={`button-container ${classrow}`}>
      <Button
        className="button"
        type="primary"
        htmlType="submit"
        loading={isSubmitting}
      >
        {label}
      </Button>
    </Row>
  );
}
