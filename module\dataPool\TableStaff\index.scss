.data-pool-table {
  .btn-filter {
    button {
      font-size: 0.875rem;
      font-weight: 400;
      border: 1px dashed $header_tf;
      border-radius: 8px;
      display: flex;
      align-items: center;
      align-self: center;
      color: $text-color-input;
      height: auto;
    }
    button:hover,
    button:focus {
      background: none;
      color: $text-color-input;
      border: 1px dashed $header_tf;
    }
  }
}

.content-filter-advance-staff {
  background-color: $white-color;
  width: 312px;

  .title-filter {
    font-size: 1.5rem;
    font-weight: 400;
    color: $text-color-input;
  }

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .btn-close-popover {
    button {
      border: none;
    }
  }
}

.add-staff-btn,
.statistical-btn {
  .primary-button {
    height: 33px !important;
  }
  &__content {
    color: $white-color;
    background-color: $primary-color;
    border-radius: 8px;
  }
}
