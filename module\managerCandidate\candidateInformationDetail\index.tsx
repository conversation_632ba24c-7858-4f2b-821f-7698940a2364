/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, {useEffect, useMemo, useRef, useState} from "react";
import "./index.scss";
import {Avatar, Spin, Input, notification, Col, Row} from "antd";
import Icon from "@app/components/Icon/Icon";
import ApiCandidate, {
  IEducations,
  IModeViewCandidate,
  ISuitableJobForCandidate,
} from "@app/api/ApiCandidate";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppButton from "@app/components/AppButton";
import ViewCvCandidate from "@app/components/viewCvCandidate";
// eslint-disable-next-line import/namespace
import {
  findApplicationStatus,
  formatMoney,
  getAbbreviatedName,
  logEventFirebase,
  moneyToNumber,
} from "@app/utils/constants/function";
import {getStatusCandidate, statusCandidate} from "@app/utils/constants/state";
import AppModalConfirm from "@app/components/AppModalConfirm";
import ModalIntroApplications from "../modalIntroApplications";
import {useMutation, useQuery} from "react-query";
import {EditCandidate} from "../editCandidate";
import {useRouter} from "next/router";
import {useDispatch, useSelector} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import config from "@app/config";
import ApiPotentialCandidate from "@app/api/ApiPotentialCandidate";
import AppAccessDenied from "@app/components/AppAccessDenied";
import {messageConfirmCommission} from "@app/utils/constants/message";
import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole} from "@app/types";
import {ColumnsType} from "antd/lib/table";
import AppTable from "@app/components/AppTable";
import Link from "next/link";
import classNames from "classnames";
import {Form, Formik, FormikProps} from "formik";
import AppRadio from "@app/components/AppRadio";
import AppTextarea from "@app/components/AppTextarea";
import {
  EvaluationOfCandidateData,
  getEvaluationOfCandidate,
  saveEvaluateCandidate,
} from "@app/api/ApiJobRequest";
import TableHistoryApplyReco from "../tableHistoryApplyReco";

interface Props {
  idCandidate?: number;
  setShowCandidateDetail?: (isShow: boolean) => void;
  reloadData?: () => void;
  introNow?: (commissionFlag: boolean) => void;
  tab?: "candidate" | "potentialCandidate";
  idPotentialCandidate?: string | number;
  requestJobId?: number;
  onFetchSuccessCandidateDetail?: (id: number) => void;
  isShowModalEvaluation?: boolean;
  refetchListCandidate?: () => void;
}

const {TextArea} = Input;

interface EvaluationOfCandidateForm {
  isSuitable: boolean;
  note: string;
}

export function CandidateInformationDetail(props: Props): JSX.Element {
  const {
    idCandidate,
    setShowCandidateDetail,
    reloadData,
    introNow,
    tab,
    idPotentialCandidate,
    requestJobId,
    onFetchSuccessCandidateDetail,
    isShowModalEvaluation,
    refetchListCandidate,
  } = props;
  const {user} = useSelector(selectUser);
  const isCTV = user?.role?.includes(IAccountRole.CTV);
  const router = useRouter();
  let id: number | undefined;
  const dispatch = useDispatch();
  const timeOut = useRef<any>();

  if (idCandidate) {
    id = idCandidate;
  } else {
    if (router.query.id) {
      id = Number(router.query.id);
    } else {
      const searchParams = new URLSearchParams(window.location.search);
      id = Number(Object.fromEntries(searchParams)?.id);
    }
  }

  const [noteData, setNoteData] = useState<string>("");
  const [showModalDelete, setShowModalDelete] = useState<boolean>(false);
  const [isOpenIntroApplication, setIsOpenIntroApplication] = useState(false);
  const [isOpenModalCommission, setIsOpenModalCommission] = useState(false);
  const evaluationRef = useRef<FormikProps<EvaluationOfCandidateForm>>(null);

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const dataCandidateDetail = useQuery(
    ["candidateDetail", id, tab],
    // eslint-disable-next-line consistent-return
    () => {
      if (tab !== "potentialCandidate") {
        return ApiCandidate.getCandidateDetailInformation(id, requestJobId);
      }
    },
    {
      enabled: id !== -1,
      onSuccess: () => {
        onFetchSuccessCandidateDetail?.(Number(id));
      },
      onError: (error: any): void => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            if (!idCandidate) {
              router.back();
            } else {
              window.history.pushState({}, "", router.route);
              setShowCandidateDetail?.(false);
            }
          }, 4000);
        }
      },
    }
  );
  const dataCandidate = dataCandidateDetail.data;

  const dataPotentialCandidateDetail = useQuery(
    ["potentialCandidateDetail", idPotentialCandidate, tab],
    // eslint-disable-next-line consistent-return
    () => {
      if (idPotentialCandidate && tab === "potentialCandidate") {
        logEventFirebase(
          config.EVENT_ANALYTICS_KEY.VIEW_DETAIL_POTENTIAL_CANDIDATE
        );
        return ApiPotentialCandidate.getDetailPotentialCandidate(
          idPotentialCandidate
        );
      }
    },
    {
      enabled: id !== -1,
      onError: (error: any): void => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            window.history.pushState({}, "", router.route);
            setShowCandidateDetail?.(false);
          }, 4000);
        }
      },
    }
  );

  const dataPotentialCandidate = dataPotentialCandidateDetail.data;

  const potentialCandidate = useMemo(() => {
    if (tab !== "potentialCandidate") {
      return null;
    }
    const status = statusCandidate.find(
      (item) => item.label === dataPotentialCandidate?.status_Name
    );
    return {
      name: dataPotentialCandidate?.name,
      birthday: dataPotentialCandidate?.birthday,
      candidateSkills: dataPotentialCandidate?.skills,
      countryId: dataPotentialCandidate?.countryID,
      countryName: dataPotentialCandidate?.country_Name,
      currencyTypeId: dataPotentialCandidate?.currencyTypeId,
      email: dataPotentialCandidate?.email,
      experienceYear: dataPotentialCandidate?.year_Of_Exp,
      currentSalary: dataPotentialCandidate?.current_Salary,
      createdDate: dataPotentialCandidate?.time,
      educations:
        dataPotentialCandidate?.education?.map((item) => ({
          startDate: item?.startTime,
          endDate: item?.endTime,
          degreeType: item?.degree,
          fieldOfStudy: item?.specialization,
          schoolName: item?.organization,
        })) || [],
      workHistories:
        dataPotentialCandidate?.experience?.map((item) => ({
          startDate: item?.startTime,
          endDate: item?.endTime,
          companyName: item?.organization,
          position: item?.level,
        })) || [],
      fileCVPath: "",
      fileCVName: "",
      facebookURL: "",
      linkedinURL: dataPotentialCandidate?.linkedin_URL,
      note: dataPotentialCandidate?.note,
      phoneNumber: dataPotentialCandidate?.phone_Number,
      onboardDate: dataPotentialCandidate?.onboardDate,
      positionExpected: dataPotentialCandidate?.position,
      githubURL: "",
      salaryExpected: dataPotentialCandidate?.expected_Salary,
      statusName: dataPotentialCandidate?.status_Name,
      status: status?.id,
      applicationHistory: [],
      workLocationNames: "",
      fileAvatarPath: "",
      languages: dataPotentialCandidate?.language || [],
      managerName: dataPotentialCandidate?.manager_UserName,
      source: dataPotentialCandidate?.source,
      skypeURL: dataPotentialCandidate?.skype_Account,
      tags: dataPotentialCandidate?.tag,
      currentSalaryType: dataPotentialCandidate?.currentSalaryType || 1,
      salaryExpectedType: dataPotentialCandidate?.salaryExpectedType || 1,
    };
  }, [dataPotentialCandidate, tab]);

  const dataOverall =
    tab === "potentialCandidate" ? potentialCandidate : dataCandidate;

  const renderLanguage = useMemo(() => {
    if (dataOverall?.languages.length === 0) return "N/A";
    if (tab === "potentialCandidate") {
      const languages = dataOverall?.languages?.map((item: any) => {
        return item.language + "-" + item.level;
      });
      return languages?.join(", ");
    }
    return dataOverall?.languages?.join(", ");
  }, [dataOverall?.languages]);

  const [openEditCandidate, setOpenEditCandidate] = useState(false);

  const handleEditNoteCandidate = useMutation(
    (note: string) => {
      return ApiCandidate.editNoteCandidate(Number(id), note);
    },
    {
      onSuccess: () => {
        notification.success({message: "Chỉnh sửa ghi chú thành công"});
        reloadData?.();
        dataCandidateDetail.refetch();
        setNoteData("");
      },
    }
  );

  const deleteCandidate = useMutation(
    (id: number) => {
      return ApiCandidate.deleteCandidate(id);
    },
    {
      onSuccess: (): void => {
        notification.success({message: "Xóa ứng viên thành công"});
        if (idCandidate) {
          setShowModalDelete(false);
          setShowCandidateDetail?.(false);
          reloadData?.();
        } else {
          router.back();
        }
      },
    }
  );

  const handleDeleteCandidate = (): void => {
    if (id) {
      deleteCandidate.mutate(id);
    }
  };

  const handleVisibleModalDelete = (): void => {
    setShowModalDelete(false);
  };

  const introJob = (): void => {
    if (introNow) {
      if (isCTV) {
        setIsOpenModalCommission(true);
      } else {
        introNow(false);
      }
    } else {
      setShowCandidateDetail?.(false);
      setIsOpenIntroApplication(true);
    }
  };

  const handleOpenEditCandidate = (): void => {
    setShowCandidateDetail?.(false);
    setOpenEditCandidate(true);
  };

  const saveNote = (): void => {
    if (!noteData) return;
    const textNote = dataOverall?.note
      ? `${dataOverall?.note}\n${noteData}`
      : noteData;
    handleEditNoteCandidate.mutate(textNote);
  };

  const loadDataAfterUpdate = () => {
    dataCandidateDetail.refetch();
    setShowCandidateDetail?.(true);
    reloadData?.();
  };

  useEffect(() => {
    dispatch(
      setLoading(deleteCandidate.isLoading || handleEditNoteCandidate.isLoading)
    );
  }, [deleteCandidate.isLoading, handleEditNoteCandidate.isLoading]);

  const navigatorApplicationDetail = (applicationId: number): void => {
    if (!applicationId) return;
    router.push(`${config.PATHNAME.MANAGER_APPLICATION}?id=${applicationId}`);
  };

  const columnsJob: ColumnsType<ISuitableJobForCandidate> = [
    {
      title: "Tên Job",
      dataIndex: "requestJobName",
      key: "requestJobName",
      width: "40%",
      render: (_, item: ISuitableJobForCandidate) => (
        <Link
          href={`${config.PATHNAME.JOB_DETAIL}?id=${item.requestJobId}`}
          target="_blank"
        >
          {item?.requestJobName || ""}
        </Link>
      ),
    },
    {
      title: "Mức Lương",
      dataIndex: "salary",
      key: "salary",
      width: "40%",
      render: (_, item: ISuitableJobForCandidate) => (
        <Link
          href={`${config.PATHNAME.JOB_DETAIL}?id=${item.requestJobId}`}
          target="_blank"
        >
          {item?.salary || ""}
        </Link>
      ),
    },
    {
      title: "Thưởng",
      dataIndex: "ratePartner",
      key: "ratePartner",
      width: "20%",
      render: (_, item: ISuitableJobForCandidate) => (
        <Link
          href={`${config.PATHNAME.JOB_DETAIL}?id=${item.requestJobId}`}
          target="_blank"
        >
          {item?.ratePartner || ""}
        </Link>
      ),
    },
  ];

  const options = [
    {label: "Phù hợp", value: true},
    {label: "Không phù hợp", value: false},
  ];

  const {data: evaluationData, refetch: refetchEvaluationData} = useQuery(
    ["getEvaluationOfCandidate", idPotentialCandidate, requestJobId],
    () => {
      return getEvaluationOfCandidate({
        candidateId: String(idPotentialCandidate || ""),
        requestJobId: Number(requestJobId),
      });
    },
    {
      enabled: !!(
        isShowModalEvaluation &&
        idPotentialCandidate &&
        requestJobId
      ),
    }
  );

  const initialValueEvaluationForm = useMemo(() => {
    const dataForm: EvaluationOfCandidateForm = {
      isSuitable: !!evaluationData?.isSuitable,
      note: evaluationData?.note || "",
    };

    return dataForm;
  }, [evaluationData]);

  useEffect(() => {
    if (
      isShowModalEvaluation &&
      isShowModalEvaluation &&
      idPotentialCandidate &&
      requestJobId
    ) {
      evaluationRef.current?.setValues(initialValueEvaluationForm);
    }

    return () => {
      evaluationRef.current?.resetForm();
    };
  }, [
    initialValueEvaluationForm,
    isShowModalEvaluation,
    idPotentialCandidate,
    requestJobId,
  ]);

  const {mutate: handleEvaluateCandidate} = useMutation(
    (data: EvaluationOfCandidateData) => {
      dispatch(setLoading(true));
      return saveEvaluateCandidate(data);
    },
    {
      onSettled() {
        dispatch(setLoading(false));
      },
      onSuccess() {
        notification.success({
          message: "Successful candidate evaluation.",
        });
        refetchEvaluationData();
        refetchListCandidate?.();
      },
      onError() {
        notification.error({
          message: "Failed candidate evaluation.",
        });
      },
    }
  );

  if (
    dataCandidateDetail?.error?.errorCode === 400 ||
    dataPotentialCandidateDetail?.error?.errorCode === 400
  ) {
    return (
      <div className="p-12">
        <AppAccessDenied />
      </div>
    );
  }

  return (
    <div className="candidate-information-ui">
      {isShowModalEvaluation && !isCTV && (
        <div className="candidate-information-ui__evaluate absolute top-1/2 bg-white p-2">
          <h2 className="candidate-information-ui__evaluate-title uppercase font-bold text-center">
            ĐÁNH GIÁ ỨNG VIÊN GỢI Ý
          </h2>
          <div className="flex justify-center mt-2">
            <Formik
              initialValues={initialValueEvaluationForm}
              onSubmit={() => {
                //
              }}
              innerRef={evaluationRef}
            >
              {({values}) => {
                return (
                  <Form className="w-full">
                    <div className="w-full flex flex-col items-center">
                      <AppRadio
                        name="isSuitable"
                        options={options}
                        value={!!values.isSuitable}
                      />
                      <AppTextarea
                        name="note"
                        value={values.note}
                        className="mt-2 mb-2"
                        maxLength={500}
                        rows={5}
                        required={!values.isSuitable}
                        placeholder="Đánh giá"
                        status={
                          !values.isSuitable && !values.note
                            ? "error"
                            : undefined
                        }
                      />
                      {!evaluationData?.candidateId && (
                        <Row justify="center" className="mt-4">
                          <Col>
                            <AppButton
                              typebutton="primary"
                              onClick={() => {
                                const valueEvaluation: EvaluationOfCandidateData =
                                  {
                                    ...values,
                                    candidateId: String(idPotentialCandidate),
                                    requestJobId: Number(requestJobId),
                                    note: values.note.trim(),
                                  };
                                if (
                                  !valueEvaluation.isSuitable &&
                                  !valueEvaluation.note
                                ) {
                                  notification.error({
                                    message: "Please fill note file",
                                  });
                                } else {
                                  handleEvaluateCandidate(valueEvaluation);
                                }
                              }}
                            >
                              Gửi đánh giá
                            </AppButton>
                          </Col>
                        </Row>
                      )}
                    </div>
                  </Form>
                );
              }}
            </Formik>
          </div>
        </div>
      )}

      {dataCandidateDetail.isLoading ||
      dataPotentialCandidateDetail?.isLoading ? (
        <Spin size="large" />
      ) : (
        <div className="w-full">
          <div
            className={`flex candidate-information-ui_padding ${
              tab && tab === "potentialCandidate" ? "potential-candidate" : ""
            }`}
          >
            <div className="candidate-information-ui_first">
              <div
                // className={`${
                //   tab !== "potentialCandidate" ? "h-[70vh]" : "h-[80vh]"
                // } overflow-y-auto overflow-x-hidden pr-1`}
                className={classNames([
                  "overflow-y-auto overflow-x-hidden pr-1 candidate-information-ui_first-wrapper",
                  {
                    "h-[80vh]": tab === "potentialCandidate",
                  },
                  {
                    "h-[70vh]": tab !== "potentialCandidate",
                  },
                ])}
              >
                <div className="candidate-information-ui_detail">
                  <div className="candidate-information-ui_detail-overview flex">
                    <div className="candidate-information-ui_detail-avatar">
                      {dataOverall?.fileAvatarPath ? (
                        <Avatar
                          size={64}
                          src={
                            config.NETWORK_CONFIG.API_BASE_URL +
                            dataOverall.fileAvatarPath
                          }
                        />
                      ) : (
                        <Avatar
                          size={64}
                          className="candidate-information-ui_avatar"
                        >
                          {getAbbreviatedName(dataOverall?.name)}
                        </Avatar>
                      )}
                    </div>
                    <div className="candidate-information-ui_detail-summary flex flex-col px-6">
                      <p className="candidate-information-ui_detail-name text-[24px]">
                        {dataOverall?.name || "N/A"}
                      </p>

                      <p className="candidate-information-ui_detail-position text-[14px]">
                        {dataOverall?.positionExpected || "N/A"}
                      </p>
                    </div>
                    {/* <div className="candidate-information-ui_detail-status text-[14px] flex items-center">
                      <div
                        className="dot-status mr-1"
                        style={{
                          backgroundColor: getStatusCandidate(
                            dataOverall?.status
                          ).color,
                        }}
                      />
                      {getStatusCandidate(dataOverall?.status).label}
                    </div> */}
                  </div>
                  <div className="candidate-information-ui_detail-overview-information mt-4 text-[14px]">
                    <div className="flex">
                      <div className="candidate-information-ui_detail-overview-information-first mr-8">
                        <div className="mt-1 flex">
                          <div>
                            <Icon size={16} icon="mail-line" />
                          </div>
                          <div className="ml-4 w-full candidate-information-ui_detail-overview-mail">
                            {dataOverall?.email || "N/A"}
                          </div>
                        </div>
                        <div className="mt-1 flex">
                          <div>
                            <Icon size={16} icon="phone-line" />
                          </div>
                          <div className="ml-4 w-full">
                            {dataOverall?.phoneNumber || "N/A"}
                          </div>
                        </div>

                        <div className="mt-1 flex">
                          <div>
                            <Icon size={16} icon="global-line" />
                          </div>
                          <div className="ml-4">{renderLanguage}</div>
                        </div>
                        <div className="mt-1 flex">
                          <div>
                            <Icon size={16} icon="team-line" />
                          </div>
                          <div className="ml-4">
                            {dataOverall?.managerName || "N/A"}
                          </div>
                        </div>
                        <div className="mt-1 flex">
                          <div>
                            <Icon size={16} icon="ic_outline-source" />
                          </div>
                          <div className="ml-4">
                            {dataOverall?.source || "N/A"}
                          </div>
                        </div>
                      </div>
                      <div className="candidate-information-ui_detail-overview-information-second">
                        <div className="flex items-center">
                          <div className="mr-4">
                            <Icon size={16} icon="map-pin-line-pin-line" />
                          </div>
                          {dataOverall?.workLocationNames ||
                          dataOverall?.countryName ? (
                            <div className="mt-1">
                              {`${dataOverall?.workLocationNames || ""} ${
                                dataOverall?.countryName || ""
                              }`}
                            </div>
                          ) : (
                            <div className="mt-1">N/A</div>
                          )}
                        </div>

                        <div className="mt-1">
                          {dataOverall?.linkedinURL && (
                            <a
                              className="mr-2"
                              href={dataOverall?.linkedinURL}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <Icon size={16} icon="linkedIn" />
                            </a>
                          )}
                          {dataOverall?.facebookURL && (
                            <a
                              className="mr-2"
                              href={dataOverall?.facebookURL}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <Icon size={16} icon="facebook" />
                            </a>
                          )}
                          {/* Đợi BE làm rồi mở lại comment */}
                          {/* {dataCandiDate?.facebookURL &&
                        dataCandiDate?.facebookURL?.includes(
                          "https://www.facebook.com/profile"
                        ) ? (
                          <a
                            className="mr-2"
                            href={dataCandiDate?.facebookURL}
                            target="_blank"
                            rel="noreferrer"
                          >
                            <Icon size={16} icon="facebook" />
                          </a>
                        ) : (
                          <a
                            className="mr-2"
                            href={dataCandiDate?.facebookURL || ""}
                            target="_blank"
                            rel="noreferrer"
                          >
                            <Icon size={16} icon="global-line" />
                          </a>
                        )} */}
                          {dataOverall?.skypeURL && (
                            <a
                              className="mr-2"
                              href={dataOverall?.skypeURL}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <Icon size={16} icon="global-line" />
                            </a>
                          )}
                          {dataOverall?.githubURL && (
                            <a
                              className="mr-2"
                              href={dataOverall?.githubURL}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <Icon size={16} icon="git" />
                            </a>
                          )}
                        </div>
                        <div className="flex mt-1 items-center">
                          <div className="ml-1 mr-5">&#x24;</div>
                          <div>
                            {formatMoney(
                              moneyToNumber(
                                String(dataOverall?.currentSalary || "")
                              ),
                              dataOverall?.currencyTypeId
                            )}
                            /
                            {dataOverall?.currentSalaryType === 0
                              ? "Net"
                              : "Gross"}
                          </div>
                        </div>
                        <div className="flex mt-1 items-center">
                          <div className="mr-4">
                            <Icon size={16} icon="funds-line" />
                          </div>
                          <div>
                            {formatMoney(
                              moneyToNumber(
                                String(dataOverall?.salaryExpected || "")
                              ),
                              dataOverall?.currencyTypeId
                            )}
                            /
                            {dataOverall?.salaryExpectedType === 0
                              ? "Net"
                              : "Gross"}
                          </div>
                        </div>
                        <div className="flex items-center mt-1">
                          <div className="mr-4">
                            <Icon size={16} icon="calendar-check-line" />
                          </div>
                          <div>
                            {`Onboard: ${
                              dataOverall?.onboardDate
                                ? moment(dataOverall?.onboardDate).format(
                                    DATE_FORMAT
                                  )
                                : "N/A"
                            }`}
                          </div>
                        </div>
                      </div>
                    </div>
                    {/* không hiển thị ở page request job */}
                    {dataCandidate?.recommendJobs?.length &&
                    !router.pathname.includes(config.PATHNAME.REQUEST_JOB) ? (
                      <div className="mt-2 candidate-information-ui_detail_job">
                        <span className="font-semibold text16 mb-1 block">
                          Job phù hợp
                        </span>
                        <AppTable
                          className="job-table"
                          dataSource={dataCandidate?.recommendJobs?.map(
                            (
                              item: ISuitableJobForCandidate,
                              index: number
                            ) => ({
                              ...item,
                              key: index,
                            })
                          )}
                          columns={columnsJob}
                          loading={false}
                          scroll={{y: "200px"}}
                        />
                      </div>
                    ) : null}
                    <div className="mt-2">
                      <span className="font-semibold text16">Kĩ năng</span>
                      <div className="flex flex-wrap mt-2">
                        {dataOverall?.candidateSkills?.map((skill, index) => (
                          <div
                            key={index}
                            className="mr-4 px-2 py-0 text-[12px] skill"
                          >
                            {skill}
                          </div>
                        )) || ""}
                      </div>
                    </div>
                    {/* <div className="mt-2">
                      <span className="font-semibold text16 mt-1">Tag</span>
                      <div className="flex flex-wrap mt-2">
                        {dataOverall?.tags?.map((skill, index) => (
                          <div
                            key={index}
                            className="mr-4 px-2 py-0 text-[12px] skill"
                          >
                            {skill}
                          </div>
                        )) || ""}
                      </div>
                    </div> */}
                    <TableHistoryApplyReco
                      email={dataOverall?.email}
                      phoneNumber={dataOverall?.phoneNumber}
                    />
                  </div>
                  <hr />
                  <div className="candidate-information-ui_experience">
                    <span className="font-semibold text16">Kinh Nghiệm</span>
                    {dataOverall?.experienceYear && (
                      <span className="text-base">
                        ( {dataOverall?.experienceYear} )
                      </span>
                    )}
                    <div>
                      {dataOverall?.workHistories?.map(
                        (
                          {companyName, position, startDate, endDate},
                          index
                        ) => {
                          return (
                            <div key={index} className="flex text-[14px]">
                              <div>
                                <Icon size={16} icon="briefcase-2-line" />
                              </div>
                              <div className="ml-4">
                                {`${position || ""} ${
                                  companyName ? " tại " + companyName : ""
                                }`}
                                {!(position || companyName)
                                  ? ""
                                  : `${startDate ? " từ " + startDate : ""} ${
                                      endDate ? " đến " + endDate : ""
                                    }`}
                              </div>
                            </div>
                          );
                        }
                      ) || ""}
                    </div>
                  </div>
                  <hr className="my-8" />
                  <div>
                    <span className="font-semibold text16">Học vấn</span>
                    <div className="mt-2">
                      {dataOverall?.educations?.map(
                        (item: IEducations, index: number) => (
                          <div className="flex items-center" key={index}>
                            <div className="mr-3">
                              <Icon size={16} icon="mdi_university-outline" />
                            </div>
                            <div>
                              {`${item?.fieldOfStudy || ""} ${
                                item?.schoolName
                                  ? " tại " + item.schoolName + " "
                                  : ""
                              } `}
                              {!(
                                item?.fieldOfStudy?.length ||
                                item?.schoolName?.length
                              )
                                ? ""
                                : `${
                                    item?.startDate
                                      ? " từ " + String(item?.startDate)
                                      : ""
                                  } ${
                                    item?.endDate
                                      ? "đến " + String(item?.endDate)
                                      : ""
                                  }`}
                            </div>
                          </div>
                        )
                      ) || ""}
                    </div>
                  </div>
                </div>
                <div className="candidate-information-ui_history">
                  <p className="font-semibold text16">Lịch sử ứng tuyển</p>
                  {dataOverall?.applicationHistory?.map(
                    ({
                      positionName,
                      customerName,
                      createdDate,
                      workingLocation,
                      stage,
                      status,
                      applicationId,
                    }) => {
                      return (
                        <div
                          key={createdDate}
                          className="flex text14 justify-between mt-2"
                        >
                          <div
                            className="w-3/5 cursor-pointer"
                            onClick={(): void =>
                              navigatorApplicationDetail(applicationId)
                            }
                          >
                            {positionName || ""} tại {customerName || ""}
                          </div>
                          <div className="w-1/5">
                            {createdDate
                              ? moment(createdDate).format(DATE_FORMAT)
                              : ""}
                          </div>

                          <div className="w-2/5 text12 flex justify-center">
                            <div
                              className="h-fit status-recruitment"
                              style={{
                                backgroundColor: findApplicationStatus(
                                  String(status || ""),
                                  true
                                ).color,
                              }}
                            >
                              {`${stage} ${status || ""}`}
                            </div>
                          </div>
                        </div>
                      );
                    }
                  ) || ""}
                </div>

                <div className="candidate-information-ui_note">
                  <span className="font-semibold text16">Ghi chú</span>
                  {dataOverall?.note && (
                    <div className="mt-2 whitespace-pre-line max-h-[160px] overflow-auto p-2 border-dashed border-[1.5px]">
                      {dataOverall?.note || ""}
                    </div>
                  )}
                  {tab !== "potentialCandidate" && (
                    <div className="flex mt-2">
                      <div className="w-5/6 mr-4">
                        <TextArea
                          value={noteData}
                          onChange={(e): void => {
                            setNoteData(e?.target?.value);
                          }}
                        />
                      </div>
                      <div className="w-1/6">
                        <div
                          className="candidate-information-ui_btn-save cursor-pointer"
                          onClick={saveNote}
                          role="button"
                          tabIndex={0}
                        >
                          Lưu
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="candidate-information-ui_second flex flex-col">
              <ViewCvCandidate
                docs={{
                  fileName: dataOverall?.fileCVName,
                  filePathBase64: dataOverall?.fileCVPath,
                }}
                modeViewCandidate={IModeViewCandidate.view}
                createdDate={dataOverall?.createdDate || ""}
                tab={tab as any}
                potential={{
                  urlPotential: dataPotentialCandidate?.file_path,
                  urlDownloadPotential: dataPotentialCandidate?.file_path,
                }}
                isShowFormatFile
                idCandidate={
                  tab === "potentialCandidate"
                    ? idPotentialCandidate
                    : idCandidate
                }
              />
            </div>
          </div>
          {tab !== "potentialCandidate" && (
            <div
              className={`candidate-information-ui__footer ${
                idCandidate ? "shadow-footer" : ""
              }`}
            >
              <div
                className="candidate-information-ui__footer-delete mx-4 underline underline-offset-1"
                onClick={(): void => setShowModalDelete(true)}
                role="button"
                tabIndex={0}
              >
                Xóa ứng viên
              </div>
              <div className="mx-4 candidate-information-ui__footer-btn">
                <AppButton
                  typebutton="secondary"
                  onClick={handleOpenEditCandidate}
                >
                  Chỉnh sửa thông tin
                </AppButton>
              </div>
              <div className="mx-4 candidate-information-ui__footer-btn">
                <AppButton typebutton="primary" onClick={introJob}>
                  Giới thiệu
                </AppButton>
              </div>
            </div>
          )}
        </div>
      )}
      <AppModalConfirm
        open={showModalDelete}
        title="Xác nhận xóa ứng viên"
        onCancel={handleVisibleModalDelete}
        onOk={handleDeleteCandidate}
      />
      <ModalIntroApplications
        isOpen={isOpenIntroApplication}
        candidateId={Number(id)}
        handleCancel={(): void => {
          setIsOpenIntroApplication(false);
          setShowCandidateDetail?.(true);
        }}
      />
      <AppModalConfirm
        open={isOpenModalCommission}
        title="Xác nhận ủy quyền"
        content={messageConfirmCommission}
        onCancel={(): void => {
          setIsOpenModalCommission(false);
          introNow?.(false);
        }}
        onOk={(): void => {
          setIsOpenModalCommission(false);
          introNow?.(true);
        }}
      />
      <EditCandidate
        candidateId={Number(id)}
        isModalVisible={openEditCandidate}
        handleCancel={(): void => {
          setOpenEditCandidate(false);
          setShowCandidateDetail?.(true);
        }}
        updateListCandidate={loadDataAfterUpdate}
        dataCandidate={dataCandidate as any}
      />
    </div>
  );
}
