import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import AppModal from "@app/components/AppModal";
import AppAccessDenied from "@app/components/AppAccessDenied";
import ApiCustomerPayment, {
  ICustomerPayment,
  IResFilter,
  paymentHistory,
} from "@app/api/ApiCustomerPayment";
import {UseQueryResult, useMutation, useQuery} from "react-query";
import AppLoading from "@app/components/AppLoading";
import {Col, Row, notification} from "antd";
import {formatMoney, getPaymentStatus} from "@app/utils/constants/function";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppButton from "@app/components/AppButton";
import AppModalConfirm from "@app/components/AppModalConfirm";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import ModalUpdateCustomerPayment from "../ModalUpdateCustomerPayment";

interface ModalPaymentDetailProp {
  isShow: boolean;
  handleClose: () => void;
  paymentId?: number;
  getCustomerPayment: UseQueryResult<IResFilter, unknown>;
  setIsShowDetail: (isShow: boolean) => void;
}

export default function ModalPaymentDetail(
  props: ModalPaymentDetailProp
): JSX.Element {
  const {isShow, handleClose, paymentId, getCustomerPayment, setIsShowDetail} =
    props;
  const timeOut = useRef<any>();
  const [isShowModalConfirm, setIsShowModalConfirm] = useState(false);
  const [isShowModalUpdate, setIsShowModalUpdate] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const getCustomerPaymentDetail = useQuery(
    ["getCustomerPaymentDetail", paymentId],
    () => {
      return ApiCustomerPayment.getCustomerPaymentDetail(paymentId);
    },
    {
      enabled: !!paymentId,
      onError: (error: any): void => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            handleClose();
          }, 4000);
        }
      },
    }
  );

  const stopCustomerPayment = useMutation(
    (id: number) => {
      return ApiCustomerPayment.stopCustomerPayment(id);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Dừng thanh toán thành công",
        });
        getCustomerPaymentDetail.refetch();
        getCustomerPayment.refetch();
        setIsShowModalConfirm(false);
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const handleStopPayment = (): void => {
    if (paymentId) {
      dispatch(setLoading(true));
      stopCustomerPayment.mutate(paymentId);
    }
  };

  const onCloseModalUpdate = (): void => {
    setIsShowDetail(true);
    setIsShowModalUpdate(false);
  };

  const updatePayment = (): void => {
    setIsShowDetail(false);
    setIsShowModalUpdate(true);
  };

  const renderBodyModal = (): JSX.Element => {
    const detail = getCustomerPaymentDetail.data as ICustomerPayment;
    const status = getPaymentStatus(detail?.status);

    const amountReceived =
      (detail?.amount || 0) - (detail?.customerRefund || 0);

    const amountReceivedVAT = amountReceived / (1 + (detail?.vat || 0) / 100);

    const requestInfo = [
      {
        title: "Thông tin chung",
        content: [
          {label: "Khách hàng", value: detail?.customerName},
          {label: "Người quản lý", value: detail?.managerAMName},
          {label: "Tên ứng viên", value: detail?.candidateName},
        ],
      },
      {
        title: "Thông tin thanh toán",
        content: [
          {label: "Ngày onboard", value: detail?.onboardDate},
          {label: "Số tháng thử việc", value: detail?.monthTrailWork},
          {
            label: "Lương offer",
            value: formatMoney(
              detail?.salaryOffered,
              detail?.currencyOfferedType
            ),
          },
          {
            label: "Ngày hết hạn bảo hành",
            value: detail?.onboardDate
              ? moment(detail?.onboardDate, DATE_FORMAT)
                  .add(detail?.monthTrailWork || 0, "months")
                  .format(DATE_FORMAT)
              : "",
          },
          {
            label: "Rate khách hàng",
            value:
              detail?.paymentRateType === "0"
                ? `${detail?.paymentRate} lần`
                : formatMoney(detail?.paymentRate, detail?.paymentRateUnit),
          },
          {
            label: "Số tiền hoàn trả khách hàng",
            value: detail?.customerRefund || 0,
          },
        ],
      },
    ];

    if (getCustomerPaymentDetail.isLoading) {
      return (
        <div className=" h-[80vh]">
          <AppLoading />
        </div>
      );
    }

    if (getCustomerPaymentDetail?.error?.errorCode === 400) {
      return (
        <div className="p-12">
          <AppAccessDenied />
        </div>
      );
    }

    return (
      <div className="text-color-primary">
        <Row>
          <Col className="pr-2" span={12}>
            <div className="container-item-detail-modal">
              <div className="p-5 h-[70vh] overflow-auto text16">
                <Row className="justify-between">
                  <span className="flex-1 font-bold text20">
                    {detail?.requestName}
                  </span>
                  <Row className="items-center">
                    <div
                      className="dot-status mr-1"
                      style={{backgroundColor: status.color}}
                    />
                    <span>{status.label}</span>
                  </Row>
                </Row>
                {requestInfo.map((item, index) => (
                  <div key={index} className="mt-5">
                    <span className="font-semibold">{item.title}</span>
                    <Row className="ml-[5%]">
                      {item.content.map((i, ind) => (
                        <Col key={ind} span={12} className="mt-2 pl-1 pr-1">
                          <div className="title-info text12">{i.label}</div>
                          <div className="mt-1 text14">{i.value as string}</div>
                        </Col>
                      ))}
                    </Row>
                  </div>
                ))}
                <div className="line" />
                <div className="ml-[5%]">
                  <Row className="justify-between font-medium mt-2">
                    <span className="text16">Doanh thu</span>
                    <span className="text14">
                      {formatMoney(detail?.salesTransacted)}
                    </span>
                  </Row>
                  <Row className="justify-between mt-2 font-light">
                    <span className="text16">VAT</span>
                    <span className="text14">{`${detail?.vat || 0} %`}</span>
                  </Row>
                  <div className="line-money" />
                  <Row className="justify-between mt-2 font-medium">
                    <span className="text16 ">Thành tiền</span>
                    <span className="text14">
                      {formatMoney(detail?.amount)}
                    </span>
                  </Row>
                  {detail?.customerRefund && (
                    <div>
                      <Row className="justify-between mt-2 font-light color-red">
                        <span className="text16">Hoàn trả (bao gồm VAT)</span>
                        <span className="text14">
                          {formatMoney(detail?.customerRefund || 0)}
                        </span>
                      </Row>
                      <div className="line-money" />
                      <Row className="justify-between mt-2 font-medium">
                        <span className="text16 flex-1">
                          Số tiền thực nhận (bao gồm VAT)
                        </span>
                        <span className="text14">
                          {formatMoney(amountReceived)}
                        </span>
                      </Row>
                      <Row className="justify-between mt-2">
                        <span className="text16 font-medium">
                          Doanh thu thực nhận
                        </span>
                        <span className="text14">
                          {formatMoney(amountReceivedVAT)}
                        </span>
                      </Row>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Col>
          <Col className="pl-2" span={12}>
            <div className="container-item-detail-modal h-[70vh] max-h-[70vh] flex flex-col">
              <span className="text16 font-bold mx-3 mt-4">
                Danh sách khách hàng
              </span>
              <div className="flex-1 overflow-y-auto h-full mb-4 mr-1 text12">
                {detail?.listPaymentHistory &&
                  detail?.listPaymentHistory
                    ?.sort((a, b) => a.paymentId - b.paymentId)
                    ?.map((payment: paymentHistory, index) => (
                      <Row
                        className="payment-card mx-3 items-center justify-between"
                        key={index}
                      >
                        <div className="flex-1">
                          <div className="font-bold text16">{`Thanh toán lần ${
                            index + 1
                          }`}</div>
                          <div className="mt-1">{`Ngày thanh toán: ${payment.paymentDate}`}</div>
                          <div className="mt-1">{`Hình thức thanh toán: ${payment.paymentMethod}`}</div>
                          <div className="mt-1">{`Người tạo thanh toán: ${
                            payment.creator || ""
                          }`}</div>
                          <div className="mt-1">Note:</div>
                          <div>{payment.note || ""}</div>
                        </div>
                        <div>{formatMoney(payment.amount)}</div>
                      </Row>
                    ))}
              </div>
              <div className="amount">
                <div>
                  <p className="text15 font-medium">{`Đã thanh toán : ${formatMoney(
                    detail?.amountPaid
                  )}`}</p>
                  <p className="text15 font-medium mt-1">{`Số tiền còn lại : ${formatMoney(
                    detail?.amountRemain
                  )}`}</p>
                </div>
              </div>
            </div>
          </Col>
        </Row>
        {(detail?.status === 0 || detail?.status === 1) && (
          <Row className="flex justify-center items-center mt-5">
            <AppButton
              classrow="w-52 btn-cancel mr-4"
              label="Dừng thanh toán"
              typebutton="primary"
              onClick={(): void => setIsShowModalConfirm(true)}
            />
            {/* <AppButton
              classrow="mx-4 w-52"
              label="Hoàn trả"
              typebutton="secondary"
            /> */}
            <AppButton
              classrow="w-52 btn-edit ml-4"
              label="Cập nhật thanh toán"
              typebutton="primary"
              onClick={updatePayment}
            />
          </Row>
        )}
      </div>
    );
  };

  return (
    <div>
      <AppModal
        className="modal-payment-detail"
        open={isShow}
        footer={null}
        onCancel={handleClose}
        width="70%"
        title="Chi tiết thanh toán"
      >
        {renderBodyModal()}
      </AppModal>
      <AppModalConfirm
        open={isShowModalConfirm}
        content="Thanh toán chưa hoàn thành, bạn có chắc chắn muốn dừng?"
        title="Xác nhận dừng thanh toán"
        onCancel={(): void => setIsShowModalConfirm(false)}
        onOk={handleStopPayment}
      />
      <ModalUpdateCustomerPayment
        open={isShowModalUpdate}
        closeModal={onCloseModalUpdate}
        getCustomerPaymentDetail={getCustomerPaymentDetail}
        getCustomerPayment={getCustomerPayment}
      />
    </div>
  );
}
