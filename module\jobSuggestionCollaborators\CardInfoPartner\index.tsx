import "./index.scss";
import React, {useEffect, useState} from "react";
import {Card, CardProps} from "antd";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import AppCheckBox from "@app/components/AppCheckbox";
import {IInfoPartner} from "@app/api/ApiRequestJob";
import moment from "moment";

interface CardViewPartnerProps extends CardProps {
  partnerInfo: IInfoPartner;
  onCheckBox: (id: number, checked: boolean) => void;
  checked?: boolean;
}

export function CardInfoPartner(props: CardViewPartnerProps): JSX.Element {
  const {partnerInfo, checked, onCheckBox} = props;
  const [isTruncatedTags, setIsTruncatedTags] = useState<boolean>(false);

  const onChangeCheckBoxSuggestion = (e: CheckboxChangeEvent): void => {
    if (e.target.checked) {
      onCheckBox(partnerInfo.partnerId, true);
    } else {
      onCheckBox(partnerInfo.partnerId, false);
    }
  };

  const calculatorTimeSentMail = (date: string): string => {
    const now = moment();
    const pastDate = moment(date);
    const hoursDiff = now.diff(pastDate, "hours");
    if (!date) return "";
    if (hoursDiff >= 24) {
      const days = Math.floor(hoursDiff / 24);
      return `Lần cuối gửi: ${days} ngày trước`;
    }
    const hours = hoursDiff;
    return `Lần cuối gửi: ${hours} giờ trước`;
  };

  useEffect(() => {
    const blockTags = document.getElementById(`tag-${partnerInfo.partnerId}`);
    if (blockTags && Number(blockTags?.offsetHeight) > 46) {
      setIsTruncatedTags(true);
      blockTags.setAttribute("style", "max-height:45px");
    }
  }, [partnerInfo]);

  return (
    <div className="container-card-partner">
      <Card
        className="container-card__ui radius-card-view hover-pointer card-padding"
        {...props}
      >
        <AppCheckBox
          name="choosePartner"
          onChange={onChangeCheckBoxSuggestion}
          checked={checked}
          className="flex-row-reverse w-full items-center justify-between"
        >
          <div className="line-clamp-2 font-bold text-base">
            {partnerInfo.partnerName}
          </div>
          <div className="line-clamp-1 text-xs font-normal my-1">
            {partnerInfo.partnerEmail}
          </div>
          <div className="line-clamp-1 text-xs font-normal text-[#9D9D9D80]">
            {calculatorTimeSentMail(partnerInfo.lastSent)}
          </div>
          <div
            className="flex flex-wrap mt-2 relative overflow-hidden"
            id={`tag-${partnerInfo.partnerId}`}
          >
            {isTruncatedTags && (
              <div className="absolute top-6 right-0 font-bold">....</div>
            )}
            {partnerInfo?.tags?.map((skill, index) => (
              <div key={index} className="skill">
                {skill}
              </div>
            )) || ""}
          </div>
        </AppCheckBox>
      </Card>
    </div>
  );
}
