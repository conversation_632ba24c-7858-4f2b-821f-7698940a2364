importScripts('https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.6.1/firebase-messaging-compat.js');

// self.addEventListener('install', (event) => {

//   console.log('install', 'event');
//   const urlParams = new URLSearchParams(location.search);
//   self.firebaseConfig = Object.fromEntries(urlParams);
//   console.log(self.firebaseConfig, 'self.firebaseConfig');
  
//   firebase.initializeApp(self.firebaseConfig)
//   const messaging = firebase.messaging();


//   console.log(messaging, 'mêmme');
  
//   messaging.onBackgroundMessage(function(payload) {
    
//     console.log('onbg');
    
//     if (!(self.Notification && self.Notification.permission === 'granted')) {
//         return;
//     }

//     const notificationTitle = payload?.data?.title || '';

//     const notificationOptions = {
//         body: payload.data.body,
//         icon: payload?.data?.icon || '/img/logo-icon.svg',
//         data: payload?.data
//     };

//     console.log('here');
    

//     return self.registration.showNotification(notificationTitle, notificationOptions) 
//   });
// });

self.addEventListener('fetch', (e) => {
  // const urlParams = new URLSearchParams(location.search);
  // self.firebaseConfig = Object.fromEntries(urlParams);
  // firebase.initializeApp(self.firebaseConfig)
  // const messaging = firebase.messaging();

  // messaging.onBackgroundMessage(function(payload) {
    
  //   if (!(self.Notification && self.Notification.permission === 'granted')) {
  //       return;
  //   }

  //   const notificationTitle = payload?.data?.title || '';

  //   const notificationOptions = {
  //       body: payload.data.body,
  //       icon: payload?.data?.icon || '/img/logo-icon.svg',
  //       data: payload?.data
  //   };

  //   return self.registration.showNotification(notificationTitle, notificationOptions) 
  // });
  
  try {
    const urlParams = new URLSearchParams(location.search);
    self.firebaseConfig = Object.fromEntries(urlParams);
  } catch (err) {
    console.error('Failed to add event listener', err);
  }
  
})

const defaultConfig = {
  apiKey: true,
  projectId: true,
  messagingSenderId: true,
  appId: true,
};

firebase.initializeApp(self.firebaseConfig || defaultConfig);

let messaging;
try {
  messaging = firebase.messaging.isSupported() ? firebase.messaging() : null
} catch (err) {
  console.error('Failed to initialize Firebase Messaging', err);
}

if(messaging) {
  messaging.onBackgroundMessage((payload) => {
    if (!(self.Notification && self.Notification.permission === 'granted')) {
      return;
    }
    const notificationTitle = payload?.data?.title || '';

    const notificationOptions = {
        body: payload.data.body,
        icon: payload?.data?.icon || '/img/logo-icon.svg',
        data: payload?.data
    };

    self.registration.showNotification(notificationTitle, notificationOptions)
  })
}

self.addEventListener('activate', (e) => {
  console.log('service worker activated')
  e.waitUntil(self.clients.claim())
});

self.addEventListener('push', (event) => {
  console.log("push called");
});

self.addEventListener('notificationclick', (event) => {
  console.log("notificationclick called");
  const link = event?.notification?.data?.link || ''
  event.notification.close();
  if(link) {
      event.waitUntil(
          clients.openWindow(link)
      );
  }
});

