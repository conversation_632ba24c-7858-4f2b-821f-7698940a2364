import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import {Col, Row, notification} from "antd";
import {Formik, FormikProps} from "formik";
import {TextInput} from "@app/components/TextInput";
import {SelectInput} from "@app/components/SelectInput";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {Input} from "formik-antd";
import AppButton from "@app/components/AppButton";
import AppModal from "@app/components/AppModal";
import {UseQueryResult, useMutation, useQuery} from "react-query";
import ApiTeam, {
  IAccountReco,
  ICreateAccount,
  IEmployeeDetail,
  IGroup,
  IResListEmployee,
  IResTeamDetail,
  IRole,
} from "@app/api/ApiTeam";
import {IAccountRole, OptionSelect} from "@app/types";
import moment from "moment";
import {useDispatch, useSelector} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {
  getStatusEmployee,
  validateEmail,
  validatePhoneNumber,
} from "@app/utils/constants/function";
import {statusEmployee} from "@app/utils/constants/state";
import {selectUser} from "@app/redux/slices/UserSlice";
import {messageValidateEmail} from "@app/utils/constants/message";
import ApiCandidate from "@app/api/ApiCandidate";

const {TextArea} = Input;

interface IFormEmployee extends IAccountReco {
  listRoleSelected: OptionSelect[];
  titleSelected: OptionSelect;
  groupSelected: OptionSelect[];
  statusSelected?: OptionSelect;
}

interface ModalFormEmployeeProp {
  open: boolean;
  onClose: () => void;
  id?: number;
  getListEmployee?: UseQueryResult<IResListEmployee, unknown>;
  getRecoProfileDetail?: UseQueryResult<IEmployeeDetail, unknown>;
  getDetailTeam?: UseQueryResult<IResTeamDetail, any>;
}

export default function ModalFormEmployee(
  props: ModalFormEmployeeProp
): JSX.Element {
  const {
    open,
    onClose,
    id,
    getListEmployee,
    getRecoProfileDetail,
    getDetailTeam,
  } = props;
  const refForm = useRef<FormikProps<IFormEmployee>>(null);
  const dispatch = useDispatch();
  const {user} = useSelector(selectUser);
  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);
  const infoEmployee = useRef<IAccountReco>();
  const [isBlockEmployee, setIsBlockEmployee] = useState<boolean>(false);

  const isTypeCreate = !id || id === -1;

  const getAccountReco = useQuery(
    ["getAccountReco", id],
    () => {
      return ApiTeam.getAccountReco(id || -1);
    },
    {
      enabled: open && !isTypeCreate,
    }
  );

  const getUserTitles = useQuery(
    ["getUserTitles"],
    () => {
      return ApiTeam.getUserTitles();
    },
    {enabled: isAdmin}
  );

  const getRecoRoles = useQuery(
    ["getRecoRoles"],
    () => {
      return ApiTeam.getRecoRoles();
    },
    {enabled: isAdmin}
  );

  const getUserGroups = useQuery(
    ["getUserGroups"],
    () => {
      return ApiTeam.getUserGroups();
    },
    {enabled: isAdmin}
  );

  const createAccount = useMutation(
    (data: ICreateAccount) => {
      return ApiTeam.createAccount(data);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Tạo nhân viên thành công",
        });
        getListEmployee?.refetch();
        handleClose();
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const editAccount = useMutation(
    (data: IAccountReco) => {
      return ApiTeam.editAccount(data);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Cập nhật nhân viên thành công",
        });
        getListEmployee?.refetch();
        getRecoProfileDetail?.refetch();
        getDetailTeam?.refetch();
        handleClose();
        dispatch(setLoading(false));
        onCloseModalBlockEmployee();
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const listUserGroup = (groupSelected?: OptionSelect[]): IGroup[] => {
    return (
      getUserGroups?.data?.filter((item) =>
        groupSelected?.some((i) => i.key === String(item.userGroupId))
      ) || []
    );
  };

  const listRole = (listRoleSelected?: OptionSelect[]): IRole[] => {
    return (
      getRecoRoles?.data?.filter((item) =>
        listRoleSelected?.some((i) => i.key === item.roleId)
      ) || []
    );
  };

  const checkEmailExist = useMutation(
    (email: string) => {
      dispatch(setLoading(true));
      return ApiTeam.checkEmailExist(email);
    },
    {
      onSuccess: (data) => {
        if (data) {
          notification.error({
            message: "Thông báo",
            description: "Email đã tồn tại",
          });
          dispatch(setLoading(false));
        } else {
          const values = refForm.current?.values || ({} as IFormEmployee);

          const param: ICreateAccount = {
            dateOfBirth: values?.dateOfBirth
              ? moment(values.dateOfBirth).format(DATE_FORMAT)
              : "",
            email: values.email?.trim(),
            joinedDate: values?.joinedDate
              ? moment(values.joinedDate).format(DATE_FORMAT)
              : "",
            listUserGroup: listUserGroup(values?.groupSelected),
            phoneNumber: values.phoneNumber?.trim(),
            listRole: listRole(values.listRoleSelected),
            name: values.name?.trim(),
          };

          if (values.titleSelected.value) {
            param.title = values.titleSelected.value;
          }
          createAccount.mutate(param);
        }
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const accountReco = getAccountReco.data || ({} as IFormEmployee);

  const optionTitle: OptionSelect[] =
    getUserTitles?.data?.map((item) => ({
      value: item.titleId,
      label: item.name,
    })) || [];

  const optionRole: OptionSelect[] =
    getRecoRoles?.data?.map((item) => ({
      key: item.roleId,
      value: item.name,
      label: item.name,
    })) || [];

  const listRoleTypeId = (listRoleSelected: OptionSelect[]): string[] => {
    if (listRoleSelected.length < 1) {
      return [];
    }

    const arrayResult: string[] = [];

    const isCST = listRoleSelected.findIndex(
      (item) => item?.key === IAccountRole.CST
    );

    if (isCST !== -1) {
      arrayResult.push("CS");
    }

    const isAMG = listRoleSelected.findIndex(
      (item) => item?.key === IAccountRole.AMG
    );

    if (isAMG !== -1) {
      arrayResult.push("AM");
    }

    const isBD = listRoleSelected.findIndex(
      (item) => item?.key === IAccountRole.BD
    );

    if (isBD !== -1) {
      arrayResult.push("BD");
    }

    return arrayResult;
  };

  const handleClose = (): void => {
    refForm.current?.resetForm();
    onClose();
  };

  const checkExistCandidateManagerByEmployee = useMutation(
    () => {
      dispatch(setLoading(true));
      return ApiCandidate.getListCandidate({
        currentPage: 1,
        pageSize: 20,
        isAdvanceSearch: true,
        managers: [
          {
            id: String(getRecoProfileDetail?.data?.userId),
            label: String(getRecoProfileDetail?.data?.name),
          },
        ],
      });
    },
    {
      onSuccess: (data) => {
        if (data?.candidatesPaging?.length) {
          dispatch(setLoading(false));
          setIsBlockEmployee(true);
        } else if (infoEmployee?.current && !data?.candidatesPaging?.length) {
          handleBlockEmployee();
        }
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const save = (): any => {
    const values = refForm.current?.values || ({} as IFormEmployee);

    if (
      !(
        values.name?.trim() &&
        values.email?.trim() &&
        values.phoneNumber?.trim() &&
        values.listRoleSelected?.length > 0
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng điền đầy đủ thông tin nhân viên",
      });
      return;
    }

    if (!validateEmail(values.email)) {
      notification.error({
        message: "Thông báo",
        description: messageValidateEmail,
      });

      return;
    }

    if (!validatePhoneNumber(values.phoneNumber)) {
      notification.error({
        message: "Thông báo",
        description: "Số điện thoại không hợp lệ",
      });
      return;
    }

    if (isTypeCreate) {
      checkEmailExist.mutate(values.email);
    } else {
      const param: IAccountReco = {
        ...accountReco,
        dateOfBirth: values.dateOfBirth
          ? moment(values.dateOfBirth).format(DATE_FORMAT)
          : "",
        name: values.name?.trim(),
        phoneNumber: values.phoneNumber?.trim(),
        status: values?.statusSelected?.value || accountReco.status,
        title: values.titleSelected?.value,
        joinedDate: values.joinedDate
          ? moment(values.joinedDate).format(DATE_FORMAT)
          : "",
        listUserGroup: listUserGroup(values?.groupSelected),
        listRole: listRole(values.listRoleSelected),
        note: values.note?.trim(),
      };
      infoEmployee.current = param;
      if (param.status === "4") {
        checkExistCandidateManagerByEmployee.mutate();
      } else {
        editAccount.mutate(param);
      }
    }
  };

  const initValue: IFormEmployee = {
    ...accountReco,
    listRoleSelected:
      accountReco?.listRole?.map((item) => ({
        key: item.roleId,
        value: item.name,
        label: item.name,
      })) || [],
    titleSelected:
      optionTitle.find((item) => item.value === accountReco.title) ||
      ({} as OptionSelect),
    groupSelected:
      accountReco?.listUserGroup
        ?.filter((i) => i.userGroupId !== -1)
        ?.map((item) => ({
          key: String(item.userGroupId),
          value: item.name,
          label: item.name,
        })) || [],
    dateOfBirth: accountReco?.dateOfBirth
      ? moment(accountReco.dateOfBirth, DATE_FORMAT)
      : "",
    joinedDate: accountReco?.joinedDate
      ? moment(accountReco.joinedDate, DATE_FORMAT)
      : "",
    statusSelected: accountReco?.status
      ? getStatusEmployee(accountReco?.status)
      : ({} as OptionSelect),
  };

  useEffect(() => {
    if (open) {
      refForm.current?.setValues(initValue);
    }
  }, [JSON.stringify(initValue), open]);

  const handleBlockEmployee = (): void => {
    editAccount.mutate(infoEmployee?.current as IAccountReco);
  };

  const onCloseModalBlockEmployee = (): void => {
    setIsBlockEmployee(false);
  };

  const renderContent = (): any => {
    return (
      <div>
        <div className="mb-2 text-base">
          <span>
            Khi bạn khóa tài khoản của nhân viên này, toàn bộ ứng viên hiện đang
            được quản lý bởi nhân viên sẽ được chuyển vào dữ liệu chung của công
            ty.
          </span>
          <br />
          <br />
          <span>Bạn có muốn tiếp tục khóa tài khoản nhân viên này không?</span>
        </div>
        <Row className="mt-6 justify-center">
          <AppButton
            classrow="w-48 btn-cancel mr-4"
            label="Hủy"
            typebutton="primary"
            onClick={onCloseModalBlockEmployee}
          />
          <AppButton
            classrow="w-48 ml-4"
            label="Ok"
            typebutton="primary"
            onClick={handleBlockEmployee}
          />
        </Row>
      </div>
    );
  };

  return (
    <>
      <AppModal
        className="modal-form-employee"
        open={open}
        footer={null}
        onCancel={handleClose}
        width="70%"
        title={`${!isTypeCreate ? "Cập nhật" : "Tạo mới"} nhân viên`}
      >
        <div className="form-employee-container">
          <div className="content">
            <h1 className="text16 font-bold">Thông tin chung</h1>
            <Formik
              initialValues={initValue}
              innerRef={refForm}
              onSubmit={(): void => {
                //
              }}
            >
              {({values}): JSX.Element => {
                const optionGroup: OptionSelect[] =
                  getUserGroups?.data?.map((item) => ({
                    key: String(item.userGroupId),
                    value: item.name,
                    label: item.name,
                    disabled: !listRoleTypeId(values.listRoleSelected).includes(
                      item.roleTypeId
                    ),
                  })) || [];

                return (
                  <div>
                    <Row className="form-input">
                      <Col span={12} className="pr-2">
                        <TextInput
                          containerclassname="mt-3"
                          label="Họ và tên"
                          name="name"
                          value={values?.name}
                          maxLength={50}
                          required
                          status={values?.name ? undefined : "error"}
                        />
                        <TextInput
                          containerclassname="mt-3"
                          label="Email"
                          name="email"
                          value={values?.email}
                          free={!values?.email}
                          maxLength={50}
                          required
                          status={values?.email ? undefined : "error"}
                          disabled={!isTypeCreate}
                        />
                        <TextInput
                          containerclassname="mt-3"
                          label="Số điện thoại"
                          name="phoneNumber"
                          isphonenumber
                          value={values?.phoneNumber}
                          free={!values?.phoneNumber}
                          maxLength={50}
                          required
                          status={values?.phoneNumber ? undefined : "error"}
                        />
                        <SelectInput
                          containerclassname="mt-3"
                          name="listRoleSelected"
                          labelselect="Phân quyền"
                          data={optionRole}
                          value={values?.listRoleSelected}
                          required
                          mode="multiple"
                          allowClear
                          status={
                            values?.listRoleSelected?.length > 0
                              ? undefined
                              : "error"
                          }
                        />
                        {!isTypeCreate && (
                          <SelectInput
                            containerclassname="mt-3"
                            name="statusSelected"
                            labelselect="Trạng thái"
                            data={statusEmployee.slice(1)}
                            value={values?.statusSelected}
                            required
                            status={
                              values?.statusSelected?.value
                                ? undefined
                                : "error"
                            }
                          />
                        )}
                      </Col>
                      <Col span={12} className="pl-2">
                        <AppDatePicker
                          classNameContainer="mt-3"
                          name="dateOfBirth"
                          label="Ngày sinh"
                          format={DATE_FORMAT}
                          valueAppDatePicker={values?.dateOfBirth}
                          disabledDate={(current): boolean =>
                            current && current > moment().startOf("day")
                          }
                        />
                        <SelectInput
                          containerclassname="mt-3"
                          name="titleSelected"
                          labelselect="Chức danh"
                          data={optionTitle}
                          value={values?.titleSelected?.value}
                          allowClear
                        />
                        <AppDatePicker
                          classNameContainer="mt-3"
                          name="joinedDate"
                          label="Ngày vào công ty"
                          format={DATE_FORMAT}
                          valueAppDatePicker={values?.joinedDate}
                        />
                        <SelectInput
                          containerclassname="mt-3"
                          name="groupSelected"
                          labelselect="Nhóm nhân viên"
                          data={optionGroup}
                          value={values?.groupSelected}
                          mode="multiple"
                          disabled={values?.listRoleSelected?.length < 1}
                          allowClear
                        />
                      </Col>
                    </Row>
                    {!isTypeCreate && (
                      <TextArea
                        name="note"
                        maxLength={1000}
                        className="input-note"
                        placeholder="Ghi chú"
                        value={values.note}
                      />
                    )}
                  </div>
                );
              }}
            </Formik>
          </div>
          <Row className="mt-6 justify-center">
            <AppButton
              classrow="w-48 btn-cancel mr-4"
              label="Hủy"
              typebutton="primary"
              onClick={handleClose}
            />
            <AppButton
              classrow="w-48 btn-edit ml-4"
              label="Lưu"
              typebutton="primary"
              onClick={save}
            />
          </Row>
        </div>
      </AppModal>
      <AppModal
        className="modal-warning-block-employee"
        open={isBlockEmployee}
        onCancel={onCloseModalBlockEmployee}
        centered
        footer={null}
        title="Lưu ý"
      >
        {renderContent()}
      </AppModal>
    </>
  );
}
