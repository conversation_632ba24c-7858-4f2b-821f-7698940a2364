import {MessageChatbot} from "@app/types";
import {PayloadAction, createSlice} from "@reduxjs/toolkit";

interface ChatbotState {
  isSendingMessage: boolean;
  messages: Array<MessageChatbot>;
}

const initialState: ChatbotState = {
  isSendingMessage: false,
  messages: [],
};

export const chatbotSlice = createSlice({
  name: "chatbot",
  initialState: initialState,
  reducers: {
    setStateSendingMessage: (state, actions) => {
      state.isSendingMessage = actions.payload;
    },
    setInitMessagesChatbot: (
      state,
      actions: PayloadAction<Array<MessageChatbot>>
    ) => {
      state.messages = actions.payload;
    },
    setNewMessagesChatbot: (state, actions: PayloadAction<MessageChatbot>) => {
      if (actions.payload.isFetchSuccess) {
        const messagesClone = [...state.messages];
        messagesClone[messagesClone.length - 1].content +=
          actions.payload.content;
        state.messages = [...messagesClone];
      } else {
        state.messages = [...state.messages, actions.payload];
      }
    },
  },
});

export const {
  setStateSendingMessage,
  setInitMessagesChatbot,
  setNewMessagesChatbot,
} = chatbotSlice.actions;

export default chatbotSlice.reducer;
