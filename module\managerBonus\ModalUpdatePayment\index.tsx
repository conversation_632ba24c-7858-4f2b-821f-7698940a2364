import AppModal from "@app/components/AppModal";
import "./index.scss";
import React, {useEffect, useRef} from "react";
import {Formik, FormikProps} from "formik";
import moment, {Moment} from "moment";
import {TextInput} from "@app/components/TextInput";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {Input} from "formik-antd";
import {Col, ModalProps, Row, notification} from "antd";
import AppButton from "@app/components/AppButton";
import {useMutation} from "react-query";
import ApiPaymentBonus, {DataUpdatePayment} from "@app/api/ApiPaymentBonus";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";

interface DataFormPayment {
  amount: number | string;
  paymentDate: string | Moment;
  paymentType: string;
  note: string;
  paymentBonusId: number;
}

interface Props extends ModalProps {
  title: string;
  onCancel: () => void;
  dataPayment: DataFormPayment;
  open: boolean;
  refreshList: () => void;
}

const {TextArea} = Input;

function ModalUpdatePayment(props: Props): JSX.Element {
  const {title, onCancel, dataPayment, open, refreshList} = props;

  const formikRef = useRef<FormikProps<DataFormPayment>>(null);
  const dispatch = useDispatch();

  useEffect(() => {
    formikRef.current?.setValues(dataPayment);
  }, [dataPayment]);

  const updatePaymentBonus = useMutation(
    (data: DataUpdatePayment) => {
      dispatch(setLoading(true));
      return ApiPaymentBonus.updatePaymentBonus(data);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Lưu thông tin thành công",
        });
        refreshList();
        onCancel();
        formikRef?.current?.resetForm();
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const onUpdatePaymentBonus = () => {
    const values = formikRef?.current?.values;
    const data: DataUpdatePayment = {
      paymentBonusId: dataPayment.paymentBonusId,
      paymentDate: values?.paymentDate
        ? moment(values?.paymentDate).format(DATE_FORMAT)
        : "",
      amount: values?.amount ? Number(values?.amount) : 0,
      paymentMethod: values?.paymentType || "",
      note: values?.note || "",
    };
    if (!(data.amount && data.paymentDate && data.paymentMethod)) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng điền đầy đủ thông tin thanh toán",
      });
      return;
    }
    updatePaymentBonus.mutate(data);
  };

  return (
    <AppModal
      title={title}
      onCancel={onCancel}
      open={open}
      centered
      footer={null}
    >
      <div className="modal-payment-ui text16">
        <div className="modal-payment-ui__border">
          <p>Cập nhật thanh toán </p>
          <Formik
            initialValues={{} as DataFormPayment}
            innerRef={formikRef}
            onSubmit={(): void => {
              //
            }}
          >
            {({values}): JSX.Element => {
              return (
                <form onSubmit={(e): void => e.preventDefault()}>
                  <div className="modal-payment-ui__form mt-4">
                    <TextInput
                      label="Số tiền thanh toán"
                      name="amount"
                      value={values?.amount}
                      free={!values?.amount}
                      onlynumber
                      typeInput="salary"
                      iscurrency
                      required
                      status={values?.amount ? undefined : "error"}
                      disabled
                    />
                    <AppDatePicker
                      label="Ngày Thanh toán"
                      name="paymentDate"
                      format={DATE_FORMAT}
                      valueAppDatePicker={
                        values?.paymentDate
                          ? moment(values?.paymentDate, DATE_FORMAT)
                          : undefined
                      }
                      allowClear
                      required
                      status={values?.paymentDate ? undefined : "error"}
                      classNameContainer="mt-2"
                      free={!values?.paymentDate}
                    />
                    <TextInput
                      label="Hình thức thanh toán"
                      name="paymentType"
                      value={values?.paymentType}
                      free={!values?.paymentType}
                      required
                      status={values?.paymentType ? undefined : "error"}
                      containerclassname="mt-2"
                    />
                    <TextArea
                      name="note"
                      className="border-dash mt-2"
                      rows={4}
                      placeholder="Ghi chú"
                      maxLength={1000}
                      value={values?.note}
                    />
                  </div>
                </form>
              );
            }}
          </Formik>
        </div>
        <Row className="mt-2" justify="center" gutter={[16, 16]}>
          <Col xs={8}>
            <AppButton label="Hủy" typebutton="secondary" onClick={onCancel} />
          </Col>
          <Col xs={8}>
            <AppButton
              label="Thanh toán"
              typebutton="primary"
              onClick={onUpdatePaymentBonus}
            />
          </Col>
        </Row>
      </div>
    </AppModal>
  );
}

export default React.memo(ModalUpdatePayment);
