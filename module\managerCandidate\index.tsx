import {Formik} from "formik";
import React, {useEffect, useRef, useState} from "react";
import {FormikProps} from "formik/dist/types";
import {TextInput} from "@app/components/TextInput";
import {SelectInput} from "@app/components/SelectInput";
import AppDatePicker from "@app/components/AppDatePicker";
import {List, Row} from "antd";
import "./index.scss";
import AppButton from "@app/components/AppButton";
import {CardViewCandidate} from "@app/components/CardViewCandidate";
import {useMutation, useQuery} from "react-query";
import ApiCandidate, {
  FileUpload,
  ICandidateObject,
  IDataCvCandidate,
  IManagerByConsultantList,
  IWorkLocationList,
  // eslint-disable-next-line import/namespace, import/default
} from "@app/api/ApiCandidate";
import AppModal from "@app/components/AppModal";
import AppPagination from "@app/components/AppPagination";
// import AppSelectCurrency from "@app/components/AppSelectCurrency";
// eslint-disable-next-line import/namespace, import/default
import {CandidateInformationDetail} from "./candidateInformationDetail";
import AppUploadCv from "@app/components/AppUploadCv";
import AddCandidate from "./addCandidate";
import {useDispatch, useSelector} from "react-redux";
import {useRouter} from "next/router";
import {setLoading} from "@app/redux/slices/SystemSlice";
import config from "@app/config";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole, IMultiSelect, OptionSelect} from "@app/types";
// eslint-disable-next-line import/no-cycle
import TableCandidate from "./tableCandidate";
import _ from "lodash";
import ApiUploadCv from "@app/api/ApiUploadCv";
import {
  listExperience,
  listLanguage,
  listStatuses,
} from "@app/utils/constants/state";
import {
  convertStringToArray,
  deleteKeyHasInvalidValue,
  mapFilterSkillData,
  setQueryUrl,
  sortWorkLocation,
} from "@app/utils/constants/function";

export interface MyFromCandidate {
  textSearch: string;
  skills: OptionSelect[];
  workLocations: OptionSelect[];
  experienceYear: OptionSelect;
  languages: OptionSelect[];
  statuses: OptionSelect[];
  to: string;
  from: string;
  salary: string;
  managers: OptionSelect[];
  historyCompanyName: string;
}

const initialValues: MyFromCandidate = {
  textSearch: "",
  skills: [],
  workLocations: [],
  experienceYear: {} as OptionSelect,
  languages: [],
  statuses: [],
  to: "",
  from: "",
  salary: "",
  managers: [],
  historyCompanyName: "",
};

export const mapFilterMultiSelect = (
  filters: OptionSelect[] | undefined
): IMultiSelect[] | undefined =>
  filters?.map((item) => ({
    id: item.value,
    label: item.label,
    value: item.value,
  }));

export const mapFilterWorkLocation = (
  filters: IWorkLocationList[]
): OptionSelect[] =>
  filters.map((item) => ({
    key: item.workLocationId,
    label: item.name,
    value: item.name,
  }));

export const convertFieldSearch = (
  data: OptionSelect[]
): Array<{id: string}> => {
  if (!data?.length) return [];
  return data.map((item) => ({
    // label: item.label,
    id: item.value,
  }));
};

export const convertArrStringToObjIdString = (
  data: string[]
): Array<{id: string}> => {
  if (!data?.length) return [];
  return data.map((i) => ({id: i}));
};

export default function ManagerCandidate(): JSX.Element {
  const {user} = useSelector(selectUser);
  const typeTable = !user?.role?.includes(IAccountRole.CTV);
  const router = useRouter();
  const formikRef = useRef<FormikProps<MyFromCandidate>>(null);
  const [openAddCandidate, setOpenAddCandidate] = useState(false);
  const [showCandidateDetail, setShowCandidateDetail] =
    useState<boolean>(false);
  const dispatch = useDispatch();
  const urlSearchParams = new URLSearchParams(window.location.search);
  const searchParams = Object.fromEntries(urlSearchParams);
  const [id, setId] = useState<number>(-1);
  const [fileCvUpload, setFileCvUpload] = useState<FileUpload>(
    {} as FileUpload
  );
  const [isCtrlClicked, setIsCtrlClicked] = useState(false);
  const refTableCandidate = useRef<any>();
  const valueQuery = {
    experienceYear: searchParams?.experienceYear || "",
    from: searchParams?.createdFrom
      ? moment(searchParams?.createdFrom).format(DATE_FORMAT)
      : "",
    to: searchParams?.createdTo
      ? moment(searchParams?.createdTo).format(DATE_FORMAT)
      : "",
    languages: convertStringToArray(searchParams?.languages),
    managers: convertStringToArray(searchParams?.creatorIds).map((i) => ({
      id: i,
    })),
    skills: convertArrStringToObjIdString(
      convertStringToArray(searchParams?.skills)
    ),
    statuses: convertArrStringToObjIdString(
      convertStringToArray(searchParams?.statuses)
    ),
    workLocations: convertArrStringToObjIdString(
      convertStringToArray(searchParams?.workLocations)
    ),
    textSearch: searchParams?.textSearch || "",
    currentPage: Number(searchParams?.currentPage || 1),
    pageSize: Number(searchParams?.pageSize || 20),
    historyCompanyName: searchParams?.historyCompanyName,
  };
  const [searchParamsFilter, setSearchParamsFilter] = useState(valueQuery);

  let idCandidate: string | undefined;
  if (router.query.id) {
    idCandidate = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    idCandidate = Object.fromEntries(searchParams)?.id;
  }

  useEffect(() => {
    if (idCandidate) {
      showModalDetail(Number(idCandidate));
    }
  }, [idCandidate]);

  const showModalDetail = (idCandidate: number): void => {
    setId(idCandidate);
    setShowCandidateDetail(true);
  };

  const handleVisibleModalCandidateDetail = (): void => {
    window.history.pushState({}, "", router.route);
    setShowCandidateDetail(false);
  };

  const requestSkillList = useQuery("requestSkillList", () => {
    return ApiCandidate.getListSkill();
  });

  const requestWorkLocationList = useQuery("requestWorkLocationList", () => {
    return ApiCandidate.getWorkLocationList();
  });

  const getManagerByConsultantLeaderId = useQuery(
    "getManagerByConsultantLeaderId",
    () => {
      return ApiCandidate.getAllManagerByConsultantLeaderId();
    },
    {
      enabled: !!(user && !user.role?.includes(IAccountRole.CTV)),
    }
  );

  useEffect(() => {
    const dataSkills = mapFilterSkillData(requestSkillList.data ?? []);
    const dataLocation = mapFilterWorkLocation(
      requestWorkLocationList?.data ?? []
    );
    // const dataLocation =
    //   requestWorkLocationList?.data?.map((item) => ({
    //     key: item.workLocationId,
    //     label: item.name,
    //     value: item.workLocationId,
    //   })) || [];
    const dataManager =
      getManagerByConsultantLeaderId?.data?.map(
        (item: IManagerByConsultantList) => ({
          key: String(item.userId),
          label: item.name,
          value: String(item.userId),
        })
      ) || [];

    const languagesData = listLanguage.map((i) => ({
      value: i.id,
      key: i.id,
      label: i.label,
      id: i.id,
    }));

    const query = {
      textSearch: searchParams?.textSearch || "",
      experienceYear:
        listExperience.find(
          (item) => item.value === searchParams?.experienceYear || ""
        ) || ({} as OptionSelect),
      languages: languagesData.filter((item) =>
        convertStringToArray(searchParams?.languages).includes(item.id)
      ) as OptionSelect[],
      skills: dataSkills.filter((item) =>
        convertStringToArray(searchParams?.skills).includes(item.value)
      ),
      statuses: listStatuses.filter((item) =>
        convertStringToArray(searchParams?.statuses).includes(item.value)
      ),
      from: searchParams?.createdFrom || "",
      to: searchParams?.createdTo || "",
      managers: dataManager.filter((item) =>
        convertStringToArray(searchParams?.creatorIds).includes(item.value)
      ),
      salary: searchParams?.salary || "",
      workLocations: dataLocation.filter((item) =>
        convertStringToArray(searchParams?.workLocations).includes(
          item?.key || ""
        )
      ),
      historyCompanyName: searchParams?.historyCompanyName || "",
    };
    // eslint-disable-next-line no-unused-expressions
    formikRef.current && formikRef.current.setValues(query);
  }, [
    requestSkillList.data,
    requestWorkLocationList.data,
    getManagerByConsultantLeaderId.data,
  ]);

  const handlePagination = (page: number, pageSize: number): void => {
    setSearchParamsFilter({
      ...searchParamsFilter,
      currentPage: page,
      pageSize: pageSize,
    });
  };

  const requestCandidateList = useQuery(
    ["requestCandidateList", searchParamsFilter],
    () => {
      // const isAdvanceSearch =
      //   !!refTableCandidate.current?.isShowFilterAdvance ||
      //   !formikRef?.current?.values?.textSearch;

      return ApiCandidate.getListCandidate({
        ...(searchParamsFilter as any),
      });
    },
    {
      onSuccess() {
        const convertValue = {
          experienceYear: searchParamsFilter?.experienceYear || "",
          createdFrom: formikRef?.current?.values?.from
            ? moment(formikRef?.current?.values?.from).toISOString()
            : "",
          createdTo: formikRef?.current?.values.to
            ? moment(formikRef?.current?.values.to).toISOString()
            : "",
          languages: searchParamsFilter?.languages?.join(",") || "",
          creatorIds:
            searchParamsFilter?.managers?.map((item) => item.id)?.join(",") ||
            "",
          skills:
            searchParamsFilter?.skills?.map((item) => item.id)?.join(",") || "",
          statuses:
            searchParamsFilter?.statuses?.map((item) => item.id)?.join(",") ||
            "",
          textSearch: searchParamsFilter?.textSearch || "",
          workLocations:
            searchParamsFilter?.workLocations
              ?.map((item) => item.id)
              ?.join(",") || "",
          historyCompanyName: searchParamsFilter?.historyCompanyName || "",
          currentPage: searchParamsFilter.currentPage,
          pageSize: searchParamsFilter.pageSize,
        };

        const queryOverall = deleteKeyHasInvalidValue(convertValue);

        const redirect = `${config.PATHNAME.MANAGER_CANDIDATE}?${Object.keys(
          queryOverall
        )
          .map(
            (item) =>
              encodeURIComponent(item) +
              "=" +
              encodeURIComponent((queryOverall as any)[item])
          )
          ?.join("&")}`;

        router.push(redirect);
      },
    }
  );

  const handleSearch = (): void => {
    const query = {
      textSearch: formikRef.current?.values.textSearch,
      workLocations:
        formikRef?.current?.values?.workLocations?.map((item) => ({
          id: item.key || "",
          label: item.label,
        })) || [],
      skills: mapFilterMultiSelect(formikRef.current?.values.skills),
      experienceYear: formikRef.current?.values.experienceYear?.label || "",
      statuses: mapFilterMultiSelect(formikRef.current?.values.statuses),
      from: formikRef.current?.values.from
        ? moment(formikRef.current?.values.from).format(DATE_FORMAT)
        : "",
      to: formikRef.current?.values.to
        ? moment(formikRef.current?.values.to).format(DATE_FORMAT)
        : "",
      languages:
        formikRef?.current?.values?.languages?.map((item) => item?.key) || [],
      managers:
        formikRef?.current?.values?.managers?.map((item) => ({
          id: item.key || "",
          label: item.label,
        })) || [],
      currentPage: 1,
      historyCompanyName: formikRef?.current?.values.historyCompanyName,
      pageSize: searchParamsFilter.pageSize,
    };

    setSearchParamsFilter({
      ...query,
    } as any);
  };

  const resetSearch = () => {
    setSearchParamsFilter({
      ...initialValues,
      experienceYear: "",
      pageSize: searchParamsFilter.pageSize,
      currentPage: 1,
    } as any);
    window.history.pushState({}, "", router.route);
  };

  const uploadFileCv = useMutation(
    (data: FormData) => {
      return ApiCandidate.fileParser(data);
    },
    {
      onSuccess: () => {
        dispatch(setLoading(false));
        setOpenAddCandidate(true);
      },
      onError: () => {
        dispatch(setLoading(false));
        setOpenAddCandidate(true);
      },
    }
  );

  const handleUploadFileTemp = useMutation((data: FormData) => {
    dispatch(setLoading(true));
    return ApiUploadCv.uploadFileTemp(data);
  });

  const handleUploadFile = (file: File, formData: FormData): void => {
    setFileCvUpload({
      file: file,
      fileName: file.name,
    });
    handleUploadFileTemp.mutate(formData);
    uploadFileCv.mutate(formData);
  };

  const getListCandidateAfterUpdate = (): void => {
    requestCandidateList.refetch();
  };

  const handleCancelFileUpload = (): void => {
    setFileCvUpload({} as FileUpload);
  };

  useEffect(() => {
    document.addEventListener("keydown", eventHandler);

    return (): void => {
      document.removeEventListener("keydown", eventHandler);
    };
  }, []);

  const eventHandler = (evt: KeyboardEvent): void => {
    if (evt.key === "Enter") {
      formikRef.current?.handleSubmit();
    }
  };

  const handleKeyDown = (event: {ctrlKey: boolean; metaKey: boolean}): void => {
    setIsCtrlClicked(event.ctrlKey || event.metaKey);
  };

  const onClickItem = (candidate: ICandidateObject): void => {
    if (isCtrlClicked) {
      router.push(
        `${config.PATHNAME.CANDIDATE_DETAIL}?id=${candidate.candidateId}`
      );
    } else {
      setQueryUrl({id: String(candidate.candidateId)});
      showModalDetail(Number(candidate.candidateId));
    }
  };

  const cancelModalAddCandidate = (): void => {
    setOpenAddCandidate(false);
    handleCancelFileUpload();
  };

  const renderTypeCard = (): JSX.Element => (
    <div className="flex container-list-candidate">
      <div className="w-3/12 p-2">
        <span className="text-2xl">Tìm kiếm</span>
        <div className="p-5 container-filter-candidate">
          <div>
            <Formik
              initialValues={initialValues}
              innerRef={formikRef}
              onSubmit={handleSearch}
            >
              {({
                values,
                handleChange,
                handleBlur,
                handleSubmit,
                handleReset,
              }): JSX.Element => (
                <form className="flex flex-col" onSubmit={handleSubmit}>
                  <TextInput
                    onPressEnter={(): void => handleSubmit()}
                    disabled={values?.workLocations?.length > 0}
                    containerclassname="mt-2"
                    label="Tìm kiếm nhanh"
                    placeholder="Nhập họ tên, email,sđt ứng viên"
                    name="textSearch"
                    free={!values?.textSearch}
                  />
                  <SelectInput
                    mode="multiple"
                    containerclassname="mt-2"
                    name="workLocations"
                    labelselect="Địa điểm làm việc"
                    data={sortWorkLocation(
                      requestWorkLocationList?.data?.map((item) => ({
                        key: item.workLocationId,
                        label: item.name,
                        value: item.workLocationId,
                      })) || [],
                      "value"
                    )}
                    free={values?.workLocations?.length === 0}
                    allowClear
                    value={values?.workLocations || []}
                  />
                  <SelectInput
                    mode="multiple"
                    containerclassname="mt-2"
                    name="skills"
                    labelselect="Kỹ năng"
                    data={mapFilterSkillData(requestSkillList.data ?? [])}
                    free={values?.skills?.length === 0}
                    value={values?.skills || []}
                  />
                  <SelectInput
                    mode="multiple"
                    containerclassname="mt-2"
                    name="languages"
                    labelselect="Ngoại ngữ"
                    data={listLanguage.map((i) => ({
                      ...i,
                      value: i.label,
                    }))}
                    free={values?.languages?.length === 0}
                    value={values?.languages || []}
                  />
                  {/* <Row className="mt-2">
                    <TextInput
                      onPressEnter={(): void => handleSubmit()}
                      containerclassname="filter-salary-expect flex-1"
                      label="Mức lương"
                      placeholder="Nhập mức lương"
                      name="salary"
                      free={!values.salary}
                      onlynumber
                    />
                    <AppSelectCurrency
                      name="currencyTypeId"
                      defaultValue="VND"
                    />
                  </Row> */}
                  <Row className="mt-2 div-time">
                    <SelectInput
                      name="experienceYear"
                      labelselect="Kinh nghiệm"
                      data={listExperience}
                      allowClear
                      free={_.isEmpty(values?.experienceYear)}
                    />
                    <SelectInput
                      mode="multiple"
                      name="statuses"
                      labelselect="Trạng thái"
                      data={listStatuses}
                      free={values?.statuses?.length === 0}
                      value={values?.statuses || []}
                    />
                  </Row>
                  <Row className="mt-2 div-time ">
                    <AppDatePicker
                      name="from"
                      label="Từ"
                      free={!values?.from}
                      format={DATE_FORMAT}
                    />
                    <AppDatePicker
                      name="to"
                      label="Đến"
                      free={!values?.to}
                      format={DATE_FORMAT}
                    />
                  </Row>
                  <Row className="mt-6 div-time">
                    <AppButton
                      label="Xoá tất cả"
                      typebutton="secondary"
                      onClick={() => {
                        handleReset();
                        resetSearch();
                      }}
                    />
                    <AppButton
                      label="Tìm kiếm"
                      typebutton="primary"
                      onClick={handleSubmit}
                    />
                  </Row>
                </form>
              )}
            </Formik>
          </div>
        </div>
      </div>
      <div className="w-9/12 p-2">
        <span className="text-2xl" style={{color: "#324054"}}>
          {requestCandidateList.data?.totalCount} Hồ sơ
        </span>

        <AppUploadCv id="file" onChangeInput={handleUploadFile} />
        <div className="list-candidate">
          <List
            className="mt-5"
            loading={requestCandidateList.isFetching}
            grid={{
              gutter: 16,
              xs: 1,
              sm: 1,
              md: 1,
              lg: 1,
              xl: 1,
              xxl: 1,
            }}
            dataSource={requestCandidateList.data?.candidatesPaging ?? []}
            renderItem={(item: ICandidateObject): JSX.Element => (
              <List.Item
                onClick={(): void => {
                  onClickItem(item);
                }}
                onMouseDown={handleKeyDown}
              >
                <CardViewCandidate candidate={item} isSmallSize={false} />
              </List.Item>
            )}
          />
        </div>
        <div className="mt-5">
          <AppPagination
            defaultCurrent={1}
            defaultPageSize={20}
            current={searchParamsFilter.currentPage}
            pageSize={searchParamsFilter.pageSize}
            total={requestCandidateList.data?.totalCount}
            onChange={handlePagination}
          />
        </div>
      </div>
    </div>
  );

  const renderTypeTable = (): JSX.Element => {
    return (
      <div>
        <TableCandidate
          ref={refTableCandidate}
          initialValues={initialValues}
          requestCandidateList={requestCandidateList}
          requestSkillList={requestSkillList}
          onClickCandidate={onClickItem}
          formikRef={formikRef}
          requestWorkLocationList={requestWorkLocationList}
          handleSearch={handleSearch}
          handleUploadFile={handleUploadFile}
          resetSearch={resetSearch}
          allUsersData={
            getManagerByConsultantLeaderId?.data?.map((item: any) => ({
              userName: item.name,
              userId: item.userId,
            })) || []
          }
        />
        <AppPagination
          className="mt-6"
          defaultPageSize={20}
          current={searchParamsFilter.currentPage}
          pageSize={searchParamsFilter.pageSize}
          total={requestCandidateList.data?.totalCount}
          onChange={handlePagination}
        />
      </div>
    );
  };

  return (
    <div>
      {typeTable ? renderTypeTable() : renderTypeCard()}
      <AppModal
        className="modal-detail-candidate"
        centered
        footer={null}
        open={showCandidateDetail}
        onCancel={handleVisibleModalCandidateDetail}
        title="Chi tiết ứng viên"
        width="70%"
      >
        <CandidateInformationDetail
          idCandidate={id}
          setShowCandidateDetail={setShowCandidateDetail}
          reloadData={getListCandidateAfterUpdate}
        />
      </AppModal>
      <AddCandidate
        isModalVisible={openAddCandidate}
        handleOk={handleSearch}
        handleCancel={cancelModalAddCandidate}
        cvCandidateData={
          {
            ...uploadFileCv?.data,
            fileCVPath: handleUploadFileTemp.data || "",
            fileCVName: fileCvUpload?.fileName || "",
          } as IDataCvCandidate
        }
        fileUpload={fileCvUpload}
        getListCandidateAfterUpdate={getListCandidateAfterUpdate}
        type="newCandidate"
      />
    </div>
  );
}
