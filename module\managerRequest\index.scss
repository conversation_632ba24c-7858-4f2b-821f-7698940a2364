.manager-request {
  height: 100%;
  width: 100%;

  .manager-request-table {
    .ant-table-tbody > tr > td {
      font-size: 12px;
    }

    .ant-table-thead > tr > th {
      font-size: 14px;
    }

    .ant-table-selection-column {
      border-right: 0 !important;
    }
  }

  .external {
    padding: 4px 8px;
    color: $text-color;
    background-color: $status-external;
    border-radius: 8px;
  }

  .internal {
    padding: 4px 8px;
    border-radius: 8px;
    color: $text-color;
    background-color: $header_tf05;
  }

  .status-job {
    padding: 4px 6px;
    color: $white-color;
    border-radius: 8px;
  }

  .open {
    background-color: $primary-color;
  }

  .close {
    background-color: $status-reject;
  }

  .draft {
    background-color: $green_color;
  }

  .name-color {
    color: $primary-color;
  }

  .label-request {
    padding: 4px 6px;
    border-radius: 8px;
    color: $white-color;
  }

  .urgent-label {
    background-color: $urgent-label;
  }

  .pending-label {
    background-color: $pending-label;
  }

  .ant-table-tbody > tr.ant-table-row-selected:hover > td {
    background: $white-color;
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background: $white-color;
  }

  .ant-checkbox-indeterminate .ant-checkbox-inner::after {
    background-color: $white-color;
  }

  .manager-request-export {
    .ant-btn {
      border: none;
      padding: 0;
    }
  }

  .btn-config {
    .ant-btn {
      border: 0;
      padding: 0;
    }
  }

  .request-link {
    margin-bottom: 2px;
  }

  .disable-checkbox-ant {
    .ant-checkbox .ant-checkbox-inner {
      border: 2px solid $border-color;
    }
  }

  a.active-name {
    color: rgba(255, 0, 0, 1);
  }

  .limit-two-row {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
