.text-input-password {
  position: relative;

  .label {
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 15px;
    top: 16px;
    height: 20px;
    transition: 0.2s ease all;
    line-height: 20px;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 80%;
    color: $header_tf;
    z-index: 99;
  }

  .as-placeholder {
    color: $header_tf;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 85%;
  }

  .as-label {
    top: 6px;
    font-size: 0.75rem;
    padding: 0 4px;
    margin-left: -4px;
    color: $header_tf;
  }

  .text-required {
    color: red;
  }

  .ant-input-password {
    padding: 24px 15px 8px 15px;
    border-radius: 8px;
    height: 52px;
    color: $text-color-input;
    font-size: 0.875rem;
    &:hover {
      border-color: $primary-color;
    }
  }

  .ant-input-suffix {
    margin-bottom: 12px;
  }

  .normal:focus {
    border-color: $primary-color;
    border-right-width: 1px;
    box-shadow: none;
  }

  .ant-input-password::placeholder {
    color: $header_tf05;
  }

  .ant-input-password[disabled] {
    background-color: $header_tf01;
    color: $text-color-input;
  }

  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    z-index: 0;
  }
}
