import React, {memo, useEffect, useMemo, useState} from "react";
import {TreeSelectProps, TreeSelect} from "formik-antd";
import Icon from "../Icon/Icon";
import classNames from "classnames";
import "./index.scss";

export interface TreeSelectDataItem {
  label: string;
  value: string;
  children?: Array<{
    label: string;
    value: string;
  }>;
}

interface AppTreeSelectProps extends TreeSelectProps {
  containerclassname?: string;
  required?: boolean;
  labelselect: string;
  data?: Array<TreeSelectDataItem>;
}

function AppTreeSelect(props: AppTreeSelectProps) {
  const {
    containerclassname,
    required,
    labelselect,
    data,
    treeDefaultExpandAll = true,
    value: valueTreeSelect,
    onChange,
  } = props;
  const [value, setValue] = useState<Array<string>>([]);
  const [focus, setFocus] = useState(false);

  const onChangeValue = (newValue: Array<string>) => {
    setValue(newValue);
  };

  useEffect(() => {
    setValue(valueTreeSelect);
  }, [valueTreeSelect]);

  const isOccupied = !!focus || (value && value.length);

  const labelClass = isOccupied ? "label as-label" : "label as-placeholder";

  const requiredMark = required ? (
    <span className="text-required">*</span>
  ) : null;

  const IconDown = useMemo(
    () => (
      <Icon
        size={10}
        icon="arrow-drop-down-line"
        color="#324054"
        className=""
      />
    ),
    []
  );

  return (
    <div
      className={classNames("app-tree-select-container", containerclassname)}
      onBlur={() => setFocus(false)}
      onFocus={() => setFocus(true)}
    >
      <TreeSelect
        {...props}
        value={value}
        dropdownStyle={{
          maxHeight: 400,
          overflow: "auto",
          borderRadius: "8px",
          borderWidth: "1px",
          borderColor: "rgba(157, 157, 157,0.5)",
          padding: "8px",
        }}
        treeDefaultExpandAll={treeDefaultExpandAll}
        showSearch
        treeCheckable
        onChange={(value: any, labelList: React.ReactNode[], extra: any) => {
          onChange?.(value, labelList, extra);
          onChangeValue(value);
        }}
        treeData={data}
        suffixIcon={IconDown}
        showArrow
        maxTagCount="responsive"
      />

      <span className={labelClass}>
        {labelselect} {requiredMark}
      </span>
    </div>
  );
}

export default memo(AppTreeSelect);
