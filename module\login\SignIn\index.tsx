import "./index.scss";
import {Formik} from "formik";
import {Col, Image, Row, notification} from "antd";
import {useMutation, useQuery} from "react-query";
import ApiUser from "@app/api/ApiUser";
import {useRouter} from "next/router";
import Config from "@app/config";
import {IUserIsLogin} from "@app/types";
import {ILoginForm} from "@app/module/login/NewPassword/form-config";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {useEffect, useState} from "react";
import {useDispatch} from "react-redux";
import {setUser} from "@app/redux/slices/UserSlice";
import {validateEmail, validatePassword} from "@app/utils/constants/function";
import {TextInput} from "@app/components/TextInput";
import {AppPassword} from "@app/components/AppPassword";
import AppButton from "@app/components/AppButton";
import AppCheckBox from "@app/components/AppCheckbox";
import Link from "next/link";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import {
  messageValidateEmail,
  messageValidatePassword,
} from "@app/utils/constants/message";

export function SignIn(): JSX.Element {
  const dispatch = useDispatch();
  const [rememberMe, setRememberMe] = useState<boolean>(true);

  const handleChecked = (e: CheckboxChangeEvent): void => {
    setRememberMe(e.target.checked);
  };

  const router = useRouter();

  let redirect: string | undefined;
  if (router.query.redirect) {
    redirect = router.query.redirect as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    redirect = Object.fromEntries(searchParams)?.redirect;
  }

  const loginMutation = useMutation(ApiUser.login);
  const loginWithCode = useMutation(ApiUser.loginWithCode);

  const getMeData = (): Promise<IUserIsLogin> => {
    return ApiUser.getMe();
  };

  const dataUser = useQuery("dataUser", getMeData, {
    onSuccess: (data) => {
      dispatch(setUser(data));
      if (redirect) {
        router.push(redirect);
      } else {
        // router.push(Config.PATHNAME.MANAGER_APPLICATION);
        // hoàn thiện rồi mở comment
        router.push(Config.PATHNAME.HOME);
      }
      dispatch(setLoading(false));
    },
    onError: () => {
      dispatch(setLoading(false));
      notification.error({
        message: "Login failed. Please try again!",
        duration: 3,
      });
    },
    enabled: false,
  });

  const searchParams = new URLSearchParams(window.location.search);

  const code = Object.fromEntries(searchParams)?.code;

  useEffect(() => {
    // Xảy ra khi chuyển từ web khác sang có cookie(code)
    autoLogin();
  }, []);

  const autoLogin = (): void => {
    if (code) {
      loginWithCode.mutate(
        {
          code: code,
        },
        {
          onSuccess: () => {
            dataUser.refetch();
          },
          onError: () => {
            dispatch(setLoading(false));
            notification.error({
              message: "Login failed. Please try again!",
              duration: 3,
            });
          },
        }
      );
    }
  };

  const handleLogin = (
    values: ILoginForm,
    {setSubmitting}: {setSubmitting: (isSubmitting: boolean) => void}
  ): void => {
    dispatch(setLoading(true));
    if (!validateEmail(values.email?.trim())) {
      notification.error({
        message: "Thông báo",
        description: messageValidateEmail,
      });
    } else if (!validatePassword(values.password.trim())) {
      notification.error({
        message: "Thông báo",
        description: messageValidatePassword,
      });
    } else {
      loginMutation.mutate(
        {
          UserName: values.email?.trim(),
          Password: values.password?.trim(),
          returnUrl: "/",
          RememberMe: rememberMe,
        },
        {
          onSuccess: () => {
            dataUser.refetch();
          },
        }
      );
    }
    setSubmitting(false);
    dispatch(setLoading(false));
  };
  return (
    <Formik
      initialValues={{email: "", password: ""}}
      validateOnChange
      validateOnBlur
      onSubmit={handleLogin}
    >
      {({isSubmitting, handleSubmit, values}): JSX.Element => (
        <div className="container-sign-in">
          <Row className="h-full">
            <Col xs={8}>
              <form onSubmit={handleSubmit} className="w-full h-full">
                <div className="container-sign-in__form flex justify-center items-center">
                  <div>
                    <div className="container-sign-in__logo flex justify-center">
                      <Link href={Config.PATHNAME.HOME}>
                        <Image
                          src="/img/logo-icon.svg"
                          alt="logo"
                          height={50}
                          width={50}
                          preview={false}
                        />
                        <Image
                          src="/img/logo-text.svg"
                          alt="logo"
                          height={56}
                          width={75}
                          preview={false}
                          className="ml-2"
                        />
                      </Link>
                    </div>
                    <h2 className="text-center container-sign-in__title text32 font-bold">
                      Chào mừng bạn đã quay lại
                    </h2>
                    <Row gutter={[16, 16]} justify="center">
                      <Col xs={20}>
                        <TextInput
                          name="email"
                          label="Email"
                          placeholder="Nhập email của bạn"
                          containerclassname="mt-2"
                          value={values?.email}
                          free={!values?.email}
                          required
                          status={
                            !validateEmail(values?.email) ? "error" : undefined
                          }
                        />
                        <AppPassword
                          name="password"
                          label="Mật khẩu"
                          placeholder="Nhập mật khẩu"
                          containerclassname="mt-2"
                          value={values?.password}
                          free={!values?.password}
                          required
                          status={
                            !validatePassword(values?.password)
                              ? "error"
                              : undefined
                          }
                        />

                        <AppButton
                          typebutton="primary"
                          classrow="mt-4"
                          htmlType="submit"
                          isSubmitting={isSubmitting}
                          disabled={loginMutation.isLoading}
                          onClick={handleSubmit}
                        >
                          Đăng nhập
                        </AppButton>
                        <div className="mt-4 flex items-center justify-between">
                          <AppCheckBox
                            onChange={handleChecked}
                            checked={rememberMe}
                          >
                            Ghi nhớ đăng nhập
                          </AppCheckBox>
                          <div>
                            <Link
                              href={Config.PATHNAME.FORGOT_PASSWORD}
                              className="hover:text-inherit"
                            >
                              Quên mật khẩu?
                            </Link>
                          </div>
                        </div>
                        <p className="text-center mt-2 text14">
                          Bạn chưa có tài khoản?{" "}
                          <Link href={Config.PATHNAME.REGISTER}>
                            <span className="container-sign-in__policy-link">
                              Đăng ký ngay
                            </span>
                          </Link>
                        </p>
                      </Col>
                    </Row>
                  </div>
                </div>
              </form>
            </Col>
            <Col xs={16}>
              <div className="flex justify-center items-center h-full">
                <div>
                  <div className="flex justify-center">
                    <Image
                      src="/img/image-security.png"
                      alt="logo"
                      height={240}
                      width={400}
                      preview={false}
                    />
                  </div>
                  <div className="text-center mt-4 text32">
                    Bảo mật & An toàn tuyệt đối
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      )}
    </Formik>
  );
}
