import AppBreadcrumb from "@app/components/AppBreadcrumb";
import config from "@app/config";
import React, {useState} from "react";
import "./index.scss";
import ApiPaymentBonus, {IPolicy} from "@app/api/ApiPaymentBonus";
import {useMutation, useQuery} from "react-query";
import moment, {Moment} from "moment";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/lib/table";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {Row, notification} from "antd";
import AppModalConfirm from "@app/components/AppModalConfirm";
import {useRouter} from "next/router";
import ModalRule from "./ModalRule";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";

const items = [
  {
    breadcrumb: "Danh sách Performance Bonus",
    href: `${config.PATHNAME.MANAGER_BONUS}?tab=performanceBonus`,
  },
  {
    href: config.PATHNAME.RULE_BONUS,
    breadcrumb: "Quy định thưởng",
  },
];

export default function RuleBonus(): JSX.Element {
  const [isShowModalConfirmDelete, setIsShowModalConfirmDelete] =
    useState(false);
  const [policy, setPolicy] = useState<IPolicy>({} as IPolicy);
  const router = useRouter();
  const [isShowModalPolicy, setIsShowModalPolicy] = useState<boolean>(false);

  const getAllPaymentBonusPolicy = useQuery(
    ["getAllPaymentBonusPolicy"],
    () => {
      return ApiPaymentBonus.getAllPaymentBonusPolicy();
    }
  );

  const deletePolicy = useMutation(
    (id: number) => {
      return ApiPaymentBonus.deletePolicy(id);
    },
    {
      onSuccess: () => {
        getAllPaymentBonusPolicy.refetch();
        notification.success({
          message: "Thông báo",
          description: "Xóa chính sách thành công",
        });
      },
    }
  );

  const listPolicy = getAllPaymentBonusPolicy.data;

  const currentPolicy = listPolicy?.find(
    (item) =>
      moment().isSameOrAfter(moment(new Date(item.startDate))) &&
      moment().isSameOrBefore(moment(new Date(item.endDate)))
  );

  const getContent = (policy?: IPolicy): string[] => {
    if (!policy) {
      return [];
    }
    return [
      `Cứ ${
        policy.cvReviewed || 0
      } CV được gửi lịch PV khách hàng của RECO => Thưởng ${
        policy.bonusReviewed || 0
      }M`,
      `Cứ có ${policy.cvInterviewed} Interviewed => Thưởng ${policy.bonusInterviewed}M`,
      `Cứ ${policy.cvOnboardDone} ứng viên đi làm bảo hành thành công => Thưởng ${policy.bonusOnboardDone}M`,
    ];
  };

  const lastTime = (): Moment => {
    const policyLastTime = listPolicy?.reduce((prev, current) =>
      moment(new Date(prev.endDate)).isAfter(moment(new Date(current.endDate)))
        ? prev
        : current
    );

    return policyLastTime?.endDate
      ? moment(new Date(policyLastTime?.endDate))
      : moment();
  };

  const handleDelete = (): void => {
    if (policy.paymentBonusPolicyId) {
      deletePolicy.mutate(policy.paymentBonusPolicyId);
    }
    setIsShowModalConfirmDelete(false);
  };

  const columns: ColumnsType<IPolicy> = [
    {
      title: "Ngày bắt đầu",
      dataIndex: "startDate",
      key: "startDate",
      render: (_, item: IPolicy): JSX.Element => (
        <span>{moment(new Date(item.startDate)).format(DATE_FORMAT)}</span>
      ),
    },
    {
      title: "Ngày kết thúc",
      dataIndex: "endDate",
      key: "endDate",
      render: (_, item: IPolicy): JSX.Element => (
        <span>{moment(new Date(item.endDate)).format(DATE_FORMAT)}</span>
      ),
    },
    {
      title: "Quy đinh thưởng",
      dataIndex: "content",
      key: "content",
      align: "left",
      render: (_, item: IPolicy): JSX.Element => {
        return (
          <div className="">
            {getContent(item).map((i, index) => (
              <div key={index}>{i}</div>
            ))}
          </div>
        );
      },
      width: "45%",
    },
    {
      title: "",
      dataIndex: "action",
      key: "action",
      width: "12%",
      render: (_, item: IPolicy) => (
        <Row>
          <AppButton
            typebutton="normal"
            classrow="btn-icon"
            onClick={(): void => {
              setPolicy(item);
              setIsShowModalConfirmDelete(true);
            }}
          >
            <Icon icon="delete-bin-6-line" size={14} />
          </AppButton>
          <AppButton
            typebutton="normal"
            classrow="btn-icon"
            onClick={(): void => {
              setPolicy(item);
              setIsShowModalPolicy(true);
            }}
          >
            <Icon icon="edit-line" size={14} />
          </AppButton>
        </Row>
      ),
    },
  ];

  return (
    <div className="rule-bonus-container">
      <span className="text24">Quy định thưởng</span>
      <AppBreadcrumb separator=">" items={items} />
      <div className="border-dash-bonus mt-4">
        <span className="font-bold">Quy định thưởng đang áp dụng</span>
        {!!listPolicy && listPolicy?.length > 0 && (
          <div className="mt-4 ml-2">
            <ul>
              {getContent(currentPolicy).map((i, index) => (
                <li key={index}>{i}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <div className="border-dash-bonus mt-4">
        <span className="font-bold">Lịch sử quy định thưởng</span>
        <div className="mt-4 h-[42vh]">
          <AppTable
            dataSource={listPolicy?.map((item: IPolicy, index: number) => ({
              ...item,
              key: index,
            }))}
            columns={columns}
            loading={getAllPaymentBonusPolicy.isLoading}
            scroll={{y: "35vh"}}
          />
        </div>
      </div>
      <Row className="justify-center mt-6">
        <AppButton
          classrow="w-48 mr-10"
          label="Trở lại"
          typebutton="secondary"
          onClick={(): void => router.back()}
        />
        <AppButton
          classrow="w-48 ml-10"
          label="Tạo quy định"
          typebutton="primary"
          onClick={(): void => setIsShowModalPolicy(true)}
        />
      </Row>
      <AppModalConfirm
        open={isShowModalConfirmDelete}
        content="Bạn có chắc chắn muốn xóa?"
        title="Xác nhận xóa"
        onCancel={(): void => setIsShowModalConfirmDelete(false)}
        onOk={handleDelete}
      />
      <ModalRule
        open={isShowModalPolicy}
        handleClose={(): void => {
          setIsShowModalPolicy(false);
          setPolicy({} as IPolicy);
        }}
        policy={policy}
        lastTime={lastTime().add(1, "day")}
        getAllPaymentBonusPolicy={getAllPaymentBonusPolicy}
      />
    </div>
  );
}
