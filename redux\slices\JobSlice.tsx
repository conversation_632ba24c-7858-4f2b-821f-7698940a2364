import {createSlice} from "@reduxjs/toolkit";

interface JobState {
  candidateIdDuplicate: number | null;
}

const initialState: JobState = {
  candidateIdDuplicate: null,
};

export const jobSlice = createSlice({
  name: "job",
  initialState: initialState,
  reducers: {
    setCandidateIdDuplicate: (state, actions) => {
      state.candidateIdDuplicate = actions.payload;
    },
  },
});

export const {setCandidateIdDuplicate} = jobSlice.actions;

export default jobSlice.reducer;
