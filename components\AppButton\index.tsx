import React from "react";
import {<PERSON><PERSON>, ButtonProps, Row} from "antd";
import "./index.scss";

interface AppButtonProps extends ButtonProps {
  isSubmitting?: boolean;
  label?: string;
  classrow?: string;
  typebutton: "primary" | "secondary" | "normal" | "warning";
  onClick?: () => void;
  children?: React.ReactNode;
}
export default function AppButton(props: AppButtonProps): JSX.Element {
  const {isSubmitting, label, classrow, typebutton, onClick, children} = props;
  const className =
    typebutton === "primary"
      ? "primary-button"
      : typebutton === "secondary"
      ? "secondary-button"
      : typebutton === "warning"
      ? "warning-button"
      : "normal-btn";

  return (
    <Row className={`app-button-container ${classrow || ""}`}>
      <Button
        className={className}
        loading={isSubmitting}
        onClick={onClick}
        {...props}
      >
        {children || label}
      </Button>
    </Row>
  );
}
