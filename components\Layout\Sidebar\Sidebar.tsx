import React, {useMemo} from "react";
import {<PERSON><PERSON>, Card, Modal, Image} from "antd";
import {UserOutlined} from "@ant-design/icons";
import classNames from "classnames";
import {useDispatch, useSelector} from "react-redux";
import {useRouter} from "next/router";
import RouteList, {GroupNavbarKey, IRoute} from "../../../routes/RouteList";
import {logoutUser} from "@app/redux/slices/UserSlice";
import "./index.scss";
import Icon from "@app/components/Icon/Icon";
import {IAccountRole} from "@app/types";
import {IRootState} from "@app/redux/store";
import ApiUser from "@app/api/ApiUser";
import Link from "next/link";
import config from "@app/config";
import {unRegisterServiceWorker} from "@app/utils/constants/function";
import AppCollapse from "@app/components/AppCollapse";
import CollapsePanel from "antd/lib/collapse/CollapsePanel";

interface RecoNavbarItem {
  label: string;
  key: GroupNavbarKey | string;
  children: IRoute[];
  icon: React.ReactNode;
}

const RenderMenu = React.memo(() => {
  const router = useRouter();
  const user = useSelector((state: IRootState) => state.user);

  const listRoleUser =
    user?.user?.roleList?.split(",")?.map((item) => item.trim()) || [];

  const checkRole = (roles: Array<IAccountRole>): boolean => {
    return listRoleUser?.some((r) => roles.includes(r as any));
  };

  const findRoute = (pathname: string[]) => {
    const result = RouteList.filter(
      (i) => pathname.includes(i.path) && checkRole(i.role as any)
    ).map((i) => ({
      label: i?.name || "",
      key: i?.path || "",
      icon: i?.icon,
      children: [],
    }));
    return result;
  };

  const permanentRoutes = [
    config.PATHNAME.MANAGER_REPORT,
    config.PATHNAME.MANAGER_APPLICATION,
  ];

  const menu = useMemo(() => {
    const applicationRoute = findRoute(permanentRoutes);
    const sidebarData: RecoNavbarItem[] = [...applicationRoute];

    const filterRoute = RouteList.filter(
      (i) =>
        checkRole(i?.role || []) &&
        i.isSidebar &&
        !permanentRoutes.includes(i.path)
    );

    const groups: RecoNavbarItem[] = [
      {
        label: "Ứng viên",
        key: GroupNavbarKey.CANDIDATE,
        children: [],
        icon: "",
      },
      {
        label: "Khách hàng",
        key: GroupNavbarKey.CUSTOMER,
        children: [],
        icon: "",
      },
      {
        label: "Tiện ích",
        key: GroupNavbarKey.UTILITY,
        children: [],
        icon: "",
      },
    ];
    let clone: RecoNavbarItem[] = [];
    groups.reduce((_: any, current: RecoNavbarItem) => {
      const filterMenuItems = filterRoute.filter(
        (item) => current.key === item.group
      );
      const newItem = {
        ...current,
        children: filterMenuItems,
      };
      clone.push(newItem);
      return clone;
    }, clone);

    const menuNotGroup = filterRoute
      .filter((i) => !i.group)
      .map((i) => ({label: i.name, key: i.path, icon: i.icon}));

    clone = clone.concat(menuNotGroup as any);

    sidebarData.push(...clone);

    return sidebarData;
  }, [listRoleUser, RouteList]);

  const checkActivePanel = (pathnames: Array<string>): boolean => {
    if (pathnames.includes(router.pathname)) return true;
    return false;
  };

  return (
    <div className="menu-sidebar-container">
      <AppCollapse className="menu-sidebar-container__menu">
        {menu.map((item) => {
          if (item?.children?.length > 0) {
            return (
              <CollapsePanel
                header={item.label}
                key={item.key}
                className={classNames(
                  checkActivePanel(item.children.map((i) => i.path)) &&
                    "panel-active"
                )}
              >
                {item.children.map((i) => (
                  <Link href={i.path} key={i.path}>
                    <div
                      className={classNames(
                        "flex items-center menu-sidebar-container__menu-item",
                        router.pathname === i.path && "menu-item-active"
                      )}
                    >
                      <div className="mb-1">{i.icon}</div>
                      <div className="ml-3">{i.name}</div>
                    </div>
                  </Link>
                ))}
              </CollapsePanel>
            );
          }

          if (
            !item?.children?.length &&
            [
              GroupNavbarKey.CANDIDATE,
              GroupNavbarKey.CUSTOMER,
              GroupNavbarKey.UTILITY,
            ].includes(item.key as any)
          ) {
            return null;
          }

          return (
            <Link
              href={item.key}
              key={item.key}
              className="menu-sidebar-container__menu-item-link"
            >
              <div
                className={classNames(
                  "flex items-center menu-sidebar-container__menu-item",
                  router.pathname === item.key && "menu-item-active"
                )}
              >
                <div className="mb-1">{item.icon}</div>
                <div className="ml-3">{item.label}</div>
              </div>
            </Link>
          );
        })}
      </AppCollapse>
    </div>
  );
});
RenderMenu.displayName = "RenderMenu";

/**
 *
 */
export default function Sidebar(): JSX.Element {
  const user = useSelector((state: IRootState) => state.user);
  const dispatch = useDispatch();

  const handleLogout = (): void => {
    Modal.confirm({
      title: "Đăng xuất",
      content: "Bạn có chắc chắn?",
      onOk: () => {
        ApiUser.logout();
        dispatch(logoutUser());
        unRegisterServiceWorker();
      },
    });
  };

  return (
    <div className={classNames("sidebar")}>
      <Card className="custom-card-view">
        <div className="logo-container items-center">
          <Link href={config.PATHNAME.HOME}>
            <Image
              src="/img/logo-icon.svg"
              alt="logo"
              height={50}
              width={50}
              preview={false}
            />
            <Image
              src="/img/logo-text.svg"
              alt="logo"
              height={56}
              width={75}
              preview={false}
              className="ml-2"
            />
          </Link>

          {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
          <div
            className="flex justify-end cursor-pointer"
            onClick={handleLogout}
          >
            <Icon
              size={20}
              className=""
              icon="logout-box-line"
              color="#324054"
            />
          </div>
        </div>
      </Card>

      <div className="div-user info-view">
        <div className="flex items-center div-avatar w-full">
          <Avatar
            icon={<UserOutlined />}
            className="w-24"
            src={user?.user?.pathImage}
            size={36}
          />
          <div className="ml-2 mr-2 w-48 flex-1">
            <p className="font-bold d-block text14">{user?.user?.name}</p>
            <p className="text-role d-block text12">{user?.user?.roleList}</p>
          </div>
          <Icon
            size={8}
            className=""
            icon="arrow-drop-down-line"
            color="#324054"
          />
        </div>
      </div>
      <RenderMenu />
      {user?.user?.role?.includes(IAccountRole.CTV) && (
        <div className="telegram">
          <p>Reco hỗ trợ CTV tuyển dụng</p>
          <a href="https://t.me/reco_support" target="_blank" rel="noreferrer">
            <Icon size={36} icon="telegram" />
          </a>
        </div>
      )}
    </div>
  );
}
