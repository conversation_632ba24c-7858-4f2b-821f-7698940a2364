import AppButton from "@app/components/AppButton";
import AppModal from "@app/components/AppModal";
import config from "@app/config";
import {Col, Image, ModalProps, Row} from "antd";
import {useRouter} from "next/router";
import React from "react";

interface Props extends ModalProps {
  email: string;
}

function ModalRegisterSuccess(props: Props): JSX.Element {
  const {open, email} = props;
  const router = useRouter();

  const redirectLogin = (): void => {
    router.push(config.PATHNAME.LOGIN);
  };
  return (
    <AppModal
      footer={null}
      title="Thông báo"
      className="modal-register-success"
      open={open}
      onCancel={redirectLogin}
      centered
    >
      <div className="modal-register-success-ui text-center">
        <h2 className="text24 font-bold">Đ<PERSON>ng ký Cộng tác viên thành công</h2>
        <p className="text14 mt-4">Tài khoản đã được tạo thành công</p>
        <p className="text14">
          <PERSON><PERSON><PERSON> kiểm tra email <span className="font-bold">{email}</span> để kích
          hoạt tài khoản
        </p>

        <div className="flex items-center mt-2">
          Tham gia cộng đồng CTV Reco để được hỗ trợ nhanh nhất{" "}
          <a
            href="https://zalo.me/g/frthxa044"
            target="_blank"
            rel="noreferrer"
          >
            <Image
              src="/img/zalo.png"
              width={24}
              className="ml-2"
              preview={false}
            />
          </a>
          <a href="https://t.me/reco_support" target="_blank" rel="noreferrer">
            <Image
              src="/img/telegram.svg"
              width={24}
              className="ml-4"
              preview={false}
            />
          </a>
        </div>
        <Row justify="center" className="mt-4">
          <Col xs={8}>
            <AppButton typebutton="primary" onClick={redirectLogin}>
              Đăng nhập
            </AppButton>
          </Col>
        </Row>
      </div>
    </AppModal>
  );
}

export default React.memo(ModalRegisterSuccess);
