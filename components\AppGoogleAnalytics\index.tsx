import Script from "next/script";

export default function GoogleAnalytics(): JSX.Element {
  return (
    <>
      <Script
        async
        src="https://www.googletagmanager.com/gtag/js?id=G-K49EG88605"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-K49EG88605');
        `}
      </Script>
    </>
  );
}
