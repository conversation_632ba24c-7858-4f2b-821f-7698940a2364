import ApiLinkedinSearch from "@app/api/ApiLinkedinSearch";
import {TextInput} from "@app/components/TextInput";
import {CandidateInfo} from "@app/module/linkedinSearch/SearchResult";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {Formik, FormikProps} from "formik";
import _ from "lodash";
import React, {useCallback, useEffect, useRef, useState} from "react";
import {useMutation} from "react-query";
import SearchResult from "../SearchResult";

interface IBooleanSearch {
  booleanSearch?: string;
}

function BooleanSearch({booleanSearch}: IBooleanSearch): JSX.Element {
  const initialValues: any = {
    booleanSearch: "",
  };

  const [searchResult, setSearchResult] = useState<CandidateInfo[]>([]);

  const formikRefBooleanSearch = useRef<FormikProps<any>>(null);

  const handleSearchAllCandidate = (): void => {
    const booleanSearch = formikRefBooleanSearch.current?.values.booleanSearch;
    searchLinkedin.mutate(booleanSearch);
  };

  const handleSearchDebounce = useCallback(
    _.debounce(() => {
      handleSearchAllCandidate();
    }, deadTimeFastSearch),
    [handleSearchAllCandidate]
  );

  const searchLinkedin = useMutation(
    (param: string) => {
      return ApiLinkedinSearch.searchLinkedin(param);
    },
    {
      onSuccess: (data) => {
        setSearchResult(data);
      },
      onError: () => {
        setSearchResult([]);
      },
    }
  );

  useEffect(() => {
    if (booleanSearch) {
      formikRefBooleanSearch.current?.setFieldValue(
        "booleanSearch",
        booleanSearch
      );
      searchLinkedin.mutate(booleanSearch);
    }
  }, [booleanSearch]);

  return (
    <div>
      <Formik
        initialValues={initialValues}
        innerRef={formikRefBooleanSearch}
        onSubmit={handleSearchAllCandidate}
      >
        {({values, handleSubmit}): JSX.Element => (
          <form onSubmit={handleSubmit}>
            <TextInput
              label="Boolean search"
              placeholder={`"Android" AND "HaNoi"`}
              onChange={handleSearchDebounce}
              name="booleanSearch"
              value={values?.booleanSearch || ""}
            />
          </form>
        )}
      </Formik>

      <div className="overflow-auto h-[590px] mt-4">
        <SearchResult
          candidates={searchResult}
          isLoading={searchLinkedin.isLoading}
        />
      </div>
    </div>
  );
}

export default BooleanSearch;
