# Tạo page

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

Như đã đề cập tại `Getting started/Folder structure.md`, `pages` và `modules` là một cặp. Trong project thông thường thì sẽ chỉ cần 1 trong 2 folder này nhưng với next.js thì sẽ cần cả 2. `pages` chứa những file khá nhỏ, chúng có nhiệm vụ routing request, khởi tạo server data và trỏ đến code trong `modules` tương ứng. `modules` là nơi chứa nhiều react component và logic chính của page.

Lí do của việc phân chia này là do Next.js sẽ coi tất cả components đặt trong folder `pages` là một page, đọc thêm https://stackoverflow.com/a/********.

## 2. Tạo page

```
📦src
 ┣ 📂module
 ┃ ┣ 📂home
 ┃ ┃ ┣ 📂components
 ┃ ┃ ┃ ┣ 📂ModalConfirm
 ┃ ┃ ┃ ┃ ┣ 📃index.scss
 ┃ ┃ ┃ ┃ ┣ 📜index.tsx
 ┃ ┃ ┣ 📃index.scss
 ┃ ┃ ┣ 📜index.tsx
 ┣ 📂pages
 ┃ ┣ 📂home
 ┃ ┃ ┣ 📜index.tsx
 ┃ ┃ ┗ 📜user.tsx
 ┃ ┣ 📜404.tsx
 ┃ ┣ 📜_app.tsx
 ┃ ┗ 📜...


```

_Routing_:  
Đ<PERSON> tạo một profile setting page với path là `http://localhost:1234/home` thì sẽ cần tạo file `📂pages/📂home/📜index.tsx`.

Trong trường hợp config private, public cho page thì sẽ cho vào ở file RouteList.tsx

```
📦src
 ┣ 📂routes
 ┃ ┣ 📜index.tsx
 ┃ ┗ 📜RouteList.tsx
```

```tsx
// Các biến có thể config được cho route
export interface IRoute {
  path: string;  // Đường link đến trang. Ví dụ đối với http://localhost:1234/home thì path sẽ là /home
  name: string; // Tên của trang 
  role?: Array<IAccountRole>; // Role có thể dùng để truy cập trang
  isSidebar?: boolean; // Để là true nếu muốn route xuất hiện trong Side bar
  icon?: string; // Icon cho route thường dùng trong trường hợp isSidebar = true
  isPrivate?: boolean; // Để là true nếu muốn người dùng phải đăng nhập để truy cập route
  isPublic?: boolean; // Để là true nếu không muốn xuất hiện sidebar cho trang. Thường sử dụng cho các trang như home page hoặc about
  children?: IRoute[]; // Khai báo thêm các route con thuộc route cha này
} // Tuỳ theo project có thể sẽ có thêm những biến config khác

// Khai báo route
const routes: IRoute[] = [
  {
    path: "/",
    name: "Home",
    icon: "Homepage",
    isSSR: true,
    isSidebar: true,
  },
  {
    path: "/account-manager",
    name: "Quản lý tài khoản",
    role: [IAccountRole.ADMIN],
    icon: "Homepage",
    isSidebar: true,
  },
 ]

```

_Module_:  
Tương ứng với file home trên sẽ có một folder tương ứng trong module:

```
📂module
┣ 📂home
┃ ┣ 📂components
┃ ┃ ┣ 📂ModalConfirm
┃ ┃ ┃ ┣ 📃index.scss
┃ ┃ ┃ ┣ 📜index.tsx
┃ ┣ 📃index.scss
┃ ┣ 📜index.tsx
```

Một page thường sẽ có cấu trúc như trên, gồm nhiều components tạo nên page. Những components này chỉ dùng cho page này nên chúng sẽ được đặt tại đây.

## 3. Server side rendering

SSR được sử dụng tại đây có 2 mục đích chính:

- Hỗ trợ SEO
- Cải thiện tốc độ render ban đầu.

Dựa theo mục đích trên thì những page public(có thể vào mà không cần login, vd: home page,...) cần được render toàn bộ nội dung thiết yếu tại backend(nextjs), những page private(vd: user setting, ...) thì không cần thiết render toàn bộ thông tin trên server(nextjs).

Đọc thêm tại đây: https://nextjs.org/docs/basic-features/pages
