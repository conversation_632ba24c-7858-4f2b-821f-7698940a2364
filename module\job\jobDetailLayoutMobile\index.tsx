import {IDataRequestJobDetail} from "@app/api/ApiRequestJob";
import Icon from "@app/components/Icon/Icon";
import {salaryRange} from "@app/utils/constants/function";
import {Tooltip} from "antd";
import React from "react";
import {getAddress} from "../jobDetail";
import HtmlComponent from "@app/components/HtmlComponent";
import "./index.scss";

interface Props {
  data: IDataRequestJobDetail;
}

function JobDetailLayoutMobile(props: Props) {
  const {data} = props;

  const value1 = [
    {
      icon: "circle-dolar",
      value: salaryRange(
        data?.salaryFrom,
        data?.salaryTo,
        data?.currencyTypeId
      ),
      label: "salary",
    },
    {
      icon: "map-pin-line-pin-line",
      value: getAddress(
        data?.districtNameCombined,
        data?.workLocationNameCombined,
        data?.detailAddress
      ),
      label: "workLocation",
    },
  ];

  const value2 = [
    {
      icon: "briefcase-2-line",
      value: data?.experienceYearFrom
        ? ` Từ ${data?.experienceYearFrom} năm
  kinh nghiệm`
        : "<PERSON>hông yêu cầu kinh nghiệm ",
      label: "experience",
    },
    {
      icon: "group-line",
      value: `${data?.headcount || 1} headcount`,
      label: "headcount",
    },
  ];

  return (
    <div className="mobile-container-job-detail mt-2">
      <h2 className="mobile-container-job-detail__name font-bold text24">
        {data?.name || "N/A"}
      </h2>
      <div className="flex mt-4 w-full text14">
        <div className="w-1/2">
          {value1.map((i) => (
            <div
              key={i.icon}
              className="flex items-center truncate text-ellipsis"
            >
              <Icon size={16} icon={i.icon} />
              <Tooltip title={i.value}>
                <p className="ml-2 truncate text-ellipsis">{i.value}</p>
              </Tooltip>
            </div>
          ))}
        </div>
        <div className="w-1/2">
          {value2.map((i) => (
            <div
              key={i.icon}
              className="flex items-center truncate text-ellipsis"
            >
              <Icon size={16} icon={i.icon} />
              <Tooltip title={i.value}>
                <p className="ml-2 truncate text-ellipsis">{i.value}</p>
              </Tooltip>
            </div>
          ))}
        </div>
      </div>
      <div className="text14">
        <p className="font-bold text14 mt-4 mb-2">Mô tả công việc</p>
        <HtmlComponent
          htmlString={data?.description || "Không có dữ liệu"}
          classNameContainer="pl-4"
        />
        <p className="flex font-bold text14 my-2">Yêu cầu công việc</p>
        <HtmlComponent
          htmlString={data?.requestDetail || "Không có dữ liệu"}
          classNameContainer="pl-4"
        />

        <p className="flex font-bold text14 my-2">Phúc lợi và chính sách</p>
        <HtmlComponent
          htmlString={data?.benefit || "Không có dữ liệu"}
          classNameContainer="pl-4"
        />
        <p className="flex font-bold text14 my-2">Quy trình phỏng vấn</p>
        <HtmlComponent
          htmlString={data?.interviewProcess || "Không có dữ liệu"}
          classNameContainer="pl-4"
        />
      </div>
    </div>
  );
}

export default JobDetailLayoutMobile;
