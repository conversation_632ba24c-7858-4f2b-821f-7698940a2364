.sidebar {
  background-color: $sidebar-background;
  width: $sidebar-width;
  top: 0;
  left: -$sidebar-width;
  transition: left $transition-layout-time;
  border-right-width: 1px;
  border-right-color: #f3f3f4;
  //overflow: auto;
  z-index: 900;
  box-shadow: 4px 0px 4px $shadow;
  position: sticky;
  height: 100vh;
  min-width: $sidebar-width;

  &::-webkit-scrollbar {
    width: 0;
  }

  &.open {
    left: 0;
    -webkit-box-shadow: 5px 0px 30px 0px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 5px 0px 30px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 5px 0px 30px 0px rgba(0, 0, 0, 0.2);
  }

  .ant-menu-inline {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .ant-menu-item-selected {
    background-color: rgba(255, 0, 0, 0.3) !important;
    width: 95%;
    border-radius: 5px;

    &:after {
      border-right-color: rgba(255, 0, 0, 0);
    }
  }

  .logo-container {
    // padding: $spacer;
    justify-content: space-between;
    display: flex;
    h1 {
      color: white;
    }
  }

  .sidebar-item {
    display: flex;
    align-items: center;
    margin: 0.5rem 0 0.5rem 0;
    padding: 1rem 24px 1rem 24px;
    border-radius: 8px;
    color: rgb(255, 255, 255) !important;

    transition: background-color 0.3s;

    background: white;

    &:hover {
      background-color: rgb(255, 255, 255);
    }

    svg {
      width: 20px;
      height: 20px;
      color: $text-color-input;
      margin-right: 10px;
    }

    span {
      color: $text-color-input;
    }
  }
}

.sidebar-overlay {
  background-color: rgb(255, 255, 255);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  display: block;
  visibility: hidden;
  transition: visibility $transition-layout-time,
    opacity $transition-layout-time;
  z-index: 1000;

  &:hover {
    cursor: pointer;
  }

  &.open {
    visibility: visible;
    opacity: 0.3;
  }
}

.custom-card-view {
  width: 100%;
  overflow: hidden;
  borderendendradius: 20px;
  borderendstartradius: 20px;
  boxshadow: $sidebar-boxshadow-background;
}

.text-role {
  color: #9d9d9d;
}
