/* eslint-disable jsx-a11y/no-static-element-interactions */
import {Image, Popover} from "antd";
import "./index.scss";
import AppLoading from "@app/components/AppLoading";
import NotResult from "../NotSearchResult";

export interface CandidateInfo {
  address: string;
  educations: {
    organization: string;
    majors: string;
    degree: string;
    startTime: string;
    endTime: string;
  }[];
  experiences: {
    organization: string;
    level: string;
    startTime: string;
    endTime: string;
  }[];
  languages: {
    language: string;
    level: string;
  }[];
  name: string;
  skills: string[];
  socialLinks: object;
  experienceYear: number;
}

export interface SearchResultProps {
  isLoading?: boolean;
  candidates: CandidateInfo[];
}

export default function SearchResult(props: SearchResultProps): JSX.Element {
  const {candidates, isLoading} = props;

  const candidateSkill = (skills: string[]): JSX.Element => (
    <div className="flex flex-wrap gap-2 w-[200px] pr-4 max-h-96 overflow-auto">
      {skills?.map((skill, index) => (
        <span
          key={index}
          className="w-max h-fit py-1 px-[10px] rounded-lg bg-[#D9D9D9] text-xs flex items-center text-[#324054] font-normal"
        >
          {skill || "N/A"}
        </span>
      ))}
    </div>
  );

  const handelToggleExperience = (index: number): void => {
    const elementToggle = document.querySelectorAll(
      `.collapse-expand-exp-${index}`
    );
    const textBtnToggle = (
      document.getElementById(`btn-toggle-exp-${index}`) as any
    ).textContent;
    if (textBtnToggle === "Xem thêm") {
      elementToggle.forEach((element) => {
        element.classList.remove("hidden");
        element.classList.add("truncate-2-lines");
      });
      (document.getElementById(`btn-toggle-exp-${index}`) as any).textContent =
        "Thu gọn";
    } else {
      elementToggle.forEach((element) => {
        element.classList.add("hidden");
        element.classList.remove("truncate-2-lines");
      });
      (document.getElementById(`btn-toggle-exp-${index}`) as any).textContent =
        "Xem thêm";
    }
  };

  return (
    <>
      <div className="p-3 font-normal text-[#324054] text-base border-b-[1px] border-[#d1d0d0]">
        Danh sách ứng viên
      </div>

      <div className="container-linkedin-search__result-content">
        {!isLoading &&
          candidates.length > 0 &&
          candidates?.map((candidate, indexCandidate) => {
            return (
              <div
                className="item-box m-3 mt-2 p-3 rounded-lg border-[1px] border-solid border-[rgba(157, 157, 157, 0.5)]"
                key={indexCandidate}
              >
                <div className="font-normal text-2xl text-[#324054]">
                  {candidate.name}
                </div>
                <div className="flex gap-6">
                  <div className="w-1/2">
                    <div className="flex gap-3 my-1 text-sm text-[#324054] font-normal">
                      <div>{candidate.address}</div>
                      <div>~</div>
                      <div>
                        {candidate.experienceYear < 1
                          ? "Ít hơn 1 "
                          : Math.floor(candidate.experienceYear) + "+ "}
                        năm kinh nghiệm
                      </div>
                    </div>
                    {candidate.skills?.length > 0 && (
                      <Popover
                        content={candidateSkill(candidate.skills)}
                        title={`Kỹ năng (${candidate.skills?.length || 0})`}
                        placement="rightTop"
                      >
                        <div className="w-max h-6 py-1 px-[10px] rounded-lg bg-[#D9D9D9] text-[#324054] text-xs flex items-center cursor-pointer">
                          Kỹ năng ({candidate.skills?.length || 0})
                        </div>
                      </Popover>
                    )}
                  </div>
                  <div className="w-1/2">
                    <div className="text-xs text-[#324054] font-bold">
                      Social link
                    </div>
                    <div className="mt-1">
                      {candidate.socialLinks &&
                        Object.keys(candidate.socialLinks).map(
                          (social, idx) => (
                            <a
                              href={(candidate.socialLinks as any)[social]}
                              target="_blank"
                              key={idx}
                              rel="noreferrer"
                              className="mr-2"
                            >
                              <Image
                                width={16}
                                height={16}
                                src={`/img/${social}.svg`}
                                preview={false}
                                alt={social}
                              />
                            </a>
                          )
                        )}
                    </div>
                  </div>
                </div>
                <div className="flex gap-6">
                  <div className="w-1/2">
                    {candidate.experiences.length > 0 && (
                      <div className="font-bold text-xs mt-3 mb-1">
                        Kinh nghiệm
                      </div>
                    )}
                    {candidate.experiences?.map((exp, indexExp) => (
                      <div
                        key={indexExp}
                        className={`text-sm text-[#324054] font-normal w-full ${
                          indexExp > 2
                            ? `collapse-expand-exp-${indexCandidate} hidden`
                            : "truncate-2-lines"
                        }`}
                      >
                        {exp.level || "N/A"}
                        <span className="ml-1 text-[#9D9D9D]">
                          @ {exp.organization}{" "}
                          {(exp.startTime || exp.endTime) &&
                            `(${exp.startTime || "N/A"} - ${
                              exp.endTime || "N/A"
                            })`}
                        </span>
                      </div>
                    ))}
                    {candidate.experiences?.length > 3 && (
                      <div
                        className="text-[#0078D4] text-xs font-normal cursor-pointer"
                        id={`btn-toggle-exp-${indexCandidate}`}
                        onClick={(): void =>
                          handelToggleExperience(indexCandidate)
                        }
                      >
                        Xem thêm
                      </div>
                    )}
                  </div>
                  <div className="w-1/2">
                    {candidate.educations.length > 0 && (
                      <div className="font-bold text-xs mt-3 mb-1">Học tập</div>
                    )}
                    {candidate.educations.length > 0 &&
                      candidate.educations.map((education, indexEdu) => (
                        <div
                          key={indexEdu}
                          className="text-sm text-[#324054] font-normal truncate-2-lines w-full"
                        >
                          {education.majors || "N/A"}
                          <span className="ml-1 text-[#9D9D9D]">
                            @ {education.organization || "N/A"}{" "}
                            {(education.startTime || education.endTime) &&
                              `(${education.startTime || "N/A"} - ${
                                education.endTime || "N/A"
                              })`}
                          </span>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            );
          })}
        {!isLoading && !candidates.length && <NotResult />}
        {isLoading && <AppLoading />}
      </div>
    </>
  );
}
