import {Row} from "antd";
import "./index.scss";
import {StackBarChart} from "@app/components/StackBarChart";
import {useQuery} from "react-query";
import ApiPaymentBonus from "@app/api/ApiPaymentBonus";
import {listPolicyIntroCollaborator} from "@app/utils/constants/state";
import {formatMoney} from "@app/utils/constants/function";
import AppButton from "@app/components/AppButton";
import {useRef} from "react";

export default function CTVManagerBonus(): JSX.Element {
  const linkRef = useRef<any>();

  const getCurrentPartnerProfile = useQuery(["currentPartnerProfile"], () => {
    return ApiPaymentBonus.getCurrentPartnerProfile();
  });

  const paymentBonusPolicy = useQuery(["paymentBonusPolicy"], () => {
    return ApiPaymentBonus.paymentBonusPolicy();
  });

  const getSummaryBonus = useQuery(["getSummaryBonus"], () => {
    return ApiPaymentBonus.getSummaryBonus();
  });

  const copyUrl = (): void => {
    if (linkRef.current) {
      linkRef.current.select();
      document.execCommand("copy");
    }
  };

  const getCount = (stage: string, status: string | null): number => {
    const appCounts = getSummaryBonus.data?.appCounts;

    if (!appCounts || appCounts.length === 0) {
      return 0;
    }

    const itemCount = appCounts.find(
      (e: any) => e.stage === stage && e.status === status
    );

    return itemCount?.count || 0;
  };

  const getUrlInviteCTV = (url: string) => {
    if (!url) return "";
    const pathUrl = new URL(url);

    const newUrl = `https://app.reco-vn.com/${pathUrl.pathname}`;
    return newUrl;
  };

  return (
    <div className="flex container-manager-bonus">
      <Row className="div-time w-9/12">
        <div className="div-time ">
          <div className=" flex-col ">
            <div className="flex flex-col background-abc text-center py-5 px-1 gap-8">
              <div className=" text-white font-bold">Tổng thưởng</div>
              <div className="text-white font-bold">
                {formatMoney(getSummaryBonus.data?.totalBonus, "VND")}
              </div>
            </div>
            <div className="flex flex-col background-abc text-center py-5 gap-8 px-1">
              <div className="text-white font-bold">Application bonus</div>
              <div className="text-white font-bold">
                {formatMoney(
                  getSummaryBonus.data?.totalApplicationBonus,
                  "VND"
                )}
              </div>
            </div>
            <div className="flex flex-col background-abc text-center py-5 gap-8 px-1">
              <div className="text-white font-bold">Recommendation bonus</div>
              <div className="text-white font-bold">
                {formatMoney(
                  getSummaryBonus.data?.totalRecommendationBonus,
                  "VND"
                )}
              </div>
            </div>
          </div>
          <div className=" flex-col ">
            <div className="flex flex-col background-abc text-center py-5 gap-8 px-1">
              <div className="text-white font-bold">Hot bonus</div>
              <div className="text-white font-bold">
                {formatMoney(getSummaryBonus.data?.totalHotBonus, "VND")}
              </div>
            </div>
            <div className="flex flex-col background-abc text-center py-5 gap-8 px-1">
              <div className="text-white font-bold">Performance bonus</div>
              <div className="text-white font-bold">
                {formatMoney(
                  getSummaryBonus.data?.totalPerformanceBonus,
                  "VND"
                )}
              </div>
            </div>
          </div>
        </div>
        <div>
          <span>Trạng thái application</span>
          <StackBarChart
            cancelData={[
              {
                name: "Cancel",
                x: "Review",
                y: getCount("Review", "Cancel"),
              },
              {
                name: "Cancel",
                x: "Interview",
                y: getCount("Interview", "Cancel"),
              },
              {
                name: "Cancel",
                x: "Offer",
                y: getCount("Offer", "Cancel"),
              },
            ]}
            failData={[
              {
                name: "Fail",
                x: "Review",
                y: getCount("Review", "Fail"),
              },
              {
                name: "Fail",
                x: "Interview",
                y: getCount("Interview", "Fail"),
              },
              {
                name: "Fail",
                x: "Offer",
                y: getCount("Offer", "Fail"),
              },
            ]}
            onBoardData={[
              {
                name: "Onboard",
                x: "Onboard",
                y: getCount("Offer", "Onboard"),
              },
            ]}
            // onBoardDoneData={[
            //   {
            //     name: "Onboard done",
            //     x: "Onboard",
            //     y: getCount("Offer", "Onboard done"),
            //   },
            // ]}
            passData={[
              {
                name: "Pass",
                x: "Review",
                y: getCount("Review", "Pass"),
              },
              {
                name: "Pass",
                x: "Interview",
                y: getCount("Interview", "Pass"),
              },
              {
                name: "Pass",
                x: "Offer",
                y: getCount("Offer", "Pass"),
              },
            ]}
            pendingData={[
              {
                name: "Pending",
                x: "Review",
                y: getCount("Review", "Pending"),
              },
              {
                name: "Pending",
                x: "Interview",
                y: getCount("Interview", "Pending"),
              },
              {
                name: "Pending",
                x: "Offer",
                y: getCount("Offer", "Pending"),
              },
            ]}
            processingData={[
              {
                name: "Processing",
                x: "Review",
                y: getCount("Review", null),
              },
              {
                name: "Processing",
                x: "Interview",
                y: getCount("Interview", null),
              },
              {
                name: "Processing",
                x: "Offer",
                y: getCount("Offer", null),
              },
            ]}
          />
        </div>
      </Row>
      <div className="w-3/12 text-color-primary">
        <ul className="border-dash-bonus">
          <li className="text-xs">
            Thưởng{" "}
            <span className="text-rose-700">
              {paymentBonusPolicy.data?.bonusReviewed} triệu
            </span>{" "}
            khi có {paymentBonusPolicy.data?.cvReviewed} ứng viên được gửi lịch
            phỏng vấn bởi khách hàng của Reco
          </li>
          <li className="text-xs">
            Thưởng{" "}
            <span className="text-rose-700">
              {paymentBonusPolicy.data?.bonusInterviewed} triệu
            </span>{" "}
            khi có {paymentBonusPolicy.data?.cvInterviewed} ứng viên tham gia
            phỏng vấn
          </li>
          <li className="text-xs">
            Thưởng{" "}
            <span className="text-rose-700">
              {paymentBonusPolicy.data?.bonusOnboardDone} triệu
            </span>{" "}
            khi có {paymentBonusPolicy.data?.cvOnboardDone} ứng viên đi làm và
            bảo hành thành công
          </li>
        </ul>
        <ul className="border-dash-bonus mt-3">
          <p className="text-rose-700 self-center">Chính sách giới thiệu CTV</p>
          {listPolicyIntroCollaborator.map((item, index) => (
            <li className="text-xs mt-2" key={index}>
              {item.content}
              <ul className="list-disc ml-5">
                {item.children &&
                  item.children.length > 0 &&
                  item.children.map((i, ind) => (
                    <li className="text-xs mt-2" key={ind}>
                      {i.content}
                    </li>
                  ))}
              </ul>
            </li>
          ))}
          <Row className="items-center row-link-intro mt-2">
            <div className="flex-1 grid div-link">
              <input
                className="link-intro"
                ref={linkRef}
                type="text"
                value={getUrlInviteCTV(
                  getCurrentPartnerProfile.data?.recommendationLink as string
                )}
                readOnly
              />
            </div>
            <AppButton typebutton="primary" label="Copy" onClick={copyUrl} />
          </Row>
        </ul>
      </div>
    </div>
  );
}
