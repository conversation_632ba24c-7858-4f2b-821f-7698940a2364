import ApiCandidate, {ICandidateJapan} from "@app/api/ApiCandidate";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import {TextInput} from "@app/components/TextInput";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {ColumnsType} from "antd/lib/table";
import {Formik} from "formik";
import React, {useEffect, useRef, useState} from "react";
import {useQuery} from "react-query";
import ModalDetail from "./ModalDetail";

export default function CandidateJapan(): JSX.Element {
  const [valuesSearch, setValuesSearch] = useState<{
    pageSize: number;
    currentPage: number;
    textSearch: string;
  }>({
    currentPage: 1,
    pageSize: 20,
    textSearch: "",
  });
  const [candidate, setCandidate] = useState<ICandidateJapan>(
    {} as ICandidateJapan
  );
  const timeOut = useRef<any>();

  const getListCandidateJapan = useQuery(
    ["getListCandidateJapan", valuesSearch],
    () => {
      return ApiCandidate.getListCandidateJapan(valuesSearch);
    }
  );

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const onChangeTextSearch = (e: any): void => {
    timeOut.current = setTimeout(() => {
      setValuesSearch({
        currentPage: 1,
        pageSize: 20,
        textSearch: e.target.value?.trim() || "",
      });
    }, deadTimeFastSearch);
  };

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesSearch({
      ...valuesSearch,
      currentPage: page,
      pageSize: pageSize,
    });
  };

  const columns: ColumnsType<ICandidateJapan> = [
    {
      title: "Tên nhân viên",
      dataIndex: "fullName",
      key: "fullName",
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
    },
    {
      title: "Vị trí",
      dataIndex: "currentPosition",
      key: "currentPosition",
    },
    {
      title: "Địa điểm",
      dataIndex: "currentLocation",
      key: "currentLocation",
    },
    {
      title: "Vị trí ứng tuyển",
      dataIndex: "requestPosition",
      key: "requestPosition",
    },
    {
      title: "Ngày ứng tuyển",
      dataIndex: "createdDate",
      key: "createdDate",
    },
  ];

  return (
    <div className="container-manager-candidate-japan">
      <Formik
        initialValues={{textSearch: ""}}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          return (
            <TextInput
              containerclassname="w-1/2"
              label="Tìm kiếm nhanh"
              name="textSearch"
              value={values.textSearch}
              onChange={onChangeTextSearch}
              placeholder="Nhập tên ứng viên"
            />
          );
        }}
      </Formik>
      <div className="mt-6">
        <AppTable
          rowClassName="cursor-pointer"
          dataSource={getListCandidateJapan.data?.dataPaging?.map(
            (item: ICandidateJapan, index: number) => ({
              ...item,
              key: index,
            })
          )}
          columns={columns}
          loading={getListCandidateJapan.isLoading}
          scroll={{y: "60vh"}}
          onRow={(item: ICandidateJapan): any => {
            return {
              onClick: (): void => {
                setCandidate(item);
              },
            };
          }}
        />
      </div>
      <AppPagination
        className="mt-6"
        defaultPageSize={valuesSearch.pageSize}
        current={valuesSearch.currentPage}
        pageSize={valuesSearch.pageSize}
        total={getListCandidateJapan.data?.totalCount}
        onChange={handlePagination}
      />
      <ModalDetail
        open={!!candidate.selfRegisterId}
        onCloseModal={(): void => setCandidate({} as ICandidateJapan)}
        candidate={candidate}
      />
    </div>
  );
}
