import "./index.scss";
import {Row} from "antd";
import React, {useEffect, useMemo, useRef, useState} from "react";
import ListCandidateTypeTable from "./ListCandidateTypeTable";
import {useQuery} from "react-query";
import FilterData from "./FilterData";
import ApiApplication from "@app/api/ApiApplication";
import ModalDetailApplication from "./ModalDetailApplication";
import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole, OptionSelect} from "@app/types";
// eslint-disable-next-line import/no-cycle
import {useSelector, useDispatch} from "react-redux";
import {useRouter} from "next/router";
// eslint-disable-next-line import/no-cycle
import {IRootState} from "@app/redux/store";
import {
  setGroupByFieldApplication,
  setGroupByList,
  setLoadingApplication,
  setTotalCount,
} from "@app/redux/slices/GroupByApplication";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {mapDataOptionHasId} from "@app/utils/constants/function";
import moment from "moment";
import {
  DATE_FORMAT,
  NORMAL_DATE_FORMAT,
} from "@app/utils/constants/formatDateTime";

export interface FormFilter {
  candidateInformation: string;
  customerName: string;
  requestJobName: string;
  workingLocationIds: OptionSelect[];
  stages: OptionSelect[];
  statuses: OptionSelect[];
  creators: OptionSelect[];
  from: string | moment.Moment;
  to: string | moment.Moment;
  currentPage?: number;
  roleFilterSelected?: null | OptionSelect;
  services: OptionSelect[] | any[];
  managerId: OptionSelect | null;
}

export default function ManagerApplication(): JSX.Element {
  // const [typeDisplay, setTypeDisplay] = useState<"table" | "column">("table");
  const {user, queryParamsPages} = useSelector(selectUser);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [applicationId, setApplicationId] = useState<number>(-1);
  const [isShowDetail, setIsShowDetail] = useState<boolean>(false);
  const refTableApplication = useRef<any>();

  const urlSearchParams = new URLSearchParams(
    queryParamsPages?.managerApplication || ""
  );
  const searchParams = Object.fromEntries(urlSearchParams);

  const {groupByFieldApplication} = useSelector(
    (state: IRootState) => state.groupByApplication
  );
  const router = useRouter();
  const dispatch = useDispatch();

  let idApplication: string | undefined;
  if (router.query.id) {
    idApplication = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    idApplication = Object.fromEntries(searchParams)?.id;
  }

  const isCS =
    user?.role &&
    user.role?.filter((item) =>
      [IAccountRole.CSL, IAccountRole.CST].some((i) => i === item)
    )?.length > 0;

  const isAM =
    user?.role &&
    user.role?.filter((item) =>
      [IAccountRole.AMG, IAccountRole.AML].some((i) => i === item)
    )?.length > 0;

  const isBD =
    user?.role &&
    user.role?.filter((item) =>
      [IAccountRole.BD, IAccountRole.BDL].some((i) => i === item)
    )?.length > 0;

  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);

  const isCTV = user?.role?.includes(IAccountRole.CTV);

  const initialValuesFilter: FormFilter = {
    candidateInformation: "",
    customerName: searchParams?.customerName || "",
    requestJobName: "",
    workingLocationIds: searchParams?.workingLocationIds
      ? JSON.parse(searchParams?.workingLocationIds)
      : [],
    stages: searchParams?.stages ? JSON.parse(searchParams?.stages) : [],
    statuses: searchParams?.statuses ? JSON.parse(searchParams?.statuses) : [],
    creators: searchParams?.creators ? JSON.parse(searchParams?.creators) : [],
    services: searchParams?.services ? JSON.parse(searchParams?.services) : [],
    managerId: searchParams?.managerId
      ? JSON.parse(searchParams?.managerId)
      : null,
    from: searchParams?.from
      ? moment(searchParams?.from, NORMAL_DATE_FORMAT)
      : "",
    to: searchParams?.to ? moment(searchParams?.to, NORMAL_DATE_FORMAT) : "",
    currentPage: 1,
    roleFilterSelected:
      !isAdmin && isCS && isAM
        ? {value: IAccountRole.CSL, label: "CST/CSL"}
        : null,
  };

  const [valuesSearch, setValuesSearch] = useState<FormFilter>({
    ...initialValuesFilter,
  });

  const valuesFilter = {
    ...valuesSearch,
    pageSize: pageSize,
    currentPage: currentPage,
    stages: mapDataOptionHasId(valuesSearch.stages),
    statuses:
      valuesSearch.stages.length > 0
        ? valuesSearch.statuses?.map((i) => ({
            ...i,
            id: i.value === "Chưa hoàn thành" ? null : i.key,
          }))
        : [],
    workingLocationIds:
      valuesSearch?.workingLocationIds?.map((item) => item?.key || "0") || [],
    creators: mapDataOptionHasId(valuesSearch.creators),
    isAdvanceSearch: true,
    from: valuesSearch?.from
      ? moment(valuesSearch?.from).format(DATE_FORMAT)
      : "",
    to: valuesSearch?.to ? moment(valuesSearch?.to).format(DATE_FORMAT) : "",
    roleFilter: valuesSearch.roleFilterSelected?.value
      ? valuesSearch.roleFilterSelected.value
      : null,
    managerId: (valuesSearch?.managerId?.value
      ? valuesSearch?.managerId?.value
      : valuesSearch?.managerId) as any,
    services: valuesSearch?.services?.map((i) => i?.value || i),
  };

  useEffect(() => {
    dispatch(
      setGroupByFieldApplication({
        ...groupByFieldApplication,
        applicationSearch: valuesFilter as any,
      })
    );
  }, []);

  const typeRoleUsed = useMemo((): string => {
    if (isAdmin) {
      return IAccountRole.ADMIN;
    }
    // role admin , có cả 2 role CS và AM khi chọn CS
    // chỉ có role CST và CSL
    if (isAM && isCS) {
      return IAccountRole.AML;
    }

    if (
      valuesSearch.roleFilterSelected?.value === IAccountRole.CSL ||
      (!isAM && isCS)
    ) {
      return IAccountRole.CSL;
    }

    // role admin , có cả 2 role CS và AM khi chon AM
    // chỉ có role AMG và AML
    if (
      valuesSearch.roleFilterSelected?.value === IAccountRole.AML ||
      (isAM && !isCS)
    ) {
      return IAccountRole.AML;
    }

    if (isBD) {
      return IAccountRole.BD;
    }

    return IAccountRole.CTV;
  }, [JSON.stringify(user?.role), valuesSearch.roleFilterSelected?.value]);

  useEffect(() => {
    if (idApplication) {
      showModalDetail(Number(idApplication));
    }
  }, [idApplication]);

  const dataManagers = useQuery(
    ["getManagers"],
    () => {
      return ApiApplication.getDataManagers();
    },
    {
      enabled: !isCTV,
    }
  );

  const optionDataManager = useMemo<Array<OptionSelect>>(() => {
    const result: Array<OptionSelect> =
      dataManagers.data?.map((i) => ({
        label: i.managerName,
        value: i.managerId.toString(),
        key: i.managerId.toString(),
      })) || [];

    return result;
  }, [dataManagers.data]);

  const dataListApplications = useQuery(
    [
      "dataListApplications",
      currentPage,
      pageSize,
      valuesSearch,
      // groupByFieldApplication,
      queryParamsPages,
    ],
    () => {
      dispatch(setLoading(true));

      return ApiApplication.getListApplications(valuesFilter);
    },
    {
      onSettled() {
        dispatch(setLoading(false));
      },
    }
  );

  useQuery(
    ["groupByApplication", groupByFieldApplication],
    () => ApiApplication.filterGroupByApplication(groupByFieldApplication),
    {
      enabled: !!groupByFieldApplication.groupByRequest.groupBy,
      onSuccess(data) {
        dispatch(setTotalCount(data.totalCount || 0));
        dispatch(setGroupByList(data.data));
      },
    }
  );

  useQuery(
    ["groupByApplication", groupByFieldApplication],
    () => {
      dispatch(setLoading(true));
      dispatch(setLoadingApplication(true));
      return ApiApplication.filterGroupByApplication(groupByFieldApplication);
    },
    {
      enabled: !!groupByFieldApplication.groupByRequest.groupBy,
      onSuccess(data) {
        dispatch(setTotalCount(data.totalCount || 0));
        dispatch(setGroupByList(data.data));
      },
      onSettled() {
        dispatch(setLoading(false));
        dispatch(setLoadingApplication(false));
      },
    }
  );

  const showModalDetail = (id: number): void => {
    setApplicationId(id);
    setIsShowDetail(true);
  };

  const closeModalDetail = (): void => {
    setIsShowDetail(false);
    window.history.pushState({}, "", router.route);
  };

  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  return (
    <div className="container">
      <FilterData
        setCurrentPage={setCurrentPage}
        updateValueFilter={setValuesSearch}
        columnsAll={refTableApplication.current?.columnsAll}
        initialValuesFilter={initialValuesFilter}
        typeRoleUsed={typeRoleUsed}
        dataListApplications={dataListApplications}
        optionDataManager={optionDataManager}
      />
      <Row className="row-title mt-4">
        <span className="number-cv">
          {dataListApplications.data?.totalCount} Hồ Sơ
        </span>
      </Row>
      <div className="container-list-candidate">
        <ListCandidateTypeTable
          ref={refTableApplication}
          dataListApplications={dataListApplications}
          handlePagination={handlePagination}
          currentPage={currentPage}
          pageSize={pageSize}
          setApplicationData={showModalDetail}
          tableType="application"
        />
        <ModalDetailApplication
          dataListApplications={dataListApplications}
          applicationId={applicationId}
          open={isShowDetail}
          closeModal={closeModalDetail}
          typeRoleUsed={typeRoleUsed}
        />
      </div>
    </div>
  );
}
