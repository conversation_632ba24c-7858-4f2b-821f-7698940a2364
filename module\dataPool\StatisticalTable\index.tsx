import {Col, DatePicker, Row} from "antd";
import React, {useRef, useState} from "react";
import "./index.scss";
import {Formik, FormikProps} from "formik";
import {SelectInput} from "@app/components/SelectInput";
import AppModal from "@app/components/AppModal";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppTable from "@app/components/AppTable";
import {ColumnsType} from "antd/lib/table";
import {useQuery} from "react-query";
import AppPagination from "@app/components/AppPagination";
import ApiStaff, {
  IParamsStatistical,
  IStatisticalObject,
  IUserInfo,
} from "@app/api/ApiStaff";
import moment, {Moment} from "moment";
import {OptionSelect} from "@app/types";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import Link from "next/link";
import ApiCandidate from "@app/api/ApiCandidate";

interface ModalInfoProps {
  isModalVisible: boolean;
  handleOk?: () => void;
  handleCancel: () => void;
  allUsersData: any;
  typeData?: "candidate" | "pool";
}

interface IFormStatisticalValue {
  creator?: OptionSelect[];
  rangeDate: [string | Moment | null, string | Moment | null] | null;
}

const initialValues: IFormStatisticalValue = {
  creator: [],
  rangeDate: null,
};

const {RangePicker} = DatePicker;

function StatisticalTable(props: ModalInfoProps): JSX.Element {
  const {isModalVisible, handleOk, handleCancel, allUsersData, typeData} =
    props;
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const formikRef = useRef<FormikProps<IFormStatisticalValue>>(null);
  const timeOut = useRef<any>();

  const onCancel = () => {
    handleCancel();
    formikRef.current?.resetForm();
    handleSearch();
  };

  const mapFilterManager =
    allUsersData?.map((item: IUserInfo) => ({
      key: String(item.userId),
      label: item.userName,
      value: String(item.userId),
    })) || [];

  const columns: ColumnsType<IStatisticalObject> = [
    {
      title: "Người tạo",
      dataIndex: "creatorName",
      key: "creatorName",
      align: "left",
      render: (_, item: IStatisticalObject): any => {
        const valueSearch = formikRef?.current?.values;
        const objectQuery = {
          creatorIds: String(item.creatorId),
          ...(valueSearch?.rangeDate?.[0] && {
            createdFrom: moment(valueSearch?.rangeDate?.[0]).toISOString(),
          }),
          ...(valueSearch?.rangeDate?.[1] && {
            createdTo: moment(valueSearch?.rangeDate?.[1]).toISOString(),
          }),
        };
        const url = new URL(window.location.href);
        Object.entries(objectQuery).forEach(([key, value]): void => {
          url.searchParams.set(key, value);
        });
        const linkStatical = url.toString();
        return (
          <Link href={linkStatical} target="_blank">
            {item?.creatorName || ""}
          </Link>
        );
      },
    },
    {
      title: "Email",
      dataIndex: "creatorEmail",
      key: "creatorEmail",
    },
    {
      title: "Số lượng",
      dataIndex: "numberOfCandidates",
      key: "numberOfCandidates",
    },
  ];

  const requestStatisticalList = useQuery(
    ["requestStatisticalList", currentPage, pageSize],
    () => {
      const values = formikRef?.current?.values;
      const queryParams: IParamsStatistical = {
        createdFrom: values?.rangeDate?.length
          ? moment(values.rangeDate[0]).toISOString()
          : null,
        createdTo: values?.rangeDate?.length
          ? moment(values.rangeDate[1]).toISOString()
          : null,
        creatorIds: values?.creator?.length
          ? values?.creator?.map((item) => Number(item?.key))
          : [],
        currentPage: currentPage,
        pageSize: pageSize,
      };
      return typeData === "candidate"
        ? ApiCandidate.getCreatorStatistics(queryParams)
        : ApiStaff.getListStatistical(queryParams);
    }
  );

  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleSearch = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      if (currentPage === 1) {
        requestStatisticalList.refetch();
      } else {
        setCurrentPage(1);
      }
    }, deadTimeFastSearch);
  };

  function renderContent(): React.ReactNode {
    return (
      <div className="left-4 px-3">
        <div className="h-[80vh]">
          <div className="flex flex-col">
            <Formik
              initialValues={initialValues}
              innerRef={formikRef}
              onSubmit={() => {
                //
              }}
            >
              {({values}): JSX.Element => {
                return (
                  <form>
                    <Row className="justify-start items-center gap-4">
                      <Col xs={8}>
                        <SelectInput
                          mode="multiple"
                          name="creator"
                          labelselect="Người đăng"
                          data={mapFilterManager}
                          optionFilterProp="label"
                          free={values?.creator?.length === 0}
                          allowClear
                          onSelect={handleSearch}
                          onDeselect={handleSearch}
                          onClear={handleSearch}
                        />
                      </Col>
                      <Col xs={6}>
                        <RangePicker
                          name="rangeDate"
                          className="w-full p-[14px] rounded-lg"
                          format={DATE_FORMAT}
                          value={[
                            values?.rangeDate?.[0]
                              ? moment(values?.rangeDate?.[0])
                              : null,
                            values?.rangeDate?.[1]
                              ? moment(values?.rangeDate?.[1])
                              : null,
                          ]}
                          disabledDate={(current) => {
                            return current && current > moment().endOf("day");
                          }}
                          placeholder={["Thời gian từ", "Đến"]}
                          onCalendarChange={(event: any): void => {
                            formikRef.current?.setFieldValue(
                              "rangeDate",
                              event
                            );
                            formikRef.current?.setFieldTouched(
                              "rangeDate",
                              true,
                              false
                            );
                            handleSearch();
                          }}
                        />
                      </Col>
                    </Row>
                  </form>
                );
              }}
            </Formik>
          </div>
          <div className="py-6">
            <AppTable
              dataSource={requestStatisticalList.data?.creatorStatisticsPaging?.map(
                (item: IStatisticalObject) => ({
                  ...item,
                  key: item.creatorId,
                })
              )}
              columns={columns}
              loading={requestStatisticalList.isLoading}
              scroll={{y: "55vh"}}
            />
            <AppPagination
              className="mt-6"
              defaultPageSize={pageSize}
              current={currentPage}
              pageSize={pageSize}
              total={requestStatisticalList.data?.totalCount}
              onChange={handlePagination}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <AppModal
        className="modal-add-candidate"
        open={isModalVisible}
        onCancel={onCancel}
        footer={null}
        title="Bảng thống kê"
        onOk={handleOk}
        width="85%"
        centered
      >
        {renderContent()}
      </AppModal>
    </div>
  );
}

export default React.memo(StatisticalTable);
