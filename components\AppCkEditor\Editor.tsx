import React, {useEffect, useState} from "react";
import "./index.scss";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import dynamic from "next/dynamic";
import {Skeleton, notification} from "antd";

export interface TextEditorProps {
  label?: string;
  value?: string;
  handleChange?: (value: any) => void;
  placeholder?: string;
  containerclassname?: string;
  required?: boolean;
  touched?: boolean;
  height?: string;
  isReadOnly?: boolean;
}

const CKEditor: any = dynamic(
  () => import("@ckeditor/ckeditor5-react").then((mod) => mod.CKEditor),
  {
    ssr: false,
    loading: () => <Skeleton active />,
  }
);

function Editor(props: TextEditorProps): JSX.Element {
  const {
    label,
    value,
    handleChange,
    placeholder,
    containerclassname,
    required,
    touched,
    height,
    isReadOnly = false,
  } = props;

  const [showMessageRequired, setShowMessageRequired] =
    useState<boolean>(false);
  const [isClient, setIsClient] = useState(false);

  const handleValidate = (value: string): void => {
    if (!value && required) {
      setShowMessageRequired(true);
    } else {
      setShowMessageRequired(false);
    }
  };

  useEffect(() => {
    if (!value && required && touched) {
      setShowMessageRequired(true);
    } else {
      setShowMessageRequired(false);
    }
  }, [touched]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return <Skeleton active />;

  return (
    <div className={`app-editor  ${containerclassname}`}>
      <div className={showMessageRequired ? "app-editor__required" : ""}>
        {label && (
          <span
            className={`app-editor__label ${
              required ? "app-editor__label-required" : ""
            }`}
          >
            {label}
          </span>
        )}
        <CKEditor
          config={{
            toolbar: {
              items: ["bold", "italic", "numberedList", "bulletedList"],
              shouldNotGroupWhenFull: true,
            },
            placeholder: placeholder || "",
          }}
          editor={ClassicEditor}
          data={value || "<p></p>"}
          onChange={(e: any, editor: any): void => {
            const data: string = editor?.getData();
            handleValidate(data.trim());
            handleChange?.(data.trim());
          }}
          onBlur={(_: any, editor: any): void => {
            const data = editor.getData();
            handleValidate(data.trim());
          }}
          onReady={(editor: any): void => {
            editor?.editing?.view?.change((writer: any) => {
              writer.setStyle(
                "height",
                height || "200px",
                editor?.editing?.view?.document.getRoot()
              );
              writer.setAttribute(
                "contenteditable",
                !isReadOnly,
                editor?.editing?.view?.document.getRoot()
              );
              if (editor.ui.view.toolbar.element) {
                editor.ui.view.toolbar.element.style.display = isReadOnly
                  ? "none"
                  : "flex";
              }
            });
          }}
          onError={(err: any): void => {
            notification.error({
              message: "Thông báo",
              description: "Trình editor bị lỗi vui lòng reload lại trang",
            });
          }}
        />
        {/* {showMessageRequired && (
          <span className="message-required">{`${label} không được để trống`}</span>
        )} */}
      </div>
    </div>
  );
}

export default React.memo(Editor);
