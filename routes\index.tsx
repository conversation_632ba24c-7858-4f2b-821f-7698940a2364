import React from "react";
import DashboardLayout from "../components/Layout/DashboardLayout";
import RouteList, {IRoute} from "./RouteList";
import Config from "../config";
import {AppProps} from "next/app";
import {useRouter} from "next/router";
import LoginComponent from "@app/pages/login";
import ApiUser from "@app/api/ApiUser";
import {IAccountRole} from "@app/types";
import {useSelector} from "react-redux";
import {IRootState} from "@app/redux/store";
import LandingLayout from "@app/components/Layout/LandingLayout";

export default function Routes({
  Component,
  pageProps,
  router,
}: AppProps): JSX.Element | null {
  const routerNext = useRouter();

  const login = routerNext.pathname === Config.PATHNAME.LOGIN;
  const user = useSelector((state: IRootState) => state.user);
  const {asPath, isReady} = routerNext;

  const isRoute = (key: keyof IRoute): boolean => {
    for (const route of RouteList) {
      if (router.pathname === route.path) {
        return !!route[key];
      }
    }
    return false;
  };

  const isRouteRequireRole = (): boolean => {
    const listRoleUser =
      user.user?.roleList?.split(",")?.map((item) => item.trim()) || [];
    for (const route of RouteList) {
      if (router.pathname === route.path) {
        if (
          route.role &&
          !listRoleUser.some((item) =>
            route.role?.includes(item as IAccountRole)
          )
        ) {
          return true;
        }
        return false;
      }
    }
    return false;
  };

  const isPrivateRoute = (): boolean | undefined => {
    for (const route of RouteList) {
      if (router.pathname === route.path) {
        if (route.isPrivate === undefined) {
          if (ApiUser.isLogin()) {
            return route.isPrivate;
          }
          return true;
        }
        return route.isPrivate;
      }
    }
    return false;
  };

  const goToLogin = (): null => {
    if (isReady) {
      router.push({
        pathname: Config.PATHNAME.LOGIN,
        query: {redirect: asPath},
      });
    }

    return null;
  };

  if (typeof window === "undefined") {
    return null;
  }

  if (login) {
    return <LoginComponent />;
  }

  if (isRoute("isPublic") && isRoute("isLandingPage")) {
    return (
      <LandingLayout>
        <Component {...pageProps} />
      </LandingLayout>
    );
  }

  if (isRoute("isPublic")) {
    return <Component {...pageProps} />;
  }

  if (isRoute("isAuth")) {
    return goToLogin();
  }

  if (isPrivateRoute()) {
    if (ApiUser.isLogin()) {
      if (isRouteRequireRole()) {
        router.push(Config.PATHNAME.NOT_FOUND);
        return null;
      }
      return (
        <DashboardLayout>
          <Component {...pageProps} />
        </DashboardLayout>
      );
    }
    return goToLogin();
  }

  return (
    <DashboardLayout>
      <Component {...pageProps} />
    </DashboardLayout>
  );
}
