.modal-form-employee {
  top: 70px;

  .content {
    border: 1px dashed $header_tf;
    border-radius: 16px;
    width: 90%;
    margin: 0 auto;
  }

  .form-input {
    margin: 0 auto;
    width: 80%;
    margin-top: 12px;
  }

  .input-note {
    height: 110px;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 16px;
    color: $text-color-input;
    width: 80%;
    resize: none;
    margin: 12px 10% 0px 10%;
  }

  .btn-cancel {
    button {
      background-color: $status-reject;

      &:focus {
        background-color: $status-reject;
      }
    }

    .ant-btn[disabled] {
      background-color: $status-reject;
      opacity: 0.5;
      color: $white-color;
    }
  }
}

.modal-warning-block-employee {
  .btn-cancel {
    button {
      background-color: $status-reject;

      &:focus {
        background-color: $status-reject;
      }
    }

    .ant-btn[disabled] {
      background-color: $status-reject;
      opacity: 0.5;
      color: $white-color;
    }
  }
}
