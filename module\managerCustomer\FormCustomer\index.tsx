import AppBreadcrumb from "@app/components/AppBreadcrumb";
import config from "@app/config";
import React, {useRef, useState} from "react";
import "./index.scss";
import {useRouter} from "next/router";
import ApiCustomer, {
  ICustomerDetail,
  IParamsCreateCustomer,
  IResInitialize,
} from "@app/api/ApiCustomer";
import {useMutation, useQuery} from "react-query";
import AppAccessDenied from "@app/components/AppAccessDenied";
import {Formik, FormikProps} from "formik";
import {Col, Input, Row, notification} from "antd";
import {TextInput} from "@app/components/TextInput";
import AppLoading from "@app/components/AppLoading";
import {SelectInput} from "@app/components/SelectInput";
import {
  companyTypes,
  rankOptions,
  statusCustomers,
} from "@app/utils/constants/state";
import AppCkEditor from "@app/components/AppCkEditor";
import {OptionSelect} from "@app/types";
import AppButton from "@app/components/AppButton";
import AppModalConfirm from "@app/components/AppModalConfirm";
import {messageValidate} from "@app/utils/constants/message";
import {setAllFieldTouched} from "@app/utils/constants/function";

interface IFormCustomer extends ICustomerDetail {
  companyTypeSelected?: OptionSelect;
  countrySelected?: OptionSelect;
  managerSelected?: OptionSelect;
  customerStatusSelected?: OptionSelect;
  rankSelected?: OptionSelect;
}

interface FormCustomerProps {
  type: "edit" | "create";
}

const listBreadcrumbCreate = [
  {
    breadcrumb: "Danh sách khách hàng",
    href: config.PATHNAME.MANAGER_CUSTOMER,
  },
  {
    breadcrumb: "Tạo mới khách hàng",
    href: config.PATHNAME.CUSTOMER_CREATE,
  },
];

export default function FormCustomer(props: FormCustomerProps): JSX.Element {
  const {type} = props;
  const isTypeEdit = type === "edit";
  const router = useRouter();
  const timeOut = useRef<any>(null);
  const [profile, setProfile] = useState("");
  const [benefit, setBenefit] = useState("");
  const [interviewProcess, setInterviewProcess] = useState("");
  const [note, setNote] = useState("");
  const [fieldTouched, setFieldTouched] = useState({
    profile: false,
    interviewProcess: false,
    benefit: false,
  });

  const [isShowModalConfirm, setIsShowModalConfirm] = useState<boolean>(false);
  const formikRef = useRef<FormikProps<IFormCustomer>>(null);

  let id = "";
  if (router.query.id) {
    id = router.query.id as string;
  }

  const listBreadcrumbEdit = [
    {
      breadcrumb: "Danh sách khách hàng",
      href: config.PATHNAME.MANAGER_CUSTOMER,
    },
    {
      breadcrumb: "Thông tin chi tiết khách hàng",
      href: `${config.PATHNAME.CUSTOMER_DETAIL}?id=${id}`,
    },
    {
      breadcrumb: "Chỉnh sửa thông tin khách hàng",
      href: config.PATHNAME.CUSTOMER_EDIT,
    },
  ];

  const getDetailCustomer = useQuery(
    ["getDetailCustomer", id],
    () => {
      return ApiCustomer.getDetailCustomer(id);
    },
    {
      onSuccess: (data: IResInitialize) => {
        setBenefit(data?.customer?.benefit || "");
        setInterviewProcess(data?.customer?.interviewProcess || "");
        setProfile(data?.customer?.profile || "");
        setNote(data?.customer?.note || "");
      },
      onError: (error: any) => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            router.back();
          }, 4000);
        }
      },
    }
  );

  const editCustomer = useMutation(
    (param: ICustomerDetail) => {
      return ApiCustomer.editCustomer(param);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Cập nhật khách hàng thành công",
          duration: 3,
        });
        router.back();
      },
    }
  );

  const createCustomer = useMutation(
    (param: IParamsCreateCustomer) => {
      return ApiCustomer.createCustomer(param);
    },
    {
      onSuccess: () => {
        notification.success({
          message: "Thông báo",
          description: "Tạo khách hàng thành công",
          duration: 3,
        });
        router.back();
      },
    }
  );

  const managerFilters = getDetailCustomer.data?.managers || [];
  const countries = getDetailCustomer.data?.countries || [];
  const customer = getDetailCustomer.data?.customer || ({} as ICustomerDetail);

  const onConfirm = (): void => {
    setIsShowModalConfirm(false);
    router.back();
  };

  const save = (): void => {
    const values = formikRef.current?.values;
    const fieldsRequired = {
      name: "",
      companyTypeSelected: "",
      size: "",
      countrySelected: "",
      address: "",
      managerSelected: "",
      contact: "",
      phone: "",
      email: "",
    };
    // set all fields is touched to check validation of form
    const checkAllTouched = setAllFieldTouched(fieldsRequired);
    formikRef?.current?.setTouched(checkAllTouched);

    setFieldTouched({profile: true, interviewProcess: true, benefit: true});
    if (values) {
      if (
        !(
          values.name?.trim() &&
          values.size?.trim() &&
          values.countrySelected?.value &&
          values.address?.trim() &&
          values.customerStatusSelected?.value &&
          values.managerSelected?.value &&
          values?.companyTypeSelected?.value &&
          profile?.trim() &&
          benefit?.trim() &&
          interviewProcess?.trim() &&
          values?.contact &&
          values?.phone &&
          values?.email
        )
      ) {
        notification.error({
          message: "Thông báo",
          description: messageValidate,
          duration: 3,
        });
      } else {
        const param: ICustomerDetail = {
          ...values,
          companyType: values.companyTypeSelected?.value || "",
          country: values.countrySelected?.key || "",
          rank: values.rankSelected?.value || "",
          countryName: values.countrySelected.value,
          customerStatus: Number(values.customerStatusSelected.value),
          customerStatusName: values.customerStatusSelected.label,
          assignTo: Number(values.managerSelected?.key || 0),
          assignName: values.managerSelected.value,
          profile,
          benefit,
          interviewProcess,
          note,
        };

        if (isTypeEdit) {
          editCustomer.mutate(param);
        } else {
          createCustomer.mutate(param as IParamsCreateCustomer);
        }
      }
    }
  };

  const getCompanyTypeSelected = (companyType?: string): OptionSelect => {
    if (companyType) {
      const companyTypeSelected = companyTypes.find(
        (item) => item.value === customer?.companyType
      );
      if (companyTypeSelected) {
        return companyTypeSelected;
      }
    }
    return {} as OptionSelect;
  };

  const getRankSelected = (companyType?: string): OptionSelect => {
    if (companyType) {
      const rankSelected = rankOptions.find(
        (item) => item.value === customer?.rank
      );
      if (rankSelected) {
        return rankSelected;
      }
    }
    return {} as OptionSelect;
  };

  const getCountrySelected = (countryId?: string): OptionSelect => {
    if (countryId) {
      const countrySelected = countries?.find(
        (item) => item.countryId === customer.country
      );
      if (countrySelected) {
        return {
          key: countrySelected.countryId,
          value: countrySelected.name,
          label: countrySelected.name,
        };
      }
    }
    return {} as OptionSelect;
  };

  const getManagersSelected = (assignTo?: number): OptionSelect => {
    if (assignTo) {
      const managerSelected = managerFilters?.find(
        (item) => item.userId === customer.assignTo
      );
      if (managerSelected) {
        return {
          key: String(managerSelected.userId),
          value: managerSelected.name,
          label: managerSelected.name,
        };
      }
    }
    return {} as OptionSelect;
  };

  const getCustomerStatusSelected = (customerStatus?: number): OptionSelect => {
    if (customerStatus) {
      const customerStatusSelected = statusCustomers.find(
        (item) => item.value === String(customer?.customerStatus)
      );
      if (customerStatusSelected) {
        return customerStatusSelected;
      }
    }
    return {} as OptionSelect;
  };

  const initialValues: IFormCustomer =
    isTypeEdit && getDetailCustomer.data?.customer
      ? {
          ...customer,
          companyTypeSelected: getCompanyTypeSelected(customer?.companyType),
          countrySelected: getCountrySelected(customer.country),
          managerSelected: getManagersSelected(customer.assignTo),
          rankSelected: getRankSelected(customer.rank),
          customerStatusSelected: getCustomerStatusSelected(
            customer.customerStatus
          ),
        }
      : ({
          customerStatusSelected: {
            label: "New",
            value: "2",
            color: "#2F6BFF",
          },
        } as IFormCustomer);

  if (getDetailCustomer?.error?.errorCode === 400) {
    return <AppAccessDenied />;
  }

  const renderContent = (): JSX.Element => {
    if (getDetailCustomer.isLoading) {
      return (
        <div className=" h-[20vh]">
          <AppLoading />
        </div>
      );
    }

    return (
      <div>
        <div className="form-customer-content">
          <Formik
            initialValues={initialValues}
            onSubmit={(): void => {
              //
            }}
            innerRef={formikRef}
          >
            {({values, touched}): JSX.Element => {
              return (
                <div>
                  <div className="text16 font-bold">Thông tin khách hàng</div>
                  <Row className="justify-between mt-1">
                    <Col span={11}>
                      <TextInput
                        containerclassname="mt-2"
                        label="Tên khách hàng"
                        name="name"
                        value={values.name}
                        free={!values.name}
                        required
                        status={
                          !values?.name && touched?.name ? "error" : undefined
                        }
                        maxLength={255}
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Lĩnh vực"
                        name="field"
                        value={values.field}
                        free={!values.field}
                        maxLength={255}
                      />
                      <SelectInput
                        containerclassname="mt-2"
                        name="rankSelected"
                        labelselect="Loại khách hàng"
                        data={rankOptions}
                        value={values.rankSelected}
                        free={!values?.rankSelected?.value}
                        allowClear
                      />
                    </Col>
                    <Col span={11}>
                      <SelectInput
                        containerclassname="mt-2"
                        name="companyTypeSelected"
                        labelselect="Loại hình"
                        data={companyTypes}
                        value={values.companyTypeSelected}
                        free={!values?.companyTypeSelected?.value}
                        required
                        status={
                          !values?.companyTypeSelected?.value &&
                          touched?.companyTypeSelected
                            ? "error"
                            : undefined
                        }
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Quy mô công ty"
                        name="size"
                        value={values.size}
                        free={!values.size}
                        required
                        status={
                          !values?.size && touched?.size ? "error" : undefined
                        }
                        maxLength={255}
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Thời gian làm việc"
                        name="workingTime"
                        value={values.workingTime}
                        free={!values.workingTime}
                        maxLength={255}
                      />
                    </Col>
                  </Row>
                  <div className="text16 font-bold mt-4">Địa chỉ</div>
                  <Row className="justify-between mt-1">
                    <Col span={11}>
                      <SelectInput
                        containerclassname="mt-2"
                        name="countrySelected"
                        labelselect="Quốc gia"
                        data={countries.map((item) => ({
                          key: item.countryId,
                          value: item.name,
                          label: item.name,
                        }))}
                        value={values.countrySelected?.value}
                        free={!values?.countrySelected?.value}
                        required
                        status={
                          !values?.countrySelected?.value &&
                          touched?.countrySelected
                            ? "error"
                            : undefined
                        }
                        allowClear
                      />
                    </Col>
                    <Col span={11}>
                      <TextInput
                        containerclassname="mt-2"
                        label="Địa chỉ"
                        name="address"
                        value={values.address}
                        free={!values.address}
                        required
                        maxLength={255}
                        status={
                          !values?.address && touched?.address
                            ? "error"
                            : undefined
                        }
                      />
                    </Col>
                  </Row>
                  <div className="text16 font-bold mt-4">Quản lý</div>
                  <Row className="justify-between mt-1">
                    <Col span={11}>
                      <SelectInput
                        containerclassname="mt-2"
                        name="managerSelected"
                        labelselect="AMG quản lý"
                        data={managerFilters?.map((item) => ({
                          key: item.userId,
                          value: item.name,
                          label: item.name,
                        }))}
                        value={values.managerSelected?.value}
                        free={!values?.managerSelected?.value}
                        required
                        status={
                          !values?.managerSelected?.value &&
                          touched?.managerSelected
                            ? "error"
                            : undefined
                        }
                        allowClear
                      />
                    </Col>
                    <Col span={11}>
                      <SelectInput
                        containerclassname="mt-2"
                        name="customerStatusSelected"
                        labelselect="Trạng thái khách hàng"
                        data={statusCustomers.map((item) => ({
                          ...item,
                          disabled: isTypeEdit && item.value === "1",
                        }))}
                        value={values.customerStatusSelected}
                        free={!values?.customerStatusSelected?.value}
                        required
                        disabled={!isTypeEdit}
                        status={
                          values?.customerStatusSelected?.value
                            ? undefined
                            : "error"
                        }
                      />
                    </Col>
                  </Row>
                  <div className="text16 font-bold mt-4">Thông tin liên hệ</div>
                  <Row className="justify-between mt-1">
                    <Col span={11}>
                      <TextInput
                        containerclassname="mt-2"
                        label="Người liên hệ"
                        name="contact"
                        value={values.contact}
                        free={!values.contact}
                        maxLength={255}
                        required
                        status={
                          !values?.contact && touched?.contact
                            ? "error"
                            : undefined
                        }
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Website"
                        name="website"
                        value={values.website}
                        free={!values.website}
                        maxLength={255}
                      />
                    </Col>
                    <Col span={11}>
                      <TextInput
                        containerclassname="mt-2"
                        label="Số điện thoại"
                        name="phone"
                        value={values.phone}
                        free={!values.phone}
                        isphonenumber
                        maxLength={13}
                        required
                        status={
                          !values?.phone && touched?.phone ? "error" : undefined
                        }
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Email"
                        name="email"
                        value={values.email}
                        free={!values.email}
                        maxLength={255}
                        required
                        status={
                          !values?.email && touched?.email ? "error" : undefined
                        }
                      />
                    </Col>
                  </Row>
                </div>
              );
            }}
          </Formik>
          <Row className="justify-between mt-2">
            <Col span={11}>
              <AppCkEditor
                label="Giới thiệu công ty"
                required
                containerclassname="mt-2"
                placeholder="Nhập nội dung giới thiệu công ty"
                value={profile}
                handleChange={(value: any): void => {
                  setProfile(value);
                }}
                touched={fieldTouched.profile}
              />
              <AppCkEditor
                label="Quy trình phỏng vấn"
                required
                containerclassname="mt-4"
                placeholder="Nhập nội dung quy trình phỏng vấn"
                value={interviewProcess}
                handleChange={(value): void => {
                  setInterviewProcess(value);
                }}
                touched={fieldTouched.interviewProcess}
              />
            </Col>
            <Col span={11}>
              <AppCkEditor
                label="Chính sách phúc lợi"
                required
                containerclassname="mt-2"
                placeholder="Nhập nội dung chinh sách phúc lợi"
                value={benefit}
                handleChange={(value): void => {
                  setBenefit(value);
                }}
                touched={fieldTouched.benefit}
              />
              <div className="font-medium mt-4 text-color-primary">Ghi chú</div>
              <Input.TextArea
                className="input-note"
                rows={4}
                placeholder="Ghi chú"
                maxLength={1000}
                value={note}
                onChange={(value: any): void => {
                  setNote(value.target.value);
                }}
              />
            </Col>
          </Row>
        </div>
        <Row className="flex justify-center items-center mt-5">
          <AppButton
            classrow="mr-10 w-64 btn-cancel"
            label="Huỷ"
            typebutton="primary"
            onClick={(): void => setIsShowModalConfirm(true)}
          />
          <AppButton
            classrow="ml-10 w-64 btn-edit"
            label="Lưu thông tin"
            typebutton="primary"
            onClick={save}
          />
        </Row>
      </div>
    );
  };

  return (
    <div className="container-form-customer">
      <AppBreadcrumb
        separator=">"
        items={isTypeEdit ? listBreadcrumbEdit : listBreadcrumbCreate}
      />
      {renderContent()}
      <AppModalConfirm
        open={isShowModalConfirm}
        content="Dữ liệu chưa được lưu, bạn có chắc chắn?"
        title={`Xác nhận dừng ${
          isTypeEdit ? "cập nhật" : "tạo mới"
        } khách hàng`}
        onCancel={(): void => setIsShowModalConfirm(false)}
        onOk={onConfirm}
      />
    </div>
  );
}
