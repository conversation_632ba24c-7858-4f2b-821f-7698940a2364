.card-container {
  border-style: dashed;
  border-color: $header_tf;
  border-radius: 15px;
  border-width: 1px;
  color: $text-color-input;
  position: relative;
  background-color: $white-color;

  .green-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }
  .btn-intro-now {
    background-color: $white-color;
    margin-top: 4px;

    button {
      height: fit-content;
      border: 1px solid $red-color;
      background-color: $white-color;
      border-radius: 20px;
    }

    span {
      font-size: 0.75rem;
      height: 1rem;
      color: $red-color;
      background-color: $white-color;
    }
  }

  &__suitable {
    background-color: #d4ffbf;
    color: $black-color;
    padding: 1px 4px;
    border-radius: 0px 4px 4px 0px;
    top: 70px;
    left: 0;
    font-size: 10px;
  }

  &__not-suitable {
    background-color: $red-color;
    color: $white-color;
  }
}

.card-padding {
  .ant-card-body {
    padding: 12px;
  }
}

.candidate-information-ui {
  width: 100%;
  min-height: 70vh;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  &_avatar {
    background-color: $primary-color;
    color: $white-color;
  }
}
