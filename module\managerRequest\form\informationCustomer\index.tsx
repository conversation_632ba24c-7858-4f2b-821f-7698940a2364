import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {Col, Row} from "antd";
import React, {useCallback} from "react";

// eslint-disable-next-line import/no-cycle
import {listCustomerRateType} from "..";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {OptionSelect} from "@app/types";
import moment from "moment";
import {IDataCustomer, OptionSelectCustomer} from "@app/api/ApiRequestJob";

interface Props {
  values: any;
  touched?: any;
  customerList: OptionSelectCustomer[];
  setFieldInput: (name: string, value: any) => void;
  dataCustomer: IDataCustomer[];
  handleChangeCustomer: (
    value: string,
    interviewProcess: string,
    benefit: string
  ) => void;
  isTypeCreate: boolean;
}

function InformationCustomer(props: Props): JSX.Element {
  const {
    values,
    customerList,
    setFieldInput,
    dataCustomer,
    handleChangeCustomer,
    isTypeCreate,
    touched,
  } = props;

  const customerId = values?.customerId?.key || "";
  const createCustomerDate =
    customerList?.find((item) => item.key === customerId)?.createdDate || "";

  const isBeforeCreateDateCustomer = (): boolean => {
    if (
      values?.requestDate &&
      customerId &&
      createCustomerDate &&
      moment(values?.requestDate) > moment(createCustomerDate)
    ) {
      return true;
    }
    return false;
  };

  const disableRequestDate = (current: any): boolean => {
    if (customerId && createCustomerDate) {
      return current && moment(current) < moment(createCustomerDate);
    }
    return false;
  };

  const changeInformation = useCallback(
    (e: OptionSelect) => {
      const valueSelect = e?.key || "";
      const findCustomer =
        dataCustomer?.find((item) => item?.customerId === valueSelect) ||
        ({} as IDataCustomer);
      const valueFields = {
        website: findCustomer?.website || "",
        proxyCustomer: findCustomer?.contact || "",
      };

      Object.entries(valueFields).forEach(([key, value]) => {
        setFieldInput(key, value);
      });
      handleChangeCustomer(
        e?.key || "",
        findCustomer?.interviewProcess || "",
        findCustomer?.benefit || ""
      );
    },
    [values]
  );

  return (
    <div className="create-request__form-customer">
      <p className="text16">Khách hàng</p>
      <Row gutter={[64, 32]} className="mt-2">
        <Col xs={12}>
          <SelectInput
            name="customerId"
            labelselect="Tên khách hàng"
            data={customerList}
            required
            status={
              !values?.customerId?.value && touched?.customerId ? "error" : ""
            }
            allowClear
            value={values?.customerId?.value}
            free={!values?.customerId?.value}
            handleChange={changeInformation}
            disabled={!isTypeCreate}
          />
        </Col>
        <Col xs={12}>
          <AppDatePicker
            name="requestDate"
            label="Ngày request"
            format={DATE_FORMAT}
            free={!values?.requestDate}
            valueAppDatePicker={
              values?.requestDate ? moment(values.requestDate) : undefined
            }
            allowClear={false}
            required
            status={
              !values?.requestDate || !isBeforeCreateDateCustomer
                ? "error"
                : undefined
            }
            disabledDate={disableRequestDate}
          />
        </Col>
      </Row>
      <Row gutter={[64, 32]} className="mt-2">
        <Col xs={12}>
          <TextInput
            label="Người đại diện"
            name="proxyCustomer"
            value={values?.proxyCustomer}
            free={!values?.proxyCustomer}
            maxLength={255}
          />
        </Col>
        <Col xs={12}>
          <div className="flex flex-1 w-full rate-customer">
            <TextInput
              containerclassname="w-full"
              label="Rate khách hàng"
              name="customerRateValue"
              required
              value={values?.customerRateValue}
              free={!values?.customerRateValue}
              status={
                (!values?.customerRateValue ||
                  (values?.customerRateType?.value === "0" &&
                    Number(values?.customerRateValue) > 100)) &&
                touched?.customerRateValue
                  ? "error"
                  : undefined
              }
              onlynumber
              maxLength={values?.customerRateType?.value === "0" ? 5 : 50}
              typeInput={
                values?.customerRateType?.value === "0" ? "" : "salary"
              }
              iscurrency={values?.customerRateType?.value !== "0"}
            />

            <AppSelectCurrency
              name="customerRateType"
              style={{width: "120px"}}
              options={listCustomerRateType}
              value={values?.customerRateType?.value}
              onChange={(): void => {
                setFieldInput("customerRateValue", "");
              }}
            />
          </div>
        </Col>
      </Row>
      <Row gutter={[64, 32]} className="mt-2">
        <Col xs={12}>
          <TextInput
            name="website"
            label="Website"
            value={values?.website}
            free={!values?.website}
            disabled
            maxLength={255}
          />
        </Col>
      </Row>
    </div>
  );
}

export default React.memo(InformationCustomer);
