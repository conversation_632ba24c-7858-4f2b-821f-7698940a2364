import {
  changeColShowHotBonus,
  changeListColShowRecommendationBonus,
  initColumnHotBonus,
  initColumnRecommendationBonus,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {IAccountRole} from "@app/types";
import React, {useEffect} from "react";
import {useSelector, useDispatch} from "react-redux";
import CTVManagerBonus from "./RoleCtv";
import CSLManagerBonus from "./RoleCsl";

export default function ManagerBonus(): JSX.Element {
  const {user, listColShowRecommendationBonus, lisColShowHotBonus} =
    useSelector(selectUser);
  const isCTV = user?.role?.includes(IAccountRole.CTV);
  const dispatch = useDispatch();

  useEffect(() => {
    if (!listColShowRecommendationBonus)
      dispatch(
        changeListColShowRecommendationBonus(initColumnRecommendationBonus)
      );

    if (!lisColShowHotBonus) {
      dispatch(changeColShowHotBonus(initColumnHotBonus));
    }
  }, []);

  if (isCTV) {
    return <CTVManagerBonus />;
  }
  return <CSLManagerBonus />;
}
