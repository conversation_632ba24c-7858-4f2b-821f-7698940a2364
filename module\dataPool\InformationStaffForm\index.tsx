import {Col, Row, notification} from "antd";
import React, {useEffect, useRef, useState, useMemo} from "react";
import {TextInput} from "@app/components/TextInput";
import AppDatePicker from "@app/components/AppDatePicker";
import "./index.scss";
import {Formik} from "formik";
import {FormikProps} from "formik/dist/types";
import {SelectInput} from "@app/components/SelectInput";
import {useMutation, useQuery} from "react-query";
import ApiCandidate, {
  IWorkLocationList,
  IWorkHistories,
  FileUpload,
  IModeViewCandidate,
} from "@app/api/ApiCandidate";
import AppModal from "@app/components/AppModal";
import AppButton from "@app/components/AppButton";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import ViewCvCandidate from "@app/components/viewCvCandidate";
import {listLanguage} from "@app/utils/constants/state";
import {
  messageCreateCandidateSuccess,
  messageRequired,
  messageValidateEmail,
  messageValidateURL,
} from "@app/utils/constants/message";
import {
  appendFormData,
  autoFormatPhoneNumber,
  mapFilterSkillData,
  sortWorkLocation,
  validatePhoneNumber,
} from "@app/utils/constants/function";
import {useDispatch} from "react-redux";
import {
  initialStateTable,
  setStateTable,
} from "@app/redux/slices/EditTableSlice";
import {IMultiSelect, OptionSelect} from "@app/types";
import {REGEX_EMAIL, REGEX_WEBSITE} from "@app/utils/constants/regex";
import AppUploadCv from "@app/components/AppUploadCv";
import ApiStaff, {
  IDataFormDataStaffSubmit,
  IDataStaff,
} from "@app/api/ApiStaff";
import TableCandidateWorkHistories from "@app/module/managerCandidate/tableCandidateWorkHistories";
import AppLoading from "@app/components/AppLoading";

interface ModalInfoProps {
  isModalVisible: boolean;
  handleCancel: () => void;
  cvStaffData: IDataStaff;
  fileUpload: FileUpload;
  handleReloadAfterCreate?: () => void;
  type: "create" | "detail";
  handleUploadFile?: (file: File, formData: FormData) => void;
  createdDate?: string;
}

interface DataInitialForm {
  candidateName: string;
  phoneNumber: string;
  candidateEmail: string;
  birthday?: any;
  positions: string;
  website?: string;
  languages?: OptionSelect[];
  workLocations?: OptionSelect[];
  skills?: OptionSelect[];
  linkedIn?: string;
  github?: string;
  facebook?: string;
  experienceYear: number;
}

const mapFilterWorkLocation = (filters: IWorkLocationList[]): OptionSelect[] =>
  filters.map((item) => ({
    value: item.workLocationId,
    label: item.name,
    key: item.workLocationId,
  }));

const mapFilterData = (filters: IMultiSelect[]): OptionSelect[] =>
  filters.map((item) => ({
    value: item.id,
    label: item.label,
  }));

const mapFilterStringData = (filters?: string[]): OptionSelect[] =>
  filters?.map((item) => ({
    value: item,
    label: item,
  })) || [];

function InformationStaffForm(props: ModalInfoProps): JSX.Element {
  const {
    isModalVisible,
    handleCancel,
    cvStaffData,
    fileUpload,
    type,
    handleUploadFile,
    handleReloadAfterCreate,
    createdDate,
  } = props;
  const [dataWorkHistories, setDataWorkHistories] = useState<IWorkHistories[]>(
    []
  );
  const dispatch = useDispatch();

  const getDataWorkHistoryFromTable = (data: IWorkHistories[]): void => {
    setDataWorkHistories(data);
  };

  const onCancel = () => {
    handleCancel();
    formikRef?.current?.resetForm();
    setDataWorkHistories([]);
    dispatch(setStateTable(initialStateTable));
  };

  const skillsDetails = useMemo(() => {
    return cvStaffData?.skills && cvStaffData?.skills?.length > 0
      ? mapFilterData(cvStaffData?.skills)
      : [];
  }, [cvStaffData?.skills]);

  const languagesDetails = useMemo(() => {
    return cvStaffData?.languages && cvStaffData?.languages?.length > 0
      ? mapFilterStringData(cvStaffData?.languages)
      : [];
  }, [cvStaffData?.languages]);

  const workLocationDetails = useMemo(() => {
    return cvStaffData?.workLocations && cvStaffData?.workLocations?.length > 0
      ? mapFilterData(cvStaffData?.workLocations)
      : [];
  }, [cvStaffData?.workLocations]);

  const formikRef = useRef<FormikProps<DataInitialForm>>(null);

  const getLastItemArray = (value: any[]): any => {
    if (Array.isArray(value) && value?.length > 0) {
      const length = value?.length;
      return value[length - 1];
    }
    return null;
  };

  const requestSkillList = useQuery("requestSkillList", () => {
    return ApiCandidate.getListSkill();
  });

  const initialValuesCreate: any = useMemo(() => {
    const valuesRef = formikRef.current?.values;
    const skillsDataFileParser =
      cvStaffData?.skills?.map((item) => ({
        label: item,
        value: item,
      })) || [];

    return {
      candidateName: cvStaffData?.full_name || "",
      phoneNumber: autoFormatPhoneNumber(cvStaffData?.phone_number || "") || "",
      candidateEmail: cvStaffData?.email || "",
      birthday: valuesRef?.birthday || "",
      positions: getLastItemArray(cvStaffData?.workHistories)?.position || "",
      website: valuesRef?.website || "",
      skills:
        mapFilterSkillData(requestSkillList?.data || [])?.filter((skillMap) =>
          skillsDataFileParser.some(
            (skill) => String(skill.value) === String(skillMap.value)
          )
        ) || [],
      languages: mapFilterStringData(cvStaffData?.languages) || [],
      workLocations: valuesRef?.workLocations || [],
      github: cvStaffData?.github_URL || "",
      facebook: cvStaffData?.facebook_URL || "",
      linkedIn: cvStaffData?.linkedin_URL || "",
    };
  }, [cvStaffData]);

  const initialValues: DataInitialForm = useMemo(() => {
    if (type === "create") {
      return initialValuesCreate;
    }
    return {
      candidateName: cvStaffData?.candidateName || "",
      phoneNumber: autoFormatPhoneNumber(cvStaffData?.phoneNumber || ""),
      candidateEmail: cvStaffData?.candidateEmail || "",
      birthday:
        moment(cvStaffData?.dateOfBirth, DATE_FORMAT).toISOString() || "",
      positions: cvStaffData?.positions?.join(", ") || "",
      website: cvStaffData?.website || "",
      skills: skillsDetails,
      languages: languagesDetails,
      workLocations: workLocationDetails,
      github: cvStaffData?.githubURL || "",
      linkedIn: cvStaffData?.linkedinURL || "",
      facebook: cvStaffData?.facebookURL || "",
      experienceYear: cvStaffData?.experienceYear || 0,
    };
  }, [cvStaffData]);

  const requestWorkLocationList = useQuery("requestWorkLocationList", () => {
    return ApiCandidate.getWorkLocationList();
  });

  const createCandidate = useMutation(
    (data: FormData) => {
      return ApiStaff.createStaff(data);
    },
    {
      onSuccess: (data) => {
        if (data) {
          notification.success({
            message: messageCreateCandidateSuccess,
            duration: 3,
          });
          onCancel();
          handleReloadAfterCreate?.();
        } else {
          notification.error({
            message: "Đã có lỗi xảy ra. Vui lòng thử lại",
            duration: 3,
          });
        }
      },
    }
  );

  useEffect(() => {
    if (cvStaffData?.experiences && cvStaffData?.experiences.length > 0) {
      const newData: IWorkHistories[] = cvStaffData?.experiences?.map(
        (item, index) => ({
          ...item,
          key: String(index),
          startDate: item?.startDate
            ? moment(item?.startDate).format(DATE_FORMAT)
            : "",
          endDate: item?.endDate
            ? item?.endDate?.toLowerCase() === "now"
              ? moment(new Date()).format(DATE_FORMAT)
              : moment(item?.endDate).format(DATE_FORMAT)
            : "",
          position: item?.position,
        })
      );
      setDataWorkHistories(newData);
    } else {
      setDataWorkHistories([]);
    }
  }, [cvStaffData?.experiences]);

  useEffect(() => {
    if (cvStaffData?.workHistories && cvStaffData?.workHistories.length > 0) {
      const newData: IWorkHistories[] = cvStaffData?.workHistories?.map(
        (item, index) => ({
          ...item,
          key: String(index),
          startDate: item?.startDate ? `01/${item?.startDate}` : "",
          endDate: item?.endDate
            ? item?.endDate?.toLowerCase() === "now"
              ? moment(new Date()).format(DATE_FORMAT)
              : `01/${item?.endDate}`
            : "",
          position: item?.position,
        })
      );
      setDataWorkHistories(newData);
    } else {
      setDataWorkHistories([]);
    }
  }, [cvStaffData?.workHistories]);

  const handleCreateCandidate = () => {
    const newDataWorkHistory = dataWorkHistories?.map((item) => ({
      companyName: item?.companyName,
      position: item?.position,
      startDate: item?.startDate ? item?.startDate : "",
      endDate: item?.endDate ? item?.endDate : "",
    }));

    const valuesRef = formikRef.current?.values;

    if (
      !(
        valuesRef?.candidateName?.trim() &&
        (valuesRef?.phoneNumber?.trim() || valuesRef?.candidateEmail?.trim()) &&
        valuesRef?.positions?.trim() &&
        valuesRef?.languages?.length &&
        valuesRef?.workLocations?.length &&
        valuesRef?.experienceYear
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: messageRequired,
      });
      return;
    }

    if (
      valuesRef?.candidateEmail &&
      !valuesRef?.candidateEmail?.match(REGEX_EMAIL)
    ) {
      notification.error({
        message: "Thông báo",
        description: messageValidateEmail,
      });
      return;
    }
    const linkList = ["website", "linkedIn", "facebook", "github"];
    // eslint-disable-next-line no-unreachable-loop
    for (const item of linkList) {
      if (
        (valuesRef as any)[item] &&
        !(valuesRef as any)[item].match(REGEX_WEBSITE)
      ) {
        notification.error({
          message: "Thông báo",
          description: messageValidateURL + item,
        });
        return;
      }
    }

    const value: IDataFormDataStaffSubmit = {
      candidateName: valuesRef?.candidateName?.trim() || "",
      phoneNumber: valuesRef?.phoneNumber?.trim() || "",
      candidateEmail: valuesRef?.candidateEmail?.trim() || "",
      dateOfBirth: valuesRef?.birthday
        ? moment(valuesRef?.birthday).format(DATE_FORMAT)
        : "",
      website: valuesRef?.website?.trim() || "",
      positions:
        valuesRef?.positions.split(",").map((item) => item.trim()) || [],
      facebookURL: valuesRef?.facebook?.trim() || "",
      languages: valuesRef?.languages?.map((item) => item.value) || [],
      workLocations:
        valuesRef?.workLocations && valuesRef?.workLocations.length > 0
          ? valuesRef?.workLocations?.map((item) => ({
              label: item.label,
              id: String(item.key),
            }))
          : [],
      experiences: newDataWorkHistory ?? [],
      fileCVName: fileUpload.fileName || "",
      githubURL: valuesRef?.github?.trim(),
      isUploadFile: !!fileUpload.file,
      linkedinURL: valuesRef?.linkedIn?.trim() || "",
      skills:
        valuesRef?.skills?.map((item) => ({
          label: item.label,
          id: String(item.value),
        })) || [],
      experienceYear: Number(valuesRef?.experienceYear) || 0,
    };
    const formData = new FormData();
    formData.append("fileCV", fileUpload.file);
    appendFormData(formData, value, "");
    createCandidate.mutate(formData);
  };

  const onClickConfirm = (): void => {
    handleCreateCandidate();
  };

  useEffect(() => {
    Object.entries(initialValues).forEach(([key, value]) => {
      formikRef.current?.setFieldValue(key, value);
    });
  }, [initialValues]);

  function renderContent(): React.ReactNode {
    return (
      <div>
        <Row className="content-modal-add-candidate">
          <div className="container-item-detail-modal left-4 p-3">
            {cvStaffData?.isLoadingFileParser ? (
              <AppLoading />
            ) : (
              <div
                className={`${
                  type === "create" ? "h-[71vh]" : "h-[80vh]"
                } overflow-y-auto p-3`}
              >
                <div className="flex flex-col">
                  <Formik
                    initialValues={initialValues}
                    innerRef={formikRef}
                    onSubmit={handleCreateCandidate}
                  >
                    {({values}): JSX.Element => {
                      return (
                        <form>
                          <span className="font-bold">Thông tin ứng viên</span>
                          <Row className="mt-2 div-time">
                            <TextInput
                              label="Họ và tên"
                              name="candidateName"
                              value={values?.candidateName}
                              status={
                                !values?.candidateName ? "error" : undefined
                              }
                              required
                              free={!values?.candidateName}
                              disabled={type === "detail"}
                            />
                            <TextInput
                              label="Số điện thoại"
                              name="phoneNumber"
                              value={values?.phoneNumber}
                              status={
                                (!values?.phoneNumber &&
                                  !values.candidateEmail) ||
                                (values?.phoneNumber &&
                                  !validatePhoneNumber(values?.phoneNumber))
                                  ? "error"
                                  : undefined
                              }
                              required={
                                !values.candidateEmail ||
                                !!(values.candidateEmail && values.phoneNumber)
                              }
                              free={!values.phoneNumber}
                              isphonenumber
                              disabled={type === "detail"}
                            />
                          </Row>
                          <Row className="mt-2 div-time">
                            <TextInput
                              label="Email"
                              name="candidateEmail"
                              status={
                                (!values?.candidateEmail &&
                                  !values.phoneNumber) ||
                                (values.candidateEmail &&
                                  !values.candidateEmail
                                    .toLowerCase()
                                    .match(REGEX_EMAIL))
                                  ? "error"
                                  : undefined
                              }
                              value={values?.candidateEmail}
                              required={
                                !values.phoneNumber ||
                                !!(
                                  !values.candidateEmail && !values.phoneNumber
                                )
                              }
                              free={!values?.candidateEmail}
                              disabled={type === "detail"}
                            />
                            <AppDatePicker
                              name="birthday"
                              label="Năm sinh"
                              format={DATE_FORMAT}
                              valueAppDatePicker={values?.birthday}
                              free={!values?.birthday}
                              disabled={type === "detail"}
                            />
                          </Row>
                          <Row className="mt-2 div-time">
                            <TextInput
                              label="Website"
                              name="website"
                              value={values?.website}
                              free={!values?.website}
                              disabled={type === "detail"}
                            />
                            <TextInput
                              label="Vị trí"
                              name="positions"
                              value={values?.positions}
                              free={!values?.positions}
                              required
                              status={!values?.positions ? "error" : undefined}
                              disabled={type === "detail"}
                            />
                          </Row>
                          <Row className="mt-2 div-time">
                            <TextInput
                              label="LinkedIn"
                              name="linkedIn"
                              value={values?.linkedIn}
                              free={!values?.linkedIn}
                              disabled={type === "detail"}
                            />
                            <TextInput
                              label="Facebook"
                              name="facebook"
                              value={values?.facebook}
                              free={!values?.facebook}
                              disabled={type === "detail"}
                            />
                          </Row>
                          <Row className="mt-2 div-time">
                            <Col xs={12} className="pr-1">
                              <TextInput
                                label="Github"
                                name="github"
                                value={values?.github}
                                free={!values?.github}
                                disabled={type === "detail"}
                              />
                            </Col>
                          </Row>
                          <div className="mt-5 font-bold">Sơ lược</div>
                          <Row className="mt-2 div-time">
                            <SelectInput
                              mode="multiple"
                              name="skills"
                              labelselect="Kỹ năng"
                              data={mapFilterSkillData(
                                requestSkillList.data ?? []
                              )}
                              allowClear={type === "create"}
                              value={values?.skills}
                              free={values?.skills?.length === 0}
                              onSelect={() => {
                                if (type === "detail") {
                                  formikRef.current?.setFieldValue(
                                    "skills",
                                    skillsDetails
                                  );
                                }
                              }}
                            />
                            <SelectInput
                              mode="multiple"
                              name="languages"
                              labelselect="Ngoại ngữ"
                              data={mapFilterData(listLanguage)}
                              value={values?.languages}
                              free={values?.languages?.length === 0}
                              required
                              allowClear={type === "create"}
                              status={
                                values?.languages?.length === 0
                                  ? "error"
                                  : undefined
                              }
                              onSelect={() => {
                                if (type === "detail") {
                                  formikRef.current?.setFieldValue(
                                    "languages",
                                    languagesDetails
                                  );
                                }
                              }}
                            />
                          </Row>
                          <Row className="mt-2 div-time">
                            <SelectInput
                              mode="multiple"
                              name="workLocations"
                              labelselect="Địa điểm làm việc"
                              data={sortWorkLocation(
                                mapFilterWorkLocation(
                                  requestWorkLocationList.data ?? []
                                )
                              )}
                              allowClear={type === "create"}
                              value={values?.workLocations}
                              free={values?.workLocations?.length === 0}
                              required
                              status={
                                values?.workLocations?.length === 0 &&
                                type === "create"
                                  ? "error"
                                  : undefined
                              }
                              onSelect={() => {
                                if (type === "detail") {
                                  formikRef.current?.setFieldValue(
                                    "workLocations",
                                    workLocationDetails
                                  );
                                }
                              }}
                            />
                            <TextInput
                              label="Số năm kinh nghiệm"
                              name="experienceYear"
                              value={
                                values?.experienceYear === 0
                                  ? 0
                                  : values?.experienceYear
                              }
                              onlynumber
                              required
                              status={
                                !values?.experienceYear ? "error" : undefined
                              }
                              disabled={type === "detail"}
                            />
                          </Row>

                          <div className="mt-4">
                            <TableCandidateWorkHistories
                              dataSource={dataWorkHistories}
                              updateDataCandidateWorkHistories={
                                getDataWorkHistoryFromTable
                              }
                              isDisabledAddRow={type === "detail"}
                            />
                          </div>
                        </form>
                      );
                    }}
                  </Formik>
                </div>
              </div>
            )}
          </div>
          <div className="container-item-detail-modal right-4 p-6">
            {cvStaffData?.fileCVPath && (
              <ViewCvCandidate
                docs={{
                  filePathBase64: cvStaffData?.fileCVPath
                    ? cvStaffData.fileCVPath
                    : "",
                  fileName: cvStaffData?.fileCVName
                    ? cvStaffData.fileCVName
                    : fileUpload && fileUpload?.fileName
                    ? fileUpload?.fileName
                    : "",
                }}
                modeViewCandidate={IModeViewCandidate.view}
                createdDate={createdDate || ""}
              />
            )}
            {type === "create" && !cvStaffData?.fileCVPath && (
              <div className="new-staff-upload flex justify-center items-center h-full">
                <AppUploadCv id="file" onChangeInput={handleUploadFile} />
              </div>
            )}
          </div>
        </Row>
        {type === "create" && (
          <Row className="flex justify-center mt-4 items-center">
            <AppButton
              classrow="mr-2 w-64 btn-cancel"
              label="Hủy bỏ"
              typebutton="primary"
              onClick={handleCancel}
            />

            <AppButton
              classrow="ml-2 w-64	"
              label="Thêm ứng viên"
              typebutton="primary"
              onClick={onClickConfirm}
              isSubmitting={createCandidate.isLoading}
            />
          </Row>
        )}
      </div>
    );
  }

  return (
    <div>
      <AppModal
        className="modal-add-candidate"
        open={isModalVisible}
        onCancel={onCancel}
        footer={null}
        title={type === "detail" ? "Chi tiết" : "Thêm ứng viên"}
        width="85%"
      >
        {renderContent()}
      </AppModal>
    </div>
  );
}

export default React.memo(InformationStaffForm);
