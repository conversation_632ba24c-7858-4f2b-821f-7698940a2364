import ApiTeam, {IEmployee} from "@app/api/ApiTeam";
import AppButton from "@app/components/AppButton";
import AppModal from "@app/components/AppModal";
import AppTable from "@app/components/AppTable";
import {TextInput} from "@app/components/TextInput";
import {OptionSelect} from "@app/types";
import {deadTimeFastSearch, statusEmployee} from "@app/utils/constants/state";
import {Row} from "antd";
import {ColumnsType} from "antd/lib/table";
import {Formik} from "formik";
import React, {useRef, useState} from "react";
import {useQuery} from "react-query";
import "./index.scss";
import _ from "lodash";
import Icon from "@app/components/Icon/Icon";
import AppPagination from "@app/components/AppPagination";

interface ModalAddUserGroupProps {
  open: boolean;
  handleClose: () => void;
  listEmployeeInGroup: IEmployee[];
  setListEmployeeInGroup: (listEmployee: IEmployee[]) => void;
  typeGroup: string;
  idLeader?: string;
}

export default function ModalAddUserGroup(
  props: ModalAddUserGroupProps
): JSX.Element {
  const {
    open,
    handleClose,
    typeGroup,
    listEmployeeInGroup,
    setListEmployeeInGroup,
    idLeader,
  } = props;
  const timeOut = useRef<any>();
  const [listEmployeeSelected, setListEmployeeSelected] = useState<IEmployee[]>(
    []
  );

  const [valuesSearch, setValuesSearch] = useState({
    keywords: "",
    currentPage: 1,
    pageSize: 20,
  });

  const getUserGroupsEmployee = useQuery(
    ["getListEmployee", valuesSearch, typeGroup],
    () => {
      return ApiTeam.getUserGroupsEmployee({
        ...valuesSearch,
        roles: typeGroup === "AM" ? "AMG" : typeGroup === "CS" ? "CST" : "BD",
      });
    }
  );

  const onChangeTextSearch = (e: any): void => {
    timeOut.current = setTimeout(() => {
      setValuesSearch({
        ...valuesSearch,
        keywords: e.target.value?.trim() || "",
      });
    }, deadTimeFastSearch);
  };

  const closeModal = (): void => {
    setListEmployeeSelected([]);
    handleClose();
  };

  const listEmployeeGet = getUserGroupsEmployee?.data?.employees || [];

  const listEmployeeShow = _.uniqBy(
    [...listEmployeeInGroup, ...listEmployeeSelected, ...listEmployeeGet],
    (e): number => {
      return e.userId;
    }
  ).filter((i) => String(i.userId) !== idLeader);

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesSearch({
      ...valuesSearch,
      currentPage: page,
      pageSize,
    });
  };

  const columns: ColumnsType<IEmployee> = [
    {
      title: "Họ và tên",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (_, item: IEmployee): JSX.Element => {
        const getStatusEmployee = (statusName?: string): OptionSelect => {
          return (
            statusEmployee.find((item) => item.label === statusName) || {
              label: "",
              value: "",
              color: "white",
            }
          );
        };
        return (
          <span
            className="status"
            style={{backgroundColor: getStatusEmployee(item?.statusName).color}}
          >
            {getStatusEmployee(item?.statusName).label}
          </span>
        );
      },
    },
    {
      title: "",
      dataIndex: "isAdd",
      key: "isAdd",
      render: (_, item: IEmployee): JSX.Element => {
        const indexInListEmployeeSelected = listEmployeeSelected.findIndex(
          (i) => i.userId === item.userId
        );

        if (indexInListEmployeeSelected !== -1) {
          return (
            <AppButton
              typebutton="normal"
              classrow="btn-delete"
              onClick={(): void => {
                const newList = listEmployeeSelected.filter(
                  (i) => i.userId !== item.userId
                );
                setListEmployeeSelected(newList);
              }}
            >
              <Icon icon="delete-bin-6-line" size={16} color="#dc2323" />
            </AppButton>
          );
        }

        if (item.isAdd) {
          return <Icon icon="check-circle" size={16} />;
        }

        return (
          <div className="flex items-center justify-center">
            <AppButton
              typebutton="primary"
              className="add-btn text12"
              onClick={(): void => {
                setListEmployeeSelected([
                  ...listEmployeeSelected,
                  {...item, isAdd: true},
                ]);
              }}
            >
              <Icon size={12} color="white" icon="add-line" className="mr-2" />
              <span>Thêm</span>
            </AppButton>
          </div>
        );
      },
      width: "15%",
    },
  ];

  const renderBodyModal = (): JSX.Element => {
    return (
      <div>
        <Formik
          initialValues={{keywords: ""}}
          onSubmit={(): void => {
            //
          }}
        >
          {({values}): JSX.Element => {
            return (
              <TextInput
                containerclassname="w-1/2"
                label="Tìm kiếm nhanh"
                name="keywords"
                value={values.keywords}
                onChange={onChangeTextSearch}
                placeholder="Nhập họ tên, email, số điện thoại"
              />
            );
          }}
        </Formik>
        <div className="mt-4">
          <AppTable
            dataSource={listEmployeeShow?.map((item: any, index: number) => ({
              ...item,
              key: index,
            }))}
            columns={columns}
            scroll={{y: "45vh"}}
            loading={getUserGroupsEmployee.isLoading}
          />
          <AppPagination
            className="mt-6"
            defaultPageSize={valuesSearch.pageSize}
            current={valuesSearch.currentPage}
            pageSize={valuesSearch.pageSize}
            total={getUserGroupsEmployee.data?.totalCount}
            onChange={handlePagination}
          />
        </div>
        <Row className="justify-center items-center mt-6">
          <AppButton
            classrow="w-48 mr-10"
            label="Huỷ"
            typebutton="secondary"
            onClick={closeModal}
          />
          <AppButton
            classrow="w-48 ml-10"
            label="Lưu"
            typebutton="primary"
            onClick={(): void => {
              setListEmployeeInGroup([
                ...listEmployeeInGroup,
                ...listEmployeeSelected,
              ]);
              closeModal();
            }}
          />
        </Row>
      </div>
    );
  };
  return (
    <AppModal
      className="modal-add-employee-container"
      open={open}
      footer={null}
      onCancel={closeModal}
      width="80%"
      title="Chi tiết nhân viên"
    >
      {renderBodyModal()}
    </AppModal>
  );
}
