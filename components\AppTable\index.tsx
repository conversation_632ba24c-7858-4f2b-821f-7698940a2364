import React, {useEffect, useRef} from "react";
import "./index.scss";
import {Table, TableProps} from "antd";

interface Props extends TableProps<any> {
  handlePositionScroll?: (position: number) => void;
}

export default function AppTable(props: Props): JSX.Element {
  const {handlePositionScroll} = props;
  const tableRef = useRef(null);

  const handleScroll = (e: any) => {
    if (tableRef.current) {
      const position = e?.target?.scrollLeft as number;
      handlePositionScroll?.(position as any);
    }
  };

  useEffect(() => {
    const tableBody = (tableRef.current as any)?.querySelector(
      ".ant-table-content"
    );

    if (tableBody) {
      tableBody.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (tableBody) {
        tableBody.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);
  return (
    <div className="container-app-table">
      <Table bordered pagination={false} ref={tableRef} {...props} />
    </div>
  );
}
