import {Pagination, PaginationProps} from "antd";
import React from "react";
import "./index.scss";

interface Props extends PaginationProps {
  isShowTotal?: boolean;
}

export default function AppPagination(props: Props) {
  const {isShowTotal = true} = props;
  return (
    <Pagination
      showTotal={(total, range) =>
        isShowTotal ? `${range[0]}-${range[1]} trên ${total} bản ghi` : ""
      }
      {...props}
      responsive
    />
  );
}
