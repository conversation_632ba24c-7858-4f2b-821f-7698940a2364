// eslint-disable-next-line import/no-cycle
import {Moment} from "moment";
// eslint-disable-next-line import/no-cycle
import {fetcher} from "./Fetcher";
import {IMultiSelect} from "@app/types";

export interface IParamsFilter {
  isAdvanceSearch?: boolean;
  isFirstInitialization?: boolean;
  pageSize?: number;
  currentPage: number;
  textSearch?: string;
  candidateName?: string;
  creators?: IMultiSelect[];
  customerName?: string;
  statuses?: IMultiSelect[];
  requestJobName?: string;
  positions?: IMultiSelect[];
}

export interface IPaymentPaging {
  accountManagerName: string;
  amount: number;
  amountPaid: number;
  candidateName: string;
  customerName: string;
  paymentId: number;
  positionName: string;
  requestJobName: string;
  salesTransacted: number;
  status: number;
  statusName: string;
  vat: number;
}

export interface IResFilter {
  accountManagerKeys: number[];
  creatorFilters: IMultiSelect[];
  paymentsPaging: IPaymentPaging[];
  role: string;
  totalCount: number;
  userId: number;
  positionFilters: IMultiSelect[];
  statusFilters: IMultiSelect[];
}

export interface paymentHistory {
  amount: number;
  paymentDate: string;
  paymentHistoryId: number;
  paymentId: number;
  paymentMethod: string;
  note: string;
  creator: string;
}

export interface ICustomerPayment {
  amount: number;
  amountPaid: number;
  amountRemain: number;
  applicationId: number;
  candidateName: string;
  currencyOfferedType: string;
  customerName: string;
  hasVAT: number;
  listPaymentHistoryAddNew: {
    amount: number;
    paymentDate: string;
    paymentMethod: string;
  }[];
  managerAMName: string;
  monthTrailWork: number;
  monthWarranty: number;
  note: string;
  onboardDate: string | Moment;
  paymentDateExpected: string | Moment;
  paymentId: number;
  paymentRate: number;
  paymentRateType: string;
  paymentRateUnit: string;
  positionName: string;
  requestJobCode: string;
  requestJobId: number;
  requestName: string;
  salaryOffered: number;
  salaryOfferedExchangeRate: number;
  salesTransacted: number;
  status: number;
  statusName: string;
  vat: number;
  listCurrency: {currencyTypeId: string; name: string}[];
  listHistoryIdToDelete: any[];
  listPaymentHistory: paymentHistory[];
  customerRefund: number;
}

const path = {
  filter: "/api/payment/filter/customer",
  export: "/api/payment/export/customer",
  detail: "/api/payment/GetCustomerPayment",
  stopCustomerPayment: "/api/payment/stopCustomerPayment",
  updateCustomerPayment: "/api/payment/updateCustomerPayment",
};

function getCustomerPayment(body?: IParamsFilter): Promise<IResFilter> {
  return fetcher({url: path.filter, method: "post", data: body});
}

function exportCustomerPayment(body?: IResFilter): Promise<any> {
  return fetcher({
    url: path.export,
    method: "post",
    data: body,
    responseType: "blob",
    headers: {
      "Content-type": "application/json",
      "Accept":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    },
  });
}

function getCustomerPaymentDetail(id?: number): Promise<ICustomerPayment> {
  return fetcher({url: path.detail, method: "get", params: {id}});
}

function stopCustomerPayment(id: number): Promise<any> {
  return fetcher({url: path.stopCustomerPayment, method: "post", params: {id}});
}

function updateCustomerPayment(data: ICustomerPayment): Promise<any> {
  return fetcher({
    url: path.updateCustomerPayment,
    data,
    method: "post",
  });
}

export default {
  path,
  getCustomerPayment,
  exportCustomerPayment,
  getCustomerPaymentDetail,
  stopCustomerPayment,
  updateCustomerPayment,
};
