import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import AppModal from "@app/components/AppModal";
import ApiPaymentBonus, {
  IApplicationBonus,
  IApplicationBonusItem,
  IPartnerPayment,
  IPaymentHistories,
} from "@app/api/ApiPaymentBonus";
import {UseQueryResult, useMutation, useQuery} from "react-query";
import AppLoading from "@app/components/AppLoading";
import {Col, Row, notification} from "antd";
import {formatMoney, getPaymentStatus} from "@app/utils/constants/function";
import AppButton from "@app/components/AppButton";
import AppModalConfirm from "@app/components/AppModalConfirm";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import ApplicationBonusEdit from "../ApplicationBonusEdit";
import ApiNotification from "@app/api/ApiNotification";
import AppAccessDenied from "@app/components/AppAccessDenied";

interface IProps {
  open?: boolean;
  closeModal: () => void;
  applicationBonusItem?: IApplicationBonusItem;
  getApplicationBonus: UseQueryResult<IApplicationBonus, unknown>;
  setIsShowDetail: (isShow: boolean) => void;
}

export default function ApplicationBonusDetail(props: IProps): JSX.Element {
  const {
    open,
    closeModal,
    applicationBonusItem,
    getApplicationBonus,
    setIsShowDetail,
  } = props;
  const timeOut = useRef<any>();
  const [isShowModalConfirm, setIsShowModalConfirm] = useState(false);
  const [isShowModalEdit, setIsShowModalEdit] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, [timeOut.current]);

  const getPartnerPayment = useQuery(
    ["getPartnerPayment", applicationBonusItem?.paymentId],
    () => {
      return ApiPaymentBonus.getPartnerPayment(applicationBonusItem?.paymentId);
    },
    {
      enabled: !!applicationBonusItem?.paymentId,
      onError: (err: any): void => {
        if (err?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            closeModal();
          }, 4000);
        }
      },
    }
  );

  const applicationBonus = getPartnerPayment.data;

  const editPartner = useMutation(
    (param: IPartnerPayment) => {
      return ApiPaymentBonus.editPartner(param);
    },
    {
      onSuccess: (res, param) => {
        if (param.status !== 3) {
          notification.success({
            message: "Lưu thông tin thành công",
          });
          closeModalEdit();
          if (param.currentPayments?.length > 0) {
            ApiNotification.createNotification({
              ApplicationId: applicationBonus?.applicationId,
              CandidateName: applicationBonus?.candidate,
              NotiType: "N009",
              PaymentId: applicationBonus?.paymentId,
              PositionName: applicationBonus?.position,
              AmountPaid: param.currentPayments[0]?.amount,
            });
          }
        } else {
          notification.success({
            message: "Dừng thanh toán thành công",
          });
        }

        getPartnerPayment.refetch();
        getApplicationBonus.refetch();
        setIsShowModalConfirm(false);
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const handleCancelPayment = (): void => {
    if (applicationBonus) {
      dispatch(setLoading(true));
      editPartner.mutate({
        ...applicationBonus,
        status: 3,
      });
    }
  };

  const closeModalEdit = (): void => {
    setIsShowModalEdit(false);
    setIsShowDetail(true);
  };

  const showModalPaymentEdit = (): void => {
    setIsShowDetail(false);
    setIsShowModalEdit(true);
  };

  const listApplicationInfo: {label: string; value?: string}[] = [
    {label: "Tên ứng viên", value: applicationBonus?.candidate},
    {label: "Vị trí ứng tuyển", value: applicationBonus?.position},
    {label: "Cộng tác viên", value: applicationBonus?.partner},
    {label: "Người tạo", value: applicationBonus?.consultant},
    {label: "Khách hàng", value: applicationBonusItem?.customerName},
    {
      label: "Ủy thác Reco hỗ trợ",
      value: applicationBonus?.commissionFlag ? "Có" : "Không",
    },
  ];

  const listPaymentInfo: {label: string; value?: string}[] = [
    {label: "Ngày onboard", value: applicationBonus?.onboardDate as string},
    {
      label: "Ngày UV chấm dứt HĐ",
      value: (applicationBonus?.contractTerminationDate as string) || "N/A",
    },
    {
      label: "Số tháng bảo hành",
      value: (applicationBonus?.monthWarranty || 0) + " tháng",
    },
    {
      label: "Ngày thanh toán dự kiến",
      value: applicationBonus?.paymentDateExpected,
    },
    {
      label: "Lương offer",
      value: formatMoney(
        applicationBonus?.salaryOffered,
        applicationBonus?.currencyOfferedType
      ),
    },
    {
      label: "Thưởng giới thiệu",
      value:
        applicationBonus?.paymentRateType === 1
          ? formatMoney(applicationBonus?.paymentRate)
          : (applicationBonus?.paymentRate || 0) + "%",
    },
  ];

  // eslint-disable-next-line react/no-unstable-nested-components
  function BodyModal(): React.ReactNode {
    if (getPartnerPayment.isLoading) {
      return <AppLoading classNameContainer="h-[70vh]" />;
    }
    if (
      getPartnerPayment.isError &&
      getPartnerPayment.error?.errorCode === 400
    ) {
      return (
        <div className="h-[70vh] flex justify-center items-center">
          <AppAccessDenied />
        </div>
      );
    }
    return (
      <div>
        <Row>
          <Col className="pr-2" span={12}>
            <div className="container-item-detail-modal">
              <div className="max-h-[66vh] px-6 py-3 overflow-y-auto mr-1">
                <Row className="justify-between items-baseline mb-6">
                  <Col className="text20 font-bold" span={14}>
                    {applicationBonus?.requestName}
                  </Col>
                  <Row className="items-baseline">
                    <div
                      className="dot-status mr-1"
                      style={{
                        backgroundColor: getPaymentStatus(
                          applicationBonus?.status || 0
                        ).color,
                      }}
                    />
                    {getPaymentStatus(applicationBonus?.status || 0).label}
                  </Row>
                </Row>
                <span className="text16 font-bold">Thông tin chung</span>
                <Row className="ml-[5%]">
                  {listApplicationInfo.map((item, index) => (
                    <Col key={index} span={12} className="mt-3 pl-1 pr-1">
                      <div className="title-info text12">{item.label}</div>
                      <div className="mt-1 text14">{item.value}</div>
                    </Col>
                  ))}
                </Row>
                <div className="line" />
                <span className="text16 font-bold">Thông tin thanh toán</span>
                <Row className="ml-[5%]">
                  {listPaymentInfo.map((item, index) => (
                    <Col key={index} span={12} className="mt-3 pl-1 pr-1">
                      <div className="title-info text12">{item.label}</div>
                      <div className="mt-1 text14">{item.value}</div>
                    </Col>
                  ))}
                </Row>
                <div className="line" />
                <Row className="justify-between items-baseline">
                  <span className="text16 font-bold">Tổng tiền</span>
                  <span className="text16 font-bold">
                    {formatMoney(applicationBonus?.amount)}
                  </span>
                </Row>
                <div className="text14 mt-3">Ghi chú</div>
                <div className="mt-1 text12">
                  {applicationBonus?.note || "N/A"}
                </div>
              </div>
            </div>
          </Col>
          <Col className="pl-2" span={12}>
            <div className="container-item-detail-modal h-[66vh] max-h-[66vh] flex flex-col">
              <span className="text16 font-bold mx-3 mt-4">
                Lịch sử thanh toán
              </span>
              <div className="flex-1 overflow-y-auto h-full mb-4 mr-1">
                {applicationBonus?.paymentHistories &&
                  applicationBonus?.paymentHistories
                    ?.sort((a, b) => a.paymentTimes - b.paymentTimes)
                    ?.map((payment: IPaymentHistories, index) => (
                      <div className="payment-card mx-3" key={index}>
                        <span className="font-bold text16">{`Thanh toán lần ${payment.paymentTimes}`}</span>
                        <Row className="items-center justify-between">
                          <span>{`Ngày thanh toán: ${payment.paymentDate}`}</span>
                          <span>{formatMoney(payment.amount)}</span>
                        </Row>
                        <span>{`Hình thức thanh toán: ${payment.paymentMethod}`}</span>
                      </div>
                    ))}
              </div>
              <div className="amount-application">
                <div>
                  <p className="text15 font-bold">{`Đã thanh toán : ${formatMoney(
                    applicationBonus?.amountPaid || 0
                  )}`}</p>
                  <p className="text15 font-bold mt-1">{`Số tiền còn lại : ${formatMoney(
                    applicationBonus?.amountRemaining
                  )}`}</p>
                </div>
              </div>
            </div>
          </Col>
        </Row>
        {/* Trường hợp chưa thanh toán và đang thanh toán mới có  */}
        {(applicationBonus?.status === 1 || applicationBonus?.status === 0) && (
          <Row className="flex justify-center items-center mt-5">
            <AppButton
              classrow="mr-2 w-64 btn-cancel"
              label="Dừng thanh toán"
              typebutton="primary"
              onClick={(): void => setIsShowModalConfirm(true)}
            />
            <AppButton
              classrow="ml-2 w-64 btn-edit"
              label="Cập nhật thanh toán"
              typebutton="primary"
              onClick={showModalPaymentEdit}
            />
          </Row>
        )}
      </div>
    );
  }

  return (
    <div>
      <AppModal
        className="modal-detail-application-bonus"
        open={open}
        footer={null}
        onCancel={closeModal}
        width="80%"
        title="Chi tiết Application Bonus"
      >
        {BodyModal()}
      </AppModal>
      <AppModalConfirm
        open={isShowModalConfirm}
        content="Thanh toán chưa hoàn thành, các thông tin đã sửa sẽ không được lưu lại, bạn có chắc chắn muốn dừng?"
        title="Xác nhận dừng thanh toán"
        onCancel={(): void => setIsShowModalConfirm(false)}
        onOk={handleCancelPayment}
      />
      {!getPartnerPayment.isLoading && getPartnerPayment.isSuccess && (
        <ApplicationBonusEdit
          closeModal={closeModalEdit}
          open={isShowModalEdit}
          partnerPayment={applicationBonus as IPartnerPayment}
          editPartner={editPartner}
        />
      )}
    </div>
  );
}
