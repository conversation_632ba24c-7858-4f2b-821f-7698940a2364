/* eslint-disable jsx-a11y/no-static-element-interactions */
import {TextInput} from "@app/components/TextInput";
import {<PERSON><PERSON>, Col, Row} from "antd";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import {Formik, FormikProps} from "formik";
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import AppCheckBox from "@app/components/AppCheckbox";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {OptionSelect, SearchParamsJobSuggestionFilter} from "@app/types";
import {SelectInput} from "@app/components/SelectInput";
import {convertValuesToOptionsSelect} from "@app/utils/constants/function";
import Icon from "@app/components/Icon/Icon";

interface MyFormValues {
  jobSearch: string;
  partnersSearch: string;
  partnerTagsSelected?: OptionSelect[];
  currentPageJob: number;
  currentPagePartner: number;
  pageSizePartner: number;
  pageSizeJob: number;
  partnerTags?: string[];
}

export interface CheckboxAllProps {
  isAllJobs?: boolean;
  isAllPartners?: boolean;
}

export const initialValuesFilterJob: MyFormValues = {
  jobSearch: "",
  partnersSearch: "",
  partnerTagsSelected: [],
  partnerTags: [],
  currentPageJob: 1,
  currentPagePartner: 1,
  pageSizeJob: 20,
  pageSizePartner: 20,
};

const initialCheckboxAll: CheckboxAllProps = {
  isAllJobs: false,
  isAllPartners: false,
};

interface SearchFilterProps {
  updateValueFilter: (values: SearchParamsJobSuggestionFilter) => void;
  updateCheckboxAll: (values: CheckboxAllProps, isJob: boolean) => void;
  typeFilter: "jobs" | "partners";
  checkedAllValues: CheckboxAllProps;
  jobAmount?: number;
  partnerAmount?: number;
  tagsOfCollaborator?: string[];
  onCopyClipboardJobsChecked?: () => void;
}

const timeShowCopied = 1; // seconds

function SearchFilter(props: SearchFilterProps): JSX.Element {
  const {
    updateValueFilter,
    typeFilter,
    updateCheckboxAll,
    checkedAllValues,
    jobAmount,
    partnerAmount,
    tagsOfCollaborator,
    onCopyClipboardJobsChecked,
  } = props;
  const formikRef = useRef<FormikProps<MyFormValues>>(null);
  const timeOutRef = useRef<any>();
  const [checkedAll, setCheckboxAll] = useState(initialCheckboxAll);
  const [countCopy, setCountCopy] = useState(timeShowCopied);

  useEffect(() => {
    setCheckboxAll(checkedAllValues);
  }, [checkedAllValues]);

  const handleSearch = (): void => {
    if (formikRef.current?.values) {
      const values = formikRef.current?.values;
      if (typeFilter === "jobs") {
        const valueConverter: SearchParamsJobSuggestionFilter = {
          jobSearch: values?.jobSearch,
          currentPageJob: 1,
          pageSizeJob: 20,
        };
        updateValueFilter(valueConverter);
      } else {
        const valueConverter: SearchParamsJobSuggestionFilter = {
          partnersSearch: values?.partnersSearch,
          partnerTags:
            values?.partnerTagsSelected?.map((tag) => tag.value) || [],
          currentPagePartner: 1,
          pageSizePartner: 20,
        };
        updateValueFilter(valueConverter);
      }
    }
  };

  const handleOnChange = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOutRef.current && clearTimeout(timeOutRef.current);
    timeOutRef.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const onCheckAllItem = (e: CheckboxChangeEvent): void => {
    const {name, checked} = e.target;
    if (name === "allJobs") {
      updateCheckboxAll(
        {
          ...checkedAll,
          isAllJobs: checked,
        },
        true
      );
      setCheckboxAll((pre) => ({
        ...pre,
        isAllJobs: checked,
      }));
    } else {
      updateCheckboxAll(
        {
          ...checkedAll,
          isAllPartners: checked,
        },
        false
      );
      setCheckboxAll((pre) => ({
        ...pre,
        isAllPartners: checked,
      }));
    }
  };

  const handleCopyClipboardJobsChecked = () => {
    if (onCopyClipboardJobsChecked) {
      setCountCopy(0);
      onCopyClipboardJobsChecked();
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCountCopy((count) => count + 1);
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <Formik
      initialValues={initialValuesFilterJob}
      innerRef={formikRef}
      onSubmit={(): void => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <form
            className="flex flex-col"
            onSubmit={(e): void => e.preventDefault()}
          >
            <Row className="w-full justify-between mb-3">
              {typeFilter === "jobs" && (
                <TextInput
                  containerclassname="w-1/2"
                  label="Tìm kiếm công việc"
                  name="jobSearch"
                  placeholder="Tìm kiếm theo việc làm, công ty, kỹ năng"
                  value={values?.jobSearch}
                  free={!values?.jobSearch}
                  onChange={handleOnChange}
                />
              )}
              {typeFilter === "partners" && (
                <>
                  <TextInput
                    containerclassname="w-full"
                    label="Tìm kiếm CTV"
                    name="partnersSearch"
                    placeholder="Tìm kiếm CTV"
                    value={values?.partnersSearch}
                    free={!values?.partnersSearch}
                    onChange={handleOnChange}
                  />
                  <SelectInput
                    containerclassname="w-full mt-2"
                    name="partnerTagsSelected"
                    labelselect="Tag"
                    mode="tags"
                    data={convertValuesToOptionsSelect(tagsOfCollaborator)}
                    allowClear
                    onSelect={handleOnChange}
                    onDeselect={handleOnChange}
                    onClear={handleOnChange}
                  />
                </>
              )}
            </Row>

            {typeFilter === "jobs" && (
              <div className="flex justify-between items-center h-[42px] mb-2">
                <Row className="w-1/2 mb-3" justify="space-between">
                  <Col>
                    <div className="text-base">
                      Bạn đã chọn {jobAmount || 0} công việc
                    </div>
                  </Col>
                  <AppCheckBox
                    name="allJobs"
                    onChange={onCheckAllItem}
                    checked={
                      typeFilter === "jobs"
                        ? checkedAll.isAllJobs
                        : checkedAll.isAllPartners
                    }
                    className="checkbox-all-jobs"
                  >
                    Tất cả
                  </AppCheckBox>
                </Row>
                {!!jobAmount && (
                  <div
                    className="ui-chatbot-sender__button flex items-center gap-2 text-white bg-[#2f6bff] whitespace-nowrap px-3 hover:opacity-90 active:opacity-95 cursor-pointer rounded-md"
                    onClick={handleCopyClipboardJobsChecked}
                  >
                    <Icon icon="copy" color="white" size={16} />
                    <span>
                      {countCopy > timeShowCopied ? "Copy tin nhắn" : "Đã copy"}
                    </span>
                  </div>
                )}
              </div>
            )}
            {typeFilter === "partners" && (
              <Row className="mb-3" justify="space-between">
                <Col xs={16}>
                  <div className="text-base">
                    Bạn đã chọn {partnerAmount || 0} CTV
                  </div>
                </Col>
                <AppCheckBox
                  name="isAllPartners"
                  onChange={onCheckAllItem}
                  checked={checkedAll.isAllPartners}
                  className="checkbox-all-partners"
                >
                  Tất cả
                </AppCheckBox>
              </Row>
            )}
          </form>
        );
      }}
    </Formik>
  );
}

export default SearchFilter;
