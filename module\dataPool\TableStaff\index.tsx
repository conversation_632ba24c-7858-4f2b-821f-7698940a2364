import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import {Formik} from "formik";
import {Popover, Row} from "antd";
import {TextInput} from "@app/components/TextInput";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {ColumnsType} from "antd/lib/table";
import {
  deadTimeFastSearch,
  listExperience,
  listLanguage,
} from "@app/utils/constants/state";
import AppTable from "@app/components/AppTable";
import {UseQueryResult} from "react-query";
import {setQueryUrl, sortWorkLocation} from "@app/utils/constants/function";
import {SelectInput} from "@app/components/SelectInput";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {mapFilterWorkLocation} from "@app/module/managerCandidate";
// eslint-disable-next-line import/no-cycle
import {IFormFilter, initialSearchParams} from "..";
import {IListStaff, IStaffObject, IUserInfo} from "@app/api/ApiStaff";
import {IWorkLocationList} from "@app/api/ApiCandidate";
import moment from "moment";
import {OptionSelect} from "@app/types";
import _ from "lodash";

interface TableStaffProps {
  requestStaffList: UseQueryResult<IListStaff, unknown>;
  onClickStaff: (staff: IStaffObject) => void;
  formikRef: any;
  requestWorkLocationList: UseQueryResult<IWorkLocationList[], unknown>;
  handleOpenPopupAddStaff: () => void;
  handleOpenStatisticalTable?: () => void;
  allUsersData: any;
  setValuesSearch: (values: IFormFilter) => void;
  valuesSearch: IFormFilter;
}

function TableStaff(props: TableStaffProps, ref: any): JSX.Element {
  const {
    requestStaffList,
    onClickStaff,
    formikRef,
    requestWorkLocationList,
    handleOpenPopupAddStaff,
    handleOpenStatisticalTable,
    allUsersData,
    setValuesSearch,
    valuesSearch,
  } = props;
  const [isShowFilterAdvance, setIsShowFilterAdvance] = useState(false);
  const timeOut = useRef<any>();

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, []);

  const columns: ColumnsType<IStaffObject> = [
    {
      title: "Họ và tên",
      dataIndex: "candidateName",
      key: "candidateName",
      align: "left",
      className: "cursor-pointer",
      onCell: (record: IStaffObject): any => {
        return {
          onClick: (): void => {
            setQueryUrl({id: String(record.publicCandidateId)});
            onClickStaff(record);
          },
        };
      },
    },
    {
      title: "Vị trí",
      dataIndex: "positions",
      key: "positions",
      render: (_, {positions}: IStaffObject): JSX.Element => {
        if (!positions) {
          return <div />;
        }
        return <div>{positions?.join(", ")}</div>;
      },
    },
    {
      title: "Thông tin liên hệ",
      dataIndex: "contactInformation",
      key: "contactInformation",
    },
    {
      title: "Ngoại ngữ",
      dataIndex: "languages",
      key: "languages",
      render: (_, {languages}: IStaffObject): JSX.Element => {
        if (!languages) {
          return <div />;
        }
        return <div>{languages?.join(", ")}</div>;
      },
    },

    {
      title: "Địa điểm",
      dataIndex: "workLocationNames",
      key: "workLocationNames",
      render: (_, {workLocations}: IStaffObject): JSX.Element => {
        return <span>{workLocations?.join(", ")}</span>;
      },
    },
    {
      title: "Năm kinh nghiệm",
      dataIndex: "experienceYear",
      key: "experienceYear",
      render: (_, {experienceYear}: IStaffObject): JSX.Element => {
        return (
          <span>
            {experienceYear === 0 ? "Chưa có kinh nghiệm" : experienceYear}
          </span>
        );
      },
    },
    {
      title: "Người đăng",
      dataIndex: "creator",
      key: "creator",
    },
  ];

  const mapFilterManager =
    allUsersData?.map((item: IUserInfo) => ({
      key: String(item.userId),
      label: item.userName,
      value: String(item.userId),
    })) || [];

  const onChangeTextSearch = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      setValuesSearch({
        ...valuesSearch,
        candidateName: formikRef?.current?.values?.candidateName,
        position: formikRef?.current?.values?.position,
        pageSize: valuesSearch?.pageSize,
        currentPage: 1,
      });
    }, deadTimeFastSearch);
  };

  const handleResetForm = (): void => {
    const initialValuesForm = {
      candidateName: "",
      contactInformation: "",
      creators: [],
      languages: [],
      position: "",
      workLocations: [],
      createdFrom: null,
      createdTo: null,
      experienceYear: null,
      pageSize: valuesSearch.pageSize,
      currentPage: 1,
    };
    formikRef.current?.setValues(initialValuesForm as any);
    setValuesSearch(initialSearchParams);
  };

  const onClickSearch = (): void => {
    const values = formikRef?.current?.values;

    const creators = values?.creators?.map((item: any) => {
      if (typeof item === "string") return Number(item);

      return Number(item.value);
    });

    setValuesSearch({
      ...valuesSearch,
      contactInformation: values.contactInformation,
      workLocations:
        formikRef?.current?.values?.workLocations?.map((item: OptionSelect) =>
          String(item?.key)
        ) || [],
      createdFrom: values.createdFrom
        ? moment(values.createdFrom).toISOString()
        : null,
      createdTo: values.createdTo
        ? moment(values.createdTo).toISOString()
        : null,
      languages:
        values?.languages?.map((item: OptionSelect) => String(item?.key)) || [],
      creatorIds: creators,
      experienceYear:
        listExperience.findIndex(
          (item) => item.value === values?.experienceYear?.value
        ) || 0,
      currentPage: 1,
      pageSize: valuesSearch.pageSize,
    });
  };

  function contentFilterAdvance(
    values: IFormFilter,
    handleSubmit: () => void
  ): JSX.Element {
    return (
      <div className="content-filter-advance-staff">
        <Row className="flex items-center justify-between mb-4">
          <span className="title-filter">Tất cả bộ lọc</span>
          <AppButton
            classrow="btn-close-popover"
            typebutton="normal"
            onClick={() => setIsShowFilterAdvance(false)}
          >
            <Icon className="" icon="close-circle-line" size={20} />
          </AppButton>
        </Row>
        <SelectInput
          mode="multiple"
          containerclassname="mt-2"
          name="languages"
          labelselect="Ngoại ngữ"
          data={listLanguage.map((i) => ({
            ...i,
            value: i.label,
          }))}
          free={values?.languages?.length === 0}
          allowClear
          value={values.languages || []}
        />
        <TextInput
          containerclassname="mt-2"
          label="Thông tin liên hệ"
          placeholder="Nhập thông tin liên hệ"
          name="contactInformation"
          value={values.contactInformation || ""}
        />
        <SelectInput
          mode="multiple"
          containerclassname="mt-2"
          name="workLocations"
          labelselect="Địa điểm"
          data={sortWorkLocation(
            mapFilterWorkLocation(requestWorkLocationList.data ?? []),
            "key"
          )}
          free={values?.workLocations?.length === 0}
          allowClear
          value={values.workLocations || []}
        />
        <SelectInput
          containerclassname="mt-2"
          name="experienceYear"
          labelselect="Số năm kinh nghiệm"
          data={listExperience}
          free={_.isEmpty(values?.experienceYear)}
          allowClear
          value={values?.experienceYear || []}
        />
        <SelectInput
          containerclassname="mt-2"
          mode="multiple"
          name="creators"
          labelselect="Người đăng"
          data={mapFilterManager}
          optionFilterProp="label"
          free={values?.creators?.length === 0}
          value={values.creators || []}
          allowClear
        />
        <div className="mt-2 font-bold">Ngày tạo</div>
        <Row className="mt-2 div-time ">
          <AppDatePicker
            name="createdFrom"
            label="Từ"
            free={!values?.createdFrom}
            format={DATE_FORMAT}
            allowClear
            valueAppDatePicker={values.createdFrom}
          />
          <AppDatePicker
            name="createdTo"
            label="Đến"
            free={!values?.createdTo}
            format={DATE_FORMAT}
            allowClear
            valueAppDatePicker={values.createdTo}
          />
        </Row>
        <Row className="mt-6 div-time">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={handleResetForm}
          />
          <AppButton
            label="Tìm kiếm"
            typebutton="primary"
            onClick={handleSubmit}
          />
        </Row>
      </div>
    );
  }

  return (
    <div className="data-pool-table">
      <Formik
        initialValues={valuesSearch}
        innerRef={formikRef}
        onSubmit={onClickSearch}
      >
        {({values, handleSubmit}): JSX.Element => {
          return (
            <Row className="flex justify-between gap-4">
              <Row className="gap-4">
                <TextInput
                  containerclassname="w-80"
                  label="Họ và tên"
                  name="candidateName"
                  value={values.candidateName}
                  onChange={onChangeTextSearch}
                  disabled={isShowFilterAdvance}
                  free={!values.candidateName}
                />
                <TextInput
                  containerclassname="w-80"
                  label="Vị trí"
                  name="position"
                  value={values.position}
                  onChange={onChangeTextSearch}
                  disabled={isShowFilterAdvance}
                  free={!values.position}
                />
              </Row>
              <Row className="items-center gap-5">
                <AppButton
                  typebutton="primary"
                  classrow="statistical-btn"
                  onClick={handleOpenStatisticalTable}
                >
                  <div className="statistical-btn__content">
                    <span className="text14">Thống kê</span>
                  </div>
                </AppButton>
                <AppButton
                  typebutton="primary"
                  classrow="add-staff-btn"
                  onClick={handleOpenPopupAddStaff}
                >
                  <div className="add-staff-btn__content items-center flex">
                    <Icon size={16} color="white" icon="add-line" />
                    <span className="text14 ml-2">Tạo ứng viên</span>
                  </div>
                </AppButton>
                <Popover
                  className="mr-1"
                  placement="bottom"
                  trigger="click"
                  content={(): React.ReactNode =>
                    contentFilterAdvance(values, handleSubmit)
                  }
                  open={isShowFilterAdvance}
                  onOpenChange={setIsShowFilterAdvance}
                >
                  <AppButton typebutton="normal" classrow="btn-filter">
                    <Icon
                      className="mr-1"
                      icon="filter-line"
                      size={12}
                      color="#324054"
                    />
                    Tìm kiếm nâng cao
                  </AppButton>
                </Popover>
              </Row>
            </Row>
          );
        }}
      </Formik>

      <div className="mt-4">
        <AppTable
          dataSource={requestStaffList.data?.candidatesPoolPaging?.map(
            (item: IStaffObject, index: number) => ({
              ...item,
              key: index,
            })
          )}
          columns={columns}
          loading={requestStaffList.isLoading}
          scroll={{y: "60vh"}}
        />
      </div>
    </div>
  );
}

export default React.forwardRef(TableStaff);
