import React, {useCallback, useEffect, useRef, useState} from "react";
import "./index.scss";
import AppModal from "@app/components/AppModal";
import {Formik, FormikProps} from "formik";
import {Col, Row, notification} from "antd";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {SelectInput} from "@app/components/SelectInput";
import {
  deadTimeFastSearch,
  optionGuarantee,
  statusRateCurrencyPercent,
} from "@app/utils/constants/state";
import {TextInput} from "@app/components/TextInput";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {Input} from "formik-antd";
import {IPartnerPayment} from "@app/api/ApiPaymentBonus";
import {
  getMonthWarrantySelected,
  moneyToNumber,
} from "@app/utils/constants/function";
import moment, {Moment} from "moment";
import {UseMutationResult} from "react-query";
import AppButton from "@app/components/AppButton";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {messageValidate} from "@app/utils/constants/message";
import {OptionSelect} from "@app/types";
import AppCollapse from "@app/components/AppCollapse";
import CollapsePanel from "antd/lib/collapse/CollapsePanel";

interface FormPayment extends IPartnerPayment {
  monthWarrantySelected: OptionSelect;
  currencyOfferedTypeSelected: OptionSelect;
  paymentRateTypeSelected: OptionSelect;
  salaryConvert?: number;
  totalAmount?: number;
  payAmount?: number;
  ratePay?: number;
  remainingAmount?: number | string;
  rateRemain?: number;
  paymentMethod?: string;
  paymentDateCurrentTime?: string | Moment;
  paymentDateExpectedNextTime?: string | Moment;
}

interface IProps {
  open?: boolean;
  closeModal: () => void;
  partnerPayment: IPartnerPayment;
  editPartner?: UseMutationResult<any, unknown, IPartnerPayment, unknown>;
}
const {TextArea} = Input;

export default function ApplicationBonusEdit(props: IProps): JSX.Element {
  const {open, closeModal, partnerPayment, editPartner} = props;
  const refPayment = useRef<FormikProps<FormPayment>>(null);
  const timeOutChangeAmountPay = useRef<any>();
  const dispatch = useDispatch();
  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOutChangeAmountPay.current &&
        clearTimeout(timeOutChangeAmountPay.current);
    };
  }, [timeOutChangeAmountPay.current]);

  const paymentTimes = (partnerPayment?.paymentHistories?.length || 0) + 1;

  const [isShowPayment, setIsShowPayment] = useState(paymentTimes > 1);

  const salaryConvert =
    partnerPayment.salaryOffered && partnerPayment.salaryOfferedExchangeRate
      ? partnerPayment.salaryOffered * partnerPayment.salaryOfferedExchangeRate
      : 0;

  const onChangeRateType = (): void => {
    refPayment.current?.setFieldValue("paymentRate", null);
    refPayment.current?.setFieldValue("totalAmount", 0);
    handleSumAmount(0);
  };

  const onCloseModal = (): void => {
    refPayment.current?.resetForm();
    closeModal();
  };

  const disabledPaymentInfo = partnerPayment.paymentHistories?.length > 0;

  const calculateRemainingAmount = (
    amountRemaining: number,
    pay: any
  ): {remainingAmount: number; rate: number} => {
    if (!pay) {
      return {remainingAmount: amountRemaining, rate: 100};
    }
    return {
      remainingAmount: amountRemaining - moneyToNumber(pay),
      rate: 100 - Math.floor((moneyToNumber(pay) * 100) / amountRemaining),
    };
  };

  const onChangePayAmount = (e: any): void => {
    const amountPaid = e.target.value;
    const amountRemaining = moneyToNumber(
      String(refPayment.current?.values?.amountRemaining)
    );
    // eslint-disable-next-line no-unused-expressions
    timeOutChangeAmountPay.current &&
      clearTimeout(timeOutChangeAmountPay.current);
    timeOutChangeAmountPay.current = setTimeout(() => {
      const ratePay = Math.floor(
        (moneyToNumber(String(amountPaid)) * 100) / amountRemaining
      );
      const remainingAmount = calculateRemainingAmount(
        amountRemaining,
        amountPaid
      );
      refPayment.current?.setFieldValue("ratePay", ratePay);
      refPayment.current?.setFieldValue("rateRemain", remainingAmount.rate);
      refPayment.current?.setFieldValue(
        "remainingAmount",
        remainingAmount.remainingAmount
      );
    }, deadTimeFastSearch);
  };

  const updatePayment = (): void => {
    const values = refPayment.current?.values;
    if (
      !(
        values?.onboardDate &&
        values?.monthWarrantySelected.value &&
        values?.salaryOffered &&
        values?.paymentRate
      ) ||
      (values?.currencyOfferedTypeSelected?.value !== "VND" &&
        !values?.salaryOfferedExchangeRate) ||
      (isShowPayment &&
        !(
          values?.payAmount &&
          values?.paymentMethod &&
          values?.paymentDateCurrentTime &&
          values?.paymentDateExpectedNextTime
        ))
    ) {
      notification.error({
        message: "Thông báo",
        description: messageValidate,
        duration: 3,
      });
      return;
    }

    if (moneyToNumber(String(values.payAmount)) > values.amountRemaining) {
      notification.error({
        message: "Thông báo",
        description: "Số tiền thanh toán phải nhỏ hơn số tiền cần thanh toán.",
        duration: 3,
      });
      return;
    }
    const param: IPartnerPayment = {
      ...partnerPayment,
      amount: moneyToNumber(String(values?.totalAmount)),
      onboardDate: moment(values.onboardDate).format(DATE_FORMAT),
      monthWarranty:
        Number(values?.monthWarrantySelected.value) ||
        partnerPayment.monthWarranty,
      contractTerminationDate: values?.contractTerminationDate
        ? moment(values.contractTerminationDate).format(DATE_FORMAT)
        : partnerPayment.contractTerminationDate,
      salaryOffered:
        moneyToNumber(String(values.salaryOffered)) ||
        partnerPayment.salaryOffered,
      salaryOfferedExchangeRate:
        moneyToNumber(String(values.salaryOfferedExchangeRate)) ||
        partnerPayment.salaryOfferedExchangeRate,
      currencyOfferedType: values?.currencyOfferedTypeSelected.value,
      paymentRate:
        moneyToNumber(String(values.paymentRate)) || partnerPayment.paymentRate,
      paymentRateType: Number(values.paymentRateTypeSelected.value),
      currentPayments: isShowPayment
        ? [
            {
              amount: moneyToNumber(String(values.payAmount)),
              paymentDate: moment(values.paymentDateCurrentTime).format(
                DATE_FORMAT
              ),
              paymentDateExpected: moment(values.paymentRateType).format(
                DATE_FORMAT
              ),
              paymentId: partnerPayment.paymentId,
              paymentMethod: values.paymentMethod || "",
              paymentTimes: paymentTimes,
            },
          ]
        : [],
      note: values.note?.trim(),
      amountPaid: isShowPayment
        ? partnerPayment.amountPaid + moneyToNumber(String(values.payAmount))
        : partnerPayment.amountPaid,
      amountRemaining:
        moneyToNumber(String(values.amountRemaining)) -
        (isShowPayment ? moneyToNumber(String(values.payAmount)) : 0),
      status: isShowPayment
        ? moneyToNumber(String(values.amountRemaining)) -
            moneyToNumber(String(values.payAmount)) ===
          0
          ? 2
          : 1
        : partnerPayment.status,
    };
    if (editPartner) {
      dispatch(setLoading(true));
      editPartner.mutate(param);
    }
  };

  const handleSumAmount = (sumAmount: number): void => {
    const amountRemaining = sumAmount - partnerPayment.amountPaid;
    refPayment.current?.setFieldValue("amountRemaining", amountRemaining);
    if (paymentTimes === 1) {
      const payAmount = Math.floor(amountRemaining * 0.25);
      const remainingAmount = amountRemaining - payAmount;
      refPayment.current?.setFieldValue("payAmount", payAmount);
      refPayment.current?.setFieldValue("remainingAmount", remainingAmount);
    } else {
      refPayment.current?.setFieldValue("payAmount", amountRemaining);
      refPayment.current?.setFieldValue("remainingAmount", 0);
    }
  };

  const onChangeShow = (key: any): void => {
    setIsShowPayment(key?.length > 0);
  };

  const initialForm: FormPayment = {
    ...partnerPayment,
    monthWarrantySelected: getMonthWarrantySelected(
      String(partnerPayment.monthWarranty)
    ),
    onboardDate: partnerPayment.onboardDate
      ? moment(partnerPayment.onboardDate, DATE_FORMAT)
      : "",
    contractTerminationDate: partnerPayment.contractTerminationDate
      ? moment(partnerPayment.contractTerminationDate, DATE_FORMAT)
      : "",
    currencyOfferedTypeSelected: partnerPayment?.currencyOfferedType
      ? {
          value: partnerPayment.currencyOfferedType,
          label: partnerPayment?.currencyOfferedType,
        }
      : {value: "VND", label: "VND"},
    paymentRateTypeSelected:
      String(partnerPayment.paymentRateType) === "0"
        ? {value: "0", label: "Tỉ lệ %"}
        : {value: "1", label: "Số tiền"},
    salaryConvert,
    totalAmount: partnerPayment.amount,
    payAmount:
      paymentTimes === 1
        ? Math.floor(partnerPayment.amountRemaining * 0.25)
        : partnerPayment.amountRemaining,
    ratePay: paymentTimes === 1 ? 25 : 100,
    remainingAmount:
      paymentTimes === 1
        ? partnerPayment.amountRemaining -
          Math.floor(partnerPayment.amountRemaining * 0.25)
        : "0",
    rateRemain: paymentTimes === 1 ? 75 : 0,
    paymentDateCurrentTime:
      paymentTimes === 1
        ? moment(partnerPayment.onboardDate, DATE_FORMAT).add(10, "days")
        : paymentTimes === 2
        ? moment(partnerPayment.onboardDate, DATE_FORMAT).add(
            partnerPayment.monthWarranty || 2,
            "months"
          )
        : "",
    paymentDateExpectedNextTime:
      paymentTimes === 1
        ? moment(partnerPayment.onboardDate, DATE_FORMAT).add(
            partnerPayment.monthWarranty || 2,
            "months"
          )
        : "",
  };

  useEffect(() => {
    if (refPayment.current) {
      refPayment.current.setValues(initialForm);
    }
  }, [JSON.stringify(partnerPayment), paymentTimes]);

  return (
    <AppModal
      className="update-payment-modal"
      open={open}
      footer={null}
      onCancel={onCloseModal}
      width="65%"
      title="Cập nhật thông tin thanh toán"
    >
      <Formik
        innerRef={refPayment}
        initialValues={initialForm}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            const timeOut = setTimeout(() => {
              const salaryOffered = values?.salaryOffered
                ? moneyToNumber(String(values?.salaryOffered))
                : null;
              const rate =
                values.currencyOfferedTypeSelected.value !== "VND" &&
                values?.salaryOfferedExchangeRate
                  ? moneyToNumber(String(values?.salaryOfferedExchangeRate))
                  : 1;

              const salaryConvert = salaryOffered
                ? Number(salaryOffered) * rate
                : null;

              refPayment.current?.setFieldValue("salaryConvert", salaryConvert);

              if (
                values?.paymentRateTypeSelected?.value === "0" &&
                salaryConvert &&
                values?.paymentRate
              ) {
                const sumAmount =
                  (salaryConvert * moneyToNumber(String(values?.paymentRate))) /
                  100;
                refPayment.current?.setFieldValue("totalAmount", sumAmount);
                handleSumAmount(sumAmount);
              }
              if (values.currencyOfferedTypeSelected.value === "VND") {
                refPayment.current?.setFieldValue(
                  "salaryOfferedExchangeRate",
                  1
                );
              }
            }, deadTimeFastSearch);

            return () => {
              clearTimeout(timeOut);
            };
          }, [
            values?.salaryOffered,
            values?.salaryOfferedExchangeRate,
            values.currencyOfferedTypeSelected.value,
          ]);

          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            const timeOut = setTimeout(() => {
              if (values?.paymentRateTypeSelected?.value === "0") {
                if (values.salaryConvert && values?.paymentRate) {
                  const sumAmount =
                    (values.salaryConvert *
                      moneyToNumber(String(values?.paymentRate))) /
                    100;
                  refPayment.current?.setFieldValue("totalAmount", sumAmount);
                  handleSumAmount(sumAmount);
                }
              } else {
                refPayment.current?.setFieldValue(
                  "totalAmount",
                  values?.paymentRate
                );
                handleSumAmount(moneyToNumber(String(values?.paymentRate)));
              }
            }, deadTimeFastSearch);
            return () => {
              clearTimeout(timeOut);
            };
          }, [values?.paymentRate]);

          // eslint-disable-next-line react-hooks/rules-of-hooks
          const disabledContractTerminationDate = useCallback(
            (current: any): boolean => {
              if (!values.onboardDate) {
                return false;
              }
              return (
                current &&
                (current < moment(values.onboardDate).endOf("day") ||
                  current >
                    moment(values.onboardDate)
                      .endOf("day")
                      .add(
                        Number(values.monthWarrantySelected.value),
                        "months"
                      ))
              );
            },
            [values.onboardDate, values.monthWarrantySelected.value]
          );

          // eslint-disable-next-line react-hooks/rules-of-hooks
          const disabledPaymentDate = useCallback(
            (current: any): boolean => {
              if (!values.onboardDate) {
                return false;
              }
              return (
                current && current < moment(values.onboardDate).endOf("day")
              );
            },
            [values.onboardDate, values.monthWarrantySelected.value]
          );

          // eslint-disable-next-line react-hooks/rules-of-hooks
          const disabledEdit = useCallback((): boolean => {
            if (!isShowPayment) {
              return false;
            }

            if (
              paymentTimes === 1 &&
              moment().isBefore(moment(values.onboardDate).add(10, "days"))
            ) {
              return true;
            }

            const datePayment2 = moment(values.onboardDate).add(
              Number(values.monthWarrantySelected.value),
              "months"
            );

            if (paymentTimes === 2 && moment().isBefore(datePayment2)) {
              return true;
            }

            return false;
          }, [
            values.onboardDate,
            values.monthWarrantySelected.value,
            paymentTimes,
            isShowPayment,
          ]);

          const disableOnboardDate = (current: any): boolean => {
            return (
              current &&
              current <
                moment(
                  partnerPayment.applicationCreateDate,
                  DATE_FORMAT
                ).startOf("day")
            );
          };

          const onChangeOnboardDate = (): void => {
            refPayment.current?.setFieldValue("contractTerminationDate", "");
            refPayment.current?.setFieldValue("paymentDateCurrentTime", "");
            refPayment.current?.setFieldValue(
              "paymentDateExpectedNextTime",
              ""
            );
          };

          return (
            <div className="text-color-primary px-10">
              <div className="payment-info">
                <p className="font-bold text16">Thông tin thanh toán</p>
                <Row className="justify-center mt-1">
                  <Col className="pr-4" span={10}>
                    <AppDatePicker
                      classNameContainer="mt-2"
                      name="onboardDate"
                      label="Ngày onboard"
                      format={DATE_FORMAT}
                      valueAppDatePicker={values?.onboardDate}
                      free={!values?.onboardDate}
                      required
                      status={values?.onboardDate ? undefined : "error"}
                      disabledDate={disableOnboardDate}
                      disabled={disabledPaymentInfo}
                      allowClear={false}
                      onChange={onChangeOnboardDate}
                    />
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Lương offer"
                        name="salaryOffered"
                        onlynumber
                        value={values?.salaryOffered}
                        free={!values?.salaryOffered}
                        typeInput="salary"
                        iscurrency
                        maxLength={50}
                        required
                        status={values?.salaryOffered ? undefined : "error"}
                        disabled={disabledPaymentInfo}
                      />
                      <AppSelectCurrency
                        name="currencyOfferedTypeSelected"
                        value={values?.currencyOfferedTypeSelected}
                        style={{width: "90px"}}
                        disabled={disabledPaymentInfo}
                      />
                    </Row>
                    <TextInput
                      containerclassname="mt-2"
                      label="Tỷ giá"
                      name="salaryOfferedExchangeRate"
                      onlynumber
                      value={values?.salaryOfferedExchangeRate}
                      free={!values?.salaryOfferedExchangeRate}
                      typeInput="salary"
                      iscurrency
                      maxLength={50}
                      disabled={
                        values?.currencyOfferedTypeSelected?.value === "VND" ||
                        disabledPaymentInfo
                      }
                      required={
                        values?.currencyOfferedTypeSelected?.value !== "VND"
                      }
                      status={
                        values?.currencyOfferedTypeSelected?.value !== "VND" &&
                        !values?.salaryOfferedExchangeRate
                          ? "error"
                          : undefined
                      }
                    />
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Số tiền quy đổi được"
                        name="salaryConvert"
                        onlynumber
                        value={values?.salaryConvert}
                        free={!values?.salaryConvert}
                        typeInput="salary"
                        iscurrency
                        maxLength={50}
                        disabled
                      />
                      <AppSelectCurrency
                        name="currencyConvert"
                        value="VND"
                        style={{width: "90px"}}
                        disabled
                      />
                    </Row>
                  </Col>
                  <Col className="pl-4" span={10}>
                    <AppDatePicker
                      classNameContainer="mt-2"
                      name="contractTerminationDate"
                      label="Ngày UV chấm dứt hợp đồng"
                      format={DATE_FORMAT}
                      valueAppDatePicker={values?.contractTerminationDate}
                      free={!values?.contractTerminationDate}
                      disabledDate={disabledContractTerminationDate}
                    />
                    <SelectInput
                      containerclassname="mt-2"
                      name="monthWarrantySelected"
                      labelselect="Số tháng bảo hành"
                      data={optionGuarantee}
                      value={values?.monthWarrantySelected}
                      required
                      disabled={disabledPaymentInfo}
                    />
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Thưởng giới thiệu"
                        name="paymentRate"
                        onlynumber
                        value={values?.paymentRate}
                        free={!values?.paymentRate}
                        typeInput={
                          values?.paymentRateTypeSelected?.value === "1"
                            ? "salary"
                            : ""
                        }
                        iscurrency={
                          values?.paymentRateTypeSelected?.value === "1"
                        }
                        maxLength={50}
                        required
                        status={values?.paymentRate ? undefined : "error"}
                        disabled={disabledPaymentInfo}
                      />
                      <AppSelectCurrency
                        name="paymentRateTypeSelected"
                        value={values?.paymentRateTypeSelected}
                        style={{width: "90px"}}
                        options={statusRateCurrencyPercent}
                        onChange={onChangeRateType}
                        disabled={disabledPaymentInfo}
                      />
                    </Row>
                    <Row className="mt-2 input-salary">
                      <TextInput
                        containerclassname="flex-1"
                        label="Tổng tiền"
                        name="totalAmount"
                        onlynumber
                        value={values?.totalAmount}
                        free={!values?.totalAmount}
                        typeInput="salary"
                        iscurrency
                        maxLength={100}
                        disabled
                      />
                      <AppSelectCurrency
                        name="currencyTotalAmount"
                        value="VND"
                        style={{width: "90px"}}
                        disabled
                      />
                    </Row>
                  </Col>
                </Row>
                <Row className="justify-center">
                  <TextArea
                    name="note"
                    className="border-dash w-5/6"
                    rows={4}
                    placeholder="Ghi chú"
                    maxLength={1000}
                    value={values?.note}
                  />
                </Row>
              </div>
              <div className="payment-info">
                <AppCollapse
                  defaultActiveKey={paymentTimes === 1 ? [] : ["1"]}
                  onChange={onChangeShow}
                >
                  <CollapsePanel
                    header={`Thanh toán lần ${paymentTimes}`}
                    key="1"
                  >
                    <Row className="justify-center">
                      <Col className="pr-4" span={10}>
                        <Row className="mt-2 input-salary">
                          <TextInput
                            containerclassname="flex-1"
                            label="Số tiền cần thanh toán"
                            name="amountRemaining"
                            onlynumber
                            value={values.amountRemaining}
                            typeInput="salary"
                            iscurrency
                            maxLength={50}
                            disabled
                          />
                          <AppSelectCurrency
                            name=""
                            value="VND"
                            style={{width: "90px"}}
                            disabled
                            showArrow={false}
                          />
                        </Row>
                        <Row className="mt-2 input-salary">
                          <TextInput
                            containerclassname="flex-1"
                            label="Số tiền thanh toán"
                            name="payAmount"
                            onlynumber
                            value={values?.payAmount}
                            typeInput="salary"
                            iscurrency
                            maxLength={50}
                            required
                            status={values?.payAmount ? undefined : "error"}
                            onChange={onChangePayAmount}
                          />
                          <AppSelectCurrency
                            name="ratePay"
                            value={`${values?.ratePay}%`}
                            style={{width: "90px"}}
                            showArrow={false}
                            disabled
                          />
                        </Row>
                        <AppDatePicker
                          classNameContainer="mt-2"
                          name="paymentDateCurrentTime"
                          label="Ngày thanh toán"
                          format={DATE_FORMAT}
                          valueAppDatePicker={values?.paymentDateCurrentTime}
                          free={!values?.paymentDateCurrentTime}
                          required
                          status={
                            values?.paymentDateCurrentTime ? undefined : "error"
                          }
                          disabledDate={disabledPaymentDate}
                        />
                      </Col>
                      <Col className="pl-4" span={10}>
                        <TextInput
                          containerclassname="mt-2"
                          label="Hình thức thanh toán"
                          name="paymentMethod"
                          value={values?.paymentMethod}
                          required
                          status={values?.paymentMethod ? undefined : "error"}
                        />
                        <Row className="mt-2 input-salary">
                          <TextInput
                            containerclassname="flex-1"
                            label="Số tiền còn lại"
                            name="remainingAmount"
                            onlynumber
                            value={values?.remainingAmount}
                            typeInput="salary"
                            iscurrency
                            maxLength={50}
                            disabled
                            free={!values?.remainingAmount}
                          />
                          <AppSelectCurrency
                            name="rateRemain"
                            value={`${values?.rateRemain}%`}
                            style={{width: "90px"}}
                            disabled
                            showArrow={false}
                          />
                        </Row>
                        <AppDatePicker
                          classNameContainer="mt-2"
                          name="paymentDateExpectedNextTime"
                          label={`Ngày thanh toán lần thứ ${paymentTimes + 1}`}
                          format={DATE_FORMAT}
                          valueAppDatePicker={
                            values?.paymentDateExpectedNextTime
                          }
                          free={!values?.paymentDateExpectedNextTime}
                          required
                          status={
                            values?.paymentDateExpectedNextTime
                              ? undefined
                              : "error"
                          }
                          disabledDate={disabledPaymentDate}
                        />
                      </Col>
                    </Row>
                  </CollapsePanel>
                </AppCollapse>
              </div>
              <Row className="flex justify-center items-center mt-5">
                <AppButton
                  classrow="mr-2 w-64 btn-cancel"
                  label="Hủy"
                  typebutton="primary"
                  onClick={onCloseModal}
                />
                <AppButton
                  classrow="ml-2 w-64 btn-edit"
                  label="Cập nhật"
                  typebutton="primary"
                  onClick={updatePayment}
                  disabled={disabledEdit()}
                />
              </Row>
            </div>
          );
        }}
      </Formik>
    </AppModal>
  );
}
