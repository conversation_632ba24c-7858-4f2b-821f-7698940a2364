{"root": true, "env": {"node": true, "es6": true}, "parserOptions": {"ecmaVersion": 8}, "ignorePatterns": ["node_modules/*", ".next/*", ".out/*", "!.prettierrc.js"], "overrides": [{"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser", "settings": {"react": {"version": "detect"}}, "env": {"browser": true, "node": true, "es6": true}, "extends": ["next", "eslint:recommended", "react-app", "airbnb", "prettier", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:import/recommended", "plugin:prettier/recommended"], "rules": {"prettier/prettier": ["error", {"endOfLine": "auto"}], "react/jsx-props-no-spreading": "off", "react/state-in-constructor": "off", "react/static-property-placement": "off", "react/no-array-index-key": "off", "react/react-in-jsx-scope": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/anchor-is-valid": "off", "react/prop-types": "off", "no-unused-vars": "warn", "react/require-default-props": "off", "no-shadow": "off", "no-restricted-exports": "off", "import/no-unresolved": 0, "no-case-declarations": 0, "no-nested-ternary": 0, "no-underscore-dangle": 0, "no-lonely-if": 0, "no-param-reassign": 0, "no-plusplus": 0, "prefer-template": 0, "object-shorthand": 0, "react-hooks/exhaustive-deps": 0, "react/forbid-prop-types": 0, "react/destructuring-assignment": 0, "react/no-access-state-in-setstate": 0, "import/order": 0, "import/no-mutable-exports": 0, "class-methods-use-this": 0, "import/prefer-default-export": 0, "no-prototype-builtins": 0, "import/no-named-as-default": 0, "global-require": 0, "max-classes-per-file": 0, "newline-per-chained-call": 0, "default-param-last": 0, "default-case": 0, "max-len": [0, {"code": 80, "ignorePattern": true}], "semi": 1, "no-console": "warn", "max-statements-per-line": [1, {"max": 2}], "react/jsx-filename-extension": [1, {"extensions": [".ts", ".tsx"]}], "quotes": [1, "double", {"avoidEscape": true, "allowTemplateLiterals": true}], "@typescript-eslint/explicit-function-return-type": 0, "import/no-cycle": "off", "no-useless-return": "error", "block-scoped-var": "error", "no-var": "error", "no-whitespace-before-property": "error", "camelcase": "error", "prefer-const": "error", "no-dupe-else-if": "error", "no-duplicate-imports": "error", "lines-between-class-members": "error", "no-trailing-spaces": "error", "keyword-spacing": "error", "jsx-quotes": "error", "comma-style": "error", "comma-spacing": "error", "arrow-spacing": "error", "@typescript-eslint/no-unused-vars": "warn", "react/jsx-no-bind": ["error", {"allowBind": true, "allowArrowFunctions": true}], "no-multi-spaces": ["error", {"ignoreEOLComments": true, "exceptions": {"ImportDeclaration": true}}], "no-use-before-define": ["error", {"functions": false, "classes": false, "variables": false}], "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "no-restricted-syntax": ["error", "ForInStatement", "LabeledStatement", "WithStatement"]}}]}