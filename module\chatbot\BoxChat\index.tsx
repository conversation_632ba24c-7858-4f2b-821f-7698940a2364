/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import Icon from "@app/components/Icon/Icon";
import ChatMessage from "../Message";
import ChatSender from "../Sender";
import ChatSkeleton from "../Skeleton";
import ApiMessageChatbot from "@app/api/ApiMessageChatbot";
import {useMutation, useQuery} from "react-query";
import {useDispatch, useSelector} from "react-redux";
import {IRootState} from "@app/redux/store";
import {setInitMessagesChatbot} from "@app/redux/slices/ChatbotSlice";
import AppModalConfirm from "@app/components/AppModalConfirm";

interface BoxChatProps {
  handleHide: () => void;
}

export default function BoxChat(props: BoxChatProps): JSX.Element {
  const {handleHide} = props;
  const [scrollHeight, setScrollHeight] = useState<number>(42);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showIconScroll, setShowIconScroll] = useState<boolean>(false);
  const [showModalDelete, setShowModalDelete] = useState<boolean>(false);
  const dummyRef = useRef<HTMLDivElement | null>(null);
  const {messages} = useSelector((state: IRootState) => state.chatbot);
  const dispatch = useDispatch();
  const contentBodyRef = useRef<HTMLDivElement | null>(null);

  const handleTextMessageChange = (scrollHeight: number) => {
    if (scrollHeight > 86) return;
    setScrollHeight(scrollHeight);
  };

  const scrollToBottom = (isInit = true): void => {
    if (isInit) {
      dummyRef?.current?.scrollIntoView();
    }
    dummyRef?.current?.scrollIntoView({block: "end", behavior: "smooth"});
  };

  const getMessageChatbot = useQuery(
    ["getMessageChatbot"],
    () => {
      setIsLoading(true);
      return ApiMessageChatbot.getMessageChatbot();
    },
    {
      onSuccess: (data) => {
        dispatch(setInitMessagesChatbot(data));
        setTimeout(() => {
          setIsLoading(false);
        }, 300);
      },
    }
  );

  useEffect(() => {
    if (messages && messages.length) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [messages]);

  const handleLoadMore = (): void => {
    const contentChatbot = document.querySelector(
      ".ui-chatbot-box__content-body"
    );
    if (
      contentChatbot &&
      contentChatbot.scrollHeight > contentChatbot.clientHeight &&
      contentChatbot.scrollTop <
        contentChatbot.scrollHeight - contentChatbot.clientHeight - 60
    ) {
      setShowIconScroll(true);
    } else {
      setShowIconScroll(false);
    }
  };

  useEffect(() => {
    if (contentBodyRef.current) {
      contentBodyRef.current?.addEventListener("scroll", handleLoadMore);
    }
    return () =>
      contentBodyRef.current?.removeEventListener("scroll", handleLoadMore);
  }, [contentBodyRef.current, messages]);

  const deleteMessageChatbot = useMutation(
    () => {
      return ApiMessageChatbot.deleteMessageChatbot();
    },
    {
      onSuccess: () => {
        dispatch(setInitMessagesChatbot([]));
        setShowModalDelete(false);
        setShowIconScroll(false);
      },
      onError: () => {
        setShowModalDelete(false);
      },
    }
  );

  return (
    <div className="ui-chatbot-box">
      <AppModalConfirm
        open={showModalDelete}
        content="Bạn có chắc chắn muốn xóa đoạn chat này?"
        title="Xác nhận xóa"
        onCancel={(): void => setShowModalDelete(false)}
        onOk={(): void => deleteMessageChatbot.mutate()}
      />
      <div className="ui-chatbot-box__header">
        <div className="ui-chatbot-box__header-title">AI Assistant</div>
        <div className="ui-chatbot-box__header-action">
          <div
            className="ui-chatbot-box__header-action--delete"
            onClick={(): void => setShowModalDelete(true)}
          >
            <Icon icon="delete-bin-6-line" size={18} color="#FFFFFF" />
          </div>
          <div
            className="ui-chatbot-box__header-action--close"
            onClick={handleHide}
          >
            <Icon icon="close-line" color="#ffffff" size={18} />
          </div>
        </div>
      </div>
      <div
        className="ui-chatbot-box__content"
        style={{height: `calc(100% - 140px - ${scrollHeight - 42}px)`}}
      >
        <div
          ref={(element): void => {
            contentBodyRef.current = element;
          }}
          className="ui-chatbot-box__content-body"
        >
          <div className="ui-chatbot-box__content-messages">
            {!!(messages && messages.length) &&
              messages.map((message, index) => (
                <div key={index}>
                  <ChatMessage
                    content={message.content}
                    isBot={message.isBot}
                  />
                </div>
              ))}
          </div>
          <div ref={dummyRef} />
          {showIconScroll && (
            <div
              className="icon-scroll-bottom"
              onClick={(): void => scrollToBottom(false)}
            >
              <Icon icon="arrow-down" size={18} color="#324054" />
            </div>
          )}
        </div>
        <div className="ui-chatbot-box__actions">
          <ChatSender onTextMessageChange={handleTextMessageChange} />
        </div>
      </div>

      {(getMessageChatbot.isLoading || isLoading) && <ChatSkeleton />}
    </div>
  );
}
