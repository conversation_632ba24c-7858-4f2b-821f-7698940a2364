.container-manager-employee {
  .add-btn {
    margin-right: 13px;

    .ant-btn {
      border-radius: 15px !important;
    }

    span {
      color: $white-color;
      margin-left: 4px;
    }

    .ant-btn {
      border-radius: 10px;
      background-color: $primary-color;
      display: flex;
      align-items: center;
    }

    .title {
      color: $white-color;
    }
  }

  .btn-filter {
    button {
      font-size: 0.875rem;
      font-weight: 400;
      border: 1px dashed $header_tf;
      border-radius: 8px;
      display: flex;
      align-items: center;
      align-self: center;
      color: $text-color-input;
      height: auto;
    }
    button:hover,
    button:focus {
      background: none;
      color: $text-color-input;
      border: 1px dashed $header_tf;
    }
  }

  .status {
    color: $white-color;
    padding: 4px 14px;
    border-radius: 8px;
  }
}

.content-filter-advance {
  background-color: $white-color;
  width: 350px;

  .title-filter {
    font-size: 1.5rem;
    font-weight: 400;
    color: $text-color-input;
  }

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .btn-close-popover {
    button {
      border: none;
    }
  }
}

.ant-popover-inner {
  border-radius: 16px;
}

.group-check-box-list-col {
  width: 380px;
}
