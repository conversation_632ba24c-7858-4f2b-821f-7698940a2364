.app-button-container {
  .button {
    font-size: 16px;
    font-weight: 400;
    border: none;
    text-shadow: none;
    border-radius: 16px;
    height: 48px;
    width: 100%;
    min-width: 100%;
  }

  .primary-button {
    @extend .button;
    background-color: $primary-color;
    color: $white-color;

    &:focus,
    &:hover {
      background-color: $primary-color;
      color: $white-color;
      opacity: 0.8;
      box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    }
  }

  .secondary-button {
    @extend .button;
    background-color: $secondary-color;
    color: $primary-color;

    &:focus,
    &:hover {
      background-color: $secondary-color;
      color: $primary-color;
      opacity: 0.8;
      box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    }
  }

  .ant-btn[disabled] {
    opacity: 0.5;
  }

  .ant-btn-dangerous.ant-btn-primary {
    @extend .button;
    width: 100%;
  }

  .warning-button {
    @extend .button;
    background-color: #f4b41a;
    color: $white-color;

    &:focus,
    &:hover {
      background-color: #f4b41a;
      opacity: 0.8;
      color: $white-color;
      box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    }
  }
}

.ant-btn > span {
  text-align: center;
  display: flex;
  justify-content: center;
}
