import ApiJob, {IJobTypes, ILevels, IWorkLocations} from "@app/api/ApiJob";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {Popover, Row} from "antd";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import {Formik, FormikProps} from "formik";
import React, {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import {useQuery} from "react-query";
import "./index.scss";
import AppCheckBox from "@app/components/AppCheckbox";
import {
  deadTimeFastSearch,
  listJobLabels,
  listSalaryRanges,
  optionServiceRequest,
} from "@app/utils/constants/state";
import {OptionSelect, SearchParamsJobFilter} from "@app/types";
import {sortWorkLocation} from "@app/utils/constants/function";
// eslint-disable-next-line import/named

export interface MyFormValues {
  textSearch: string;
  workLocations: OptionSelect[];
  jobLabels: OptionSelect[];
  levels: OptionSelect[];
  salaryRanges: OptionSelect[];
  jobTypes: OptionSelect[];
  isSearchHotJob?: boolean;
  isBookmark?: boolean;
  haveRecommendCandidate?: boolean;
  services: OptionSelect[];
}

export const initialValuesFilterJob: MyFormValues = {
  textSearch: "",
  workLocations: [],
  jobLabels: [],
  levels: [],
  salaryRanges: [],
  jobTypes: [],
  isSearchHotJob: false,
  isBookmark: false,
  haveRecommendCandidate: false,
  services: [],
};

interface FilterJobProps {
  setCurrentPage: (page: number) => void;
  initValueUrlParams?: SearchParamsJobFilter;
  isSaveValueSearch: boolean;
  updateValueFilter: (values: SearchParamsJobFilter) => void;
}

function FilterJob(props: FilterJobProps, ref: any): JSX.Element {
  const {
    setCurrentPage,
    initValueUrlParams,
    isSaveValueSearch,
    updateValueFilter,
  } = props;
  const formikRef = useRef<FormikProps<MyFormValues>>(null);
  const [open, setOpen] = useState(false);
  const [hotSearch, setHotSearch] = useState(false);
  const [bookmark, setBookmark] = useState(false);
  const [haveRecommendCandidate, setHaveRecommendCandidate] = useState(false);
  const timeOutSearchBookmark = useRef<any>(null);
  const timeOutRef = useRef<any>();
  const jobMarketFilters = useQuery(["jobMarketFilters"], () => {
    return ApiJob.getJobMarketFilters();
  });

  useImperativeHandle(
    ref,
    () => ({
      resetForm: () => {
        onReset();
        setBookmark(false);
        setHaveRecommendCandidate(false);
      },
    }),
    [formikRef.current]
  );
  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOutSearchBookmark.current &&
        clearTimeout(timeOutSearchBookmark.current);
      // eslint-disable-next-line no-unused-expressions
      timeOutRef.current && clearTimeout(timeOutRef.current);
    };
  }, []);

  const hide = () => {
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean): void => {
    setOpen(newOpen);
  };

  const onChange = (e: CheckboxChangeEvent): void => {
    setHotSearch(e.target.checked);
  };

  const onSortListCandidate = (e: CheckboxChangeEvent): void => {
    const {name, checked} = e.target;
    const isBookMarkJob = name === "bookmarkJob" ? checked : bookmark;
    const isRecommendCandidate =
      name === "recommendCandidate" ? checked : haveRecommendCandidate;
    if (name === "recommendCandidate") {
      setHaveRecommendCandidate(checked);
    } else {
      setBookmark(checked);
    }
    // eslint-disable-next-line no-unused-expressions
    timeOutSearchBookmark.current &&
      clearTimeout(timeOutSearchBookmark.current);

    timeOutSearchBookmark.current = setTimeout(() => {
      if (formikRef.current?.values) {
        const values = formikRef.current?.values;
        const valueConverter: SearchParamsJobFilter = {
          ...values,
          isSearchHotJob: hotSearch,
          isBookmark: isBookMarkJob,
          jobLabels: mapDataSearch(values?.jobLabels),
          jobTypes: mapDataSearch(values?.jobTypes),
          levels: mapDataSearch(values?.levels),
          salaryRanges: values?.salaryRanges?.map((i) => i?.key || "") || [],
          workLocations: mapDataSearch(values?.workLocations),
          currentPage: 1,
          haveRecommendCandidate: isRecommendCandidate,
          services: mapDataSearch(values?.services),
        };
        setCurrentPage(1);
        updateValueFilter(valueConverter);
      }
    }, deadTimeFastSearch);
  };

  const mapFilterWorkLocation = (filters: IWorkLocations[]): OptionSelect[] =>
    filters.map((item) => ({
      value: item.workLocationId,
      label: item.name,
    }));

  const mapFilterLever = (filters: ILevels[]): OptionSelect[] =>
    filters.map((item) => ({
      value: item.levelId,
      label: item.name,
    }));

  const mapJobType = (filters: IJobTypes[]): OptionSelect[] =>
    filters.map((item) => ({
      value: item.workTypeId,
      label: item.name,
    }));

  const mapDataSearch = (values: OptionSelect[]) => {
    if (!values || values?.length === 0) return [];
    return values?.map((i) => i.value);
  };

  const handleSearch = (): void => {
    if (formikRef.current?.values) {
      const values = formikRef.current?.values;
      const valueConverter: SearchParamsJobFilter = {
        ...values,
        isSearchHotJob: hotSearch,
        isBookmark: bookmark,
        jobLabels: mapDataSearch(values?.jobLabels),
        jobTypes: mapDataSearch(values?.jobTypes),
        levels: mapDataSearch(values?.levels),
        salaryRanges: values?.salaryRanges?.map((i) => i?.key || "") || [],
        workLocations: mapDataSearch(values?.workLocations),
        currentPage: 1,
        haveRecommendCandidate: haveRecommendCandidate,
        services: mapDataSearch(values?.services),
      };

      setCurrentPage(1);
      updateValueFilter(valueConverter);
    }
  };

  const handleOnChange = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOutRef.current && clearTimeout(timeOutRef.current);
    timeOutRef.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const onReset = (): void => {
    formikRef.current?.resetForm();
    updateValueFilter(initialValuesFilterJob as any);
    setCurrentPage(1);
    setHotSearch(false);
  };

  const dataForm = useMemo(() => {
    return {
      workLocations: mapFilterWorkLocation(
        jobMarketFilters.data?.workLocations ?? []
      ),
      levels: mapFilterLever(jobMarketFilters.data?.levels ?? []),
      jobTypes: mapJobType(jobMarketFilters.data?.jobTypes ?? []),
    };
  }, [jobMarketFilters]);

  const findInputValues = (
    values: string[],
    dataSearch: OptionSelect[]
  ): OptionSelect[] => {
    const defaultData: OptionSelect[] = [];
    if (values?.length === 0 || dataSearch?.length === 0) return defaultData;
    return dataSearch?.filter((item) => values?.includes(item?.value || ""));
  };

  const initValues: MyFormValues = initValueUrlParams
    ? {
        ...initValueUrlParams,
        workLocations: findInputValues(
          initValueUrlParams?.workLocations,
          dataForm?.workLocations
        ),
        isBookmark: initValueUrlParams?.isBookmark,
        isSearchHotJob: initValueUrlParams?.isSearchHotJob,
        jobLabels: findInputValues(
          initValueUrlParams?.jobLabels,
          listJobLabels
        ),
        jobTypes: findInputValues(
          initValueUrlParams?.jobTypes,
          dataForm?.jobTypes
        ),
        levels: findInputValues(initValueUrlParams?.levels, dataForm?.levels),
        salaryRanges: findInputValues(
          initValueUrlParams?.salaryRanges?.map((i) => i.toString()),
          listSalaryRanges
        ),
        services: findInputValues(
          initValueUrlParams.services,
          optionServiceRequest
        ),
      }
    : initialValuesFilterJob;

  useEffect(() => {
    if (isSaveValueSearch && initValueUrlParams) {
      setBookmark(!!initValueUrlParams?.isBookmark);
      setHaveRecommendCandidate(!!initValueUrlParams?.haveRecommendCandidate);
      setHotSearch(!!initValueUrlParams?.isSearchHotJob);
      // eslint-disable-next-line no-unused-expressions
    }
    formikRef.current?.setValues(initValues);
  }, [JSON.stringify(dataForm), JSON.stringify(initValues), isSaveValueSearch]);

  return (
    <Formik
      initialValues={isSaveValueSearch ? initValues : initialValuesFilterJob}
      innerRef={formikRef}
      onSubmit={(): void => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <form
            className="flex flex-col"
            onSubmit={(e): void => e.preventDefault()}
          >
            <Row className="container-filter-advance mb-3">
              <TextInput
                containerclassname="w-4/12 pr-2"
                label="Tìm kiếm nhanh"
                name="textSearch"
                placeholder="Tìm kiếm theo việc làm, công ty, kỹ năng"
                value={values?.textSearch}
                free={!values?.textSearch}
                onChange={handleOnChange}
              />
              <SelectInput
                mode="multiple"
                containerclassname="w-3/12 pr-2"
                name="workLocations"
                labelselect="Địa điểm làm việc"
                data={sortWorkLocation(dataForm?.workLocations)}
                value={values?.workLocations}
                free={values?.workLocations?.length === 0}
                handleChange={handleOnChange}
                allowClear
              />
              <SelectInput
                mode="multiple"
                containerclassname="w-3/12 pr-2"
                name="jobLabels"
                labelselect="Nhãn job"
                data={listJobLabels}
                value={values?.jobLabels}
                free={values?.jobLabels?.length === 0}
                handleChange={handleOnChange}
                allowClear
              />
              <div className="w-2/12 filter-container">
                <Popover
                  open={open}
                  trigger="click"
                  onOpenChange={handleOpenChange}
                  placement="bottom"
                  content={
                    <div className="filter-content">
                      <Row className="flex items-center justify-between mb-4">
                        <span>Tất cả bộ lọc</span>
                        <AppButton
                          classrow="btn-close-popover"
                          typebutton="normal"
                          onClick={hide}
                        >
                          <Icon
                            className=""
                            icon="close-circle-line"
                            size={20}
                          />
                        </AppButton>
                      </Row>
                      <SelectInput
                        containerclassname="mb-2.5"
                        mode="multiple"
                        className="w-full"
                        name="levels"
                        labelselect="Cấp bậc"
                        data={dataForm?.levels}
                        value={values?.levels}
                        free={values?.levels?.length === 0}
                        allowClear
                      />
                      <SelectInput
                        containerclassname="mb-2.5"
                        mode="multiple"
                        className="w-full"
                        name="salaryRanges"
                        labelselect="Mức lương"
                        data={listSalaryRanges}
                        value={values?.salaryRanges}
                        free={values?.salaryRanges?.length === 0}
                        allowClear
                      />
                      <SelectInput
                        containerclassname="mb-2.5"
                        mode="multiple"
                        className="w-full"
                        name="jobTypes"
                        labelselect="Job type"
                        data={dataForm?.jobTypes}
                        value={values?.jobTypes}
                        free={values?.jobTypes?.length === 0}
                        allowClear
                      />
                      <SelectInput
                        containerclassname="mb-2.5"
                        mode="multiple"
                        className="w-full"
                        name="services"
                        labelselect="Dịch vụ"
                        data={optionServiceRequest}
                        value={values?.services}
                        free={values?.services?.length === 0}
                        allowClear
                      />
                      <AppCheckBox
                        className="check-box"
                        onChange={onChange}
                        checked={hotSearch}
                      >
                        Hot job
                      </AppCheckBox>
                      <Row className="mt-6 div-time">
                        <AppButton
                          label="Xoá tất cả"
                          typebutton="secondary"
                          onClick={onReset}
                        />
                        <AppButton
                          label="Tìm kiếm"
                          typebutton="primary"
                          onClick={handleSearch}
                        />
                      </Row>
                    </div>
                  }
                >
                  <AppButton typebutton="normal">
                    <Icon className="mr-1" icon="filter-line" size={9} />
                    Tìm kiếm nâng cao
                  </AppButton>
                </Popover>
              </div>
            </Row>
            <Row>
              <AppCheckBox
                className="check-box mb-3 mr-8"
                name="bookmarkJob"
                onChange={onSortListCandidate}
                checked={bookmark}
              >
                Công việc đã lưu
              </AppCheckBox>
              <AppCheckBox
                className="check-box mb-3"
                name="recommendCandidate"
                onChange={onSortListCandidate}
                checked={haveRecommendCandidate}
              >
                Jobs có gợi ý
              </AppCheckBox>
            </Row>
          </form>
        );
      }}
    </Formik>
  );
}

export default React.forwardRef(FilterJob);
