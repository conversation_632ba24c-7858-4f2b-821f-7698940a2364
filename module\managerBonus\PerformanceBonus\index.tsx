import ApiPaymentBonus, {
  DashboardPagingDataPerformance,
  IDataPerformanceBonus,
} from "@app/api/ApiPaymentBonus";
import AppDatePicker from "@app/components/AppDatePicker";
import AppPagination from "@app/components/AppPagination";
import AppTable from "@app/components/AppTable";
import {TextInput} from "@app/components/TextInput";
import {YEAR_FORMAT} from "@app/utils/constants/formatDateTime";
import {formatMoney} from "@app/utils/constants/function";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {Col, Row} from "antd";
import {ColumnType} from "antd/lib/table";
import {Formik, FormikProps} from "formik";
import moment, {Moment} from "moment";
import React, {useEffect, useRef, useState} from "react";
import {useQuery} from "react-query";
import "./index.scss";
import ModalDetailPerformance from "./ModalDetailPerformance";
import {useSelector} from "react-redux";
import {selectUser} from "@app/redux/slices/UserSlice";
import {IAccountRole} from "@app/types";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {useRouter} from "next/router";
import config from "@app/config";

interface DataFormSearchPerformance {
  consultantName: string;
  paymentBonusYear: string | Moment | undefined;
}

function PerformanceBonus(): JSX.Element {
  const router = useRouter();
  const formikRef = useRef<FormikProps<DataFormSearchPerformance>>(null);
  const timeOut = useRef<any>(null);
  const [showModalDetail, setShowModalDetail] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<IDataPerformanceBonus>({
    isFirstInitialization: true,
    paymentBonusYear: new Date().getFullYear().toString(),
    currentPage: 1,
    pageSize: 20,
  });
  const [selectPerformanceBonus, setSelectPerformanceBonus] = useState<{
    partnerId: number;
    partnerName: string;
    consultantName: string;
  }>({
    partnerName: "",
    partnerId: -1,
    consultantName: "",
  });
  const {user} = useSelector(selectUser);
  const isCSL =
    user?.role?.includes(IAccountRole.CSL) ||
    user?.role?.includes(IAccountRole.ADMIN);
  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);

  const getPerformanceBonus = useQuery(
    ["getPerformanceBonus", searchParams],
    () => {
      return ApiPaymentBonus.getPerformanceBonus(searchParams);
    }
  );
  const initialValue: DataFormSearchPerformance = {
    consultantName: "",
    paymentBonusYear: moment().format(YEAR_FORMAT),
  };

  const allColumn: ColumnType<DashboardPagingDataPerformance>[] = [
    {
      title: "Cộng tác viên",
      dataIndex: "userFullName",
      key: "userFullName",
      align: "left",
      className: "cursor-pointer",
      onCell: (record: DashboardPagingDataPerformance): any => {
        return {
          onClick: (): void => {
            setSelectPerformanceBonus({
              partnerId: record?.userId,
              partnerName: record?.userFullName,
              consultantName: record?.consultantName,
            });
            setShowModalDetail(true);
          },
        };
      },
    },
    {
      title: "Passed review (CV)",
      dataIndex: "totalPassedReview",
      key: "totalPassedReview",
    },
    {
      title: "Interviewed (CV)",
      dataIndex: "totalInterviewed",
      key: "totalInterviewed",
    },
    {
      title: "Onboard Pass (CV)",
      dataIndex: "totalOnboardDone",
      key: "totalOnboardDone",
    },
    {
      title: "Số bonus đã nhận",
      dataIndex: "totalBonusTimesDone",
      key: "totalBonusTimesDone",
    },
    {
      title: "Số tiền đã nhận",
      dataIndex: "totalBonusDoneAmount",
      key: "totalBonusDoneAmount",
      render: (
        _: string,
        record: DashboardPagingDataPerformance
      ): JSX.Element => {
        return (
          <span>
            {record.totalBonusDoneAmount
              ? formatMoney(record.totalBonusDoneAmount)
              : ""}
          </span>
        );
      },
    },
    {
      title: "Số bonus còn lại",
      dataIndex: "totalBonusTimesRemain",
      key: "totalBonusTimesRemain",
    },
  ];

  if (isCSL) {
    allColumn.push({
      title: "CST quản lý",
      dataIndex: "consultantName",
      key: "consultantName",
    });
  }

  const handlePagination = (page: number, pageSize: number): void => {
    setSearchParams((prev) => ({
      ...prev,
      pageSize: pageSize,
      currentPage: page,
    }));
  };

  const handleSearchParams = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      setSearchParams({
        ...searchParams,
        consultantName: formikRef?.current?.values?.consultantName,
        paymentBonusYear: formikRef?.current?.values?.paymentBonusYear
          ? moment(formikRef?.current?.values?.paymentBonusYear).format(
              YEAR_FORMAT
            )
          : moment().format(YEAR_FORMAT),
      });
    }, deadTimeFastSearch);
  };

  const onCancel = (): void => {
    setSelectPerformanceBonus({
      partnerName: "",
      partnerId: -1,
      consultantName: "",
    });
    setShowModalDetail(false);
  };

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);
  return (
    <div className="performance-bonus-ui">
      <Row className="items-center">
        <Formik
          initialValues={initialValue}
          innerRef={formikRef}
          onSubmit={(): void => {
            //
          }}
        >
          {({values}): JSX.Element => {
            return (
              <Row gutter={[16, 16]} className="flex-1">
                {isCSL && (
                  <Col xs={8}>
                    <TextInput
                      label="CST quản lý"
                      placeholder="Nhập CST quản lý"
                      name="consultantName"
                      value={values?.consultantName}
                      free={!values?.consultantName}
                      onChange={handleSearchParams}
                    />
                  </Col>
                )}
                <Col xs={5}>
                  <AppDatePicker
                    label="Năm"
                    placeholder="Nhập năm"
                    name="paymentBonusYear"
                    format={YEAR_FORMAT}
                    valueAppDatePicker={values?.paymentBonusYear}
                    free={!values?.paymentBonusYear}
                    onChange={handleSearchParams}
                    picker="year"
                    allowClear
                  />
                </Col>
              </Row>
            );
          }}
        </Formik>
        {isAdmin && (
          <AppButton
            typebutton="normal"
            classrow="add-btn"
            onClick={(): void => {
              router.push(config.PATHNAME.RULE_BONUS);
            }}
          >
            <Icon icon="add-line" size={16} />
            Quy định thưởng
          </AppButton>
        )}
      </Row>

      <div className="performance-bonus-ui__table mt-2">
        <AppTable
          columns={allColumn}
          dataSource={getPerformanceBonus.data?.dashboardPagingDatas ?? []}
          key="key"
          style={{
            maxHeight: "60vh",
            overflowY: "auto",
          }}
          loading={getPerformanceBonus.isLoading}
        />
      </div>
      <div className="performance-bonus-ui__paging mt-2">
        <AppPagination
          current={searchParams.currentPage}
          pageSize={searchParams.pageSize}
          onChange={handlePagination}
          total={getPerformanceBonus.data?.totalCount ?? 0}
        />
      </div>
      <ModalDetailPerformance
        open={showModalDetail}
        onCancel={onCancel}
        onOk={(): void => {
          getPerformanceBonus.refetch();
        }}
        title="Chi tiết Performance bonus"
        valueSearch={selectPerformanceBonus}
      />
    </div>
  );
}

export default React.memo(PerformanceBonus);
