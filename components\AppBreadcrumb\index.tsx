import React from "react";
import "./index.scss";
import Link from "next/link";
import {Breadcrumb} from "antd";

interface ItemsBreadcrumb {
  breadcrumb: string;
  href: string;
}

interface Props {
  separator: ">" | "/";
  items: ItemsBreadcrumb[];
}

function AppBreadcrumb(props: Props): JSX.Element {
  const {separator, items} = props;
  const lastIndex: number = items?.length > 0 ? items.length - 1 : 0;
  return (
    <div className="breadcrumb-ui">
      <Breadcrumb separator={separator}>
        {items.map((item, index) => (
          <Breadcrumb.Item key={index}>
            {index === lastIndex ? (
              <span>{item.breadcrumb}</span>
            ) : (
              <Link href={item.href}>{item.breadcrumb}</Link>
            )}
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>
    </div>
  );
}

export default React.memo(AppBreadcrumb);
