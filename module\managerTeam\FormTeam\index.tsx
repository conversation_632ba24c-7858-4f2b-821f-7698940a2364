import ApiTeam, {IEmployee, IFormGroup, IResTeamDetail} from "@app/api/ApiTeam";
import {TextInput} from "@app/components/TextInput";
import {Col, Row, notification} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import AppTable from "@app/components/AppTable";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import {statusEmployee} from "@app/utils/constants/state";
import {ColumnsType} from "antd/lib/table";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {useMutation, useQueries, useQuery} from "react-query";
import {SelectInput} from "@app/components/SelectInput";
import {useRouter} from "next/router";
import AppAccessDenied from "@app/components/AppAccessDenied";
import AppLoading from "@app/components/AppLoading";
import {OptionSelect} from "@app/types";
import ModalAddUserGroup from "../ModalAddUserGroup";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";

interface FormTeamProps {
  type: "create" | "edit";
}
interface IForm {
  name: string;
  roleTypeNameSelected: OptionSelect;
  groupLeaderNameSelected: OptionSelect;
}

export default function FormTeam(props: FormTeamProps): JSX.Element {
  const {type} = props;
  const isTypeEdit = type === "edit";
  const router = useRouter();
  const timeOut = useRef<any>();
  const [listEmployee, setListEmployee] = useState<IEmployee[]>([]);
  const refForm = useRef<FormikProps<IForm>>(null);
  const [isShowModalAddUser, setIsShowModalAddUser] = useState<boolean>(false);
  const dispatch = useDispatch();
  let id: string;
  if (router.query.id) {
    id = router.query.id as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    id = Object.fromEntries(searchParams)?.id;
  }

  const getDetailTeam = useQuery(
    ["getDetailTeam", id],
    () => {
      return ApiTeam.getDetailTeam(id);
    },
    {
      enabled: !!id,
      onSuccess: (data: IResTeamDetail): void => {
        const employeeGroupList =
          data?.employeeGroupList?.filter(
            (i) => i.userId !== data?.groupLeaderId
          ) || ([] as IEmployee[]);
        setListEmployee(employeeGroupList);
      },
      onError: (error: any): void => {
        if (error?.errorCode === 400) {
          timeOut.current = setTimeout(() => {
            router.back();
          }, 4000);
        }
      },
    }
  );

  const editAccount = useMutation((data: IFormGroup) => {
    return ApiTeam.editGroup(data);
  });

  const createGroup = useMutation((data: IFormGroup) => {
    return ApiTeam.createGroup(data);
  });

  const getAllData = useQueries([
    {
      queryKey: ["getListAccountManagerLeader"],
      queryFn: () => ApiTeam.getListAccountManagerLeader(),
    },
    {
      queryKey: ["getListConsultantLeader"],
      queryFn: () => ApiTeam.getListConsultantLeader(),
    },
    {
      queryKey: ["getTypeGroup"],
      queryFn: () => ApiTeam.getTypeGroup(),
    },
    {
      queryKey: ["getListBusinessDevelopmentLeader"],
      queryFn: () => ApiTeam.getListBusinessDevelopmentLeader(),
    },
  ]);

  const optionAML =
    getAllData[0]?.data?.map((item) => ({
      key: String(item.userId),
      label: item.name,
      value: item.name,
    })) || [];

  const optionCSL =
    getAllData[1]?.data?.map((item) => ({
      key: String(item.userId),
      label: item.name,
      value: item.name,
    })) || [];

  const optionTypeGroup =
    getAllData[2]?.data?.map((item) => ({
      key: item.roleTypeId,
      label: item.name,
      value: item.name,
    })) || [];

  const optionBDL =
    getAllData[3]?.data?.map((item) => ({
      key: String(item.userId),
      label: item.name,
      value: item.name,
    })) || [];

  const getTypeGroup = (type?: string): OptionSelect => {
    return optionTypeGroup.find((i) => i.key === type) || ({} as OptionSelect);
  };

  const getLeader = (type?: string, groupLeaderId?: number): OptionSelect => {
    if (type === "AM") {
      return (
        optionAML.find((i) => i.key === String(groupLeaderId)) ||
        ({} as OptionSelect)
      );
    }
    if (type === "BD") {
      return (
        optionBDL.find((i) => i.key === String(groupLeaderId)) ||
        ({} as OptionSelect)
      );
    }

    return (
      optionCSL.find((i) => i.key === String(groupLeaderId)) ||
      ({} as OptionSelect)
    );
  };

  const save = (): void => {
    const values = refForm.current?.values;
    if (
      !(
        values?.name?.trim() &&
        values?.roleTypeNameSelected?.key &&
        values.groupLeaderNameSelected?.key
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng điền đầy đủ thông tin nhóm",
      });
    } else {
      dispatch(setLoading(true));
      const param = {
        name: values.name.trim(),
        groupLeaderId: Number(values.groupLeaderNameSelected.key),
        roleTypeId: values?.roleTypeNameSelected.key,
        listUserIds: listEmployee?.map((i) => i.userId),
      };

      if (type === "edit") {
        editAccount.mutate(
          {
            ...param,
            userGroupId: getDetailTeam?.data?.userGroupId,
          },
          {
            onSuccess: () => {
              notification.success({
                message: "Thông báo",
                description: "Cập nhật nhóm thành công",
              });
              dispatch(setLoading(false));
              router.back();
            },
            onError: () => {
              dispatch(setLoading(false));
            },
          }
        );
      } else {
        createGroup.mutate(param, {
          onSuccess: () => {
            notification.success({
              message: "Thông báo",
              description: "Tạo nhóm thành công",
            });
            dispatch(setLoading(false));
            router.back();
          },
          onError: () => {
            dispatch(setLoading(false));
          },
        });
      }
    }
  };

  const initialValues: IForm = {
    name: getDetailTeam.data?.name || "",
    roleTypeNameSelected: getTypeGroup(getDetailTeam.data?.roleTypeId),
    groupLeaderNameSelected: getLeader(
      getDetailTeam.data?.roleTypeId,
      getDetailTeam.data?.groupLeaderId
    ),
  };

  useEffect(() => {
    refForm.current?.setValues(initialValues);
  }, [JSON.stringify(initialValues)]);

  const columns: ColumnsType<IEmployee> = [
    {
      title: "Tên nhân viên",
      dataIndex: "name",
      key: "name",
      className: "cursor-pointer",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
    },
    {
      title: "Trạng thái tài khoản",
      dataIndex: "statusName",
      key: "statusName",
      render: (_, item: IEmployee): JSX.Element => {
        const getStatusEmployee = (statusName: string): OptionSelect => {
          return (
            statusEmployee.find((item) => item.label === statusName) || {
              label: "",
              value: "",
              color: "white",
            }
          );
        };

        return (
          <span
            className="status"
            style={{backgroundColor: getStatusEmployee(item?.statusName).color}}
          >
            {getStatusEmployee(item?.statusName).label}
          </span>
        );
      },
    },
    {
      title: "Ngày thêm vào nhóm",
      dataIndex: "groupJoinDate",
      key: "groupJoinDate",
      render: (_, item: IEmployee) => (
        <span>
          {item.groupJoinDate
            ? moment(item.groupJoinDate).format(DATE_FORMAT)
            : ""}
        </span>
      ),
    },
    {
      title: "",
      dataIndex: "",
      key: "action",
      render: (_, item: IEmployee) => (
        <AppButton
          typebutton="normal"
          classrow="btn-delete"
          onClick={(): void => {
            const newList = listEmployee.filter(
              (i) => i.userId !== item.userId
            );
            setListEmployee(newList);
          }}
        >
          <Icon icon="delete-bin-6-line" size={14} color="#dc2323" />
        </AppButton>
      ),
      width: "7%",
    },
  ];

  if (getDetailTeam.isLoading) {
    return (
      <div className="h-[80vh]">
        <AppLoading />
      </div>
    );
  }

  if (getDetailTeam?.error?.errorCode === 400) {
    return (
      <div className="p-12">
        <AppAccessDenied />
      </div>
    );
  }

  const dataTeamLeader = (role: string): any => {
    switch (role) {
      case "CS":
        return optionCSL;
      case "AM":
        return optionAML;
      case "BD":
        return optionBDL;
      default:
        return undefined;
    }
  };

  return (
    <div className="form-team-container">
      <h5 className="font-bold text24 text-color-primary">
        {`${isTypeEdit ? "Chỉnh sửa" : "Tạo mới"} nhóm nhân viên`}
      </h5>
      <Formik
        innerRef={refForm}
        initialValues={initialValues}
        onSubmit={(): void => {
          //
        }}
      >
        {({values, setFieldValue}): JSX.Element => {
          return (
            <div>
              <div className="card-information">
                <span className="font-bold text16">Thông tin chung</span>
                <Row gutter={[64, 32]} className="mt-4">
                  <Col xs={12}>
                    <TextInput
                      label="Tên nhóm"
                      name="name"
                      value={values?.name}
                      required
                      status={!values?.name ? "error" : undefined}
                    />
                  </Col>
                  <Col xs={12}>
                    <SelectInput
                      name="roleTypeNameSelected"
                      labelselect="Loại nhóm"
                      data={optionTypeGroup}
                      value={values?.roleTypeNameSelected?.value}
                      required
                      status={
                        !values?.roleTypeNameSelected?.value
                          ? "error"
                          : undefined
                      }
                      disabled={type === "edit"}
                      handleChange={(): void => {
                        setFieldValue(
                          "groupLeaderNameSelected",
                          {} as OptionSelect
                        );
                      }}
                    />
                  </Col>
                </Row>
                <Row gutter={[64, 32]} className="mt-6">
                  <Col xs={12}>
                    <SelectInput
                      name="groupLeaderNameSelected"
                      labelselect="Trưởng nhóm"
                      data={dataTeamLeader(
                        String(values?.roleTypeNameSelected?.key)
                      )}
                      value={values?.groupLeaderNameSelected?.value}
                      disabled={!values?.roleTypeNameSelected?.value}
                      required
                      status={
                        !values?.groupLeaderNameSelected?.value
                          ? "error"
                          : undefined
                      }
                    />
                  </Col>
                </Row>
              </div>
              <Row className="justify-between items-center mt-5">
                <span className="font-bold text16 text-color-primary">
                  Danh sách nhân viên
                </span>
                <AppButton
                  typebutton="normal"
                  classrow="add-btn"
                  onClick={(): void => setIsShowModalAddUser(true)}
                  disabled={
                    !values?.groupLeaderNameSelected?.value ||
                    !values?.roleTypeNameSelected?.value
                  }
                >
                  + Thêm thành viên
                </AppButton>
              </Row>
              <ModalAddUserGroup
                open={isShowModalAddUser}
                handleClose={(): void => setIsShowModalAddUser(false)}
                typeGroup={values?.roleTypeNameSelected?.key || "CS"}
                listEmployeeInGroup={listEmployee.map((item) => ({
                  ...item,
                  isAdd: true,
                }))}
                setListEmployeeInGroup={setListEmployee}
                idLeader={values?.groupLeaderNameSelected?.key as string}
              />
            </div>
          );
        }}
      </Formik>
      <div className="mt-4">
        <AppTable
          dataSource={listEmployee?.map((item: IEmployee, index: number) => ({
            ...item,
            key: index,
          }))}
          columns={columns}
          scroll={{y: "32vh"}}
        />
      </div>
      <Row className="justify-center items-center mt-6">
        <AppButton
          classrow="w-48 mr-10"
          label="Huỷ"
          typebutton="secondary"
          onClick={(): void => {
            router.back();
          }}
        />
        <AppButton
          classrow="w-48 ml-10"
          label="Lưu"
          typebutton="primary"
          onClick={save}
        />
      </Row>
    </div>
  );
}
