.container {
  .row-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .number-cv {
      font-size: 24px;
      font-weight: 400;
      color: $text-color-input;
    }

    .row-type-display {
      overflow: hidden;
      border-radius: 8px;

      button {
        border: none;
        font-size: 12px;
        font-weight: 400;
        color: $text-color-input;
      }

      .btn-active {
        button {
          background-color: rgba(47, 107, 255, 0.7);
        }
      }

      .btn-inactive {
        button {
          background-color: $header_tf05;
        }
      }
    }
  }
  .container-list-candidate {
    margin-top: 10px;
  }
}
