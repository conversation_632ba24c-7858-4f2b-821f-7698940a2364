.text-input-container {
  position: relative;
  width: "100%";

  .label {
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 15px;
    top: 16px;
    transition: 0.2s ease all;
    line-height: 20px;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 70%;
  }

  .as-placeholder {
    color: $header_tf;
  }

  .as-label {
    top: 6px;
    font-size: 0.75rem;
    padding: 0 4px;
    margin-left: -4px;
    color: $header_tf;
  }

  .text-required {
    color: red;
  }

  .ant-picker {
    border-radius: 8px;
    padding: 23px 10px 7px 15px;
    height: 52px;
    min-width: 100%;

    .ant-picker-suffix {
      margin-top: -16px;
    }

    input {
      font-size: 0.875rem;
      font-weight: 400;
      color: $text-color-input;
    }
  }

  input {
    font-size: 1rem;
  }

  .ant-picker-clear {
    right: 1px;
    top: 20%;
  }
}
