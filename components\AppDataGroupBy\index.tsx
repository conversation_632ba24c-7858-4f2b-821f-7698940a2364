import {memo, useEffect, useMemo, useState} from "react";
import "./index.scss";
import AppTable from "../AppTable";
import {Affix, Empty, Skeleton} from "antd";
import AppCollapse from "../AppCollapse";
import CollapsePanel from "antd/lib/collapse/CollapsePanel";
import {DataGroupByItem} from "@app/types";
import AppPagination from "../AppPagination";
import {ColumnsType} from "antd/es/table";

interface Props {
  column: ColumnsType<any>;
  dataGroupBy: Array<DataGroupByItem>;
  rowTableClassName?: string;
  onClickItem: (id: string, currentPage: number) => void;
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  onPageChangeGroupBy: (page: number, size: number) => void;
  dataQueryGroupBy?: any;
  isLoadingGroupBy?: boolean;
}

function AppDataGroupBy(props: Props) {
  const {
    column,
    dataGroupBy,
    rowTableClassName,
    onClickItem,
    pageNumber,
    pageSize,
    totalCount,
    onPageChangeGroupBy,
    dataQueryGroupBy,
    isLoadingGroupBy,
  } = props;
  const [scrollPosition, setScrollPosition] = useState<number>(0);
  const [active, setActive] = useState<Array<string>>([]);

  const columnTable = useMemo(() => {
    const cloneCol = column.map((col, index) => {
      if (index === 0) {
        return {
          ...col,
          // width: 180,
          fixed: "left",
        };
      }
      return {
        ...col,
        // width: 180,
      };
    });
    return cloneCol;
  }, [column]);

  useEffect(() => {
    const tableContent = document.querySelectorAll(".ant-table-content");

    const arr = Array.from(tableContent);

    arr.forEach((item) => {
      item.scrollLeft = scrollPosition;
      item.scrollTo({
        behavior: "smooth",
      });
    });
  }, [scrollPosition, dataGroupBy]);

  const handleCollapseActive = (value: Array<string>) => {
    setActive(value);
  };

  useEffect(() => {
    setActive([]);
  }, [totalCount, pageNumber, pageSize, dataQueryGroupBy]);

  return (
    <div className="app-data-group-by">
      <div className="">
        <div className="app-data-group-by__header">
          <Affix>
            <AppTable
              columns={columnTable as any}
              dataSource={[]}
              rowClassName={rowTableClassName}
              bordered
              pagination={false}
              scroll={{x: "max-content"}}
              handlePositionScroll={setScrollPosition}
            />
          </Affix>
        </div>
        {isLoadingGroupBy ? (
          <>
            <Skeleton className="mt-2" />
            <Skeleton className="mt-2" />
            <Skeleton className="mt-2" />
            <Skeleton className="mt-2" />
          </>
        ) : (
          <>
            <AppCollapse
              className="w-full"
              // defaultActiveKey={active}
              activeKey={active}
              destroyInactivePanel
              onChange={(value) => {
                if (typeof value !== "string") {
                  if (value.length > active.length) {
                    const dataDifferent = value.filter(
                      (key) => !active.includes(key)
                    );
                    if (dataDifferent.length > 0) {
                      onClickItem(dataDifferent[0], 1);
                    }
                  }
                  handleCollapseActive(value);
                }
              }}
            >
              {dataGroupBy.map((item) => {
                const key = String(item?.groupByValue);
                return (
                  <CollapsePanel
                    key={key}
                    header={`${item.groupByName} (${item.totalRecords || 0})`}
                    id={key}
                  >
                    <div className="app-data-group-by__body">
                      {item?.data && item?.data?.length ? (
                        <div className="mb-4">
                          <AppTable
                            columns={columnTable as any}
                            dataSource={item.data}
                            rowClassName={rowTableClassName}
                            rowKey="applicationId"
                            key="applicationId"
                            showHeader={false}
                            bordered
                            pagination={false}
                            scroll={{x: "max-content"}}
                            handlePositionScroll={setScrollPosition}
                          />
                          <AppPagination
                            defaultPageSize={20}
                            current={item?.pageNumber || 1}
                            pageSize={20}
                            total={item?.totalCount || 0}
                            showSizeChanger={false}
                            onChange={(page: number) => {
                              onClickItem(key, page);
                            }}
                          />
                        </div>
                      ) : (
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                      )}
                    </div>
                  </CollapsePanel>
                );
              })}
            </AppCollapse>
            <AppPagination
              pageSize={pageSize}
              current={pageNumber}
              total={totalCount}
              onChange={onPageChangeGroupBy}
            />
          </>
        )}
      </div>
    </div>
  );
}

export default memo(AppDataGroupBy);
