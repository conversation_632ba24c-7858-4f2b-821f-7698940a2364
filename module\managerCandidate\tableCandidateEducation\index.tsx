import {
  InputNumber,
  Form,
  Input,
  Popconfirm,
  Typography,
  DatePicker,
  Tooltip,
  Row,
} from "antd";
import React, {useEffect, useState} from "react";
import Icon from "@app/components/Icon/Icon";
import "./index.scss";
import {IEducations} from "@app/api/ApiCandidate";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import {formatTimeToTableEdit} from "@app/utils/constants/function";
import {useSelector, useDispatch} from "react-redux";
import {IRootState} from "@app/redux/store";
import {setStateTable} from "@app/redux/slices/EditTableSlice";
import AppTable from "@app/components/AppTable";

const {RangePicker} = DatePicker;

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  inputType: "number" | "text" | "date";
  index: number;
  children: React.ReactNode;
  record: IEducations;
}

interface Prop {
  dataSource: IEducations[];
  updateDataCandidateEducation?: (data: IEducations[]) => void;
}

// eslint-disable-next-line react/function-component-definition
const TableCandidateEducation: React.FC<Prop> = ({...props}) => {
  const {dataSource, updateDataCandidateEducation} = props;
  const [form] = Form.useForm();
  const [data, setData] = useState<IEducations[]>([]);
  const [count, setCount] = useState(data.length);
  const stateTable = useSelector((state: IRootState) => state.editTableSlice);
  const dispatch = useDispatch();

  const onChangeDate = (dates: any) => {
    form.setFieldValue("timeEducation", dates);
  };

  useEffect(() => {
    if (dataSource && dataSource?.length > 0) {
      const newData =
        dataSource?.map((item, index) => ({
          ...item,
          key: String(index),
        })) || [];
      setData(newData);
      setCount(newData?.length);
    } else {
      setData([]);
      setCount(0);
    }
  }, [dataSource]);

  // eslint-disable-next-line react/no-unstable-nested-components, react/function-component-definition
  const EditableCell: React.FC<EditableCellProps> = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "number" ? (
        <Form.Item name={dataIndex}>
          <InputNumber />
        </Form.Item>
      ) : inputType === "date" ? (
        <Form.Item name={dataIndex}>
          <RangePicker
            format={DATE_FORMAT}
            onChange={onChangeDate}
            disabledDate={(current) => {
              return current && current > moment().endOf("day");
            }}
          />
        </Form.Item>
      ) : (
        <Form.Item name={dataIndex}>
          <Input />
        </Form.Item>
      );

    return <td {...restProps}>{editing ? inputNode : children}</td>;
  };

  const isEditing = (record: IEducations) =>
    record.key === stateTable.editingKeyEducation;

  const edit = (record: Partial<IEducations> & {key: string}) => {
    form.setFieldsValue({
      ...record,
      timeEducation: [
        formatTimeToTableEdit(record?.startDate as string),
        formatTimeToTableEdit(record?.endDate as string),
      ],
    });
    dispatch(
      setStateTable({
        ...stateTable,
        editingKeyEducation: record.key,
      })
    );
  };

  const handleDelete = (key: string) => {
    const newData: IEducations[] = data.filter((item: IEducations) => {
      return item.key !== key;
    });
    setData(newData);
    setCount(newData?.length || 0);
    updateDataCandidateEducation?.(newData);
  };

  const cancel = (key: string) => {
    if (
      !(
        data[Number(key)]?.schoolName &&
        data[Number(key)]?.degreeType &&
        data[Number(key)]?.fieldOfStudy &&
        data[Number(key)]?.startDate &&
        data[Number(key)]?.endDate
      )
    ) {
      handleDelete(key);
    }
    dispatch(
      setStateTable({
        ...stateTable,
        isAddRowEducation: false,
        editingKeyEducation: "",
      })
    );
  };

  const handleAdd = () => {
    const newData = {
      key: count.toString(),
      degreeType: "",
      endDate: "",
      fieldOfStudy: "",
      schoolName: "",
      startDate: "",
    };
    setData([...data, newData]);
    updateDataCandidateEducation?.([...data, newData]);
    setCount(count + 1);
    dispatch(
      setStateTable({
        ...stateTable,
        isAddRowEducation: true,
      })
    );
    edit(newData);
  };

  const save = async (key: string) => {
    try {
      const row = await form.validateFields();
      const dataChange = {
        startDate:
          row?.timeEducation && row?.timeEducation[0]
            ? row?.timeEducation[0]?.format(DATE_FORMAT)
            : "",
        endDate:
          row?.timeEducation && row?.timeEducation[1]
            ? row?.timeEducation[1].format(DATE_FORMAT)
            : "",
        key: String(key),
        schoolName: row?.schoolName ? row?.schoolName : "",
        degreeType: row?.degreeType ? row?.degreeType : "",
        fieldOfStudy: row?.fieldOfStudy ? row?.fieldOfStudy : "",
      };
      const newData = [...data];
      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, {
          ...item,
          ...dataChange,
        });
        setData(newData);
      } else {
        newData.push(dataChange);
        setData(newData);
      }
      updateDataCandidateEducation?.(newData);
      dispatch(
        setStateTable({
          ...stateTable,
          isAddRowEducation: false,
          editingKeyEducation: "",
        })
      );
      form.resetFields();
    } catch (errInfo) {
      //
    }
  };

  const columnsEducation = [
    {
      title: "Trường học",
      dataIndex: "schoolName",
      width: "20%",
      editable: true,
      ellipsis: true,
      render: (_: string, record: IEducations) => {
        return record?.schoolName ? (
          <Tooltip title={record?.schoolName} placement="bottomLeft">
            <span>{record?.schoolName}</span>
          </Tooltip>
        ) : (
          <span />
        );
      },
    },
    {
      title: "Bằng cấp",
      dataIndex: "degreeType",
      width: "15%",
      editable: true,
      ellipsis: true,
      render: (_: string, record: IEducations) => {
        return record?.degreeType ? (
          <Tooltip title={record?.degreeType} placement="bottomLeft">
            <span>{record?.degreeType}</span>
          </Tooltip>
        ) : (
          <span />
        );
      },
    },
    {
      title: "Chuyên ngành",
      dataIndex: "fieldOfStudy",
      width: "20%",
      editable: true,
      ellipsis: true,
      render: (_: string, record: IEducations) => {
        return record?.fieldOfStudy ? (
          <Tooltip title={record?.fieldOfStudy} placement="bottomLeft">
            <span>{record?.fieldOfStudy}</span>
          </Tooltip>
        ) : (
          <span />
        );
      },
    },
    {
      title: "Thời gian",
      dataIndex: "timeEducation",
      width: "35%",
      editable: true,
      ellipsis: true,
      render: (_: string, record: IEducations) => {
        const timeDisplay = (time: string): string => {
          if (!time) return "";
          const timeSplit = time.split("/");
          timeSplit.shift();
          return timeSplit.join("/");
        };
        return (
          <span>
            {record?.startDate &&
              record?.endDate &&
              `${timeDisplay(record?.startDate)} - ${timeDisplay(
                record?.endDate
              )}`}
          </span>
        );
      },
    },
    {
      title: "",
      dataIndex: "operation",
      width: "10%",
      render: (_: any, record: IEducations) => {
        const editable = isEditing(record);
        return editable ? (
          <span>
            <Typography.Link
              onClick={() => save(record.key as any)}
              style={{marginRight: 8}}
            >
              <span className="cursor-pointer">
                <Icon icon="check-line" size={12} />
              </span>
            </Typography.Link>
            <Popconfirm
              title="Sure to cancel?"
              onConfirm={() => cancel(record.key as any)}
            >
              <span className="cursor-pointer">
                <Icon icon="close-line" size={12} />
              </span>
            </Popconfirm>
          </span>
        ) : (
          <div className="flex items-center justify-center">
            <div className="mr-4">
              <Typography.Link
                disabled={stateTable.editingKeyEducation !== ""}
                onClick={() => handleDelete(record.key as any)}
              >
                <Icon icon="delete-bin-6-line" size={12} />
              </Typography.Link>
            </div>
            <div>
              <Typography.Link
                disabled={stateTable.editingKeyEducation !== ""}
                onClick={() => {
                  edit(record as any);
                }}
              >
                <Icon icon="edit-line" size={12} />
              </Typography.Link>
            </div>
          </div>
        );
      },
    },
  ];

  const mergedColumns = columnsEducation.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: IEducations) => ({
        record,
        inputType:
          col.dataIndex === ""
            ? "number"
            : col.dataIndex === "timeEducation"
            ? "date"
            : "text",
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  return (
    <Form form={form} component={false}>
      <Row className="mb-1 items-baseline">
        <span className="text16 font-bold">Học vấn</span>
        {!(stateTable.isAddRowEducation || stateTable.editingKeyEducation) && (
          // eslint-disable-next-line jsx-a11y/no-static-element-interactions
          <span onClick={handleAdd} className="ml-2 cursor-pointer flex">
            <Icon icon="user-add-line" size={14} />
          </span>
        )}
      </Row>
      <AppTable
        className="table-education"
        pagination={false}
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        bordered
        dataSource={data as any}
        columns={mergedColumns as any}
        rowClassName="editable-row"
        key="key"
        rowKey={(record) => record.key}
      />
    </Form>
  );
};

export default React.memo(TableCandidateEducation);
