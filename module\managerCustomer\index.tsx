import React, {useEffect, useState} from "react";
import "./index.scss";
// eslint-disable-next-line import/no-cycle
import FormFilter from "./FormFilter";
// import AppButton from "@app/components/AppButton";
// import Icon from "@app/components/Icon/Icon";
import AppTable from "@app/components/AppTable";
import ApiCustomer, {
  ICustomer,
  IParamsListCustomer,
  IResCustomer,
} from "@app/api/ApiCustomer";
import {useQuery} from "react-query";
import AppPagination from "@app/components/AppPagination";
import {ColumnsType} from "antd/lib/table";
import {getStatusCustomer} from "@app/utils/constants/function";
import {IAccountRole, OptionSelect} from "@app/types";
import {
  changeColCustomer,
  initListColShowCustomer,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {useSelector, useDispatch} from "react-redux";
import {useRouter} from "next/router";
import config from "@app/config";
import _ from "lodash";
import {rankOptions} from "@app/utils/constants/state";

export interface IFormFilter extends IParamsListCustomer {
  customerStatusSelected?: OptionSelect;
  companyTypeSelected?: OptionSelect;
  managersSelected?: OptionSelect[];
}

export default function ManagerCustomer(): JSX.Element {
  const route = useRouter();

  let query = {} as any;

  if (!_.isEmpty(route.query)) {
    query = route.query;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    query = Object.fromEntries(searchParams);
  }

  query = {
    ...query,
    currentPage: query.currentPage ? Number(query.currentPage) : 1,
    pageSize: query.pageSize ? Number(query.pageSize) : 20,
    companyTypeSelected: query.companyTypes
      ? {
          label: query.companyTypes,
          value: query.companyTypes,
        }
      : undefined,
    managers: query.managers
      ? query.managers?.split(",")?.map((i: any) => String(i))
      : [],
    customerStatuses: query.customerStatuses
      ? query.customerStatuses?.split(",")?.map((i: any) => String(i))
      : [],
    customerStatusSelected: getStatusCustomer(query.customerStatuses),
    companyTypes: query.companyTypes ? [query.companyTypes] : [],
  };

  const [valuesSearch, setValuesSearch] = useState<IFormFilter>(query);
  const {listColShowCustomer, user} = useSelector(selectUser);
  const dispatch = useDispatch();
  const isAML = user?.role?.includes(IAccountRole.AML);

  const getListCustomer = useQuery(
    ["getListCustomer", valuesSearch],
    () => {
      const searchQuery = {
        ...valuesSearch,
        isFirstInitialization: true,
      };
      delete searchQuery.customerStatusSelected;
      delete searchQuery.companyTypeSelected;
      return ApiCustomer.getListCustomer(searchQuery);
    },
    {
      onSuccess: (data: IResCustomer): void => {
        const newQuery = {
          name: data.name,
          textSearch: data.textSearch,
          companyTypes: data.companyTypes ? data.companyTypes.join() : "",
          managers: data.managers ? data.managers.join() : "",
          pageSize: data.pageSize,
          currentPage: data.currentPage,
          customerStatuses: data.customerStatuses
            ? data.customerStatuses.join()
            : "",
        } as any;

        Object.keys(newQuery).forEach((key) => {
          if (!newQuery[key]) {
            delete newQuery[key];
          }
        });

        route.push(route.pathname, {
          query: newQuery,
        });
      },
    }
  );

  useEffect(() => {
    if (!listColShowCustomer) {
      dispatch(changeColCustomer(initListColShowCustomer));
    }
  }, []);

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesSearch({
      ...valuesSearch,
      currentPage: page,
      pageSize,
    });
  };

  const columnAll: ColumnsType<ICustomer> = [
    {
      title: "AM quản lý",
      dataIndex: "manager",
      key: "manager",
      width: 150,
    },
    {
      title: "Tên khách hàng",
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: "Tên người liên hệ",
      dataIndex: "contact",
      key: "contact",
      width: 150,
    },
    {
      title: "Số điện thoại",
      dataIndex: "phone",
      key: "phone",
      width: 120,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Loại hình",
      dataIndex: "companyType",
      key: "companyType",
      width: 120,
    },
    {
      title: "Loại khách hàng",
      dataIndex: "rank",
      key: "rank",
      width: 120,
      render: (rank: string) => (
        <div>{rankOptions.find((item) => item.value === rank)?.label}</div>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "customerStatus",
      key: "customerStatus",
      width: 115,
      render: (_, item: ICustomer) => (
        <span
          className="status-customer"
          style={{
            backgroundColor: getStatusCustomer(String(item.customerStatus))
              .color,
          }}
        >
          {getStatusCustomer(String(item.customerStatus)).label}
        </span>
      ),
    },
    {
      title: "Địa chỉ",
      dataIndex: "address",
      key: "address",
    },
    {
      title: "Số lượng request job",
      dataIndex: "countRequestJob",
      width: 115,
      key: "countRequestJob",
    },
  ];

  const columns = columnAll.filter((item) =>
    [
      ...(listColShowCustomer || initListColShowCustomer),
      isAML ? "manager" : "",
      "name",
    ].some((i) => i === item.key)
  );

  return (
    <div className="manager-customer-container">
      <FormFilter
        listCustomerData={getListCustomer.data}
        setValuesSearch={setValuesSearch}
        valuesSearch={valuesSearch}
        sumCol={columnAll.length}
      />
      {/* {isAML && (
        <AppButton typebutton="normal" classrow="btn-export">
          <Icon icon="download-cloud-line" size={24} color="#324054" />
        </AppButton>
      )} */}
      <div className="mt-3">
        <AppTable
          rowClassName="cursor-pointer"
          onRow={(record: ICustomer): any => {
            return {
              onClick: (): void => {
                route.push(
                  `${config.PATHNAME.CUSTOMER_DETAIL}?id=${record.customerId}`
                );
              },
            };
          }}
          dataSource={getListCustomer.data?.customersPaging?.map(
            (item: ICustomer, index: number) => ({
              ...item,
              key: index,
            })
          )}
          columns={columns}
          loading={getListCustomer.isLoading}
          scroll={{y: "60vh", x: 1500}}
        />
        <AppPagination
          className="mt-6"
          defaultPageSize={valuesSearch.pageSize}
          current={valuesSearch.currentPage}
          pageSize={valuesSearch.pageSize}
          total={getListCustomer.data?.totalCount}
          onChange={handlePagination}
        />
      </div>
    </div>
  );
}
