# API
```tsx
import {fetcher} from "./Fetcher"; // Import thư viện

// Khai báo type cho params, body, response
export interface IUserLogin {
  _id?: string;
  fullName?: string;
  email?: string;
}

export interface IParamsGetUser {
  sort?: string[];
  searchFields?: string[];
  pageSize?: number;
  pageNumber?: number;
  disablePagination?: boolean;
  search?: string;
  searchType?: string;
}

// Khai báo url
const path = {
    login: "/auth/login",
    getMe: "/users/me",
    getUserAccount: "/users",
};

// Khai báo API
function getUserAccount(params?: IParamsGetUser): Promise<IUserLogin[]> { 
    return fetcher({url: path.getUserAccount, method: "get", params: params});
}

```

Lưu ý: 
- Khai báo interface parameters, body, response phải đúng với server trả về nếu không sẽ bị sai tất cả các phần xử lý đằng sau. Nhất là các trường hợp trường dữ liệu trả về có thể bị null hoặc undefined
- Đặt tên cho interface thì phải có chữ `I` đằng trước. Ví dụ: IUserLogin, IParamsGetUser
