import AppBreadcrumb from "@app/components/AppBreadcrumb";
import {SelectInput} from "@app/components/SelectInput";
import {IAccountRole, OptionSelect, StatusRequestJob} from "@app/types";
import {Col, Input, Row, notification} from "antd";
import {Formik, FormikProps} from "formik";
import moment, {Moment} from "moment";
import React, {useEffect, useMemo, useRef, useState} from "react";
import {TextInput} from "@app/components/TextInput";
import "./index.scss";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {
  currencyList,
  deadTimeFastSearch,
  fileTypeJdAllow,
  labelOption,
  messageErrorUploadCv,
  optionGuarantee,
  optionServiceRequest,
  priorities,
  requestTypes,
  statusRequest,
} from "@app/utils/constants/state";
import {useMutation, useQueries, useQuery} from "react-query";
import ApiRequestJob, {
  BonusTypeOptionSelect,
  DataUpdateStatusJob,
  IParamsJobCode,
  InfoJobDescription,
  OptionSelectCustomer,
} from "@app/api/ApiRequestJob";
import {useRouter} from "next/router";
import config from "@app/config";
import AppButton from "@app/components/AppButton";
// eslint-disable-next-line import/no-cycle
import InformationCustomer from "./informationCustomer";
// eslint-disable-next-line import/no-cycle
import GeneralInformation from "./generalInformation";
import RequestEditor from "./editor";

import {useDispatch, useSelector} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import Icon from "@app/components/Icon/Icon";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {
  moneyToNumber,
  setAllFieldTouched,
  sortWorkLocation,
  useDebounce,
} from "@app/utils/constants/function";
import {
  messageSuccessRequestJob,
  messageValidate,
} from "@app/utils/constants/message";
import {selectUser} from "@app/redux/slices/UserSlice";
import AppModalConfirm from "@app/components/AppModalConfirm";
import AppLoading from "@app/components/AppLoading";
import ApiUploadCv from "@app/api/ApiUploadCv";

interface Props {
  type: "create" | "edit";
}

export interface DataInitFormRequest {
  priority?: OptionSelect;
  requestType?: OptionSelect;
  label?: OptionSelect;
  partnerRateValue?: number | string;
  experienceType?: any;
  currencyTypeId: OptionSelect;
  partCurrencyTypeId?: OptionSelect;
  accountManagerId?: OptionSelect;
  name?: string;
  positionId: OptionSelect;
  levelIds: OptionSelect[];
  countryIds: OptionSelect[];
  workLocationIds: OptionSelect[];
  districtIds: OptionSelect[];
  salaryFrom: number | string;
  salaryTo: number | string;
  workTypeId?: OptionSelect;
  monthWarranty?: OptionSelect;
  customerId?: OptionSelect;
  requestJobCode?: string;
  requestSkillIds: OptionSelect[];
  proxyCustomer?: string;
  requestDate?: string | Moment | undefined;
  headcount: number | string;
  expiryDate?: string | Moment | undefined;
  filePathTemp?: string;
  customerRateValue?: number | string;
  note?: string;
  experienceYearFrom: number | string;
  experienceYearTo: number | string;
  status?: OptionSelect;
  experienceYear?: string;
  customerRateType?: OptionSelect;
  partnerRateType?: OptionSelect;
  website?: string;
  hotBonusId?: OptionSelect;
  hotBonusUnit?: OptionSelect;
  hotBonusAmount?: string | number;
  partnerCurrencyTypeId?: string;
  services: OptionSelect[];
  detailAddress?: string;
  durationValue?: number;
  durationUnit?: string;
}
interface AddressLocation {
  countryId: string[];
  workLocationId: string[];
}

export const listCustomerRateType: OptionSelect[] = [
  {value: "0", label: "Tỉ lệ"},
  {value: "VND", label: "VND"},
];

export const listPartnerRateType: OptionSelect[] = [
  {value: "0", label: "Tỉ lệ (%)"},
  {value: "1", label: "Số tiền"},
];

const optionStatusRequest: OptionSelect[] = statusRequest.filter(
  (item) => item.key !== "null"
);

const {TextArea} = Input;

function RequestForm(props: Props): JSX.Element {
  const {type} = props;
  const formikRef = useRef<FormikProps<DataInitFormRequest>>(null);
  const {user} = useSelector(selectUser);
  const userId = user?.userId || "";

  const roleUser = user?.role || [];
  const isRoleAmg = roleUser?.includes(IAccountRole.AMG);

  const [benefit, setBenefit] = useState("");
  const [requestDetail, setRequestDetail] = useState("");
  const [description, setDescription] = useState("");
  const [interviewProcess, setInterviewProcess] = useState("");
  const [textJDParams, setTextJDParams] = useState<string>();

  const debounceTextJDParams = useDebounce(textJDParams, deadTimeFastSearch);
  const [addressValue, setAddressValue] = useState<AddressLocation>({
    countryId: [],
    workLocationId: [],
  });
  const [file, setFile] = useState<{
    fileJDName: string;
    fileJD: string | File;
  }>({fileJD: "", fileJDName: ""});
  const [paramsJobCode, setParamsJobCode] = useState<IParamsJobCode>(
    {} as IParamsJobCode
  );
  const [showModalCancel, setShowModalCancel] = useState<boolean>(false);

  const [infoJobDescription, setInfoJobDescription] =
    useState<InfoJobDescription>();

  const fileUploadRef = useRef() as React.MutableRefObject<HTMLInputElement>;
  const router = useRouter();
  let idRequest: string | number = -1;
  if (type === "edit" && router.query.id) {
    idRequest = router.query.id as string;
  }
  const isTypeCreate = type === "create" && idRequest === -1;
  const dispatch = useDispatch();

  const handleShowModalCancel = (): void => {
    setShowModalCancel(true);
  };

  const visibleModalCancel = (): void => {
    setShowModalCancel(false);
  };

  const handleConfirmModal = (): void => {
    visibleModalCancel();
    router?.back();
  };

  const handleSyncDataToAI = useMutation((id: number) => {
    return ApiRequestJob.syncDataToAI(id);
  });

  const createRequest = useMutation(
    (data: FormData) => {
      return ApiRequestJob.createRequest(data);
    },
    {
      onSuccess: (data) => {
        notification?.success({
          message: "Thông báo",
          description: messageSuccessRequestJob,
        });
        handleSyncDataToAI.mutate(data.requestJobId);
        router.push(config.PATHNAME.MANAGER_REQUEST);
      },
    }
  );

  const editRequest = useMutation(
    (data: FormData) => {
      return ApiRequestJob.editRequest(data);
    },
    {
      onSuccess: (data) => {
        notification?.success({
          message: "Thông báo",
          description: messageSuccessRequestJob,
        });
        handleSyncDataToAI.mutate(data.requestJobId);
        formikRef.current?.resetForm();
        router.back();
      },
    }
  );

  const items = [
    {
      breadcrumb: "Danh sách request",
      href: config.PATHNAME.MANAGER_REQUEST,
    },
    {
      href: isTypeCreate
        ? config.PATHNAME.MANAGER_REQUEST_ADD
        : config.PATHNAME.MANAGER_REQUEST_EDIT,
      breadcrumb: isTypeCreate ? "Tạo mới request" : "Chỉnh sửa request",
    },
  ];

  const handleChangeCustomer = (
    value: string,
    interviewProcess: string,
    benefit: string
  ): void => {
    setParamsJobCode({
      ...paramsJobCode,
      customerId: value,
    });
    setBenefit(benefit);
    setInterviewProcess(interviewProcess);
  };

  const getAllData = useQueries([
    {
      queryKey: ["getWorkTypeList"],
      queryFn: () => ApiRequestJob.getWorkTypeList(),
    },
    {
      queryKey: ["getPositionList"],
      queryFn: () => ApiRequestJob.getPositionList(),
    },
    {
      queryKey: ["getLevelList"],
      queryFn: () => ApiRequestJob.getLevelList(),
    },
    {
      queryKey: ["getCountryList"],
      queryFn: () => ApiRequestJob.getCountryList(),
    },
    {
      queryKey: ["getWorkLocationList"],
      queryFn: () => ApiRequestJob.getWorkLocationList(),
    },
    {
      queryKey: ["getHotBonus"],
      queryFn: () => ApiRequestJob.getHotBonus(),
    },
    {
      queryKey: ["getCustomerList"],
      // eslint-disable-next-line consistent-return
      queryFn: () => {
        if (isTypeCreate) {
          return ApiRequestJob.getCustomerList();
        }
      },
    },
    {
      queryKey: ["getAMList"],
      queryFn: () => ApiRequestJob.getAMList(),
    },
    {
      queryKey: ["getSkillsList"],
      queryFn: () => ApiRequestJob.getSkillsList(),
    },
    {
      queryKey: ["getDistrictList"],
      queryFn: () => ApiRequestJob.getDistrictList(),
    },
  ]);

  const uploadFileTemp = useMutation((data: FormData) => {
    return ApiUploadCv.uploadFileTemp(data);
  });

  const workLocationListExpected = useMemo(() => {
    if (addressValue.countryId.length > 0) {
      const findWorkLocation: OptionSelect[] =
        (getAllData[4]?.data || [])
          .filter((item) => addressValue.countryId.includes(item.countryId))
          .map((item) => ({
            label: item?.name,
            value: item?.name,
            key: item?.workLocationId,
          })) || [];

      return findWorkLocation;
    }
    return [];
  }, [
    JSON.stringify(addressValue.countryId),
    JSON.stringify(getAllData[4]?.data),
  ]);

  const districtListExpected = useMemo(() => {
    if (addressValue.workLocationId?.length > 0) {
      const data: OptionSelect[] = (getAllData[9]?.data ?? [])
        ?.filter((item) =>
          addressValue.workLocationId?.includes(item.workLocationId)
        )
        .map((item) => ({
          label: item?.name,
          value: item?.name,
          key: item?.districtId,
        }));
      return data;
    }
    return [];
  }, [addressValue.workLocationId, JSON.stringify(getAllData[9]?.data)]);

  const allData = useMemo(() => {
    const workTypeList: OptionSelect[] = (getAllData[0]?.data ?? [])?.map(
      (item) => ({
        label: item?.name,
        key: String(item?.workTypeId),
        value: item?.name,
      })
    );

    const positionList: OptionSelect[] = (getAllData[1]?.data ?? [])?.map(
      (item) => ({
        label: item?.name,
        value: item?.name,
        key: String(item?.positionId),
      })
    );

    const levelList: OptionSelect[] = (getAllData[2]?.data ?? [])?.map(
      (item) => ({
        label: item?.name,
        value: item?.name,
        key: String(item?.levelId),
      })
    );

    const countryList: OptionSelect[] = (getAllData[3]?.data ?? [])?.map(
      (item) => ({
        label: item?.name,
        value: item?.name,
        key: String(item?.countryId),
      })
    );

    const workLocationList: OptionSelect[] = (getAllData[4]?.data ?? [])?.map(
      (item) => ({
        label: item?.name,
        value: item?.name,
        key: String(item?.workLocationId),
      })
    );

    const hotBonus: BonusTypeOptionSelect[] = (getAllData[5]?.data ?? [])?.map(
      (item) => ({
        label: item?.hotBonusName,
        value: item?.hotBonusName,
        key: String(item?.hotBonusId),
        amount: item?.amount ? String(Math.floor(item.amount)) : "",
      })
    );

    const hotBonusExpected = [
      ...hotBonus,
      {label: "Khác", value: "0", amount: "0", key: "0"},
    ];

    const customerIds: OptionSelectCustomer[] = (
      getAllData[6]?.data ?? []
    )?.map((item) => ({
      label: item?.name,
      value: item?.name,
      key: String(item?.customerId),
      website: item?.website || "",
      createdDate: item?.createdDate || "",
      benefit: item?.benefit || "",
      interviewProcess: item?.interviewProcess || "",
      contact: item?.contact || "",
    }));

    const amList: OptionSelect[] = (getAllData[7]?.data ?? [])?.map((item) => ({
      label: item?.name,
      value: item?.name,
      key: String(item?.userId),
    }));

    const skillsList: OptionSelect[] = (getAllData[8]?.data ?? [])?.map(
      (item) => ({
        label: item?.name,
        value: item?.name,
        key: String(item?.skillId),
      })
    );

    const districtList: OptionSelect[] = (getAllData[9]?.data ?? [])?.map(
      (item) => ({
        label: item?.name,
        value: item?.name,
        key: String(item?.districtId),
      })
    );

    return {
      workTypeList,
      districtList,
      skillsList,
      amList,
      customerIds,
      hotBonusExpected,
      workLocationList,
      countryList,
      positionList,
      levelList,
    };
  }, [JSON.stringify(getAllData)]);

  let customerIdParam: string;

  if (router.query.customerId) {
    customerIdParam = router.query.customerId as string;
  } else {
    const searchParams = new URLSearchParams(window.location.search);
    customerIdParam = Object.fromEntries(searchParams)?.id;
  }

  const getDetail = useQuery(
    ["getDetail", idRequest],
    () => {
      return ApiRequestJob.getDetail(idRequest);
    },
    {
      enabled: idRequest !== -1,
      onError(err: any) {
        if (err?.response?.data?.status === 400) {
          setTimeout(() => {
            router.back();
          }, deadTimeFastSearch);
        }
      },
    }
  );
  const {data} = getDetail;

  const dataCustomer: OptionSelectCustomer[] = [
    {
      label: data?.customerName || "",
      value: data?.customerId || "",
      key: data?.customerId || "",
    },
  ];

  function findStatusSelectNotMultiple(
    value: string | number,
    statusOptions: OptionSelect[],
    defaultSelect?: OptionSelect
  ): OptionSelect {
    const defaultValue = defaultSelect || ({} as OptionSelect);
    if (
      !statusOptions ||
      value === null ||
      (typeof value === "string" && !value)
    )
      return defaultValue;

    return (
      statusOptions.find((item) => item.key === String(value)) || defaultValue
    );
  }

  const setFieldInput = (name: string, value: any): void => {
    // eslint-disable-next-line no-unused-expressions
    formikRef?.current && formikRef?.current?.setFieldValue(name, value);
  };

  const findDataExpectFieldMulti = (value: string[], arr: OptionSelect[]) => {
    const defaultData: OptionSelect[] = [];
    if (value?.length === 0 || arr?.length === 0) return defaultData;
    return (
      arr?.filter((item) => value?.includes(item?.key || "")) || defaultData
    );
  };

  const findDataExpectFieldMultiByValue = (
    value: string[],
    arr: OptionSelect[]
  ): any => {
    const defaultData: OptionSelect[] = [];
    if (value?.length === 0 || arr?.length === 0) return defaultData;
    const upperCaseValue = value.map((str) => str.toUpperCase());
    return (
      arr?.filter((item) =>
        upperCaseValue?.includes(item?.value?.toUpperCase() || "")
      ) || defaultData
    );
  };

  const valueSelectDefault = {} as OptionSelect;
  const valueCustomerDefault = {} as OptionSelectCustomer;
  let findCountry: OptionSelect[] = [];
  let customerId: OptionSelectCustomer = {} as OptionSelectCustomer;
  let levelIds: OptionSelect[] = [];
  let listWorkLocationId: OptionSelect[] = [];
  let skillRequests: OptionSelect[] = [];
  let listDistrict: OptionSelect[] = [];
  let services: OptionSelect[] = [];
  let requestType: OptionSelect = {} as OptionSelect;
  let accountManager: OptionSelect =
    isTypeCreate && isRoleAmg
      ? findStatusSelectNotMultiple(userId, allData.amList)
      : valueSelectDefault;
  const currencyType = {label: "VND", value: "VND", key: "VND"};

  useEffect(() => {
    if (customerIdParam) {
      customerId =
        allData.customerIds.find((item) => item.key === customerIdParam) ||
        valueCustomerDefault;

      setParamsJobCode({
        ...paramsJobCode,
        customerId: customerIdParam,
      });

      setBenefit(customerId?.benefit || "");
      setInterviewProcess(customerId?.interviewProcess || "");
    }

    if (getDetail.isSuccess) {
      const countryIds = (data?.countryIds || "").split(",");
      findCountry = findDataExpectFieldMulti(countryIds, allData.countryList);
      customerId =
        dataCustomer.length > 0 ? dataCustomer[0] : valueCustomerDefault;
      const levelIdsStringToArr = (data?.levelIds || "")?.split(",");

      levelIds = findDataExpectFieldMulti(
        levelIdsStringToArr,
        allData.levelList
      );
      const workLocationIds = (data?.workLocationIds || "").split(",");
      listWorkLocationId = findDataExpectFieldMulti(
        workLocationIds,
        allData.workLocationList
      );
      const requestSkillIds = data?.requestSkillIds || [];
      skillRequests = findDataExpectFieldMulti(
        requestSkillIds,
        allData.skillsList
      );
      const districtIds = data?.districtIds || [];
      listDistrict = findDataExpectFieldMulti(
        districtIds,
        allData.districtList
      );
      requestType =
        requestTypes?.find((item) => item.key === String(data?.requestType)) ||
        valueSelectDefault;
      accountManager = findStatusSelectNotMultiple(
        String(data?.accountManagerId),
        allData.amList
      );

      services = findDataExpectFieldMulti(
        data?.services || [],
        optionServiceRequest
      );
    }

    const value: DataInitFormRequest = {
      currencyTypeId: data?.currencyTypeId
        ? {
            value: data?.currencyTypeId,
            label: data?.currencyTypeId,
            key: data?.currencyTypeId,
          }
        : currencyType,
      countryIds: findCountry,
      customerId: customerId,
      name: data?.name || "",
      proxyCustomer: data?.proxyCustomer
        ? data.proxyCustomer
        : customerId.contact || "",
      requestDate: isTypeCreate
        ? moment()
        : data?.requestDate
        ? moment(data?.requestDate, DATE_FORMAT)
        : "",
      customerRateValue: data?.customerRateValue || "",
      customerRateType: data?.customerRateType
        ? listCustomerRateType[1]
        : listCustomerRateType[0],
      status: findStatusSelectNotMultiple(
        isTypeCreate
          ? StatusRequestJob.Review
          : data?.status === StatusRequestJob.Unclear
          ? StatusRequestJob.Review
          : data?.status || "",
        optionStatusRequest
      ),
      positionId: findStatusSelectNotMultiple(
        data?.positionId as any,
        allData.positionList
      ),
      levelIds: levelIds,
      headcount: data?.headcount || "",
      requestSkillIds: skillRequests,
      workLocationIds: listWorkLocationId,
      districtIds: listDistrict,
      salaryFrom: data?.salaryFrom ? data?.salaryFrom : "",
      salaryTo: data?.salaryTo ? data?.salaryTo : "",
      experienceYearFrom: data?.experienceYearFrom || "",
      experienceYearTo: data?.experienceYearTo || "",
      priority: findStatusSelectNotMultiple(data?.priority as any, priorities),
      requestType: requestType,
      accountManagerId: accountManager,
      expiryDate: data?.expiryDate ? moment(data?.expiryDate, DATE_FORMAT) : "",
      partnerRateValue: isTypeCreate ? "" : data?.partnerRateValue || "0",
      partnerRateType: data?.partnerRateType
        ? listCustomerRateType[1]
        : listCustomerRateType[0],
      label: findStatusSelectNotMultiple(String(data?.label), labelOption, {
        value: "0",
        label: "Không dán nhãn",
        key: "0",
      }),
      partCurrencyTypeId: {label: "VND", value: "VND", key: "VND"},
      experienceType: "",
      workTypeId: findStatusSelectNotMultiple(
        data?.workTypeId as any,
        allData.workTypeList
      ),
      monthWarranty: findStatusSelectNotMultiple(
        data?.monthWarranty as any,
        optionGuarantee
      ),
      requestJobCode: data?.requestJobCode || "",
      filePathTemp: data?.fileJDPath || "",
      note: data?.note || "",
      experienceYear: "",
      website: data?.website ? data.website : customerId.website || "",
      hotBonusId: findStatusSelectNotMultiple(
        data?.hotBonusId as any,
        allData.hotBonusExpected
      ),
      hotBonusUnit: data?.hotBonusUnit
        ? {label: data?.hotBonusUnit, value: data?.hotBonusUnit}
        : currencyType,
      hotBonusAmount: data?.hotBonusAmount || "",
      partnerCurrencyTypeId: "VND",
      services,
      detailAddress: data?.detailAddress,
      durationValue: data?.durationValue,
      durationUnit: data?.durationUnit || "MONTH",
    };

    // eslint-disable-next-line no-unused-expressions
    formikRef?.current && formikRef.current.setValues(value);
  }, [isTypeCreate, data, allData, JSON.stringify(dataCustomer)]);

  useEffect(() => {
    let positionId: OptionSelect = {} as OptionSelect;
    let currencyTypeId: OptionSelect = {} as OptionSelect;
    const defaultMonthWarranty: OptionSelect = optionGuarantee[1];
    const defaultStatus: OptionSelect = {
      label: "Open",
      value: "1",
      key: "1",
    };

    if (
      isTypeCreate &&
      (findJobDescriptionByText.isSuccess || findJobDescriptionByFile.isSuccess)
    ) {
      const countryIds = (infoJobDescription?.address?.national || "").split(
        ","
      );
      findCountry = findDataExpectFieldMultiByValue(
        countryIds,
        allData.countryList
      );
      levelIds = findDataExpectFieldMultiByValue(
        infoJobDescription?.level || [],
        allData.levelList
      );
      const workLocationIds = (infoJobDescription?.address?.city || "").split(
        ","
      );
      listWorkLocationId = findDataExpectFieldMultiByValue(
        workLocationIds,
        allData.workLocationList
      );
      skillRequests = findDataExpectFieldMultiByValue(
        infoJobDescription?.skills || [],
        allData.skillsList
      );
      const districtIds = (infoJobDescription?.address?.district || "").split(
        ","
      );
      listDistrict = findDataExpectFieldMultiByValue(
        districtIds,
        allData.districtList
      );
      positionId =
        allData.positionList?.find((item) =>
          infoJobDescription?.position?.includes(item.value)
        ) || valueSelectDefault;

      setParamsJobCode({
        ...paramsJobCode,
        positionId: positionId.value,
      });
      currencyTypeId =
        currencyList.find(
          (item) => item.key === String(infoJobDescription?.salary?.concurrency)
        ) || currencyType;

      const value: DataInitFormRequest = {
        ...formikRef.current?.values,
        name: infoJobDescription?.position || "",
        status: defaultStatus,
        currencyTypeId: currencyTypeId,
        countryIds: findCountry,
        positionId: positionId,
        levelIds: levelIds,
        headcount: infoJobDescription?.head_count || "",
        monthWarranty: defaultMonthWarranty,
        requestSkillIds: skillRequests,
        workLocationIds: listWorkLocationId,
        districtIds: listDistrict,
        salaryFrom: infoJobDescription?.salary?.from || "",
        salaryTo: infoJobDescription?.salary?.to || "",
        experienceYearFrom: infoJobDescription?.year_of_experience?.from || "",
        experienceYearTo: infoJobDescription?.year_of_experience?.to || "",
        services: [],
      };
      // eslint-disable-next-line no-unused-expressions
      formikRef?.current && formikRef.current.setValues(value);
    }
  }, [isTypeCreate, allData, infoJobDescription]);

  useEffect(() => {
    const requirementJD = (): string => {
      const requirementMust = infoJobDescription?.requirement?.must_have;
      const requirementNice = infoJobDescription?.requirement?.nice_to_have;
      return requirementMust?.length || requirementNice?.length
        ? `<ul>${
            requirementMust?.length &&
            `<li>
                <strong>Must have:</strong>
                <ul>
                    ${requirementMust.map(
                      (item, idx) => `<li key={${idx}}>${item}</li>`
                    )}
                </ul>
             </li>`
          }
          ${
            requirementNice?.length &&
            `<li>
                <strong>Nice to have:</strong>
                <ul>
                    ${requirementNice.map(
                      (item, idx) => `<li key={${idx}}>${item}</li>`
                    )}
                </ul>
             </li>`
          }</ul>`
        : "";
    };

    const descriptionJD = (): string => {
      const descriptionJDs = infoJobDescription?.description;
      return descriptionJDs?.length
        ? `<ul>${descriptionJDs.map(
            (item, idx) => `<li key={${idx}}>${item}</li>`
          )}</ul>`
        : "";
    };

    const benefitJDs = infoJobDescription?.benefit;
    const benefitJD = (): string => {
      return benefitJDs?.length
        ? `<ul>${benefitJDs.map(
            (item, idx) => `<li key={${idx}}>${item}</li>`
          )}</ul>`
        : "";
    };

    const interviewProcessJDs = infoJobDescription?.interview_process;
    const interviewProcessJD = (): string => {
      return interviewProcessJDs?.length
        ? `<ul>${interviewProcessJDs.map(
            (item, idx) => `<li key={${idx}}>${item}</li>`
          )}</ul>`
        : "";
    };

    if (
      isTypeCreate &&
      (findJobDescriptionByText.isSuccess || findJobDescriptionByFile.isSuccess)
    ) {
      setRequestDetail(requirementJD() || "");
      setDescription(descriptionJD() || "");
      setBenefit((preValue) => {
        if (benefitJDs?.length) {
          return benefitJD();
        }
        return preValue;
      });
      setInterviewProcess((preValue) => {
        if (interviewProcessJDs?.length) {
          return interviewProcessJD();
        }
        return preValue;
      });
    } else {
      setRequestDetail(data?.requestDetail || "");
      setDescription(data?.description || "");
      setBenefit(data?.benefit || "");
      setInterviewProcess(data?.interviewProcess || "");
    }
  }, [
    data?.benefit,
    data?.description,
    data?.requestDetail,
    data?.interviewProcess,
    infoJobDescription?.description,
    infoJobDescription?.requirement,
    infoJobDescription?.benefit,
    infoJobDescription?.interview_process,
    isTypeCreate,
  ]);

  useEffect(() => {
    if (getDetail.isSuccess) {
      setFile({
        fileJD: data?.fileJDPath || "",
        fileJDName: data?.fileJDName || "",
      });
    }
  }, [data?.fileJDName, data?.fileJDPath]);

  const handleChangeDescription = (value: string): void => {
    setDescription(value);
  };

  const handleChangeRequestDetail = (value: string): void => {
    setRequestDetail(value);
  };

  const handleChangeBenefit = (value: string): void => {
    setBenefit(value);
  };

  const handleChangeInterviewProcess = (value: string): void => {
    setInterviewProcess(value);
  };

  const findJobDescriptionByFile = useMutation(
    (data: File) => ApiRequestJob.getJobDescriptionByFile(data),
    {
      onSuccess: (data: InfoJobDescription) => {
        setInfoJobDescription(data);
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const findJobDescriptionByText = useMutation(
    (text: string) => ApiRequestJob.getJobDescriptionByText(text),
    {
      onSuccess: (data: InfoJobDescription) => {
        setInfoJobDescription(data);
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const handleChangeInput = (file: File) => {
    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      const fileType = file?.name.split(".").pop();
      const fileSize = file.size / 1024 / 1024;
      if (file && fileType && fileSize) {
        if (!fileTypeJdAllow.includes(fileType?.toLowerCase())) {
          notification.error({
            message: messageErrorUploadCv.errFileType,
          });
          return "";
        }

        if (fileSize > 5) {
          notification.error({
            message: messageErrorUploadCv.errFileSize,
          });
          return "";
        }
      }
      setFile({
        fileJD: file,
        fileJDName: file?.name,
      });
      dispatch(setLoading(true));
      findJobDescriptionByFile.mutate(file);
    }
    return "";
  };

  const assignObject = (values: any): any => {
    const result: any = {};
    Object.entries(values as any)?.forEach(([keyObject, valueObject]) => {
      if (typeof valueObject === "object" && Array.isArray(valueObject)) {
        const arrToString = valueObject?.map((i) => i?.key)?.join(",") || "";
        result[keyObject] = arrToString;
      } else if (
        typeof valueObject === "object" &&
        !Array.isArray(valueObject)
      ) {
        const valueExpect = (valueObject as any)?.key || "";
        result[keyObject] = valueExpect;
      } else {
        result[keyObject] = valueObject;
      }
    });
    return result;
  };

  const {mutate: handleUpdateStatusJob} = useMutation(
    (data: DataUpdateStatusJob) => ApiRequestJob.updateStatusJob(data)
  );

  const handleSubmitForm = async () => {
    dispatch(setLoading(true));
    const jobCode = data?.requestJobCode || "";
    let filePathTemp = "";
    const valuesForm = formikRef?.current?.values;

    const newFieldRequiredClone = {
      ...valuesForm,
      description: "",
      requestDetail: "",
    };
    // set all fields is touched to check validation of form
    const checkAllTouched = setAllFieldTouched(newFieldRequiredClone);
    formikRef?.current?.setTouched(checkAllTouched);

    const value = assignObject({
      ...valuesForm,
      benefit,
      requestDetail,
      description,
      interviewProcess,
    });

    if (
      !(
        value?.customerId &&
        value?.customerRateValue &&
        value?.name &&
        value?.status &&
        value?.positionId &&
        value?.levelIds &&
        value?.headcount &&
        value?.countryIds &&
        value?.workLocationIds &&
        description &&
        requestDetail &&
        value?.priority &&
        value?.requestType &&
        value?.partnerRateValue &&
        value?.services
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: messageValidate,
      });
    } else {
      if (
        (valuesForm?.customerRateType?.value === "0" &&
          Number(valuesForm?.customerRateValue) > 100) ||
        (valuesForm?.partnerRateType?.value === "0" &&
          Number(valuesForm?.partnerRateValue) > 100)
      ) {
        notification.error({
          message: "Thông báo",
          description: "Rate khách hàng hoặc Rate CTV không hợp lệ",
        });
      } else {
        try {
          if (file.fileJD && data?.fileJDPath !== file.fileJD) {
            const formDataUploadFile = new FormData();
            formDataUploadFile.append("file", file.fileJD);
            filePathTemp = await uploadFileTemp.mutateAsync(formDataUploadFile);
          }
        } catch (e) {
          filePathTemp = "";
        }
        const result = {
          ...(data || {}),
          ...value,
          customerRateType: valuesForm?.customerRateType?.value !== "0",
          customerCurrencyTypeId:
            valuesForm?.customerRateType?.value !== "0"
              ? valuesForm?.customerRateType?.value
              : "VND",
          customerRateValue:
            valuesForm?.customerRateType?.value === "0"
              ? valuesForm?.customerRateValue
              : moneyToNumber(String(valuesForm?.customerRateValue)),
          expiryDate: valuesForm?.expiryDate
            ? moment(valuesForm?.expiryDate).format(DATE_FORMAT)
            : "",
          requestDate: valuesForm?.requestDate
            ? moment(valuesForm?.requestDate).format(DATE_FORMAT)
            : "",
          requestJobCode: isTypeCreate ? null : jobCode,
          partnerRateType: valuesForm?.partnerRateType?.value !== "0",
          partnerRateValue:
            valuesForm?.partnerRateType?.value === "0"
              ? valuesForm?.partnerRateValue
              : moneyToNumber(String(valuesForm?.partnerRateValue)) || "0",
          isUploadFile: !!file?.fileJD,
          fileJD: isTypeCreate && file.fileJD ? file.fileJD : "",
          fileJDName: isTypeCreate && file.fileJDName ? file.fileJDName : "",
          fileName:
            !isTypeCreate && file.fileJD
              ? file.fileJDName
              : data?.fileJDName || "",
          requestJobId:
            !isTypeCreate && data?.requestJobId ? data?.requestJobId : "",
          salaryFrom: moneyToNumber(String(value?.salaryFrom)),
          salaryTo: moneyToNumber(String(value?.salaryTo)),
          filePathTemp: filePathTemp,
          partnerCurrencyTypeId: "VND",
        };

        Object.entries(result)?.forEach(([key, valueObject]) => {
          if (!valueObject && typeof valueObject !== "boolean") {
            delete result[key];
          }
        });

        const formData = new FormData();

        Object.entries(result)?.forEach(([keyObject, valueObject]) => {
          formData.append(keyObject, valueObject as any);
        });

        if (isTypeCreate) {
          createRequest.mutate(result);
        } else {
          if (
            [
              StatusRequestJob.Reject,
              StatusRequestJob.Review,
              StatusRequestJob.Unclear,
            ].includes(result.status)
          ) {
            handleUpdateStatusJob({
              jobId: Number(idRequest),
              jobStatus: Number(result.status),
            });
          }
          editRequest.mutate(result);
        }
      }
    }
    dispatch(setLoading(false));
  };

  useEffect(() => {
    return () => {
      formikRef?.current?.resetForm();
    };
  }, []);

  const onChangeTextSearch = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ): void => {
    const value = e.target.value.trim();
    setTextJDParams(value);
  };

  useEffect(() => {
    if (textJDParams) {
      dispatch(setLoading(true));
      findJobDescriptionByText.mutate(textJDParams);
    }
  }, [debounceTextJDParams]);

  const isRequiredField = (value: any, isTouched?: boolean): boolean => {
    return (!value || value?.length === 0) && !!isTouched;
  };

  const dataStatusRequest = useMemo(() => {
    if (!isTypeCreate && data?.status === StatusRequestJob.Unclear) {
      return optionStatusRequest.map((item) => ({
        ...item,
        disabled: item.key !== StatusRequestJob.Closed,
      }));
    }

    // Role AM, AML không được edit status -> Reject, Open, Unclear khi request có status là Review
    const roleUnableEdit = [IAccountRole.AML, IAccountRole.AMG];
    const statusShouldBeDisable = [
      StatusRequestJob.Reject,
      StatusRequestJob.Unclear,
      StatusRequestJob.Open,
      StatusRequestJob.Processing,
      StatusRequestJob.Draft,
    ];
    if (
      !isTypeCreate &&
      data?.status === StatusRequestJob.Review &&
      user?.role?.some((i) => roleUnableEdit.includes(i))
    ) {
      return optionStatusRequest.map((item) => ({
        ...item,
        disabled: !!statusShouldBeDisable.includes(
          item.key as StatusRequestJob
        ),
      }));
    }

    return optionStatusRequest.map((item) => ({
      ...item,
      disabled:
        item.key === StatusRequestJob.Processing ||
        (type === "edit" && item.key === StatusRequestJob.Draft),
    }));
  }, [type, data?.status, user]);

  if (getDetail?.isLoading) return <AppLoading classNameContainer="h-screen" />;

  return (
    <div className="create-request">
      <div>
        <AppBreadcrumb separator=">" items={items} />
        <h2 className="text24">Thông tin khách hàng</h2>
        <div className="create-request__form">
          <Formik
            initialValues={{} as DataInitFormRequest}
            innerRef={formikRef}
            onSubmit={(): void => {
              //
            }}
          >
            {({values, touched}): JSX.Element => {
              return (
                <form
                  onSubmit={(e) => {
                    e?.preventDefault();
                  }}
                >
                  <div className="create-request__information">
                    <InformationCustomer
                      values={values}
                      touched={touched}
                      customerList={
                        isTypeCreate ? allData.customerIds : dataCustomer
                      }
                      setFieldInput={setFieldInput}
                      dataCustomer={getAllData[6]?.data ?? []}
                      handleChangeCustomer={handleChangeCustomer}
                      isTypeCreate={isTypeCreate}
                    />
                    <p className="mb-4 text24">Thông tin tuyển dụng</p>
                    <div className="create-request__form-information">
                      <Row gutter={[64, 32]} className="mt-2">
                        <Col xs={12}>
                          <div className="upload-file mb-3">
                            <p className="text24">Bản mô tả công việc</p>
                            <div className="mt-2">
                              <label
                                htmlFor="file-jd"
                                className="cursor-pointer"
                              >
                                <div
                                  className={`flex create-request__upload-file ${
                                    !isTypeCreate
                                      ? "bg-[#f5f5f5] cursor-default"
                                      : ""
                                  }`}
                                >
                                  <Icon
                                    size={48}
                                    color="#324054"
                                    icon="upload-cloud-2-line"
                                    className="upload-icon"
                                  />
                                  <div className="tex16 ml-4">
                                    {file.fileJDName ||
                                      "Bấm vào khung để upload file JD"}
                                  </div>
                                </div>
                                <input
                                  type="file"
                                  name="file-jd"
                                  id="file-jd"
                                  className="hidden"
                                  accept="doc,docx,pdf"
                                  ref={fileUploadRef}
                                  onChange={(e: any): void => {
                                    handleChangeInput(e?.target?.files[0]);
                                    fileUploadRef.current.value = "";
                                  }}
                                  disabled={!isTypeCreate}
                                />
                              </label>
                            </div>
                            <p className="mt-2">
                              Chỉ có thể tải lên file pdf, doc, docx tối đa 5mb
                            </p>
                          </div>
                          <p className="text16">Nội dung tuyển dụng</p>
                          <TextInput
                            name="name"
                            label="Tên request"
                            required
                            status={
                              isRequiredField(values.name, touched?.name)
                                ? "error"
                                : ""
                            }
                            value={values?.name}
                            free={!values?.name}
                            containerclassname="mt-2"
                            maxLength={100}
                          />
                          <SelectInput
                            name="status"
                            labelselect="Trạng thái request"
                            data={dataStatusRequest}
                            value={values?.status?.value}
                            free={!values?.status?.value}
                            allowClear
                            required
                            containerclassname="mt-2"
                            status={
                              isRequiredField(
                                values.status?.value,
                                touched?.status
                              )
                                ? "error"
                                : ""
                            }
                            disabled={isTypeCreate}
                            handleChange={(e): void => {
                              if (values?.status?.value === "4") {
                                const valueLabelFile = labelOption.find(
                                  (item) => item?.key === "0"
                                );
                                setFieldInput("label", valueLabelFile);
                              }

                              if (e?.key === "4") {
                                // removed option label pending
                                // find status pending
                                const valueLabelFile = labelOption.find(
                                  (item) => item?.key === "0"
                                );
                                setFieldInput("label", valueLabelFile);
                              }
                            }}
                          />
                          <SelectInput
                            name="positionId"
                            labelselect="Vị trí làm việc"
                            data={allData.positionList}
                            value={values?.positionId?.value}
                            free={!values?.positionId?.value}
                            allowClear
                            containerclassname="mt-2"
                            required
                            status={
                              isRequiredField(
                                values.positionId?.value,
                                touched?.positionId as any
                              )
                                ? "error"
                                : ""
                            }
                            handleChange={(e): void => {
                              const value = e?.value || "";
                              setParamsJobCode({
                                ...paramsJobCode,
                                positionId: value,
                              });
                            }}
                          />
                          <SelectInput
                            name="levelIds"
                            labelselect="Level"
                            data={allData.levelList}
                            value={values?.levelIds || []}
                            free={values?.levelIds?.length === 0}
                            mode="multiple"
                            allowClear
                            containerclassname="mt-2"
                            required
                            status={
                              isRequiredField(
                                values.levelIds,
                                touched?.levelIds as any
                              )
                                ? "error"
                                : ""
                            }
                          />
                          <TextInput
                            label="Số lượng tuyển"
                            name="headcount"
                            value={values?.headcount}
                            free={!values?.headcount}
                            required
                            containerclassname="mt-2"
                            status={
                              isRequiredField(
                                values.headcount,
                                touched?.headcount
                              )
                                ? "error"
                                : ""
                            }
                            onlynumber
                          />
                          <SelectInput
                            name="workTypeId"
                            labelselect="Loại hình"
                            data={allData.workTypeList}
                            value={values?.workTypeId}
                            free={!values?.workTypeId?.value}
                            allowClear
                            containerclassname="mt-2"
                          />
                          <SelectInput
                            name="monthWarranty"
                            labelselect="Số tháng bảo hành"
                            data={optionGuarantee}
                            value={values?.monthWarranty?.value}
                            free={!values?.monthWarranty?.value}
                            allowClear
                            containerclassname="mt-2"
                          />
                          <SelectInput
                            name="requestSkillIds"
                            labelselect="Kỹ năng"
                            data={allData.skillsList}
                            value={values?.requestSkillIds || []}
                            free={values?.requestSkillIds?.length === 0}
                            mode="multiple"
                            allowClear
                            containerclassname="mt-2"
                          />
                          <p className="text16 mt-2">Địa chỉ</p>
                          <SelectInput
                            name="countryIds"
                            labelselect="Quốc gia"
                            data={allData.countryList}
                            value={values?.countryIds || []}
                            free={values?.countryIds?.length === 0}
                            mode="multiple"
                            allowClear
                            containerclassname="mt-2"
                            required
                            status={
                              isRequiredField(
                                values.countryIds,
                                touched?.countryIds as any
                              )
                                ? "error"
                                : ""
                            }
                            handleChange={(e: OptionSelect[]): void => {
                              setAddressValue({
                                ...addressValue,
                                countryId: e?.map((i) => i.key || ""),
                              });
                              setParamsJobCode({
                                ...paramsJobCode,
                                countryIds: e
                                  ?.map((i) => i.key || "")
                                  ?.join("_"),
                              });
                              setFieldInput("workLocationIds", []);
                              setFieldInput("districtIds", []);
                            }}
                          />
                          <SelectInput
                            name="workLocationIds"
                            labelselect="Thành phố"
                            data={sortWorkLocation(
                              addressValue.countryId.length > 0
                                ? workLocationListExpected
                                : allData.workLocationList,
                              "key"
                            )}
                            value={values?.workLocationIds || []}
                            free={values?.workLocationIds?.length === 0}
                            mode="multiple"
                            allowClear
                            containerclassname="mt-2"
                            required
                            status={
                              isRequiredField(
                                values.workLocationIds,
                                touched?.workLocationIds as any
                              )
                                ? "error"
                                : ""
                            }
                            handleChange={(e: OptionSelect[]): void => {
                              setAddressValue({
                                ...addressValue,
                                workLocationId: e?.map((i) => i.key || ""),
                              });
                              setFieldInput("districtIds", []);
                            }}
                          />
                          <SelectInput
                            name="districtIds"
                            labelselect="Quận huyện"
                            data={
                              addressValue?.workLocationId?.length > 0
                                ? districtListExpected
                                : allData.districtList
                            }
                            value={values?.districtIds || []}
                            free={values?.districtIds?.length === 0}
                            mode="multiple"
                            allowClear
                            containerclassname="mt-2"
                          />
                          <TextInput
                            name="detailAddress"
                            label="Địa chỉ chi tiết"
                            value={values?.detailAddress || ""}
                            free={!values?.detailAddress}
                            containerclassname="mt-2"
                            maxLength={250}
                          />
                          <p className="text16 mt-2">Salary</p>
                          <Row
                            gutter={[16, 16]}
                            className="items-center mt-2"
                            justify="center"
                          >
                            <Col xs={11}>
                              <div className="flex input-salary">
                                <TextInput
                                  containerclassname="flex-1"
                                  label="Từ"
                                  name="salaryFrom"
                                  value={values?.salaryFrom}
                                  free={!values?.salaryFrom}
                                  onlynumber
                                  typeInput="salary"
                                  iscurrency
                                />
                                <AppSelectCurrency
                                  name="currencyTypeId"
                                  value={values?.currencyTypeId}
                                  style={{width: "80px"}}
                                />
                              </div>
                            </Col>
                            <Col xs={2}>
                              <div className="text-center">-</div>
                            </Col>
                            <Col xs={11}>
                              <div className="flex input-salary">
                                <TextInput
                                  containerclassname="flex-1"
                                  label="Đến"
                                  name="salaryTo"
                                  onlynumber
                                  typeInput="salary"
                                  iscurrency
                                  maxLength={100}
                                  value={values?.salaryTo}
                                  free={!values?.salaryTo}
                                />
                                <AppSelectCurrency
                                  name="currencyTypeId"
                                  value={values?.currencyTypeId}
                                  style={{width: "80px"}}
                                />
                              </div>
                            </Col>
                          </Row>
                          <p className="text16 mt-2">Số năm kinh nghiệm</p>
                          <Row gutter={16} className="items-center mt-2">
                            <Col xs={11}>
                              <TextInput
                                label="Từ"
                                name="experienceYearFrom"
                                value={values?.experienceYearFrom}
                                free={!values?.experienceYearFrom}
                                onlynumber
                              />
                            </Col>
                            <Col xs={2}>
                              <div className="text-center">-</div>
                            </Col>
                            <Col xs={11}>
                              <TextInput
                                label="Đến"
                                name="experienceYearTo"
                                value={values?.experienceYearTo}
                                free={!values?.experienceYearTo}
                                onlynumber
                              />
                            </Col>
                          </Row>
                        </Col>
                        <Col xs={12}>
                          <div className="mb-11" />
                          <TextArea
                            name="textSearch"
                            className="input-note"
                            style={{height: 90, resize: "none"}}
                            placeholder="Nhập nội dung JD"
                            onChange={onChangeTextSearch}
                            disabled={!isTypeCreate}
                          />
                          <RequestEditor
                            valueRequestEditor={{
                              description,
                              requestDetail,
                              benefit,
                            }}
                            handleChangeDescription={handleChangeDescription}
                            handleChangeRequestDetail={
                              handleChangeRequestDetail
                            }
                            handleChangeBenefit={handleChangeBenefit}
                            touchedField={{
                              description: (touched as any)?.description,
                              requestDetail: (touched as any)?.requestDetail,
                            }}
                          />
                        </Col>
                      </Row>
                    </div>
                  </div>
                  <GeneralInformation
                    amList={allData.amList}
                    hotBonus={allData.hotBonusExpected}
                    setFieldInput={setFieldInput}
                    values={values}
                    partnerRateType={
                      isTypeCreate ? "0" : data?.partnerRateType ? "1" : "0"
                    }
                    interviewProcess={interviewProcess}
                    handleChangeInterviewProcess={handleChangeInterviewProcess}
                    touched={touched}
                  />
                  <Row gutter={[128, 32]} justify="center" className="mt-4">
                    <Col xs={6}>
                      <AppButton
                        typebutton="secondary"
                        onClick={handleShowModalCancel}
                      >
                        Hủy
                      </AppButton>
                    </Col>
                    {(data?.isOwner || isTypeCreate) && (
                      <Col xs={6}>
                        <AppButton
                          typebutton="primary"
                          onClick={handleSubmitForm}
                          isSubmitting={
                            createRequest?.isLoading || editRequest?.isLoading
                          }
                        >
                          Lưu
                        </AppButton>
                      </Col>
                    )}
                  </Row>
                </form>
              );
            }}
          </Formik>
        </div>
      </div>
      <AppModalConfirm
        open={showModalCancel}
        title="Xác nhận"
        content="Dữ liệu chưa lưu sẽ bị mất. Bạn có chắc chắn ?"
        onCancel={visibleModalCancel}
        onOk={handleConfirmModal}
      />
    </div>
  );
}

export default RequestForm;
