import ApiUser from "@app/api/ApiUser";
import AppButton from "@app/components/AppButton";
import config from "@app/config";
import {logoutUser, selectUser} from "@app/redux/slices/UserSlice";
import {Popover} from "antd";
import Link from "next/link";
import {useRouter} from "next/router";
import React from "react";
import {useDispatch, useSelector} from "react-redux";

function Account(): JSX.Element {
  const {user} = useSelector(selectUser);
  const router = useRouter();
  const isLogin = ApiUser.isLogin();
  const dispatch = useDispatch();

  const redirectLogin = (): void => {
    router.push(config.PATHNAME.LOGIN);
  };

  const redirectRegister = (): void => {
    router.push(config.PATHNAME.REGISTER);
  };

  const handleLogout = (): void => {
    router.push(config.PATHNAME.LOGIN);
    ApiUser.logout();
    dispatch(logoutUser());
  };

  // eslint-disable-next-line react/no-unstable-nested-components
  function ContentPopover(): JSX.Element {
    return (
      <div className="account__popup-logout">
        <AppButton
          typebutton="primary"
          className="mr-2 border-0 shadow-white"
          onClick={handleLogout}
        >
          Đăng xuất
        </AppButton>
      </div>
    );
  }

  return (
    <div className="account">
      {isLogin ? (
        <div className="flex">
          <Link
            href={config.PATHNAME.MANAGER_APPLICATION}
            className="text-blue-500"
          >
            Trang quản lý
          </Link>
          <Popover
            placement="bottomRight"
            trigger={["click"]}
            content={ContentPopover()}
          >
            <div className="ml-8 cursor-pointer">{user?.name || ""}</div>
          </Popover>
        </div>
      ) : (
        <div className="flex">
          <AppButton
            typebutton="primary"
            classrow="mr-4 w-32 text16"
            onClick={redirectLogin}
          >
            Đăng nhập
          </AppButton>
          <AppButton
            typebutton="secondary"
            onClick={redirectRegister}
            classrow="w-32 text16"
          >
            Đăng ký
          </AppButton>
        </div>
      )}
    </div>
  );
}

export default React.memo(Account);
