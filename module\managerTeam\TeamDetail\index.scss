.container-team-detail {
  .card-information {
    border: 1px dashed $header_tf;
    padding: 20px 30px;
    border-radius: 8px;
    margin-top: 16px;
  }

  input {
    background-color: $white-color !important;
  }

  .ant-table-body {
    height: 32vh;
  }

  .status {
    color: $white-color;
    padding: 4px 14px;
    border-radius: 8px;
  }

  .btn-delete-group {
    justify-content: center;

    button {
      border: none;
      background-color: none;
    }

    button:hover,
    button:focus {
      background: none;
      border: none;
    }

    span {
      text-decoration: underline;
      color: $status-reject;
    }
  }
}
