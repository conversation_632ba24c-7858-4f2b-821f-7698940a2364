import {ModalProps, Row, notification} from "antd";
import React, {useEffect, useMemo, useRef, useState} from "react";
// eslint-disable-next-line import/namespace
import {TextInput} from "@app/components/TextInput";
import AppDatePicker from "@app/components/AppDatePicker";
import "./index.scss";
import {Formik} from "formik";
import {FormikProps} from "formik/dist/types";
import {SelectInput} from "@app/components/SelectInput";
import {useMutation, useQuery} from "react-query";
import ApiCandidate, {
  IEducations,
  IListSkill,
  IModeViewCandidate,
  IWorkHistories,
  IWorkLocationList,
  ICandidateDetail,
  IDataFormDataCandidateSubmit,
} from "@app/api/ApiCandidate";
import AppModal from "@app/components/AppModal";
import AppButton from "@app/components/AppButton";
import ViewCvCandidate from "@app/components/viewCvCandidate";
// eslint-disable-next-line import/no-cycle, import/namespace
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import moment from "moment";
import TableCandidateEducation from "../tableCandidateEducation";
import TableCandidateWorkHistories from "../tableCandidateWorkHistories";
import {Input} from "formik-antd";
import {
  appendFormData,
  getFirst20Item,
  moneyToNumber,
  sortWorkLocation,
  validateEmail,
  validatePhoneNumber,
} from "@app/utils/constants/function";
import {
  messageRequired,
  messageValidateEmail,
} from "@app/utils/constants/message";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {
  initialStateTable,
  setStateTable,
} from "@app/redux/slices/EditTableSlice";
import {
  listExperience,
  listLanguage,
  salaryType,
  statusCandidateList,
} from "@app/utils/constants/state";
import {IMultiSelect, OptionSelect} from "@app/types";
import {REGEX_EMAIL} from "@app/utils/constants/regex";

export enum TypeEditTable {
  workHistories = "workHistories",
  educations = "educations",
}

interface ModalInfoProps extends ModalProps {
  isModalVisible: boolean;
  handleCancel: () => void;
  candidateId: number;
  updateListCandidate?: () => void;
  dataCandidate?: ICandidateDetail;
  resetData?: () => void;
}

interface MyFromCandidate {
  name?: string;
  phoneNumber?: string;
  email?: string;
  birthday?: any;
  note?: string;
  positionName?: string;
  currentSalary?: number;
  salaryExpected?: number;
  languages?: OptionSelect[];
  workLocationName: OptionSelect[];
  currencyTypeId?: OptionSelect;
  listCandidateSkillId?: OptionSelect[];
  onboardDate?: any;
  website?: string;
  linkedin?: string;
  status?: OptionSelect;
  github?: string;
  experienceYear: OptionSelect;
  currentSalaryTypeSelected: OptionSelect;
  salaryExpectedTypeSelected: OptionSelect;
}

const mapFilterSkillData = (filters: IListSkill[]): OptionSelect[] =>
  filters.map((item) => ({
    value: item.skillId,
    label: item.name,
  }));

const mapFilterWorkLocation = (filters: IWorkLocationList[]): OptionSelect[] =>
  filters.map((item) => ({
    key: item.workLocationId,
    label: item.name,
    value: item.name,
  }));

const mapFilterData = (filters: IMultiSelect[]): OptionSelect[] =>
  filters.map((item) => ({
    value: item.id,
    label: item.label,
  }));

export function EditCandidate(props: ModalInfoProps): JSX.Element {
  const {
    isModalVisible,
    handleCancel,
    updateListCandidate,
    dataCandidate,
    resetData,
  } = props;
  const formikRef = useRef<FormikProps<MyFromCandidate>>(null);

  const [dataWorkHistories, setDataWorkHistories] = useState<IWorkHistories[]>(
    []
  );
  const [dataEducations, setDataEducations] = useState<IEducations[]>([]);
  const dispatch = useDispatch();
  const timeCallback = useRef(1);

  const onCancel = () => {
    timeCallback.current = 1;
    handleCancel();
    dispatch(setStateTable(initialStateTable));
  };

  useEffect(() => {
    if (
      dataCandidate?.workHistories &&
      dataCandidate?.workHistories?.length > 0
    ) {
      setDataWorkHistories(
        dataCandidate?.workHistories?.map((item, index) => ({
          ...item,
          key: String(index),
          startDate: item?.startDate ? item?.startDate : "",
          endDate: item?.endDate ? item?.endDate : "",
        }))
      );
    }

    if (dataCandidate?.educations && dataCandidate?.educations?.length > 0) {
      setDataEducations(
        dataCandidate?.educations?.map((item, index) => ({
          ...item,
          key: String(index),
          startDate: item?.startDate ? item?.startDate : "",
          endDate: item?.endDate ? item?.endDate : "",
        }))
      );
    }
  }, [dataCandidate?.workHistories, dataCandidate?.educations]);

  const requestSkillList = useQuery("requestSkillList", () => {
    return ApiCandidate.getListSkill();
  });

  const requestWorkLocationList = useQuery("requestWorkLocationList", () => {
    return ApiCandidate.getWorkLocationList();
  });

  const updateInformationCandidate = useMutation(
    (data: any) => {
      return ApiCandidate.updateInformationCandidate(data);
    },
    {
      onSuccess: (): void => {
        notification.success({
          message: "Chỉnh sửa thông tin ứng viên thành công",
        });
        formikRef.current?.resetForm();
        resetData?.();
        onCancel();
        updateListCandidate?.();
        timeCallback.current = 1;
      },
      onError: (error: any, variables: any): void => {
        // gọi lại khi timeout vì BE đang lỗi ko trả về response khi update candidate lần đầu
        // đảm bảo gọi lại 1 lần timeCallback.current
        if (error.code === "ECONNABORTED" && timeCallback.current < 2) {
          updateInformationCandidate.mutate(variables);
          timeCallback.current += 1;
        }
      },
    }
  );

  useEffect(() => {
    dispatch(setLoading(updateInformationCandidate.isLoading));
  }, [updateInformationCandidate.isLoading]);

  const handleUpdateCandidate = () => {
    const valueRef = formikRef.current?.values;
    const newDataWorkHistory =
      dataWorkHistories?.map((item) => ({
        companyName: item?.companyName,
        position: item?.position,
        startDate: item?.startDate ? item?.startDate : "",
        endDate: item?.endDate ? item?.endDate : "",
      })) || [];

    const newDataEducation =
      dataEducations?.map((item) => ({
        schoolName: item?.schoolName,
        degreeType: item?.degreeType,
        endDate: item?.endDate ? item?.endDate : "",
        fieldOfStudy: item?.fieldOfStudy,
        startDate: item?.startDate ? item?.startDate : "",
      })) || [];

    if (
      !(
        valueRef?.name?.trim() &&
        valueRef?.phoneNumber?.trim() &&
        valueRef?.email?.trim() &&
        valueRef?.positionName?.trim() &&
        valueRef?.experienceYear?.value
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: messageRequired,
      });
      return;
    }

    if (!valueRef?.email?.match(REGEX_EMAIL)) {
      notification.error({
        message: "Thông báo",
        description: messageValidateEmail,
      });
      return;
    }

    const value: IDataFormDataCandidateSubmit = {
      candidateId: dataCandidate?.candidateId as number,
      birthday: valueRef?.birthday
        ? moment(valueRef?.birthday).format(DATE_FORMAT)
        : "",
      facebookURL: valueRef?.website?.trim() || "",
      languages: valueRef?.languages?.map((item) => item.value) || [],
      onboardDate: valueRef?.onboardDate
        ? moment(valueRef?.onboardDate).format(DATE_FORMAT)
        : "",
      workLocationIds:
        valueRef?.workLocationName?.map((item) => item.key)?.join(",") || "",
      currencyTypeId: valueRef?.currencyTypeId?.value,
      workHistories: newDataWorkHistory ?? [],
      educations: newDataEducation ?? [],
      name: valueRef?.name?.trim() as string,
      phoneNumber: valueRef?.phoneNumber?.trim() || "",
      email: valueRef?.email?.trim() || "",
      currentSalary: moneyToNumber(String(valueRef?.currentSalary)),
      salaryExpected: moneyToNumber(String(valueRef?.salaryExpected)),
      note: valueRef?.note?.trim() || "",
      source: "Reco_Job_Database",
      tags: getFirst20Item(dataCandidate?.tags),
      fileCVName: dataCandidate?.fileCVName || "",
      experienceYear: valueRef?.experienceYear?.value,
      isUploadFile: false,
      countryId: dataCandidate?.countryId || "",
      fileCV: dataCandidate?.fileCVPath || "",
      fileAvatar: dataCandidate?.fileAvatarPath || "",
      fileAvatarPath: dataCandidate?.fileAvatarPath || "",
      githubURL: valueRef?.github?.trim() || "",
      listCandidateSkillId:
        valueRef?.listCandidateSkillId?.map((item) => item.value) || [],
      positionExpected: valueRef?.positionName?.trim() || "",
      linkedinURL: valueRef?.linkedin?.trim() || "",
      status: valueRef?.status?.value ? Number(valueRef?.status?.value) : 1,
      salaryExpectedType:
        valueRef?.salaryExpectedTypeSelected.value === "0" ? 0 : 1,
      currentSalaryType:
        valueRef?.currentSalaryTypeSelected.value === "0" ? 0 : 1,
    };

    const formData = new FormData();
    appendFormData(formData, value);
    updateInformationCandidate.mutate(formData);
  };

  const getDataWorkHistoryFromTable = (data: IWorkHistories[]) => {
    setDataWorkHistories(data);
  };

  const getDataEducationFromTable = (data: IEducations[]) => {
    setDataEducations(data);
  };

  const searchLocationName = (data?: string[]) => {
    if (!data || data?.length === 0) return [];
    const dataWorkLocationClone = mapFilterWorkLocation(
      requestWorkLocationList.data ?? []
    );
    return dataWorkLocationClone?.filter((item) =>
      data?.some((i) => i === item.label)
    );
  };

  const searchLanguages = (data?: string[]) => {
    if (!data || data?.length === 0) return [];
    const dataLanguageClone = mapFilterData(listLanguage || []);
    return dataLanguageClone?.filter((item) =>
      data?.some((i) => i === item.label)
    );
  };

  const initialValue: MyFromCandidate = useMemo(() => {
    return {
      name: dataCandidate?.name || "",
      phoneNumber: dataCandidate?.phoneNumber || "",
      email: dataCandidate?.email ? dataCandidate?.email : "",
      note: dataCandidate?.note || "",
      currentSalary: dataCandidate?.currentSalary
        ? (dataCandidate.currentSalary as any)
        : "",
      salaryExpected: dataCandidate?.salaryExpected
        ? (dataCandidate?.salaryExpected as any)
        : "",
      languages: searchLanguages(dataCandidate?.languages),
      workLocationName: searchLocationName(dataCandidate?.workLocationNames),
      listCandidateSkillId:
        dataCandidate?.candidateSkills?.map((skill) => ({
          label: skill,
          value: skill,
        })) || [],
      currencyTypeId: dataCandidate?.currencyTypeId
        ? {
            label: dataCandidate?.currencyTypeId,
            value: dataCandidate?.currencyTypeId,
          }
        : {
            label: "VND",
            value: "VND",
          },

      onboardDate: dataCandidate?.onboardDateString
        ? moment(dataCandidate?.onboardDateString, DATE_FORMAT)
        : "",
      positionName: dataCandidate?.positionExpected || "",
      website: dataCandidate?.facebookURL || "",
      birthday: dataCandidate?.birthdayString
        ? moment(dataCandidate?.birthdayString, DATE_FORMAT)
        : "",
      linkedin: dataCandidate?.linkedinURL || "",
      github: dataCandidate?.githubURL || "",
      status: statusCandidateList.find(
        (item) => item.value === String(dataCandidate?.status || 1)
      ),
      experienceYear: dataCandidate?.experienceYear
        ? {
            value: dataCandidate?.experienceYear,
            label: dataCandidate?.experienceYear,
          }
        : ({} as OptionSelect),
      currentSalaryTypeSelected:
        dataCandidate?.currentSalaryType === 0 ? salaryType[0] : salaryType[1],
      salaryExpectedTypeSelected:
        dataCandidate?.salaryExpectedType === 0 ? salaryType[0] : salaryType[1],
    };
  }, [dataCandidate]);

  useEffect(() => {
    Object.entries(initialValue).forEach(([key, value]) => {
      formikRef.current?.setFieldValue(key, value);
    });
  }, [initialValue]);

  function renderContent(): React.ReactNode {
    return (
      <div className="w-full">
        <Row className="content-modal-edit-candidate w-full">
          <div className="container-item-detail-modal left-4 p-3">
            <div className="h-[70vh] overflow-y-auto p-3">
              <div className="flex flex-col">
                <Formik
                  initialValues={initialValue}
                  innerRef={formikRef}
                  onSubmit={handleUpdateCandidate}
                >
                  {({
                    values,
                    handleChange,
                    handleBlur,
                    handleSubmit,
                    handleReset,
                    touched,
                    errors,
                  }): JSX.Element => {
                    return (
                      <form onSubmit={handleSubmit}>
                        <span className="font-bold">Thông tin ứng viên</span>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="Họ và tên"
                            name="name"
                            required
                            status={!values?.name ? "error" : undefined}
                            value={values?.name}
                            free={!values?.name}
                          />
                          <TextInput
                            label="Số điện thoại"
                            name="phoneNumber"
                            value={values.phoneNumber}
                            status={
                              !values?.phoneNumber ||
                              !validatePhoneNumber(values?.phoneNumber)
                                ? "error"
                                : undefined
                            }
                            required
                            free={!values.phoneNumber}
                            isphonenumber
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="Email"
                            name="email"
                            status={
                              !validateEmail(values?.email || "")
                                ? "error"
                                : undefined
                            }
                            required
                            value={values?.email}
                            free={!values?.email}
                          />
                          <AppDatePicker
                            name="birthday"
                            label="Năm sinh"
                            format={DATE_FORMAT}
                            valueAppDatePicker={values?.birthday}
                            free={!values?.birthday}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="Website"
                            name="website"
                            value={values?.website}
                            free={!values?.website}
                            disabled
                          />
                          <TextInput
                            label="Vị trí"
                            name="positionName"
                            value={values?.positionName}
                            free={!values?.positionName}
                            required
                            status={!values?.positionName ? "error" : undefined}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="LinkedIn"
                            name="linkedin"
                            value={values?.linkedin}
                            free={!values?.linkedin}
                          />
                          <SelectInput
                            name="status"
                            labelselect="Trạng thái"
                            data={statusCandidateList}
                            value={values?.status}
                            free={!values?.status}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            label="Github"
                            name="github"
                            value={values?.github}
                            free={!values?.github}
                          />
                          <SelectInput
                            name="experienceYear"
                            labelselect="Kinh nghiệm"
                            data={listExperience}
                            value={values?.experienceYear?.value}
                            free={!values?.experienceYear?.value}
                            required
                            status={
                              !values?.experienceYear?.value
                                ? "error"
                                : undefined
                            }
                          />
                        </Row>
                        <div className="mt-5 font-bold">Sơ lược</div>
                        <Row className="mt-2 div-time">
                          <SelectInput
                            mode="multiple"
                            name="listCandidateSkillId"
                            labelselect="Kỹ năng"
                            value={values.listCandidateSkillId}
                            data={mapFilterSkillData(
                              requestSkillList.data ?? []
                            )}
                            free={values.listCandidateSkillId?.length === 0}
                          />
                          <SelectInput
                            mode="multiple"
                            name="languages"
                            labelselect="Ngoại ngữ"
                            data={mapFilterData(listLanguage)}
                            value={values.languages}
                            free={values.languages?.length === 0}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <SelectInput
                            mode="multiple"
                            name="workLocationName"
                            labelselect="Địa điểm làm việc"
                            data={sortWorkLocation(
                              mapFilterWorkLocation(
                                requestWorkLocationList.data ?? []
                              ),
                              "key"
                            )}
                            value={values.workLocationName}
                            free={values?.workLocationName?.length === 0}
                          />
                          <AppDatePicker
                            name="onboardDate"
                            label="Ngày có thể onboard"
                            format={DATE_FORMAT}
                            valueAppDatePicker={values?.onboardDate}
                            free={!values?.onboardDate}
                          />
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            containerclassname="flex"
                            label="Mức lương hiện tại"
                            name="currentSalary"
                            onlynumber
                            value={values.currentSalary}
                            free={!values.currentSalary}
                            typeInput="salary"
                            iscurrency
                            maxLength={100}
                          />
                          <Row className="flex">
                            <AppSelectCurrency
                              name="currencyTypeId"
                              value={values?.currencyTypeId}
                              style={{width: "80px"}}
                              isAlone
                            />
                            <AppSelectCurrency
                              classNameContainer="ml-2"
                              name="salaryExpectedTypeSelected"
                              value={values?.salaryExpectedTypeSelected}
                              style={{width: "90px"}}
                              options={salaryType}
                              isAlone
                            />
                          </Row>
                        </Row>
                        <Row className="mt-2 div-time">
                          <TextInput
                            containerclassname="flex"
                            label="Mức lương mong muốn"
                            name="salaryExpected"
                            onlynumber
                            value={values.salaryExpected}
                            free={!values.salaryExpected}
                            typeInput="salary"
                            iscurrency
                            maxLength={100}
                          />
                          <Row className="flex">
                            <AppSelectCurrency
                              name="currencyTypeId"
                              value={values?.currencyTypeId}
                              style={{width: "80px"}}
                              isAlone
                            />
                            <AppSelectCurrency
                              classNameContainer="ml-2"
                              name="currentSalaryTypeSelected"
                              value={values?.currentSalaryTypeSelected}
                              style={{width: "90px"}}
                              options={salaryType}
                              isAlone
                            />
                          </Row>
                        </Row>
                        <div className="edit-table-textearea">
                          <Input.TextArea
                            name="note"
                            className="border-dash"
                            rows={4}
                            placeholder="Cộng tác viên vui lòng nhập thêm thông tin của ứng viên về lý do chuyển việc, trình độ ngoại ngữ, kinh nghiệm bổ sung để rút ngắn thời gian xử lý hồ sơ"
                            maxLength={1000}
                            value={values?.note}
                          />
                        </div>
                        <div className="mt-4">
                          <TableCandidateWorkHistories
                            dataSource={dataCandidate?.workHistories ?? []}
                            updateDataCandidateWorkHistories={
                              getDataWorkHistoryFromTable
                            }
                            experienceString={
                              dataCandidate?.experienceYear || ""
                            }
                            isVisibleModal={isModalVisible}
                          />
                        </div>

                        <div className="mt-4">
                          <TableCandidateEducation
                            dataSource={dataCandidate?.educations ?? []}
                            updateDataCandidateEducation={
                              getDataEducationFromTable
                            }
                          />
                        </div>
                      </form>
                    );
                  }}
                </Formik>
              </div>
            </div>
          </div>
          <div className="container-item-detail-modal right-4 p-6">
            <ViewCvCandidate
              docs={{
                filePathBase64: dataCandidate?.fileCVPath
                  ? dataCandidate.fileCVPath
                  : "",
                fileName: dataCandidate?.fileCVName
                  ? dataCandidate.fileCVName
                  : "",
              }}
              modeViewCandidate={IModeViewCandidate.edit}
              createdDate={dataCandidate?.createdDate || ""}
            />
          </div>
        </Row>
        <Row className="flex justify-center my-4 items-center">
          <AppButton
            classrow="mr-2 w-64  btn-cancel"
            label="Hủy bỏ"
            typebutton="primary"
            onClick={handleCancel}
          />
          <AppButton
            classrow="ml-2 w-64	"
            label="Lưu thông tin"
            typebutton="primary"
            onClick={handleUpdateCandidate}
            isSubmitting={updateInformationCandidate?.isLoading}
          />
        </Row>
      </div>
    );
  }

  return (
    <AppModal
      className="modal-edit-candidate"
      open={isModalVisible}
      onCancel={onCancel}
      footer={null}
      title="Sửa thông tin ứng viên"
      width="85%"
    >
      {renderContent()}
    </AppModal>
  );
}
