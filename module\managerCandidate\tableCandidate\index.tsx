import React, {useC<PERSON>back, useImperative<PERSON>andle, useRef, useState} from "react";
import "./index.scss";
import {Formik} from "formik";
import {Checkbox, Col, Popover, Row, Select} from "antd";
import {TextInput} from "@app/components/TextInput";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import AppCheckBox from "@app/components/AppCheckbox";
import {ColumnsType} from "antd/lib/table";
import ApiCandidate, {
  ICandidateObject,
  IListCandidate,
  IListSkill,
  IManagerByConsultantList,
  IWorkLocationList,
} from "@app/api/ApiCandidate";
import {
  deadTimeFastSearch,
  getStatusCandidate,
  listExperience,
  listLanguage,
  listStatuses,
} from "@app/utils/constants/state";
import AppTable from "@app/components/AppTable";
import {UseQueryResult, useMutation, useQuery} from "react-query";
import {useSelector, useDispatch} from "react-redux";
import {
  changeListColShowCandidate,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {CheckboxValueType} from "antd/lib/checkbox/Group";
import {
  formatMoney,
  setQueryUrl,
  sortWorkLocation,
} from "@app/utils/constants/function";
import {SelectInput} from "@app/components/SelectInput";
// eslint-disable-next-line import/no-cycle
import {mapFilterWorkLocation, MyFromCandidate} from "..";
import AppDatePicker from "@app/components/AppDatePicker";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppUploadCv from "@app/components/AppUploadCv";
import _ from "lodash";
import {setLoading} from "@app/redux/slices/SystemSlice";
import moment from "moment";
import {IAccountRole} from "@app/types";
import StatisticalTable from "@app/module/dataPool/StatisticalTable";

interface TableCandidateProps {
  initialValues: MyFromCandidate;
  requestCandidateList: UseQueryResult<IListCandidate, unknown>;
  onClickCandidate: (candidate: ICandidateObject) => void;
  formikRef: any;
  requestSkillList: UseQueryResult<IListSkill[], unknown>;
  requestWorkLocationList: UseQueryResult<IWorkLocationList[], unknown>;
  handleSearch: () => void;
  handleUploadFile: (file: File, formData: FormData) => void;
  resetSearch: () => void;
  allUsersData: Array<{userName: string; userId: string}>;
}

interface IItemCol {
  label: string;
  value: string;
}

function TableCandidate(props: TableCandidateProps, ref: any): JSX.Element {
  const {
    initialValues,
    requestCandidateList,
    onClickCandidate,
    formikRef,
    requestSkillList,
    requestWorkLocationList,
    handleSearch,
    handleUploadFile,
    resetSearch,
    allUsersData,
  } = props;
  const [isShowFilterAdvance, setIsShowFilterAdvance] = useState(false);
  const {listColShowCandidate} = useSelector(selectUser);
  const dispatch = useDispatch();
  const timeOut = useRef<any>();
  const {user} = useSelector(selectUser);
  const roleDisplayStatistical = [IAccountRole.CSL, IAccountRole.ADMIN];
  const isDisplayStatistical = roleDisplayStatistical.some((item) =>
    user?.role?.includes(item)
  );
  const [isShowModalStatistical, setIsShowModalStatistical] =
    useState<boolean>(false);

  const handleShowModalStatistical = (): void => {
    setIsShowModalStatistical(true);
  };

  const handleHideModalStatistical = (): void => {
    setIsShowModalStatistical(false);
  };

  useImperativeHandle(
    ref,
    () => ({
      isShowFilterAdvance,
    }),
    [isShowFilterAdvance]
  );

  const getManagerByConsultantLeaderId = useQuery(
    "getManagerByConsultantLeaderId",
    () => {
      return ApiCandidate.getAllManagerByConsultantLeaderId();
    }
  );

  const exportCandidate = useMutation(
    (param: IListCandidate) => {
      return ApiCandidate.exportCandidate(param);
    },
    {
      onSuccess: () => {
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const columnAll: ColumnsType<ICandidateObject> = [
    {
      title: "Họ tên",
      dataIndex: "name",
      key: "name",
      align: "left",
      className: "cursor-pointer",
      onCell: (record: ICandidateObject): any => {
        return {
          onClick: (): void => {
            setQueryUrl({id: String(record.candidateId)});
            onClickCandidate(record);
          },
        };
      },
      render: (_, {candidateId, name}: ICandidateObject) => (
        <span>{`${candidateId} - ${name}`}</span>
      ),
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: 200,
    },
    {
      title: "Số điện thoại",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
      width: 120,
    },
    {
      title: "Vị trí",
      dataIndex: "positionExpected",
      key: "positionExpected",
    },
    {
      title: "Kĩ năng",
      dataIndex: "skill",
      key: "skill",
      width: 80,
      render: (_, {skills}: ICandidateObject): JSX.Element => {
        if (!skills) {
          return <div />;
        }
        const listSkill = skills.split(",");
        const listOption = listSkill.map((item, index) => ({
          label: item,
          value: item,
        }));
        return (
          <Select
            mode="multiple"
            className="list-skill w-full text12"
            value={listSkill}
            options={listOption}
            disabled
            maxTagCount="responsive"
          />
        );
      },
    },
    {
      title: "Ngoại ngữ",
      dataIndex: "languages",
      key: "languages",
      width: 120,
      render: (_, {languages}: ICandidateObject): JSX.Element => {
        if (!languages) {
          return <div />;
        }
        return <div>{languages?.join(", ")}</div>;
      },
    },
    {
      title: "Kinh nghiệm",
      dataIndex: "experienceYear",
      width: 120,
      key: "experienceYear",
    },
    // {
    //   title: "Trạng thái",
    //   key: "statusName",
    //   dataIndex: "statusName",
    //   width: 100,
    //   render: (_, {status, statusName}: ICandidateObject): JSX.Element => {
    //     return (
    //       <span
    //         className="status-candidate text12"
    //         style={{backgroundColor: getStatusCandidate(status).color}}
    //       >
    //         {statusName}
    //       </span>
    //     );
    //   },
    // },
    {
      title: "Địa điểm",
      dataIndex: "workLocationNames",
      key: "workLocationNames",
      render: (_, {workLocationNames}: ICandidateObject): JSX.Element => {
        return <span>{workLocationNames?.toString()}</span>;
      },
    },
    {
      title: "Mức lương",
      dataIndex: "salaryExpected",
      key: "salaryExpected",
      render: (
        _,
        {salaryExpected, currencyTypeId}: ICandidateObject
      ): JSX.Element => {
        return <span>{formatMoney(salaryExpected, currencyTypeId)}</span>;
      },
    },
    // Ẩn createdByName tạo vì lí do bảo mật
    // {
    //   title: "Người tạo",
    //   dataIndex: "createdByName",
    //   key: "createdByName",
    // },
    {
      title: "Người tạo",
      dataIndex: "createdByName",
      key: "createdByName",
    },
  ];

  const columns = columnAll.filter((item) =>
    [...listColShowCandidate, "name"].some((i) => i === item.key)
  );

  const listColLeft = columnAll
    .slice(1, 7)
    .map((i) => ({label: i.title, value: i.key})) as IItemCol[];

  const listColRight = columnAll
    .slice(7)
    .map((i) => ({label: i.title, value: i.key})) as IItemCol[];

  const onChangeListCol = (checkedValues: CheckboxValueType[]): void => {
    dispatch(changeListColShowCandidate(checkedValues as string[]));
  };

  const exportData = () => {
    if (requestCandidateList?.data) {
      dispatch(setLoading(true));
      exportCandidate.mutate(requestCandidateList.data);
    }
  };

  const mapFilterManager =
    getManagerByConsultantLeaderId?.data?.map(
      (item: IManagerByConsultantList) => ({
        key: String(item.userId),
        label: item.name,
        value: String(item.userId),
      })
    ) || [];

  const handleSearchDebounce = useCallback(
    _.debounce(() => {
      handleSearch();
    }, deadTimeFastSearch),
    [handleSearch]
  );

  const handleResetForm = (): void => {
    resetSearch();
    formikRef?.current?.resetForm();
  };

  const filterCol = (
    <Checkbox.Group
      className="group-check-box-list-col"
      value={listColShowCandidate}
      onChange={onChangeListCol}
    >
      <Row>
        <Col span={12}>
          {listColLeft.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col span={12}>
          {listColRight.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );

  function contentFilterAdvance(
    values: MyFromCandidate,
    handleSubmit: () => void,
    handleReset: () => void
  ): JSX.Element {
    return (
      <div className="content-filter-advance-candidate">
        <Row className="flex items-center justify-between mb-4">
          <span className="title-filter">Tất cả bộ lọc</span>
          <AppButton
            classrow="btn-close-popover"
            typebutton="normal"
            onClick={() => setIsShowFilterAdvance(false)}
          >
            <Icon className="" icon="close-circle-line" size={20} />
          </AppButton>
        </Row>
        <SelectInput
          mode="multiple"
          containerclassname="mt-2"
          name="skills"
          labelselect="Kỹ năng"
          data={
            requestSkillList?.data?.map((item) => ({
              label: item.name,
              value: item.skillId,
              id: item.skillId,
              // key: item.skillId,
            })) as any
          }
          free={values.skills.length === 0}
          allowClear
          value={values.skills || []}
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          mode="multiple"
          containerclassname="mt-2"
          name="languages"
          labelselect="Ngoại ngữ"
          data={listLanguage.map((i) => ({
            value: i.id,
            key: i.id,
            label: i.label,
            id: i.id,
          }))}
          free={values.languages.length === 0}
          allowClear
          value={values.languages || []}
          handleChange={handleSearchDebounce}
        />
        <SelectInput
          mode="multiple"
          containerclassname="mt-2"
          name="workLocations"
          labelselect="Địa điểm làm việc"
          data={sortWorkLocation(
            mapFilterWorkLocation(requestWorkLocationList.data ?? []),
            "key"
          )}
          free={values.workLocations.length === 0}
          allowClear
          value={values.workLocations || []}
          handleChange={handleSearchDebounce}
          // optionFilterProp="label"
        />
        <Row className="mt-2 div-time ">
          <AppDatePicker
            name="from"
            label="Từ"
            free={!values?.from}
            format={DATE_FORMAT}
            valueAppDatePicker={values.from ? moment(values.from) : ""}
            allowClear
            onChange={handleSearchDebounce}
          />
          <AppDatePicker
            name="to"
            label="Đến"
            free={!values?.to}
            format={DATE_FORMAT}
            valueAppDatePicker={values.to ? moment(values.to) : ""}
            allowClear
            onChange={handleSearchDebounce}
          />
        </Row>
        <SelectInput
          containerclassname="mt-2"
          name="experienceYear"
          labelselect="Số năm kinh nghiệm"
          data={listExperience}
          free={_.isEmpty(values?.experienceYear)}
          allowClear
          value={values?.experienceYear || []}
          handleChange={handleSearchDebounce}
          // optionFilterProp="label"
        />
        {/* <SelectInput
          containerclassname="mt-2"
          mode="multiple"
          name="statuses"
          labelselect="Trạng thái"
          data={listStatuses}
          free={values?.statuses?.length === 0}
          allowClear
          value={values.statuses}
          // optionFilterProp="label"
        /> */}
        <SelectInput
          containerclassname="mt-2"
          mode="multiple"
          name="managers"
          labelselect="Người tạo"
          data={mapFilterManager}
          free={values?.managers?.length === 0}
          allowClear
          value={values?.managers}
          optionFilterProp="label"
          handleChange={handleSearchDebounce}
        />
        <TextInput
          containerclassname="mt-2"
          name="historyCompanyName"
          label="Lịch sử làm việc"
          free={!values?.historyCompanyName}
          value={values?.historyCompanyName}
          onChange={handleSearchDebounce}
        />
        <Row className="mt-6 div-time">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={handleResetForm}
          />
          {/* <AppButton
            label="Tìm kiếm"
            typebutton="primary"
            onClick={handleSubmit}
          /> */}
        </Row>
      </div>
    );
  }

  return (
    <div>
      <StatisticalTable
        isModalVisible={isShowModalStatistical}
        handleCancel={handleHideModalStatistical}
        allUsersData={allUsersData}
        typeData="candidate"
      />
      <Formik
        initialValues={initialValues}
        innerRef={formikRef}
        onSubmit={handleSearch}
      >
        {({values, handleSubmit, handleReset}): JSX.Element => {
          return (
            <Row className="flex justify-between filter-candidate-container">
              <TextInput
                containerclassname="w-80"
                label="Nhập họ tên, sđt, email UV"
                name="textSearch"
                value={values.textSearch}
                onChange={handleSearchDebounce}
                disabled={isShowFilterAdvance}
                free={!values.textSearch}
              />
              <Row className="items-center">
                {isDisplayStatistical && (
                  <AppButton
                    typebutton="primary"
                    classrow="statistical-btn mr-1"
                    onClick={handleShowModalStatistical}
                  >
                    <div className="statistical-btn__content">
                      <span className="text14">Thống kê</span>
                    </div>
                  </AppButton>
                )}

                <AppUploadCv
                  id="file"
                  onChangeInput={handleUploadFile}
                  type="btn"
                />
                <Popover
                  className="mr-1"
                  placement="bottom"
                  trigger="click"
                  content={(): React.ReactNode =>
                    contentFilterAdvance(values, handleSubmit, handleReset)
                  }
                  open={isShowFilterAdvance}
                  onOpenChange={setIsShowFilterAdvance}
                >
                  <AppButton typebutton="normal" classrow="btn-filter">
                    <Icon
                      className="mr-1"
                      icon="filter-line"
                      size={12}
                      color="#324054"
                    />
                    Tìm kiếm nâng cao
                  </AppButton>
                </Popover>
                <Popover placement="bottom" trigger="click" content={filterCol}>
                  <AppButton typebutton="normal" classrow="btn-filter">
                    <Icon
                      className="mr-1"
                      icon="eye"
                      size={16}
                      color="#324054"
                    />
                    Hiển thị {listColShowCandidate.length + 1}/
                    {columnAll.length}
                  </AppButton>
                </Popover>
              </Row>
            </Row>
          );
        }}
      </Formik>
      <AppButton typebutton="normal" classrow="btn-export" onClick={exportData}>
        <Icon icon="download-cloud-line" size={24} color="#324054" />
      </AppButton>
      <div className="table-candidate mt-3">
        <AppTable
          rowClassName="row-table-candidate"
          dataSource={requestCandidateList.data?.candidatesPaging?.map(
            (item: ICandidateObject, index: number) => ({
              ...item,
              key: index,
            })
          )}
          columns={columns}
          loading={requestCandidateList.isLoading}
          scroll={{y: "60vh", x: 1300}}
        />
      </div>
    </div>
  );
}

export default React.forwardRef(TableCandidate);
