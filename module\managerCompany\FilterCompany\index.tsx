import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {Col, Row} from "antd";
import {Formik, FormikProps} from "formik";
import {FormEvent, useEffect, useRef} from "react";
import {deadTimeFastSearch} from "@app/utils/constants/state";
import {OptionSelect} from "@app/types";
import {DataFormSearchCompany, ValuesSearchCompany} from "@app/api/ApiCompany";
import {sortWorkLocation} from "@app/utils/constants/function";

interface Props {
  workLocations: OptionSelect[];
  skills: OptionSelect[];
  onResetCurrentPage: () => void;
  onSearchParams: (data: ValuesSearchCompany) => void;
}

const initialValue: ValuesSearchCompany = {
  companyName: "",
  companyLocation: [],
  skills: [],
  currentPage: 1,
  pageSize: 50,
};

const initialValueForm: DataFormSearchCompany = {
  companyName: "",
  companyLocation: [],
  skills: [],
};

export function FilterCompany(props: Props): JSX.Element {
  const {workLocations, skills, onResetCurrentPage, onSearchParams} = props;
  const formikRef = useRef<FormikProps<DataFormSearchCompany>>(null);
  const timeOut = useRef<any>();

  const handleSearch = (): void => {
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      onResetCurrentPage();
      onSearchParams({
        ...initialValue,
        companyName: formikRef?.current?.values?.companyName?.trim(),
        companyLocation:
          formikRef?.current?.values?.companyLocation?.map(
            (item) => (item?.key as string) || (item.value as string)
          ) || [],
        skills:
          formikRef?.current?.values?.skills?.map(
            (item) => (item?.key as string) || (item.value as string)
          ) || [],
      });
    }, deadTimeFastSearch);
  };

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  return (
    <Formik
      initialValues={initialValueForm}
      innerRef={formikRef}
      onSubmit={() => {
        //
      }}
    >
      {({values}) => {
        return (
          <form
            onSubmit={(e: FormEvent<HTMLFormElement>): void => {
              e.preventDefault();
            }}
          >
            <div className="search-form">
              <Row gutter={[16, 16]} justify="start" className="w-full">
                <Col xs={6}>
                  <SelectInput
                    data={sortWorkLocation(workLocations, "key")}
                    name="companyLocation"
                    labelselect="Địa điểm làm việc"
                    mode="multiple"
                    onSelect={handleSearch}
                    onClear={handleSearch}
                    onDeselect={handleSearch}
                    value={values?.companyLocation}
                    free={values?.companyLocation.length === 0}
                    allowClear
                  />
                </Col>
                <Col xs={6}>
                  <SelectInput
                    name="skills"
                    labelselect="Kĩ năng"
                    data={skills}
                    mode="tags"
                    onSelect={handleSearch}
                    onClear={handleSearch}
                    onDeselect={handleSearch}
                    value={values?.skills}
                    free={values?.skills?.length === 0}
                    allowClear
                  />
                </Col>
                <Col xs={6}>
                  <TextInput
                    name="companyName"
                    label="Công ty"
                    onChange={handleSearch}
                    value={values.companyName}
                  />
                </Col>
              </Row>
            </div>
          </form>
        );
      }}
    </Formik>
  );
}
