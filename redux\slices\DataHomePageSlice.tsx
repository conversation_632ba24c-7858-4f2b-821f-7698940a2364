import {IJob} from "@app/api/ApiJob";
import {SearchParamsJobFilter} from "@app/types";
import {createSlice, PayloadAction} from "@reduxjs/toolkit";

interface HomePageData {
  data: IJob;
  searchParamsJobFilter: SearchParamsJobFilter | null;
}

const initialState: HomePageData = {
  data: {
    currentPage: 1,
    jobLabels: [],
    isSearchHotJob: false,
    hasNext: false,
    jobs: [],
    jobTypes: [],
    levels: [],
    pageSize: 20,
    salaryRanges: [],
    sortOption: 0,
    textSearch: "",
    totalJob: 0,
    workLocations: [],
  },
  searchParamsJobFilter: null,
};

const homePageDataSlice = createSlice({
  name: "homePageData",
  initialState,
  reducers: {
    setDataJob(state, action: PayloadAction<IJob>) {
      state.data = action.payload;
    },
    setSearchParamJobFilter(
      state,
      action: PayloadAction<SearchParamsJobFilter>
    ) {
      state.searchParamsJobFilter = action.payload;
    },
  },
});

export const {setDataJob, setSearchParamJobFilter} = homePageDataSlice.actions;

export default homePageDataSlice.reducer;
