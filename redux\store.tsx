import Config from "../config";
import createWebStorage from "redux-persist/lib/storage/createWebStorage";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
// eslint-disable-next-line import/no-cycle
import UserReducer from "./slices/UserSlice";
import MenuReducer from "./slices/MenuSlice";
import {combineReducers} from "redux";
import {configureStore} from "@reduxjs/toolkit";
import editTableReducer from "./slices/EditTableSlice";
import systemReducer from "./slices/SystemSlice";
// eslint-disable-next-line import/no-cycle
import NewsReducer from "./slices/NewsSlice";
import ChatbotReducer from "./slices/ChatbotSlice";
import JobReducer from "./slices/JobSlice";
// eslint-disable-next-line import/no-cycle
import GroupByApplicationReducer from "./slices/GroupByApplication";
import CloudMessageReducer from "./slices/CloudMessaging";
import HomePageDataReducer from "./slices/DataHomePageSlice";

const createNoopStorage = (): {
  getItem: (_key: string) => Promise<null>;
  setItem: (_key: string, value: unknown) => Promise<unknown>;
  removeItem: (_key: string) => Promise<void>;
} => {
  return {
    getItem(_key): Promise<null> {
      return Promise.resolve(null);
    },
    setItem(_key, value): Promise<unknown> {
      return Promise.resolve(value);
    },
    removeItem(_key): Promise<void> {
      return Promise.resolve();
    },
  };
};

const storage =
  typeof window !== "undefined"
    ? createWebStorage("local")
    : createNoopStorage();

const persistConfig = {
  key: Config.STORE_NAME,
  version: 1,
  storage: storage,
  whitelist: ["user", "news"],
};

const rootReducers = combineReducers({
  user: UserReducer,
  menu: MenuReducer,
  editTableSlice: editTableReducer,
  system: systemReducer,
  news: NewsReducer,
  chatbot: ChatbotReducer,
  job: JobReducer,
  groupByApplication: GroupByApplicationReducer,
  cloudMessage: CloudMessageReducer,
  homePageData: HomePageDataReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducers);

const store = configureStore({
  reducer: persistedReducer,
  devTools: process.env.NODE_ENV !== "production",
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export const persistor = persistStore(store);

export default store;

export type IRootState = ReturnType<typeof store.getState>;

export type IAppDispatch = typeof store.dispatch;
