.modal-add-candidate {
  top: 20px;
  color: $text-color-input;

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .container-item-detail-modal {
    border: 1px dashed $border-color;
    border-radius: 16px;
  }

  .content-modal-add-candidate {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .btn-cancel {
    button {
      background-color: $secondary-color;
      color: $primary-color;
    }
  }

  .new-staff-upload {
    .cv-form {
      padding: 20px;
      background-color: #fff;
      border-color: #ccc;
    }
  }
}
