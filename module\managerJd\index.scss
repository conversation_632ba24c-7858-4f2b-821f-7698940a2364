.jd {
  // height: 90vh;
  overflow: hidden;

  &__history,
  &__description {
    padding: 12px 6px 12px 12px;
    overflow: hidden;
    height: 85vh;
  }

  &__list {
    height: calc(100% - 12px - 21px);
  }

  &__item {
    padding: 2px 4px;
    margin-bottom: 4px;
    border-radius: 4px;

    &:hover {
      background-color: $select_color;
    }
  }

  &__active {
    background-color: $select_color;
  }

  &__description-block {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    padding: 12px 16px;
    height: calc(100% - 12px - 52px);
  }

  &__description-block-item {
    margin-bottom: 16px;
  }
}
