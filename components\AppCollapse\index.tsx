import {Collapse, CollapseProps} from "antd";
import React from "react";
import Icon from "../Icon/Icon";
import "./index.scss";

export default function AppCollapse(props: CollapseProps): JSX.Element {
  return (
    <Collapse
      {...props}
      bordered={false}
      // eslint-disable-next-line react/no-unstable-nested-components
      expandIcon={({isActive}): React.ReactNode => {
        return (
          <Icon
            size={10}
            icon="arrow-drop-down-line"
            color="#324054"
            className={`icon-down${!isActive ? " as-up" : ""}`}
          />
        );
      }}
      expandIconPosition={props.expandIconPosition || "end"}
      className={`app-collapse ${props.className}`}
    />
  );
}
