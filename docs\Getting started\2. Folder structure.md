# Cấu trúc thư mục

## 1. Folder structure
```
📦src
 ┣ 📂api
 ┃ ┣ 📂ErrorMessage
 ┃ ┃ ┣ 📜ListErrorMessage.tsx
 ┃ ┣ 📜ApiUser.tsx
 ┃ ┣ 📜Fetcher.tsx
 ┃ ┗ 📜...
 ┣ 📂components
 ┃ ┣ 📂Icon
 ┃ ┣ 📂Layout
 ┃ ┣ 📂ModalCustom
 ┃ ┃ ┣ 📜index.tsx
 ┃ ┃ ┣ 📃index.scss
 ┃ ┗ 📂...
 ┣ 📂config
 ┃ ┗ 📜index.tsx
 ┣ 📂docs
 ┣ 📂i18n
 ┃ ┣ 📂en
 ┃ ┃ ┣ 📄translation.json
 ┃ ┣ 📂vi
 ┃ ┃ ┣ 📄translation.json
 ┃ ┗ 📜index.tsx
 ┣ 📂module
 ┃ ┣ 📂auth
 ┃ ┃ ┣ 📂forgot-password
 ┃ ┃ ┃ ┃ 📂components
 ┃ ┃ ┃ ┃ ┃ 📂forgot-password-form
 ┃ ┃ ┃ ┃ ┃ ┣ 📃index.scss
 ┃ ┃ ┃ ┃ ┃ ┣ 📜form-config.tsx
 ┃ ┃ ┃ ┃ ┃ ┗ 📜index.tsx
 ┃ ┃ ┃ ┣ 📃index.scss
 ┃ ┃ ┃ ┗ 📜index.tsx
 ┃ ┃ ┗ 📂...
 ┃ ┗ 📂...
 ┣ 📂pages
 ┃ ┣ 📂auth
 ┃ ┃ ┣ 📜forgot-password.tsx
 ┃ ┃ ┗ 📜...
 ┃ ┗ 📜...
 ┣ 📂public
 ┣ 📂redux
 ┃ ┣ 📂slices
 ┃ ┃ ┣ 📜MenuSlice.tsx
 ┃ ┃ ┗ 📜...
 ┃ ┗ 📜index.tsx
 ┣ 📂routes
 ┃ ┣ 📜RouteList.tsx
 ┃ ┗ 📜index.tsx
 ┣ 📂styles
 ┃ ┣ 📂mixin
 ┃ ┃ ┣ 📃_shape.scss
 ┃ ┃ ┗ 📃...
 ┃ ┣ 📃_app.scss
 ┃ ┗ 📃_global.scss
 ┣ 📂types
 ┣ 📂utils
 ┃ ┣ 📂constants
 ┃ ┣ 📂validation
 ┃ ┣ 📂hooks
 ┃ ┣ 📂...
 ┃ ┣ 📜index.tsx
 ┃ ┗ 📜...

```

*Chú thích:*
- `📂api`: đây là nơi cài đặt code để call API cùng với định nghĩa type request body, param và response
- `📂components`: folder này chứa những components được dùng chung như `Modal`, `Icon`, `Layout`, ... 
- `📂config`: là nơi chứa constant được dùng chung của project. 
- `📂docs`: là nơi chứa tài liệu về dự án cũng như hướng dẫn sử dụng base.
- `📂i18n`: là nơi chứa file dịch text sang các ngôn ngữ khác nhau.
- `📂pages` và `📂modules`: thông thường thì sẽ chỉ cần 1 trong 2 folder này nhưng với next.js thì sẽ cần cả 2. `pages` chứa những file khá nhỏ, chúng có nhiệm vụ routing request, khởi tạo server data và trỏ đến code trong `modules` tương ứng.  
_(Lưu ý: mỗi file trong `pages` sẽ tương ứng với một folder trong `modules` - như ở sơ đồ bên trên, nếu `pages` có file `auth/forgot-password.tsx` thì `modules` sẽ có folder `auth/forgot-password`. Các components con của module sẽ đặt tại folder `components` trong thư mục `auth/forgot-pasword`)
- `📂public`: nơi chứa các file static có thể gọi bằng http request bình thường ví dụ có file `public/logo192.png` thì trên web sẽ truy cập file bằng đường link `http://...:3000/logo192.png`
- `📂redux`: chứa định nghĩa reducer, action để sử dụng cho redux trong folder `slices` viết sử dụng redux toolkit 
- `📂routes`: chứa định nghĩa cấu hình routes cho project (folder `page` khai báo các route cho app nhưng lại không thể phân quyền cho route). Có thể định nghĩa route là private, public, phân quyền cho route, add route vào sidebar, ...
- `📂styles`: nơi chứa các style được dùng chung cho toàn bộ project.
- `📂types`: chứa type được dùng chung, *không liên quan duy nhất* với một thành phần nào.
- `📂utils`: chứa các hàm utilities dùng chung trong project. (Chứa cả định nghĩa validation cho form hoặc các dữ liệu tĩnh)

## 2. Component folder structure
Một component tiêu chuẩn sẽ có cấu trúc như dưới đây:
```
📂[component-name]
 ┣ 📃index.scss
 ┣ 📜form-config.tsx
 ┗ 📜index.tsx
```
*Chú thích:*
- `📜index.tsx`: chứa code component(JSX, event handler, ...).
- `📜form-config.tsx`: chứa code validate form vaf interface cho form. (Optional)
- `📃index.scss`: file này chứa style dùng riêng cho component này. (Lưu ý: cần phải sử dụng wrapper class khi viết css)

## 3. Module folder structure
Một module tiêu chuẩn sẽ có cấu trúc như dưới đây:
```
📂[module-name]
 ┣ 📂components
 ┃ ┣ 📃index.scss
 ┃ ┗ 📜index.tsx
 ┣ 📂context
 ┣ 📃index.scss
 ┗ 📜index.tsx
```
*Chú thích:*
- `📂components`: chứa các component chỉ sử dụng trong module này, nếu component đươc dùng tại các module khác thì phải di chuyển lên thư mục `components` bên ngoài.
- `📂context`: chứa khai báo wrapper, context được sử dụng trong module
- `📜index.tsx`: chứa code component(JSX, event handler, ...).
- `📃index.scss`: file này chứa style dùng riêng cho component này. (Lưu ý: cần phải sử dụng wrapper class khi viết css)

