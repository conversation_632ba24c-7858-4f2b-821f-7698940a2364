import React, {useMemo, useRef, useState} from "react";
import "./index.scss";
import {Col, Image, Row, notification} from "antd";
import {Formik, FormikProps} from "formik";
import {TextInput} from "@app/components/TextInput";
import {SelectInput} from "@app/components/SelectInput";
import {
  careerTypes,
  levelRecruitment,
  recruitmentLanguages,
  recruitmentTypes,
  timeForReco,
  yearOfRecruitmentExperience,
} from "@app/utils/constants/state";
import {OptionSelect} from "@app/types";
import AppCheckBox from "@app/components/AppCheckbox";
import AppButton from "@app/components/AppButton";
import Link from "next/link";
import config from "@app/config";
import {AppPassword} from "@app/components/AppPassword";
import {
  validateEmail,
  validatePassword,
  validatePhoneNumber,
} from "@app/utils/constants/function";
import {useMutation, useQueries} from "react-query";
import ApiAccount, {
  AccountRegisterParams,
  LocationParams,
} from "@app/api/ApiAccount";
import ApiNotification, {
  IParamNotificationSave,
} from "@app/api/ApiNotification";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import ModalRegisterSuccess from "./ModalRegisterSuccess";
import {useRouter} from "next/router";
import ApiRequestJob from "@app/api/ApiRequestJob";
import AppTreeSelect, {TreeSelectDataItem} from "@app/components/AppTreeSelect";

interface RegisterForm {
  name: string;
  email: string;
  phoneNumber: string;
  password: string;
  recommendationEmail: string;
  recruitmentTypeId: OptionSelect;
  careerTypeId: OptionSelect;
  yoe: OptionSelect | null;
  positions?: Array<OptionSelect>;
  locations: Array<OptionSelect>;
  languages: Array<OptionSelect>;
  levels: Array<OptionSelect>;
  timeForReco: OptionSelect | null;
  needSupport?: string;
}

const initialValues: RegisterForm = {
  name: "",
  email: "",
  phoneNumber: "",
  password: "",
  recommendationEmail: "",
  recruitmentTypeId: recruitmentTypes[0],
  careerTypeId: careerTypes[0],
  yoe: null,
  levels: [],
  locations: [],
  languages: [],
  timeForReco: null,
  positions: [],
};

function Register(): JSX.Element {
  const formikRef = useRef<FormikProps<RegisterForm>>(null);
  const [agreeChecked, setAgreeChecked] = useState<boolean>(false);
  const [showModalSuccess, setShowModalSuccess] = useState<boolean>(false);
  const router = useRouter();
  if (router?.query?.id) {
    initialValues.recommendationEmail = router?.query?.id as string;
  }

  const dispatch = useDispatch();
  const onChecked = (e: any): void => {
    setAgreeChecked(e?.target?.checked);
  };

  const registerCollaborators = useMutation(
    (data: AccountRegisterParams) => {
      return ApiAccount.registerCollaborators(data);
    },
    {
      onSuccess(_, variables) {
        save.mutate({
          NotiType: "N001",
          PartnerName: variables?.name || "",
        });
        setShowModalSuccess(true);
      },
    }
  );

  const save = useMutation((data: IParamNotificationSave) => {
    return ApiNotification.createNotification(data);
  });

  const getAllData = useQueries([
    {
      queryKey: ["getPositionList"],
      queryFn: () => ApiRequestJob.getPositionList(),
    },
    {
      queryKey: ["groupLocation"],
      queryFn: () => ApiRequestJob.getDataGroupLocation(),
    },
  ]);

  const renderValueArrayToForm = (
    value: Array<OptionSelect> = []
  ): Array<string> => {
    if (!value || value.length === 0) {
      return [];
    }
    return value.map((item) => item.value);
  };

  const mapDataLocation = (
    value: Array<string> = []
  ): Array<LocationParams> => {
    if (!value || value.length === 0) return [];

    const countryId = getAllData[1]?.data?.map((i) => i.countryId) || [];
    const result: Array<LocationParams> = [];

    countryId.forEach((id) => {
      const data: Array<string> = [];
      value.forEach((i) => {
        const split = i.split("_");
        if (split.includes(id)) {
          data.push(split[0]);
        }
      });
      if (data.length > 0) {
        result.push({
          countryId: id,
          workLocationIds: data,
        });
      }
    });

    return result;
  };

  const handleRegisterCollaborators = (): void => {
    dispatch(setLoading(true));
    const values = formikRef.current?.values;

    const valueConverter: AccountRegisterParams = {
      recruitmentTypeId: Number(values?.recruitmentTypeId.key),
      agreeRecoPolicy: agreeChecked,
      careerTypeId: Number(values?.careerTypeId?.key),
      email: values?.email?.trim() || "",
      name: values?.name?.trim() || "",
      password: values?.password?.trim() || "",
      phoneNumber: values?.phoneNumber?.trim() || "",
      recommendationEmail: values?.recommendationEmail?.trim() || "",
      userName: values?.email?.trim() || "",
      languages: renderValueArrayToForm(values?.languages),
      levels: renderValueArrayToForm(values?.levels),
      locations: mapDataLocation(values?.locations as any),
      needSupport: values?.needSupport?.trim() || "",
      positions: renderValueArrayToForm(values?.positions),
      timeForReco: Number(values?.timeForReco?.value || 0),
      yoe: Number(values?.yoe?.value || 0),
    };

    const {
      agreeRecoPolicy,
      careerTypeId,
      email,
      languages,
      levels,
      locations,
      name,
      password,
      phoneNumber,
      positions,
      recruitmentTypeId,
      userName,
    } = valueConverter;

    if (
      !(
        name &&
        validateEmail(email) &&
        validatePhoneNumber(phoneNumber) &&
        validatePassword(password) &&
        careerTypeId &&
        recruitmentTypeId &&
        agreeRecoPolicy &&
        languages.length &&
        levels.length &&
        locations.length &&
        positions.length &&
        values?.timeForReco?.value &&
        values?.yoe?.value &&
        userName
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng nhập thông tin đầy đủ và chính xác",
      });
    } else {
      registerCollaborators.mutate(valueConverter);
    }
    dispatch(setLoading(false));
  };

  const optionDataFilter = useMemo(() => {
    if (getAllData && getAllData.length > 0) {
      const position: Array<OptionSelect> =
        getAllData[0].data?.map((i) => ({
          label: i.name,
          value: i.positionId,
          key: i.positionId,
        })) || [];

      const location: Array<TreeSelectDataItem> =
        getAllData[1].data?.map((location) => {
          const workLocation = location.workLocations.map((i) => ({
            label: i.name,
            value: `${i.workLocationId}_${i.countryId}`,
          }));

          return {
            label: location.countryName,
            value: location.countryId,
            children: workLocation,
          };
        }) || [];

      return {
        position,
        location,
      };
    }
    return {
      position: [],
      location: [],
    };
  }, [getAllData]);

  return (
    <div className="ui-register">
      <div className="ui-register__container">
        <Row gutter={[16, 16]}>
          <Col xs={12}>
            <div className="ui-register__form">
              <div className="ui-register__logo flex justify-center">
                <Link href={config.PATHNAME.HOME}>
                  <Image
                    src="/img/logo-icon.svg"
                    alt="logo"
                    height={50}
                    width={50}
                    preview={false}
                  />
                  <Image
                    src="/img/logo-text.svg"
                    alt="logo"
                    height={56}
                    width={75}
                    preview={false}
                    className="ml-2"
                  />
                </Link>
              </div>
              <h2 className="text-center ui-register__title text32 font-bold">
                Đăng ký cộng tác viên của Reco
              </h2>
              <Row justify="center" className="w-full">
                <Col xs={18}>
                  <Formik
                    innerRef={formikRef}
                    initialValues={initialValues}
                    onSubmit={handleRegisterCollaborators}
                  >
                    {({values, handleSubmit}): JSX.Element => {
                      return (
                        <form onSubmit={handleSubmit}>
                          <Row gutter={[16, 16]} className="mt-2">
                            <Col xs={12}>
                              <TextInput
                                name="name"
                                label="Họ và tên"
                                placeholder="Nhập họ và tên"
                                value={values?.name}
                                free={!values?.name}
                                required
                                status={!values?.name ? "error" : undefined}
                              />
                            </Col>
                            <Col xs={12}>
                              <TextInput
                                name="email"
                                label="Email"
                                placeholder="Nhập email của bạn"
                                value={values?.email}
                                free={!values?.email}
                                required
                                status={
                                  !validateEmail(values?.email)
                                    ? "error"
                                    : undefined
                                }
                              />
                            </Col>
                          </Row>

                          <Row gutter={[16, 16]} className="mt-2">
                            <Col xs={12}>
                              <TextInput
                                name="phoneNumber"
                                label="Số điện thoại"
                                placeholder="Nhập số điện thoại của bạn"
                                value={values?.phoneNumber}
                                free={!values?.phoneNumber}
                                required
                                status={
                                  !values?.phoneNumber ||
                                  !validatePhoneNumber(values?.phoneNumber)
                                    ? "error"
                                    : undefined
                                }
                                isphonenumber
                              />
                            </Col>
                            <Col xs={12}>
                              <AppPassword
                                name="password"
                                label="Mật khẩu"
                                placeholder="Nhập mật khẩu"
                                value={values?.password}
                                free={!values?.password}
                                required
                                status={
                                  !validatePassword(values?.password)
                                    ? "error"
                                    : undefined
                                }
                              />
                            </Col>
                          </Row>

                          <Row gutter={[16, 16]} className="mt-2">
                            <Col xs={12}>
                              <TextInput
                                name="recommendationEmail"
                                label="Mã giới thiệu"
                                placeholder="Nhập mã giới thiệu (nếu có)"
                                value={values?.recommendationEmail}
                                free={!values?.recommendationEmail}
                              />
                            </Col>
                            <Col xs={12}>
                              <SelectInput
                                name="recruitmentTypeId"
                                data={recruitmentTypes}
                                labelselect="Lĩnh vực tuyển dụng"
                                value={values?.recruitmentTypeId}
                                free={!values?.recruitmentTypeId?.value}
                                required
                                status={
                                  values?.recruitmentTypeId
                                    ? undefined
                                    : "error"
                                }
                              />
                            </Col>
                          </Row>

                          <Row gutter={[16, 16]} className="mt-2">
                            <Col xs={12}>
                              <SelectInput
                                name="careerTypeId"
                                data={careerTypes}
                                labelselect="Hình thức làm việc"
                                value={values?.careerTypeId}
                                free={!values?.careerTypeId?.value}
                                status={
                                  values?.careerTypeId ? undefined : "error"
                                }
                                required
                              />
                            </Col>
                            <Col xs={12}>
                              <SelectInput
                                name="yoe"
                                data={yearOfRecruitmentExperience}
                                labelselect="Số năm kinh nghiệm tuyển dụng IT"
                                value={values?.yoe?.value}
                                free={!values?.yoe?.value}
                                required
                                status={
                                  values?.yoe?.value ? undefined : "error"
                                }
                              />
                            </Col>
                          </Row>

                          <Row gutter={[16, 16]} className="mt-2">
                            <Col xs={12}>
                              <SelectInput
                                name="positions"
                                data={optionDataFilter.position}
                                labelselect="Vị trí thường xuyên tuyển dụng"
                                value={values?.positions}
                                free={!values?.positions?.length}
                                status={
                                  values?.positions?.length
                                    ? undefined
                                    : "error"
                                }
                                required
                                mode="multiple"
                                optionFilterProp="label"
                              />
                            </Col>
                            <Col xs={12}>
                              <AppTreeSelect
                                name="locations"
                                data={optionDataFilter.location}
                                labelselect="Khu vực thường xuyên tuyển dụng"
                                value={values?.locations || []}
                                required
                                status={
                                  values?.locations?.length
                                    ? undefined
                                    : "error"
                                }
                                treeNodeFilterProp="label"
                              />
                            </Col>
                          </Row>

                          <Row gutter={[16, 16]} className="mt-2">
                            <Col xs={12}>
                              <SelectInput
                                name="levels"
                                data={levelRecruitment}
                                labelselect="Level bạn thường xuyên tuyển dụng"
                                value={values?.levels || []}
                                free={!values?.levels?.length}
                                status={
                                  values?.levels?.length ? undefined : "error"
                                }
                                required
                                mode="multiple"
                              />
                            </Col>
                            <Col xs={12}>
                              <SelectInput
                                name="languages"
                                data={recruitmentLanguages}
                                labelselect="Ngoại ngữ sử dụng trong tuyển dụng"
                                value={values?.languages || []}
                                free={!values?.languages?.length}
                                required
                                status={
                                  values?.languages?.length
                                    ? undefined
                                    : "error"
                                }
                                mode="multiple"
                                handleChange={(value) => {
                                  const data = value?.map(
                                    (i: OptionSelect) => i.value
                                  );
                                  const lengthOptionData =
                                    recruitmentLanguages.length;
                                  if (
                                    data.includes(
                                      recruitmentLanguages[lengthOptionData - 1]
                                        .value
                                    )
                                  ) {
                                    formikRef.current?.setFieldValue(
                                      "languages",
                                      [
                                        recruitmentLanguages[
                                          lengthOptionData - 1
                                        ],
                                      ]
                                    );
                                  }
                                }}
                              />
                            </Col>
                          </Row>

                          <Row gutter={[16, 16]} className="mt-2">
                            <Col xs={12}>
                              <SelectInput
                                name="timeForReco"
                                data={timeForReco}
                                labelselect="Bạn dành bao nhiêu thời gian cho hoạt động tuyển dụng với RECO 1 ngày"
                                value={values?.timeForReco?.value}
                                free={!values?.timeForReco?.value}
                                status={
                                  values?.timeForReco?.value
                                    ? undefined
                                    : "error"
                                }
                                required
                              />
                            </Col>
                            <Col xs={12}>
                              <TextInput
                                name="needSupport"
                                label="Bạn có cần RECO hỗ trợ gì về chuyên môn trong quá trình tuyển dụng không?"
                                value={values.needSupport}
                                free={!values.needSupport}
                              />
                            </Col>
                          </Row>

                          <AppCheckBox
                            className="mt-2"
                            checked={agreeChecked}
                            onChange={onChecked}
                          >
                            Tôi đồng ý với{" "}
                            <a
                              href="https://job.reco-vn.com/reco-manpower-for-partners/chinh-sach"
                              target="_blank"
                              rel="noreferrer"
                              className="ui-register__policy-link"
                            >
                              chính sách hợp tác
                            </a>{" "}
                            của Reco
                          </AppCheckBox>
                          <AppButton
                            typebutton="primary"
                            classrow="mt-4"
                            onClick={handleSubmit}
                            disabled={registerCollaborators.isLoading}
                            isSubmitting={registerCollaborators.isLoading}
                            htmlType="submit"
                          >
                            Đăng ký
                          </AppButton>
                          <p className="text-center mt-2 text16">
                            Bạn đã có tài khoản?{" "}
                            <Link href={config.PATHNAME.LOGIN}>
                              <span className="ui-register__policy-link">
                                Đăng nhập ngay
                              </span>
                            </Link>
                          </p>
                        </form>
                      );
                    }}
                  </Formik>
                </Col>
              </Row>
            </div>
          </Col>
          <Col xs={12}>
            <div className="flex justify-center">
              <Image
                src="/img/image-register.png"
                alt="logo"
                height={350}
                width={350}
                preview={false}
              />
            </div>
            <div className="text-center mt-4 text32">
              <p>Chính sách hấp dẫn</p>
              <p>Đồng hành và hỗ trợ CTV 1-1</p>
              <p>Đào tạo và phát triển kỹ năng chuyên sâu</p>
            </div>
          </Col>
        </Row>
      </div>
      <ModalRegisterSuccess
        open={showModalSuccess}
        email={formikRef?.current?.values?.email?.trim() || ""}
      />
    </div>
  );
}

export default Register;
