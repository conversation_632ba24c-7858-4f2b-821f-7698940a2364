import {IApplication} from "@app/api/ApiApplication";
import AppModal from "@app/components/AppModal";
import AppTable from "@app/components/AppTable";
import config from "@app/config";
import {findApplicationStatus} from "@app/utils/constants/function";
import {TableColumnsType} from "antd";
import React from "react";

interface IModalTableCV {
  data: IApplication[];
  loading?: boolean;
  open: boolean;
  onCloseModal: () => void;
}

export default function ModalTableCV({
  data,
  loading,
  open,
  onCloseModal,
}: IModalTableCV): JSX.Element {
  interface DataType {
    candidateName: string;
    positionName: string;
    customerName: number;
    statusName: string;
    stageName: string;
    services?: string[];
  }

  const columns: TableColumnsType<DataType> = [
    {
      title: "Tên ứng viên",
      dataIndex: "candidateName",
      key: "candidateName",
      align: "left",
      width: "200px",
      render: (value) => <div className="mt-1 whitespace-normal">{value}</div>,
    },
    {
      title: "Vị trí",
      dataIndex: "positionName",
      width: "35%",
      key: "positionName",
    },
    {
      title: "Công ty",
      dataIndex: "customerName",
      key: "customerName",
    },
    {
      title: "Trạng thái",
      dataIndex: "statusName",
      width: "180px",
      key: "statusName",
      render: (status: string, record: DataType) => (
        <span
          className="text-white py-1 px-2 rounded-lg"
          style={{
            backgroundColor: findApplicationStatus(
              String(record?.statusName),
              true
            ).color,
          }}
        >
          {`${record?.stageName || ""} ${status || ""} `}
        </span>
      ),
    },
    {
      title: "Dịch vụ",
      dataIndex: "services",
      key: "services",
      width: "150px",
      render: (service: string[]) => service.join(", "),
    },
  ];

  return (
    <AppModal
      className="modal-detail-application-japan"
      open={open}
      footer={null}
      onCancel={onCloseModal}
      width="80%"
      title="Hồ sơ"
    >
      <AppTable
        columns={columns}
        dataSource={data}
        loading={loading}
        scroll={{y: 600}}
        onRow={(record) => ({
          onClick: () => {
            window
              .open(
                `${window.origin}${config.PATHNAME.MANAGER_APPLICATION}?id=${record?.applicationId}`,
                "_blank"
              )
              ?.focus();
          },
        })}
        rowClassName={() => "cursor-pointer"}
      />
    </AppModal>
  );
}
