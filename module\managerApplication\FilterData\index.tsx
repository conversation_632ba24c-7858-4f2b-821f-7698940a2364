import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {TextInput} from "@app/components/TextInput";
import {Checkbox, Col, Popover, Row} from "antd";
import {Formik, FormikProps} from "formik";
import {SelectInput} from "@app/components/SelectInput";
import AppDatePicker from "@app/components/AppDatePicker";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import "./index.scss";
import {UseQueryResult, useQuery} from "react-query";
import {useSelector, useDispatch} from "react-redux";
import {
  changeListColShowApplication,
  initListColShowApplication,
  selectUser,
  setQueryParamsPages,
} from "@app/redux/slices/UserSlice";
import ApiCandidate, {IWorkLocationList} from "@app/api/ApiCandidate";
import moment from "moment";
import {
  DATE_FORMAT,
  NORMAL_DATE_FORMAT,
} from "@app/utils/constants/formatDateTime";
import {ColumnsType} from "antd/lib/table";
import {
  GroupByApplication,
  IApplication,
  IListApplication,
} from "@app/api/ApiApplication";
import AppCheckBox from "@app/components/AppCheckbox";
import {CheckboxValueType} from "antd/es/checkbox/Group";
import {IAccountRole, IMultiSelect, OptionSelect} from "@app/types";
import {
  deadTimeFastSearch,
  optionServiceRequest,
  stagesApplication,
  statusesApplication,
} from "@app/utils/constants/state";
// eslint-disable-next-line import/no-cycle
import {FormFilter} from "..";
import {
  convertStringToArray,
  mapDataOptionHasId,
  sortWorkLocation,
} from "@app/utils/constants/function";
import AppGroupBy from "@app/components/AppGroupBy";
import {setGroupByFieldApplication} from "@app/redux/slices/GroupByApplication";
import {IRootState} from "@app/redux/store";
import {useRouter} from "next/router";
import _ from "lodash";

interface FilterDataProps {
  updateValueFilter: (values: FormFilter) => void;
  setCurrentPage: (page: number) => void;
  columnsAll: ColumnsType<IApplication>;
  initialValuesFilter: FormFilter;
  typeRoleUsed: string;
  dataListApplications: UseQueryResult<IListApplication, unknown>;
  optionDataManager: Array<OptionSelect>;
}

interface IItemCol {
  label: string;
  value: string;
}

const optionsRoleFilter: OptionSelect[] = [
  {value: IAccountRole.CSL, label: "CST/CSL"},
  {value: IAccountRole.AML, label: "AMG/AML"},
];

function FilterData(props: FilterDataProps): JSX.Element {
  const {
    updateValueFilter,
    setCurrentPage,
    columnsAll,
    initialValuesFilter,
    typeRoleUsed,
    dataListApplications,
    optionDataManager,
  } = props;
  const {listColShowApplication, user} = useSelector(selectUser);
  const {groupByFieldApplication} = useSelector(
    (state: IRootState) => state.groupByApplication
  );

  const router = useRouter();
  const formikFilterNormalRef = useRef<FormikProps<FormFilter>>(null);
  const formikFilterAdvancedRef = useRef<FormikProps<FormFilter>>(null);
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();

  const urlSearchParams = new URLSearchParams(window.location.search);
  const searchParams = Object.fromEntries(urlSearchParams);

  const isCTV = user?.role?.includes(IAccountRole.CTV);
  const isCS =
    user?.role &&
    user.role?.filter((item) =>
      [IAccountRole.CSL, IAccountRole.CST].some((i) => i === item)
    )?.length > 0;
  const isAdmin = user?.role?.includes(IAccountRole.ADMIN);

  const workLocationData = useQuery("DataWorkLocation", () => {
    return ApiCandidate.getWorkLocationList();
  });

  // chi co user co role cst, csl, admin moi co filter nguoi quan ly
  const listColShowByCSL = (list: Array<string>): Array<string> =>
    !(isCS || isAdmin) ? list.filter((col) => col !== "managerName") : list;

  // khi khong co khoi tao listColShowApplication do website dang su dung
  const listColShow =
    listColShowByCSL(listColShowApplication) ||
    listColShowByCSL(initListColShowApplication);

  const validateTime = (values: any): boolean => {
    return (
      !values?.from ||
      !values?.to ||
      moment(values?.from).isSameOrBefore(moment(values?.to))
    );
  };

  const handleSearch = (): void => {
    const values = formikFilterAdvancedRef.current?.values;

    if (!validateTime(values)) {
      return;
    }
    const candidateInformation =
      formikFilterNormalRef.current?.values?.candidateInformation?.trim() || "";
    const requestJobName =
      formikFilterNormalRef.current?.values?.requestJobName?.trim() || "";
    const paramsGroupByClone = {
      ...groupByFieldApplication,
      applicationSearch: {
        ...groupByFieldApplication.applicationSearch,
        ...values,
        stages: mapDataOptionHasId(values?.stages || []),
        workingLocationIds: (values?.workingLocationIds || []).map(
          (i) => i.key
        ),
        candidateInformation,
        from: values?.from ? moment(values.from).format(DATE_FORMAT) : "",
        to: values?.to ? moment(values.to).format(DATE_FORMAT) : "",
        roleFilter: values?.roleFilterSelected?.value
          ? values.roleFilterSelected.value
          : null,
        isAdvanceSearch: true,
        creators: mapDataOptionHasId(values?.creators || []),
        statuses:
          (values?.stages || [])?.length > 0
            ? values?.statuses?.map((i) => ({
                ...i,
                id: i.value === "Chưa hoàn thành" ? null : i.key,
              }))
            : [],
        services: values?.services.map((i) => i.key),
        requestJobName,
        managerId: values?.managerId ? Number(values.managerId.value) : null,
        customerName: values?.customerName?.trim() || "",
      },
      groupByRequest: {
        ...groupByFieldApplication.groupByRequest,
        pageNumber: 1,
      },
    };

    dispatch(setGroupByFieldApplication(paramsGroupByClone as any));

    setCurrentPage(1);
    const result = {
      ...values,
      workingLocationIds: values?.workingLocationIds || [],
      candidateInformation,
      currentPage: 1,
      requestJobName,
      stages: mapDataOptionHasId(values?.stages || []),
      services: values?.services.map((i) => i?.key as any),
      from: values?.from ? moment(values.from) : "",
      to: values?.to ? moment(values.to) : "",
      managerId: values?.managerId ? Number(values.managerId.value) : null,
      customerName: values?.customerName?.trim() || "",
    };

    const queryParams = {
      customerName: values?.customerName || "",
      workingLocationIds: values?.workingLocationIds?.length
        ? JSON.stringify(values?.workingLocationIds)
        : "",
      stages: values?.stages?.length ? JSON.stringify(values?.stages) : "",
      statuses: values?.statuses?.length
        ? JSON.stringify(values?.statuses)
        : "",
      creators: values?.creators?.length
        ? JSON.stringify(values?.creators)
        : "",
      managerId: values?.managerId ? JSON.stringify(values?.managerId) : "",
      services: values?.services?.length
        ? JSON.stringify(values?.services)
        : "",
      from: values?.from ? moment(values?.from).format(NORMAL_DATE_FORMAT) : "",
      to: values?.to ? moment(values?.to).format(NORMAL_DATE_FORMAT) : "",
    };

    const queryParamsString = new URLSearchParams(queryParams).toString();

    updateValueFilter(result as any);

    dispatch(setQueryParamsPages({managerApplication: queryParamsString}));
  };

  const hide = (): void => {
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean): void => {
    setOpen(newOpen);
  };

  const listLocation = workLocationData.data?.map(
    (item: IWorkLocationList) => ({
      value: item.name,
      label: item.name,
      key: item.workLocationId,
    })
  );

  const listCreator = useMemo(() => {
    return dataListApplications.data?.creatorFilters.map(
      (item: IMultiSelect) => ({
        value: item.label,
        label: item.label,
        key: item.id,
      })
    );
  }, [dataListApplications.data?.creatorFilters]);

  useEffect(() => {
    const eventHandler = (evt: KeyboardEvent): void => {
      if (evt.key === "Enter") {
        formikFilterAdvancedRef.current?.handleSubmit();
      }
    };
    document.addEventListener("keydown", eventHandler);

    return () => {
      document.removeEventListener("keydown", eventHandler);
    };
  }, []);

  const disableDate = (current: any): boolean => {
    return current && current > moment().endOf("day");
  };

  const listColLeft: IItemCol[] = [
    {
      label: "Email",
      value: "candidateEmail",
    },
    {
      label: "Trạng thái",
      value: "statusName",
    },
    {
      label: "Số điện thoại",
      value: "candidatePhone",
    },
    {
      label: "Thời gian xử lý",
      value: "timeProcess",
    },
    {
      label: "Tỷ lệ phù hợp",
      value: "rate",
    },
  ];

  const listColRight: IItemCol[] =
    typeRoleUsed === IAccountRole.CSL || typeRoleUsed === IAccountRole.ADMIN
      ? [
          {
            label: "Thời gian ứng tuyển",
            value: "createDate",
          },
          {
            label: "Người tạo",
            value: "creatorName",
          },
          {
            label: "CSL quản lý",
            value: "managerName",
          },
          {
            label: "Đánh giá",
            value: "summary",
          },
          {
            label: "Ủy thác",
            value: "commissionFlag",
          },
          {
            label: "Dịch vụ",
            value: "services",
          },
        ]
      : [
          {
            label: "Thời gian ứng tuyển",
            value: "createDate",
          },
          {
            label: "Người tạo",
            value: "creatorName",
          },
          {
            label: "Ủy thác",
            value: "commissionFlag",
          },
          {
            label: "Đánh giá",
            value: "summary",
          },
          {
            label: "Dịch vụ",
            value: "services",
          },
        ];

  const onChangeListCol = (checkedValues: CheckboxValueType[]): void => {
    dispatch(changeListColShowApplication(checkedValues as string[]));
  };

  const filterCol = (
    <Checkbox.Group
      className="group-check-box-list-col"
      value={listColShow}
      onChange={onChangeListCol}
    >
      <Row>
        <Col span={12}>
          {listColLeft.map((itemCheck: IItemCol, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col span={12}>
          {listColRight?.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );

  const content = (
    <div className="filter-content">
      <Row className="flex items-center justify-between mb-4">
        <span className="title-filter">Tất cả bộ lọc</span>
        <AppButton
          classrow="btn-close-popover"
          typebutton="normal"
          onClick={hide}
        >
          <Icon className="" icon="close-circle-line" size={20} />
        </AppButton>
      </Row>
      <Formik
        initialValues={initialValuesFilter}
        onSubmit={handleSearch}
        innerRef={formikFilterAdvancedRef}
      >
        {({values, handleSubmit, resetForm, handleReset}): JSX.Element => {
          const isDisableStatuses =
            !values?.stages || values?.stages?.length === 0;

          const resetData = (): void => {
            window.history.pushState({}, "", router.route);
            formikFilterNormalRef?.current?.resetForm();
            const dataReset = {
              candidateInformation: "",
              requestJobName: "",
              customerName: "",
              statuses: [],
              stages: [],
              creators: [],
              to: "",
              from: "",
              workingLocationIds: [],
              currentPage: 1,
              services: [],
              managerId: null,
            };
            setCurrentPage(1);
            updateValueFilter(dataReset);
            formikFilterAdvancedRef.current?.setValues(dataReset);

            const paramsGroupByClone = {
              ...groupByFieldApplication,
              applicationSearch: {
                ...groupByFieldApplication.applicationSearch,
                ...dataReset,
              },
            };

            dispatch(setGroupByFieldApplication(paramsGroupByClone));
            dispatch(setQueryParamsPages({managerApplication: ""}));

            hide();
          };

          const disabledStatusOnboardAndPass = (id?: string): boolean => {
            const isHasOffer =
              values.stages.findIndex((e) => e.key === "3") > -1;
            if (id && (id === "3" || id === "5") && !isHasOffer) {
              return true;
            }
            return false;
          };
          return (
            <form className="flex flex-col" onSubmit={handleSubmit}>
              <TextInput
                containerclassname="mt-2"
                label="Tên công ty"
                placeholder="Nhập tên công ty"
                name="customerName"
                value={values.customerName}
                free={!values.customerName}
                onChange={handleSearchDebounce}
              />

              <SelectInput
                containerclassname="mt-2"
                name="workingLocationIds"
                mode="multiple"
                labelselect="Địa điểm làm việc"
                data={sortWorkLocation(listLocation || [], "key")}
                free={values.workingLocationIds.length === 0}
                value={values.workingLocationIds || []}
                allowClear
                handleChange={handleSearchDebounce}
              />
              <SelectInput
                containerclassname="mt-2"
                name="stages"
                mode="multiple"
                labelselect="Chọn giai đoạn"
                data={stagesApplication}
                value={values.stages}
                allowClear
                handleChange={handleSearchDebounce}
              />
              <SelectInput
                containerclassname="mt-2"
                name="statuses"
                mode="multiple"
                labelselect="Chọn trạng thái"
                data={
                  statusesApplication?.map((i: OptionSelect) => ({
                    ...i,
                    disabled: disabledStatusOnboardAndPass(i.id as string),
                  })) || []
                }
                disabled={isDisableStatuses}
                value={isDisableStatuses ? [] : values.statuses}
                free={isDisableStatuses || values.statuses.length === 0}
                allowClear
                handleChange={handleSearchDebounce}
              />
              <SelectInput
                containerclassname="mt-2"
                name="creators"
                mode="multiple"
                labelselect="Người tạo"
                data={listCreator || []}
                free={values.creators?.length === 0}
                value={values.creators || []}
                allowClear
                handleChange={handleSearchDebounce}
              />

              {!isCTV && (
                <SelectInput
                  containerclassname="mt-2"
                  name="managerId"
                  labelselect="Người quản lý"
                  data={optionDataManager || []}
                  free={!values.managerId}
                  value={values.managerId}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  handleChange={handleSearchDebounce}
                />
              )}

              <SelectInput
                containerclassname="mt-2"
                name="services"
                mode="multiple"
                labelselect="Dịch vụ"
                data={optionServiceRequest}
                free={values.services?.length === 0}
                value={values.services || []}
                handleChange={handleSearchDebounce}
              />
              <Row className="mt-2 div-time">
                <AppDatePicker
                  name="from"
                  label="Từ"
                  valueAppDatePicker={values.from || ""}
                  format={DATE_FORMAT}
                  free={!values.from}
                  disabledDate={disableDate}
                  status={!validateTime(values) ? "error" : ""}
                  allowClear
                  onChange={handleSearchDebounce}
                />
                <AppDatePicker
                  name="to"
                  label="Đến"
                  valueAppDatePicker={values.to || ""}
                  format={DATE_FORMAT}
                  free={!values.to}
                  disabledDate={disableDate}
                  status={!validateTime(values) ? "error" : ""}
                  allowClear
                  onChange={handleSearchDebounce}
                />
              </Row>
              <Row className="mt-6 div-time">
                <AppButton
                  label="Xoá tất cả"
                  typebutton="secondary"
                  onClick={resetData}
                />
                {/* <AppButton
                  label="Tìm kiếm"
                  typebutton="primary"
                  onClick={handleSubmit}
                /> */}
              </Row>
            </form>
          );
        }}
      </Formik>
    </div>
  );

  const groupByOption: OptionSelect[] = [
    {
      label: "Request Job",
      value: String(GroupByApplication.RequestJob),
    },
    {
      label: "Company",
      value: String(GroupByApplication.Customer),
    },
    {
      label: "Status",
      value: String(GroupByApplication.Stage),
    },
    {
      label: "Created By",
      value: String(GroupByApplication.CreatedBy),
    },
    // {
    //   label: "",
    //   value: "",
    // },
  ];

  const handleSearchDebounce = useCallback(
    _.debounce(() => {
      handleSearch();
    }, deadTimeFastSearch),
    [handleSearch]
  );

  return (
    <Formik
      initialValues={initialValuesFilter}
      onSubmit={handleSearch}
      innerRef={formikFilterNormalRef}
    >
      {({values, handleSubmit}): JSX.Element => {
        return (
          <form className="flex flex-col" onSubmit={handleSubmit}>
            <Row>
              <TextInput
                containerclassname="w-1/5 pr-2"
                label="Nhập họ tên, sđt, email UV"
                name="candidateInformation"
                value={values?.candidateInformation}
                onChange={handleSearchDebounce}
              />
              <TextInput
                containerclassname="w-1/5 pr-2"
                label="Vị trí ứng tuyển"
                placeholder="Nhập vị trí ứng tuyển"
                name="requestJobName"
                value={values?.requestJobName}
                onChange={handleSearchDebounce}
              />
              {values.roleFilterSelected?.value ? (
                <SelectInput
                  containerclassname="w-1/5"
                  name="roleFilterSelected"
                  labelselect="Phân loại"
                  data={optionsRoleFilter}
                  free={!values?.roleFilterSelected?.value}
                  value={values.roleFilterSelected || []}
                />
              ) : (
                <div className="w-1/5" />
              )}
              <Row className="filter-container w-2/5 justify-end">
                {!isCTV && (
                  <AppGroupBy
                    options={groupByOption}
                    className="mr-1 w-2/5"
                    value={groupByFieldApplication.groupByRequest.groupBy}
                    onChange={(value) => {
                      const paramsGroupByClone = {
                        ...groupByFieldApplication,
                        groupByRequest: {
                          ...groupByFieldApplication.groupByRequest,
                          groupBy: value,
                          pageNumber: 1,
                        },
                      };
                      dispatch(setGroupByFieldApplication(paramsGroupByClone));
                    }}
                  />
                )}
                <Popover
                  placement="bottom"
                  trigger="click"
                  content={content}
                  open={open}
                  onOpenChange={handleOpenChange}
                >
                  <AppButton typebutton="normal">
                    <Icon className="mr-1" icon="filter-line" size={12} />
                    Tìm kiếm nâng cao
                  </AppButton>
                </Popover>

                {!isCTV && (
                  <Popover
                    placement="bottom"
                    trigger="click"
                    content={filterCol}
                  >
                    <AppButton typebutton="normal" classrow="btn-filter ml-2">
                      <Icon
                        className="mr-1"
                        icon="eye"
                        size={16}
                        color="#324054"
                      />
                      Hiển thị {listColShow.length + 4}/
                      {typeRoleUsed === IAccountRole.CSL ||
                      typeRoleUsed === IAccountRole.ADMIN
                        ? columnsAll?.length
                        : (columnsAll?.length || 1) - 1}
                    </AppButton>
                  </Popover>
                )}
              </Row>
            </Row>
          </form>
        );
      }}
    </Formik>
  );
}

export default FilterData;
