.container-manager-collaborator {
  .row-table-ctv {
    td {
      color: $text-color-input;
      font-size: 0.75rem;
    }
  }

  .ant-table-thead .ant-table-cell {
    color: $text-color-input;
    font-size: 1rem;
  }

  .ant-table-tbody > tr.ant-table-row-selected:hover > td {
    background: $white-color;
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background: $white-color;
  }

  .ant-checkbox-indeterminate .ant-checkbox-inner::after {
    background-color: $white-color;
  }

  .ant-table-selection-column {
    border-right: 0px !important;
  }

  .status-collaborator {
    color: $white-color;
    padding: 4px 10px;
    align-self: center;
    border-radius: 8px;
  }
}

.modal-update-collaborator {
  .ant-modal-title {
    display: flex;
    justify-content: center;
  }

  .content-modal {
    font-size: 16px;
    color: $text-color-input;
    display: flex;
    justify-content: center;
    align-items: baseline;
  }

  .ant-select-selector {
    min-width: 160px;
    border-radius: 10px !important;
    overflow: hidden;
  }

  .btn-cancel,
  .btn-confirm {
    width: 150px;
  }

  .ant-select-selection-item {
    color: $text-color-input;
  }
}
