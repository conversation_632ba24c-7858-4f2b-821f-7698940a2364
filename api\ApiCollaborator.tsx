import {Moment} from "moment";
// eslint-disable-next-line import/no-cycle
import {IManagerByConsultantList} from "./ApiCandidate";
// eslint-disable-next-line import/no-cycle
import {fetcher} from "./Fetcher";
import {OptionSelect} from "@app/types";

export interface IParamsCollaborator {
  textSearch?: string;
  name?: string;
  phoneNumber?: string;
  from?: string | Moment; // DD/MM/YYYY
  to?: string | Moment; // DD/MM/YYYY
  email?: string;
  managerSelected?: OptionSelect[];
  statusSelected?: OptionSelect[];
  isBackFromChildrenView?: boolean;
  isFirstInitialization?: boolean;
  managers?: string[];
  statuses?: number[];
  isAdvanceSearch?: boolean;
  pageSize?: number;
  currentPage?: number;
  activeDate?: OptionSelect;
}

export interface IPartnerPaging {
  userId: number;
  name?: string;
  email?: string;
  phoneNumber?: string;
  consultantId?: number;
  consultantName?: string;
  recomenndationPerson?: string | null;
  status?: string;
  statusId?: number;
  createdDate?: string;
  countCandidate?: number;
  countInterview?: number;
  countOffer?: number;
  bonus?: number;
  gender: 0 | 1 | null;
  note?: string;
  recomenndationPersonName?: string;
}

export interface IManagerFilters {
  roleUsers?: string;
  userId?: number;
  name?: string;
  email?: string;
  image?: string;
  role?: string;
  roleId?: number;
  userGroupId?: number;
}

export interface IListCollaborator {
  isAdvanceSearch: true;
  isFirstInitialization: false;
  userId: number;
  role: string;
  textSearch: string;
  name: string;
  phoneNumber: string;
  email: string;
  from: string;
  to: string;
  totalPages: number;
  currentPage: number;
  totalCount: number;
  pageSize: number;
  hasPrevious: false;
  hasNext: true;
  statuses: number[];
  managers: number[];
  statusFilters: {statusId: number; name: string}[];
  partnerPaging: IPartnerPaging[];
  managerFilters: IManagerFilters[];
}

export interface IParamsItemChangePartnerStatuses {
  partnerId: number;
  newPartnerStatus: number;
}

export interface IParamsItemChangePartnerManager {
  partnerId: number;
  consultantId: number;
}

export interface IPayments {
  amount: number;
  amountPaid: number;
  monthWarranty: number;
  paymentDateExpected: string;
  paymentDateExpectedString: string;
  status: number;
  statusString: number;
  application: {
    applicationId: number;
    applicationName: number;
    candidate: {
      candidateId: number;
      name: string;
    };
    requestJob: {
      name: string;
      requestJobId: string;
      position: {
        name: string;
        positionId: string;
      };
    };
  };
}

export interface ICollaborator {
  addressCode: string;
  addressDetail: string;
  addressName: string;
  bank_AccountNumber?: string;
  bank_Branch?: string;
  bank_Name?: string;
  careerTypeId: number;
  consultantId: number;
  consultantName: string;
  countAmountPayment: number;
  countApplication: number;
  countCandidate: number;
  createdDate: string;
  dateOfBirth: string;
  email: string;
  facebook: string;
  gender: boolean;
  image: string;
  issuedAt: string;
  issuedOn: string;
  job: string;
  linkein: string;
  name: string;
  note: string;
  payments: IPayments[];
  phoneNumber: string;
  profileId: number;
  recommendationCode: string;
  recommendationEmail: string;
  recommendationLink: string;
  recommendationUserId: string;
  recruitmentTypeId: number;
  skype: string;
  statusId: number;
  statusName: string;
  userId: number;
  startWorkDate: string;
  taxCode: number;
  userIdHash: string;
  identifyCardNo: string;
  recommendationPerson?: string;
  notes: {
    content: string;
    createdBy: number;
    creatorName: string;
    createdDate: string;
  }[];
  tags: string[];
  yoe: number;
  timeForReco: number;
  workLocations: string[];
  positions: string[];
  levels: string[];
  languages: string[];
}

export interface IRecruitmentTypeList {
  recruitmentTypeId: number;
  description: string;
  name: string;
}

export interface ICareerTypeList {
  careerTypeId: number;
  name: string;
  description: string;
}

export interface IParamEditCollaborator {
  addressCode: string;
  addressDetail: string;
  addressName: string;
  careerTypeId: number;
  consultantId: number;
  consultantName: string;
  createdDate: string;
  dateOfBirth: string;
  email: string;
  facebook: string;
  gender: boolean;
  image: string;
  job: string;
  linkein: string;
  name: string;
  note: string;
  phoneNumber: string;
  profileId: number;
  recommendationUserId: number;
  recruitmentTypeId: number;
  skype: string;
  statusId: number;
  statusName: string;
  userId?: number;
}

export interface IParamCheckIsEmail {
  email: string;
  userId?: number;
}

const paths = {
  list: "/api/profile/filter",
  export: "/api/profile/export",
  saveChangePartnerStatuses: "/api/myPage/saveChangePartnerStatuses",
  saveChangePartnerManager: "/api/myPage/saveChangePartnerManager",
  detail: "/api/profile/getPartnerProfile?userId=",
  getAllCareerTypeList: "api/profile/getAllCareerTypeList",
  getAllRecruitmentTypeList: "/api/profile/getAllRecruitmentTypeList",
  editCollaborator: "/api/profile/edit",
  isEmailExisted: "api/user/isEmailExisted",
  getManagerByConsultantLeaderId: "api/user/getManagerByConsultantLeaderId",
  addNote: "api/profile/addNote",
  getAllTagsOfCollaborator: "api/profile/getTags",
};

function getListCollaborator(
  body?: IParamsCollaborator
): Promise<IListCollaborator> {
  return fetcher({url: paths.list, method: "post", data: body});
}

function exportListCollaborator(body?: IParamsCollaborator): Promise<any> {
  return fetcher({
    url: paths.export,
    method: "post",
    data: body,
    responseType: "blob",
    headers: {
      "Content-type": "application/json",
      "Accept":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    },
    timeout: 100000,
  });
}

function saveChangePartnerStatuses(
  body?: IParamsItemChangePartnerStatuses[]
): Promise<any> {
  return fetcher({
    url: paths.saveChangePartnerStatuses,
    method: "post",
    data: body,
  });
}

function saveChangePartnerManager(
  body?: IParamsItemChangePartnerManager[]
): Promise<any> {
  return fetcher({
    url: paths.saveChangePartnerManager,
    method: "post",
    data: body,
  });
}

function getDetailCollaborator(id?: string): Promise<ICollaborator> {
  return fetcher({url: paths.detail + id, method: "get"});
}

function getAllCareerTypeList(): Promise<ICareerTypeList[]> {
  return fetcher({url: paths.getAllCareerTypeList, method: "get"});
}

function getAllRecruitmentTypeList(): Promise<IRecruitmentTypeList[]> {
  return fetcher({url: paths.getAllRecruitmentTypeList, method: "get"});
}

function editCollaborator(body: IParamEditCollaborator): Promise<any> {
  return fetcher({url: paths.editCollaborator, method: "post", data: body});
}

function checkEmailExisted(body: IParamCheckIsEmail): Promise<any> {
  return fetcher({
    url: `${paths.isEmailExisted}?email=${body.email}&userId=${body.userId}`,
    method: "get",
  });
}

function getManagerByConsultantLeaderId(): Promise<IManagerByConsultantList[]> {
  return fetcher({
    url: paths.getManagerByConsultantLeaderId,
    method: "get",
  });
}

function addNote(body: {userId: number; content: string}): Promise<any> {
  return fetcher({url: paths.addNote, method: "post", data: body});
}

function getAllTagsOfCollaborator(): Promise<string[]> {
  return fetcher({url: paths.getAllTagsOfCollaborator, method: "get"});
}

export default {
  getListCollaborator,
  exportListCollaborator,
  saveChangePartnerStatuses,
  saveChangePartnerManager,
  getDetailCollaborator,
  getAllCareerTypeList,
  getAllRecruitmentTypeList,
  editCollaborator,
  checkEmailExisted,
  getManagerByConsultantLeaderId,
  addNote,
  paths,
  getAllTagsOfCollaborator,
};
