import AppCkEditor from "@app/components/AppCkEditor";
import React from "react";

interface ValueRequestEditor {
  description: string;
  requestDetail: string;
  benefit: string;
}

interface Props {
  valueRequestEditor: ValueRequestEditor;
  handleChangeDescription: (value: string) => void;
  handleChangeRequestDetail: (value: string) => void;
  handleChangeBenefit: (value: string) => void;
  touchedField?: {
    description: boolean;
    requestDetail: boolean;
  };
}

function RequestEditor(props: Props): JSX.Element {
  const {
    valueRequestEditor,
    handleChangeDescription,
    handleChangeRequestDetail,
    handleChangeBenefit,
    touchedField,
  } = props;
  return (
    <div>
      <AppCkEditor
        label="Mô tả công việc"
        containerclassname="mt-2"
        required
        value={valueRequestEditor.description}
        handleChange={handleChangeDescription}
        touched={touchedField?.description}
      />
      <AppCkEditor
        label="<PERSON><PERSON><PERSON> cầu công việc"
        containerclassname="mt-4"
        required
        value={valueRequestEditor.requestDetail}
        handleChange={handleChangeRequestDetail}
        touched={touchedField?.requestDetail}
      />
      <AppCkEditor
        label="Chính sách phúc lợi"
        containerclassname="mt-4"
        value={valueRequestEditor.benefit}
        handleChange={handleChangeBenefit}
      />
    </div>
  );
}

export default React.memo(RequestEditor);
