/**
Config here
 */
$sidebar-width: 270px;
$sidebar-background: #ffffff;
$navbar-height: 50px;
$transition-layout: all 0.3s;
$transition-layout-time: 0.3s;
$sidebar-boxshadow-background: 0px 4px 2px -2px rgba(208, 216, 243, 0.6);
$navbar-background: rgba(193, 0, 0, 0.8);
$navbar-boxshadow-background: 2px 2px 5px 0 #f0f1f2;

// End config

$font-family: "BeVietnamPro-Black", cursive !default;

@font-face {
  font-family: "BeVietnamPro-Black";
  src: local("BeVietnamPro-Black"),
    url(/fonts/BeVietnamPro-Regular.ttf) format("truetype");
}

$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-bold: 600 !default;
$font-size-base: 0.875rem !default;
$font-size-lg: 1rem !default;
$font-size-sm: 0.75rem !default;
$headings-font-weight: 400 !default;

$spacer: 1rem !default;

$spacers: (
  0: 0,
  1: (
    $spacer * 0.25,
  ),
  2: (
    $spacer * 0.5,
  ),
  3: $spacer,
  4: (
    $spacer * 1.5,
  ),
  5: (
    $spacer * 3,
  ),
  6: (
    $spacer * 4.5,
  ),
  7: (
    $spacer * 6,
  ),
) !default;

// Grid breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1440px,
) !default;

$grid-breakpoints-xs: 0 !default;
$grid-breakpoints-sm: 576px !default;
$grid-breakpoints-md: 768px !default;
$grid-breakpoints-lg: 992px !default;
$grid-breakpoints-xl: 1200px !default;
$grid-breakpoints-xxl: 1400px !default;
$grid-breakpoints-xxxl: 1600px !default;

// Grid containers
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1200px,
  xxl: 1440px,
  xxxl: 1600px,
) !default;

// Social colors
$social-colors: (
  "facebook": #3b5998,
  "twitter": #1da1f2,
  "google": #dc4e41,
  "youtube": #f00,
  "vimeo": #1ab7ea,
  "dribbble": #ea4c89,
  "github": #181717,
  "instagram": #e4405f,
  "pinterest": #bd081c,
  "flickr": #0063dc,
  "bitbucket": #0052cc,
) !default;

//color
$border-color: #dee2e6 !default;
$text-color: #000000;
$white-color: #ffffff;
$red-color: #cb2131;
$blue-color: #0092ff;
$primary-color: #2f6bff;
$secondary-color: rgba(47, 107, 255, 0.2);
$header_tf: #9d9d9d;
$header_tf05: rgba(157, 157, 157, 0.5);
$header_tf02: rgba(157, 157, 157, 0.2);
$header_tf01: rgba(157, 157, 157, 0.1);
$text-color-input: #324054;
$select_color: rgba(255, 0, 0, 0.3);
$header_tf02: rgba(157, 157, 157, 0.2);
$green_color: #329932;
$border-color: #e6e6e6;
$status-reject: #dc2323;
$link-color: #dc2323;
$black-500: #333333;
$color-button-popup: rgba(0, 0, 0, 0.85);
$shadow: rgba(0, 0, 0, 0.25);
$status-external: rgba(255, 0, 0, 0.3);
$status-pending: #f4b41a;
$urgent-label: #c10000;
$pending-label: #667eea;
$tab-inactive: rgba(50, 64, 84, 0.8);
$black-color: #000000;
$primary-color01: rgba(47, 107, 255, 0.1);
$scrollbar-color: #c7c7c7;
$header-landing-page: 64px;

::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 2px grey;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: $scrollbar-color;
  border-radius: 8px;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  color: $text-color-input;
}
