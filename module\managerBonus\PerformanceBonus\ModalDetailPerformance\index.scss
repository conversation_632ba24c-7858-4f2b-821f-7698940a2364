.detail-performance-ui {
  &__information {
    border: 1px dashed $header_tf;
    border-radius: 8px;
    padding: 12px;
    margin-right: 8px;
    height: 68vh;
  }

  &__history {
    border: 1px dashed $header_tf;
    border-radius: 8px;
    height: 68vh;
  }

  .payment-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 4px;
  }

  &__history-list {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 4px 16px 12px 16px;
  }

  &__history-card {
    padding: 12px;
    border: 1px dashed $header_tf;
    border-radius: 8px;
    margin-top: 8px;
    margin-right: 4px;
  }

  &__history-card-btn {
    padding: 4px 20px;
    border: 1px solid $status-reject;
    border-radius: 12px;
    color: $status-reject;
    margin-top: 8px;
  }

  .ant-picker {
    border-radius: 8px;
    width: 90px;
  }

  &__history-money {
    background-color: rgba(47, 107, 255, 0.1);
    padding: 4px 12px;
  }

  .ant-form-item {
    margin-bottom: 0;
  }
}
