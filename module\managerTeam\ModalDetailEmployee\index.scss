.modal-employee-detail-container {
  top: 50px;

  .container-item-detail-modal {
    border: 1px dashed $header_tf;
    border-radius: 16px;
    overflow: hidden;
  }

  .avatar {
    background-color: $primary-color;
  }

  .dot {
    height: 12px;
    width: 12px;
    border-radius: 12px;
  }

  .line {
    height: 0.5px;
    width: 70%;
    background-color: $header_tf05;
    margin: 16px 15%;
  }

  .ant-table-cell {
    font-size: 0.75rem !important;
  }

  .custom-ant-tab-active {
    margin-top: 8px;
    .ant-tabs {
      .ant-tabs-nav {
        margin: 0;

        .ant-tabs-tab-active {
          background: $green_color;
          color: $white-color;
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
          .ant-tabs-tab-btn {
            color: $white-color;
            font-size: 0.875rem !important;
          }
        }

        .ant-tabs-tab {
          border-start-start-radius: 12px;
          border-start-end-radius: 12px;
          .ant-tabs-tab-btn {
            font-size: 0.875rem !important;
          }
        }
      }
    }

    .ant-tabs-tab:hover {
      color: $text-color-input;
    }
  }

  .btn-delete-employee {
    justify-content: center;

    button {
      border: none;
      background-color: none;
    }

    button:hover,
    button:focus {
      background: none;
      border: none;
    }

    span {
      text-decoration: underline;
      color: $status-reject;
    }
  }

  .status-cv {
    font-size: 12px;
    color: $white-color;
    padding: 4px 8px;
    background-color: $primary-color;
    border-radius: 8px;
    font-weight: 400;
  }
}
