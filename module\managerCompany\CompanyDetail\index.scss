.company-information-detail {
  height: 80vh;
  overflow: auto;
  &__info {
    display: flex;
    justify-content: space-between;
    width: 100%;
    &-left,
    &-right {
      width: 49%;
      border: 2px dotted $border-color;
      padding: 12px;
      border-radius: 5px;
    }
    &-left {
      max-height: 190px;
      overflow: auto;
      .skills-tag {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        &-item {
          padding: 1px 7px;
          width: max-content;
          border-radius: 12px;
          background-color: $primary-color;
          color: $white-color;
        }
      }
    }
  }

  .job-table {
    .ant-table-tbody .ant-table-cell {
      padding: 8px 6px !important;
    }
  }
}
