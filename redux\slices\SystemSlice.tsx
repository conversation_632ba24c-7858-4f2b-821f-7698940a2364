import {createSlice} from "@reduxjs/toolkit";

interface SystemState {
  isLoading: boolean;
}

const initialState: SystemState = {
  isLoading: false,
};

export const systemSlice = createSlice({
  name: "system",
  initialState: initialState,
  reducers: {
    setLoading: (state, actions) => {
      state.isLoading = actions.payload;
    },
  },
});

export const {setLoading} = systemSlice.actions;

export default systemSlice.reducer;
