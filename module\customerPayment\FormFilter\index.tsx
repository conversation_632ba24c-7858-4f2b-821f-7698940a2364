import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {TextInput} from "@app/components/TextInput";
import {Checkbox, Col, Popover, Row} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import {SelectInput} from "@app/components/SelectInput";
import {deadTimeFastSearch, paymentStatus} from "@app/utils/constants/state";
// eslint-disable-next-line import/no-cycle
import {IFormFilter} from "..";
import AppCheckBox from "@app/components/AppCheckbox";
import {useDispatch, useSelector} from "react-redux";
import {
  changeColCustomerPayment,
  initListColShowCustomerPayment,
  selectUser,
} from "@app/redux/slices/UserSlice";
import {CheckboxValueType} from "antd/lib/checkbox/Group";
import {IAccountRole, OptionSelect} from "@app/types";

interface FormFilterProps {
  setValuesSearch: (value: IFormFilter) => void;
  valuesSearch: IFormFilter;
  sumCol: number;
  listFilter: {
    creatorFilters: OptionSelect[];
    positionFilters: OptionSelect[];
  };
}

interface IItemCol {
  label: string;
  value: string;
}

export default function FormFilter(props: FormFilterProps): JSX.Element {
  const {setValuesSearch, valuesSearch, sumCol, listFilter} = props;
  const [isShowFilterAdvance, setIsShowFilterAdvance] =
    useState<boolean>(false);
  const timeOut = useRef<any>();
  const filterAdvancedRef = useRef<FormikProps<IFormFilter>>(null);
  const dispatch = useDispatch();
  const {listColShowCustomerPayment, user} = useSelector(selectUser);
  const isAML =
    user?.role?.includes(IAccountRole.AML) ||
    user?.role?.includes(IAccountRole.ADMIN);
  const isBD = [IAccountRole.BD, IAccountRole.BDL].some((item) =>
    user?.role?.includes(item)
  );
  // const isFirstInitialization =
  //   (isAML && listFilter.creatorFilters.length === 0) ||
  //   listFilter.positionFilters.length === 0;

  // Để tạm sau BE sửa thì dùng bên trên
  const isFirstInitialization = true;

  const listColLeft: IItemCol[] = [
    {
      label: "Tên ứng viên",
      value: "candidateName",
    },
    {
      label: "Vị trí",
      value: "positionName",
    },
    {
      label: "Doanh thu",
      value: "salesTransacted",
    },
    {
      label: "VAT",
      value: "vat",
    },
  ];

  const listColRight: IItemCol[] = [
    {
      label: "Thành tiền",
      value: "amount",
    },
    {
      label: "Đã thanh toán",
      value: "amountPaid",
    },
    {
      label: "Trạng thái",
      value: "statusName",
    },
    {
      label: "Ghi chú",
      value: "note",
    },
  ];

  const listColShow =
    listColShowCustomerPayment || initListColShowCustomerPayment;

  useEffect(() => {
    return () => {
      if (timeOut.current) {
        clearTimeout(timeOut.current);
      }
    };
  }, []);

  const handleSearch = () => {
    const values = filterAdvancedRef.current?.values;
    const searchParams = {
      currentPage: 1,
      pageSize: valuesSearch.pageSize,
      isFirstInitialization,
      requestJobName: values?.requestJobName,
      candidateName: values?.candidateName,
      positions:
        values?.positionsSelected?.map((item) => ({
          id: item.key || "",
          label: item.label,
        })) || [],
      statuses:
        values?.statusSelected?.map((item) => ({
          id: item.key || "",
          label: item.label,
        })) || [],
      customerName: values?.customerName?.trim() || "",
      creators:
        values?.creatorFilterSelected?.map((item) => ({
          id: item.key || "",
          label: item.label,
        })) || [],
    };
    setValuesSearch(searchParams);
  };

  const onChangeCustomerName = (): void => {
    timeOut.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const onChangeCreator = (): void => {
    timeOut.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const resetData = (): void => {
    filterAdvancedRef.current?.resetForm();
    setValuesSearch({
      currentPage: 1,
      pageSize: valuesSearch.pageSize,
      isFirstInitialization,
    });
  };

  const onClickSearch = (): void => {
    handleSearch();
  };

  const onChangeListCol = (checkedValues: CheckboxValueType[]): void => {
    dispatch(changeColCustomerPayment(checkedValues as string[]));
  };

  const filterCol = (
    <Checkbox.Group
      className="group-check-box-list-col"
      value={listColShow}
      onChange={onChangeListCol}
    >
      <Row>
        <Col span={12}>
          {listColLeft.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col span={12}>
          {listColRight.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );

  function contentFilterAdvance(values: IFormFilter): JSX.Element {
    return (
      <div className="content-filter-advance">
        <Row className="flex items-center justify-between mb-4">
          <span className="title-filter">Tất cả bộ lọc</span>
          <AppButton
            classrow="btn-close-popover"
            typebutton="normal"
            onClick={(): void => setIsShowFilterAdvance(false)}
          >
            <Icon icon="close-circle-line" size={20} />
          </AppButton>
        </Row>
        <TextInput
          containerclassname="mt-2"
          label="Tên request"
          name="requestJobName"
          value={values.requestJobName}
          free={!values.requestJobName}
        />
        <TextInput
          containerclassname="mt-2"
          label="Tên ứng viên"
          name="candidateName"
          value={values.candidateName}
          free={!values.candidateName}
        />
        <SelectInput
          containerclassname="mt-2"
          name="positionsSelected"
          labelselect="Vị trí"
          data={listFilter.positionFilters}
          value={values.positionsSelected}
          free={values?.positionsSelected?.length === 0}
          allowClear
          mode="multiple"
        />
        <SelectInput
          containerclassname="mt-2"
          name="statusSelected"
          labelselect="Trạng thái"
          data={paymentStatus}
          value={values.statusSelected}
          free={values?.statusSelected?.length === 0}
          allowClear
          mode="multiple"
        />
        <Row className="mt-6 div-time">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={resetData}
          />
          <AppButton
            label="Tìm kiếm"
            typebutton="primary"
            onClick={onClickSearch}
          />
        </Row>
      </div>
    );
  }

  return (
    <Formik
      initialValues={{} as IFormFilter}
      innerRef={filterAdvancedRef}
      onSubmit={() => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <Row className="items-center filter-container-payment">
            <Row className="w-1/2">
              {(isAML || isBD) && (
                <SelectInput
                  containerclassname="w-1/2 pr-2"
                  name="creatorFilterSelected"
                  mode="multiple"
                  labelselect="AM quản lý"
                  data={listFilter.creatorFilters}
                  value={values.creatorFilterSelected}
                  free={values?.creatorFilterSelected?.length === 0}
                  allowClear
                  handleChange={onChangeCreator}
                />
              )}
              <TextInput
                containerclassname="w-1/2"
                label="Tên khách hàng"
                name="customerName"
                value={values.customerName}
                onChange={onChangeCustomerName}
                placeholder="Nhập tên khách hàng"
              />
            </Row>
            <Row className="w-1/2 justify-end pl-2">
              <Popover
                className="mr-1"
                placement="bottom"
                trigger="click"
                content={(): React.ReactNode => contentFilterAdvance(values)}
                open={isShowFilterAdvance}
                onOpenChange={setIsShowFilterAdvance}
              >
                <AppButton typebutton="normal" classrow="btn-filter">
                  <Icon
                    className="mr-1"
                    icon="filter-line"
                    size={12}
                    color="#324054"
                  />
                  Tìm kiếm nâng cao
                </AppButton>
              </Popover>
              <Popover placement="bottom" trigger="click" content={filterCol}>
                <AppButton typebutton="normal" classrow="btn-filter">
                  <Icon className="mr-1" icon="eye" size={16} color="#324054" />
                  Hiển thị {listColShow.length + 3}/{sumCol}
                </AppButton>
              </Popover>
            </Row>
          </Row>
        );
      }}
    </Formik>
  );
}
