import React from "react";
import "./index.scss";
import {Avatar, Image} from "antd";

function HomeContact(): JSX.Element {
  const contacts = [
    {
      label: "Hỗ trợ Cộng tác viên tuyển dụng",
      nameUserContact: "<PERSON> <PERSON>",
      avatar: "/img/partner-support.png",
      links: [
        {
          icon: "/img/zalo.png",
          link: "https://zalo.me/0987890003",
          alt: "Zalo",
        },
        {
          icon: "/img/skype.png",
          link: "skype:live:.cid.3960ba81367f9cbc?chat",
          alt: "Skype",
        },
        {
          icon: "/img/telegram.svg",
          link: "https://t.me/ctvreco",
          alt: "Telegram",
        },
        {
          icon: "/img/facebook.svg",
          link: "https://www.messenger.com/t/1740823249570831",
          alt: "Facebook",
        },
      ],
    },
    {
      label: "Hỗ trợ Ứng viên",
      nameUserContact: "Ms Nhung",
      avatar: "/img/candidate-support.jpg",
      links: [
        {
          icon: "/img/zalo.png",
          link: "https://zalo.me/0941903536",
          alt: "Zalo",
        },
        {
          icon: "/img/skype.png",
          link: "skype:nhung.dong_3?chat",
          alt: "Skype",
        },
        // {
        //   icon: "/img/telegram.svg",
        //   link: "https://t.me/Hanh_Ngo",
        //   alt: "Telegram",
        // },
        {
          icon: "/img/facebook.svg",
          link: "https://www.facebook.com/dongtuyetnhungg",
          alt: "Facebook",
        },
      ],
    },
  ];
  return (
    <div className="home-contact">
      {contacts.map((item, index) => (
        <div key={index}>
          <h1 className="mt-3 font-bold">{item.label}</h1>
          <div className="flex justify-between home-contact__container mt-2">
            <div className="home-contact__user flex items-center">
              <Avatar size={36} src={item.avatar} alt={item.nameUserContact} />
              <p className="ml-2 font-bold">{item.nameUserContact}</p>
            </div>
            <div className="home-contact__links">
              {item.links.map((link, idx) => (
                <a
                  href={link.link}
                  target="_blank"
                  key={idx}
                  rel="noreferrer"
                  className="ml-2"
                >
                  <Image
                    width={30}
                    height={30}
                    src={link.icon}
                    preview={false}
                    alt={link.alt}
                  />
                </a>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default React.memo(HomeContact);
