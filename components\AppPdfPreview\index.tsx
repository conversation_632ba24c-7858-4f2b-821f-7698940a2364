import React, {useEffect, useState} from "react";
import {Document, Page, pdfjs} from "react-pdf";
import AppLoading from "../AppLoading";
import "./index.scss";
import {useRouter} from "next/router";
import config from "@app/config";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

interface IAppPdfPreviewProp {
  url?: string;
  initScale?: number;
  classNameContainer?: string;
}
export default function AppPdfPreview(props: IAppPdfPreviewProp): JSX.Element {
  const {url, initScale, classNameContainer} = props;
  const routerNext = useRouter();
  const [numPages, setNumPages] = useState(null);
  const [scale, setScale] = useState(initScale || 1);
  const onDocumentLoadSuccess = ({numPages}: any): any => {
    setNumPages(numPages);
  };
  const pdfElement = document.querySelector(".pdf-preview-container");

  const textError = () => (
    <h3
      className={`text-base text-center ${
        routerNext.pathname.includes(config.PATHNAME.PREVIEW_CV)
          ? "mt-[10%] text-white"
          : ""
      }`}
    >
      An error occurred.
    </h3>
  );

  const handleZoomContent = (event: any) => {
    if (event.ctrlKey) {
      event.preventDefault();
      const newScale = scale - event.deltaY * 0.001;
      if (newScale >= 0.1 && newScale <= 2) {
        setScale(newScale);
      }
    }
  };

  useEffect(() => {
    pdfElement?.addEventListener("wheel", handleZoomContent, {passive: false});
    return () => {
      pdfElement?.removeEventListener("wheel", handleZoomContent);
    };
  }, [pdfElement, scale]);

  return (
    <div className={`pdf-preview-container ${classNameContainer || ""}`}>
      <Document
        file={url}
        loading={<AppLoading />}
        onContextMenu={(e): void => e.preventDefault()}
        onLoadSuccess={onDocumentLoadSuccess}
        error={textError()}
      >
        {Array.from(new Array(numPages), (_, index) => (
          <Page
            key={`page_${index + 1}`}
            pageNumber={index + 1}
            renderTextLayer={false}
            renderAnnotationLayer={false}
            loading={<AppLoading />}
            scale={scale}
          />
        ))}
      </Document>
    </div>
  );
}
