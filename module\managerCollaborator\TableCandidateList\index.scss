.container-candidate-list {
  .status-candidate {
    color: $white-color;
    border-radius: 8px;
    padding: 2px 10px;
  }

  .list-skill {
    cursor: default;

    .ant-select-selector {
      background: $white-color !important;
      cursor: default;
    }

    .ant-select-selection-overflow {
      cursor: default;
    }

    .ant-select-disabled.ant-select:not(.ant-select-customize-input)
      .ant-select-selector {
      cursor: default;
    }

    .ant-select-multiple.ant-select-disabled.ant-select:not(
        .ant-select-customize-input
      )
      .ant-select-selector {
      cursor: default;
    }

    .ant-select-disabled.ant-select:not(.ant-select-customize-input)
      .ant-select-selector {
      cursor: default;
    }

    .ant-select-selection-overflow-item {
      cursor: default;
    }
  }
  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector
    input {
    cursor: default;
  }
  .ant-select-disabled.ant-select-multiple .ant-select-selection-item {
    border: none;
    background-color: $primary-color;
    color: $white-color;
    border-radius: 8px;
  }
  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border: none;
  }
  .ant-select-disabled.ant-select-multiple .ant-select-selection-item {
    cursor: default;
  }
}
