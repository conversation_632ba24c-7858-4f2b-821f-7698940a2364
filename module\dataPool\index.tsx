import React, {useEffect, useMemo, useRef, useState} from "react";
import {FormikProps} from "formik/dist/types";
import {useMutation, useQuery} from "react-query";
import ApiCandidate, {
  FileUpload,
  IDataCvCandidate,
} from "@app/api/ApiCandidate";
import AppPagination from "@app/components/AppPagination";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {OptionSelect} from "@app/types";
import ApiUploadCv from "@app/api/ApiUploadCv";
import ApiStaff, {
  IDataStaff,
  IParamsStaff,
  IStaffObject,
} from "@app/api/ApiStaff";
import InformationStaffForm from "./InformationStaffForm";
import {useRouter} from "next/router";
import {setQueryUrl} from "@app/utils/constants/function";
// eslint-disable-next-line import/no-cycle
import TableStaff from "./TableStaff";
import StatisticalTable from "./StatisticalTable";
import _ from "lodash";

export interface IFormFilter extends IParamsStaff {
  creators?: OptionSelect[];
}

export const initialSearchParams: IFormFilter = {
  candidateName: "",
  contactInformation: "",
  creatorIds: [],
  currentPage: 1,
  languages: [],
  pageSize: 20,
  position: "",
  workLocations: [],
  createdFrom: null,
  createdTo: null,
  experienceYear: null,
};

export default function ManagerDataPool(): JSX.Element {
  const formikRef = useRef<FormikProps<IFormFilter>>(null);
  const [openAddStaff, setOpenAddStaff] = useState(false);
  const [openDetailStaff, setOpenDetailStaff] = useState(false);
  const [openStatisticalTable, setOpenStatisticalTable] = useState(false);
  const dispatch = useDispatch();
  const [fileCvUpload, setFileCvUpload] = useState<FileUpload>(
    {} as FileUpload
  );
  const [fileResponse, setFileResponse] = useState<string>("");
  const [fileParser, setFileParser] = useState<IDataCvCandidate>(
    {} as IDataCvCandidate
  );
  const [idStaff, setIdStaff] = useState<string>("");
  const refTableStaff = useRef<any>();
  const router = useRouter();

  const getAllReco = useQuery("getAllReco", () => {
    return ApiStaff.getAllReco();
  });

  const queryParams = useMemo(() => {
    let query = {} as any;

    if (!_.isEmpty(router.query)) {
      query = router.query;
    } else {
      const searchParams = new URLSearchParams(window.location.search);
      query = Object.fromEntries(searchParams);
    }
    return {
      ...initialSearchParams,
      creatorIds: query.creatorIds
        ? query.creatorIds?.split(",")?.map((i: any) => Number(i))
        : [],
      creators: query.creatorIds
        ? query.creatorIds?.split(",")?.map((i: any) => String(i))
        : [],
      createdFrom: query.createdFrom || null,
      createdTo: query.createdTo || null,
      currentPage: 1,
      pageSize: 20,
    };
  }, [router.query]);

  const [valuesSearch, setValuesSearch] = useState<IFormFilter>(queryParams);

  useEffect(() => {
    if (router.query.id) {
      setIdStaff(String(router.query.id));
    }
  }, [router.query.id]);

  const requestWorkLocationList = useQuery("requestWorkLocationList", () => {
    return ApiCandidate.getWorkLocationList();
  });

  const handlePagination = (page: number, pageSize: number): void => {
    setValuesSearch({
      ...valuesSearch,
      currentPage: page,
      pageSize,
    });
  };

  const requestStaffList = useQuery(
    ["requestStaffList", valuesSearch],
    () => {
      const newSearchParams = {
        ...valuesSearch,
        creators: undefined,
      };
      return ApiStaff.getListStaff(newSearchParams);
    },
    {
      onSuccess: () => {
        const newQuery = {
          creatorIds: valuesSearch.creatorIds
            ? valuesSearch.creatorIds.join()
            : "",
          createdFrom: valuesSearch.createdFrom || "",
          createdTo: valuesSearch.createdTo || "",
        } as any;

        Object.keys(newQuery).forEach((key) => {
          if (!newQuery[key]) {
            delete newQuery[key];
          }
        });

        router.push(router.pathname, {
          query: newQuery,
        });
      },
    }
  );

  const handleUploadFileTemp = useMutation(
    (data: FormData) => {
      dispatch(setLoading(true));
      return ApiUploadCv.uploadFileTemp(data);
    },
    {
      onSuccess: (data) => {
        setFileResponse(data);
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const uploadFileCv = useMutation(
    (data: FormData) => {
      return ApiCandidate.fileParser(data);
    },
    {
      onSuccess: (data) => {
        setFileParser(data);
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const handleUploadFile = (file: File, formData: FormData): void => {
    setOpenAddStaff(true);
    setFileCvUpload({
      file: file,
      fileName: file.name,
    });
    handleUploadFileTemp.mutate(formData);
    uploadFileCv.mutate(formData);
  };

  const getListStaffAfterCreate = (): void => {
    requestStaffList.refetch();
  };

  const handleCancelFileUpload = (): void => {
    setFileCvUpload({} as FileUpload);
    setFileResponse("");
    setFileParser({} as IDataCvCandidate);
  };

  const getDetailStaff = useQuery(
    ["getDetailStaff", idStaff],
    () => ApiStaff.getDetailStaff(String(idStaff)),
    {
      enabled: !!idStaff,
      onSuccess: () => {
        dispatch(setLoading(false));
        setOpenDetailStaff(true);
      },
      onError: () => {
        dispatch(setLoading(false));
        setOpenDetailStaff(true);
      },
    }
  );

  const onClickItem = (staff: IStaffObject): void => {
    setQueryUrl({id: String(staff.publicCandidateId)});
    setIdStaff(String(staff.publicCandidateId));
  };

  const cancelModalAddStaff = (): void => {
    setOpenAddStaff(false);
    handleCancelFileUpload();
  };

  const closeModalDetailStaff = (): void => {
    setOpenDetailStaff(false);
    window.history.pushState({}, "", router.route);
    setIdStaff("");
  };

  const closeModalStatisticalTable = (): void => {
    setOpenStatisticalTable(false);
    window.history.pushState({}, "", router.route);
  };

  const renderTableStaff = (): JSX.Element => {
    return (
      <div>
        <TableStaff
          ref={refTableStaff}
          requestStaffList={requestStaffList}
          onClickStaff={onClickItem}
          formikRef={formikRef}
          requestWorkLocationList={requestWorkLocationList}
          handleOpenPopupAddStaff={(): void => setOpenAddStaff(true)}
          allUsersData={getAllReco?.data}
          handleOpenStatisticalTable={(): void => setOpenStatisticalTable(true)}
          setValuesSearch={setValuesSearch}
          valuesSearch={valuesSearch}
        />
        <AppPagination
          className="mt-6"
          defaultPageSize={valuesSearch.pageSize}
          current={valuesSearch.currentPage}
          pageSize={valuesSearch.pageSize}
          total={requestStaffList.data?.totalCount}
          onChange={handlePagination}
        />
      </div>
    );
  };

  return (
    <div className="data-pool">
      {renderTableStaff()}
      <InformationStaffForm
        isModalVisible={openAddStaff}
        handleCancel={cancelModalAddStaff}
        cvStaffData={
          {
            ...fileParser,
            isLoadingFileParser: uploadFileCv.isLoading,
            fileCVPath: fileResponse || "",
            fileCVName: fileCvUpload?.fileName || "",
          } as unknown as IDataStaff
        }
        fileUpload={fileCvUpload}
        handleReloadAfterCreate={getListStaffAfterCreate}
        handleUploadFile={handleUploadFile}
        type="create"
      />
      <InformationStaffForm
        isModalVisible={openDetailStaff}
        handleCancel={closeModalDetailStaff}
        cvStaffData={getDetailStaff.data as IDataStaff}
        fileUpload={fileCvUpload}
        type="detail"
        createdDate={getDetailStaff.data?.createdDate || ""}
      />
      <StatisticalTable
        isModalVisible={openStatisticalTable}
        handleCancel={closeModalStatisticalTable}
        allUsersData={getAllReco?.data}
      />
    </div>
  );
}
