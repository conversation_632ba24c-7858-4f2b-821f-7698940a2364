import "./index.scss";
import React, {useState} from "react";
import {Col, Empty, Image, List, Row} from "antd";
import {useQuery, UseQueryResult} from "react-query";
import {CardViewJob} from "@app/components/CardViewJob";
import ApiJob, {IJob, INews} from "@app/api/ApiJob";
import AppPagination from "@app/components/AppPagination";
import AppLoading from "@app/components/AppLoading";
import {Swiper, SwiperSlide} from "swiper/react";
import {Pagination, Autoplay} from "swiper";
import "swiper/css";
import "swiper/css/pagination";
import FilterJob from "./filterJob";
import {useRouter} from "next/router";
import config from "@app/config";
import {useSelector, useDispatch} from "react-redux";
import {selectNews, setListNews} from "@app/redux/slices/NewsSlice";
// eslint-disable-next-line import/named
import {SearchParamsJobFilter} from "@app/types";
import {heightBanner} from "@app/utils/constants/state";

export function getListJob(
  currentPage: number,
  pageSize: number,
  valuesFilter: SearchParamsJobFilter,
  isSaveValueSearch: boolean
): UseQueryResult<IJob, unknown> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const router = useRouter();

  const convertStringArrToNumberArr = (values: string[]): number[] => {
    if (!values || values?.length === 0) return [];
    return values?.map((item) => Number(item));
  };

  const handleSetQueryUrl = (valuesFilter: any): void => {
    const searchParams = {
      pageSize,
      currentPage,
      ...valuesFilter,
      salaryRanges: valuesFilter?.salaryRanges?.map((i: any) => {
        if (i === null) return "null";
        return i;
      }),
    };

    const convertedParams: any = {};
    Object.entries(searchParams)?.forEach(([key, value]) => {
      if (Array.isArray(value)) {
        convertedParams[key] = value?.join(",") || "";
      } else {
        convertedParams[key] = value;
      }
    });
    const redirect = `${config.PATHNAME.REQUEST_JOB}?${Object.keys(
      convertedParams
    )
      .map(
        (item) =>
          encodeURIComponent(item) +
          "=" +
          encodeURIComponent((convertedParams as any)[item])
      )
      ?.join("&")}`;
    router.push(redirect);
  };

  const valueSearch = {
    currentPage: currentPage,
    isSearchHotJob: valuesFilter.isSearchHotJob,
    jobLabels: convertStringArrToNumberArr(
      (valuesFilter.jobLabels as string[]) ?? []
    ),
    jobTypes: valuesFilter.jobTypes ?? [],
    levels: valuesFilter.levels ?? [],
    pageSize: pageSize,
    salaryRanges:
      valuesFilter.salaryRanges?.map((i) => {
        if (i === "null") {
          return null;
        }
        return Number(i);
      }) ?? [],
    textSearch: valuesFilter.textSearch ?? "",
    workLocations: valuesFilter.workLocations ?? [],
    isBookmark: valuesFilter.isBookmark,
    haveRecommendCandidate: valuesFilter.haveRecommendCandidate,
    services: valuesFilter.services ?? [],
  };

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const jobList = useQuery(
    ["jobList", valueSearch],
    () => {
      return ApiJob.getListJob(valueSearch as any);
    },
    {
      onSuccess: () => {
        if (isSaveValueSearch) {
          handleSetQueryUrl(valueSearch);
        }
      },
    }
  );
  return jobList;
}

export interface UrlParamsJobFilter {
  textSearch: string;
  workLocations: string;
  jobLabels: string;
  levels: string;
  salaryRanges: string;
  jobTypes: string;
  isSearchHotJob: boolean;
  isBookmark: boolean;
  pageSize: number;
  currentPage: number;
  services: string;
}

export const stringToArr = (value: string) => {
  if (!value) return [];
  return value.split(",");
};

export default function Job(): JSX.Element {
  const urlSearchParams = new URLSearchParams(window.location.search);
  const searchParams = Object.fromEntries(urlSearchParams);

  const initialValue = {
    textSearch: searchParams?.textSearch || "",
    workLocations: stringToArr(searchParams?.workLocations),
    isBookmark: String(searchParams?.isBookmark) === "true",
    isSearchHotJob: String(searchParams?.isSearchHotJob) === "true",
    jobLabels: stringToArr(searchParams?.jobLabels),
    jobTypes: stringToArr(searchParams?.jobTypes),
    levels: stringToArr(searchParams?.levels),
    salaryRanges: stringToArr(searchParams?.salaryRanges),
    haveRecommendCandidate:
      String(searchParams?.haveRecommendCandidate) === "true",
    services: stringToArr(searchParams?.services),
  };

  const [currentPage, setCurrentPage] = useState(
    Number(searchParams?.currentPage || 1)
  );
  const [pageSize, setPageSize] = useState(
    Number(searchParams?.pageSize || 20)
  );
  const [valuesFilter, setValuesFilter] = useState(initialValue);

  const router = useRouter();
  const {listNews} = useSelector(selectNews);
  const dispatch = useDispatch();

  const handlePagination = (page: number, pageSize: number): void => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const onClickItemJob = (idJob: number): void => {
    router.push(`${config.PATHNAME.JOB_DETAIL}?id=${idJob}`);
  };

  const jobList = getListJob(currentPage, pageSize, valuesFilter, true);

  const getListNews = useQuery(
    ["getListNews"],
    () => {
      return ApiJob.getListNews();
    },
    {
      onSuccess: (data) => {
        dispatch(setListNews(data ?? []));
      },
    }
  );

  return (
    <div>
      <FilterJob
        setCurrentPage={setCurrentPage}
        initValueUrlParams={valuesFilter}
        isSaveValueSearch
        updateValueFilter={setValuesFilter as any}
      />
      <div>
        <Row>
          <Col span={16}>
            <div className="list-job-container">
              <List
                loading={jobList.isFetching}
                grid={{
                  gutter: 16,
                  xs: 1,
                  sm: 1,
                  md: 1,
                  lg: 2,
                  xl: 2,
                  xxl: 2,
                }}
                dataSource={jobList.data?.jobs ?? []}
                renderItem={(item) => (
                  <List.Item>
                    <CardViewJob
                      requestJob={item}
                      onClick={() => onClickItemJob(item.requestJobId)}
                    />
                  </List.Item>
                )}
              />
            </div>
            <AppPagination
              className="mt-4"
              defaultCurrent={1}
              defaultPageSize={20}
              current={currentPage}
              pageSize={pageSize}
              total={jobList.data?.totalJob}
              onChange={handlePagination}
            />
          </Col>
          <Col span={8} className="pl-4 container-list-news">
            <Row className="justify-between mb-2 items-baseline">
              <span className="title-news">Tin Mới nhất</span>
              <a
                className="see-all"
                href={config.HREF_NEWS}
                target="_blank"
                rel="noreferrer"
              >
                Xem tất cả
              </a>
            </Row>
            {listNews && listNews.length > 0 ? (
              <Swiper
                pagination
                modules={[Pagination, Autoplay]}
                autoplay={{
                  delay: 3000,
                  disableOnInteraction: false,
                }}
              >
                {listNews.map((item: INews, index) => (
                  <SwiperSlide key={index} className="cursor-pointer">
                    <Image
                      width="100%"
                      height={heightBanner}
                      src={item?.yoast_head_json?.og_image[0]?.url}
                      preview={false}
                      onClick={() => {
                        window.open(item.link, "_blank", "noopener,noreferrer");
                      }}
                    />
                  </SwiperSlide>
                ))}
              </Swiper>
            ) : (
              <div
                style={{height: heightBanner}}
                className="flex justify-center items-center"
              >
                {getListNews.isLoading ? <AppLoading /> : <Empty />}
              </div>
            )}
          </Col>
        </Row>
      </div>
    </div>
  );
}
