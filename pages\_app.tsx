import {Provider} from "react-redux";
import {PersistGate} from "redux-persist/integration/react";
import {QueryClient, QueryClientProvider} from "react-query";
import store, {persistor} from "../redux/store";
import Routes from "../routes";
import "@fortawesome/fontawesome-free/css/all.min.css";
import "@fortawesome/fontawesome-free/js/all.min";
import "antd/dist/antd.css";
import "tailwindcss/tailwind.css";
import "../styles/_app.scss";
import "../utils/I18n";
import {AppProps} from "next/app";
import Head from "next/head";
import {getAnalytics} from "firebase/analytics";
import {app} from "@app/utils/firebase";
import config from "../config";
import Chatbot from "@app/module/chatbot";
import GoogleAnalytics from "@app/components/AppGoogleAnalytics";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: config.NETWORK_CONFIG.RETRY,
      refetchOnWindowFocus: false,
    },
  },
});

const HeaderSystem = (
  <Head>
    <title>Reco - Hệ sinh thái tuyển dụng</title>
    <link
      rel="shortcut icon"
      href={`${config.NETWORK_CONFIG.API_BASE_URL}/image/layout/header/logo-icon.svg`}
    />
    <meta name="viewport" content="initial-scale=1.0, width=device-width" />
  </Head>
);

export default function MyApp({
  Component,
  pageProps,
  router,
}: AppProps): JSX.Element {
  if (
    app &&
    app.name &&
    typeof window !== "undefined" &&
    process.env.NODE_ENV === "production"
  ) {
    getAnalytics(app);
  }

  if (typeof window !== "undefined") {
    return (
      <Provider store={store}>
        <PersistGate persistor={persistor}>
          <QueryClientProvider client={queryClient}>
            {HeaderSystem}
            <GoogleAnalytics />
            <Chatbot />
            <Routes
              Component={Component}
              pageProps={pageProps}
              router={router}
            />
          </QueryClientProvider>
        </PersistGate>
      </Provider>
    );
  }

  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        {HeaderSystem}
        <Routes Component={Component} pageProps={pageProps} router={router} />
      </QueryClientProvider>
    </Provider>
  );
}
