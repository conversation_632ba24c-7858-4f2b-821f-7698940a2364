import React, {useEffect, useMemo, useRef, useState} from "react";
import Icon from "@app/components/Icon/Icon";
import {useMutation, useQuery} from "react-query";
import AppTable from "@app/components/AppTable";
import {useDispatch, useSelector} from "react-redux";
import ApiR<PERSON><PERSON>Job, {
  GroupByParams,
  GroupByRequestJob,
  IDataFormUpdateBatchRequest,
  IDataSearchFormAdvance,
  IListRequestJob,
  IRequestJob,
  ISearchParamsRequestJob,
  ITypeFormUpdateBatch,
} from "@app/api/ApiRequestJob";
import AppPagination from "@app/components/AppPagination";
import FilterJobRequest from "./filterJobRequest";
import "./index.scss";
import moment from "moment";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {useRouter} from "next/router";
import config from "@app/config";
import {
  changeColManagerRequestRoleAm,
  initColShowManagerRequestRoleAm,
  selectUser,
  setQueryParamsPages,
} from "@app/redux/slices/UserSlice";
import {ColumnsType} from "antd/es/table";
import {
  convertToOptionSelect,
  formatDateTimeValue,
  inviteBonus,
  upperCaseFirstChar,
} from "@app/utils/constants/function";
import AppButton from "@app/components/AppButton";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {IAccountRole, RequestType, StatusRequestJob} from "@app/types";
import {Formik, FormikProps} from "formik";
import {SelectInput} from "@app/components/SelectInput";
import {
  getStatusRequestJob,
  labelRequestBatch,
  priorities,
  rankOptions,
  requestTypeBatch,
  statusRequest,
} from "@app/utils/constants/state";
import AppModal from "@app/components/AppModal";
import {Col, Row, Tooltip, notification} from "antd";
import Link from "next/link";
import _ from "lodash";
import AppDataGroupBy from "@app/components/AppDataGroupBy";

interface JobLabel {
  label: string;
  value: number;
  classNames: string;
}

export const newLabels: JobLabel[] = [
  {value: 0, label: "Không dán nhãn", classNames: ""},
  {
    value: 1,
    label: "Pending",
    classNames: "pending-label",
  },
  {value: 2, label: "Urgent", classNames: "urgent-label"},
  {value: 3, label: "Siêu bonus", classNames: "urgent-label"},
];

const renderRequest = (requestType: number): string => {
  if (requestType === RequestType.external) {
    return "external";
  }
  return "internal";
};

export default function ManagerRequest(): JSX.Element {
  const {
    listColShowRequest,
    user,
    listColShowManagerRequestRoleAm,
    queryParamsPages,
  } = useSelector(selectUser);
  const router = useRouter();
  const navigatorJobDetail = (idJob: number): void => {
    router.push(`${config.PATHNAME.JOB_DETAIL}?id=${idJob}`);
  };
  const dispatch = useDispatch();

  let queryParams = {} as any;

  if (!_.isEmpty(router.query)) {
    queryParams = router.query;
  } else {
    const searchParams = new URLSearchParams(
      queryParamsPages?.managerRequest || ""
    );
    queryParams = Object.fromEntries(searchParams);
  }

  const initialStatusFilter = [
    {id: StatusRequestJob.Open, label: "Open"},
    {id: StatusRequestJob.Processing, label: "Processing"},
    {id: StatusRequestJob.Review, label: "Review"},
    {id: StatusRequestJob.Unclear, label: "Unclear"},
  ];

  queryParams = {
    ...queryParams,
    isFirstInitialization: true,
    // isAdvanceSearch: true,
    currentPage: queryParams.currentPage ? Number(queryParams.currentPage) : 1,
    pageSize: queryParams.pageSize ? Number(queryParams.pageSize) : 20,
    countries: queryParams.countries ? JSON.parse(queryParams.countries) : [],
    customers: queryParams.customers ? JSON.parse(queryParams.customers) : [],
    labels: queryParams.labels ? JSON.parse(queryParams.labels) : [],
    name: queryParams.name || "",
    positionName: queryParams.positionName || "",
    priorities: queryParams.priorities
      ? JSON.parse(queryParams.priorities)
      : [],
    requestTypes: queryParams.requestTypes
      ? JSON.parse(queryParams.requestTypes)
      : [],
    statuses: queryParams.statuses
      ? JSON.parse(queryParams.statuses)
      : queryParams?.notUseInitStatus === "true"
      ? []
      : initialStatusFilter,
    workLocations: queryParams.workLocations
      ? JSON.parse(queryParams.workLocations)
      : [],
    textSearch: queryParams.textSearch,
    services: queryParams.services ? JSON.parse(queryParams.services) : [],
    from: formatDateTimeValue(queryParams?.from),
    to: formatDateTimeValue(queryParams?.to),
  };

  const initQueryParams = useMemo(() => {
    return {
      ...queryParams,
      countries: queryParams.countries
        ? convertToOptionSelect(queryParams.countries)
        : [],
      customers: queryParams.customers
        ? convertToOptionSelect(queryParams.customers)
        : [],
      labels: queryParams.labels
        ? convertToOptionSelect(queryParams.labels)
        : [],
      priorities: queryParams.priorities
        ? convertToOptionSelect(queryParams.priorities)
        : [],
      requestTypes: queryParams.requestTypes
        ? convertToOptionSelect(queryParams.requestTypes)
        : [],
      statuses: queryParams.statuses
        ? convertToOptionSelect(queryParams.statuses)
        : [],
      workLocations: queryParams.workLocations
        ? convertToOptionSelect(queryParams.workLocations)
        : [],
      from: queryParams.from
        ? moment(queryParams.from, DATE_FORMAT)
        : undefined,
      to: queryParams.to ? moment(queryParams.to, DATE_FORMAT) : undefined,
    };
  }, [queryParams]);

  const [searchParams, setSearchParams] =
    useState<ISearchParamsRequestJob>(queryParams);

  const isRoleAm = [
    IAccountRole.AML,
    IAccountRole.AMG,
    IAccountRole.ADMIN,
    IAccountRole.BD,
    IAccountRole.BDL,
  ].some((item) => user?.role?.includes(item));

  const isRoleCSL = [IAccountRole.CSL].some((item) =>
    user?.role?.includes(item)
  );

  const currentCol = listColShowManagerRequestRoleAm;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isGroupBy, setIsGroupBy] = useState<boolean>(false);
  const [groupBySearchParams, setGroupBySearchParams] =
    useState<GroupByRequestJob>({
      pageNumber: 1,
      pageSize: 20,
    } as GroupByRequestJob);
  const keyGroupBy = useRef<string>("");
  const [dataGroupByOverall, setDataGroupByOverall] = useState<any[]>([]);

  const updateKeyGroupBy = (val: string) => {
    keyGroupBy.current = val;
  };

  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    record: IRequestJob[]
  ): void => {
    const value = (record || [])
      .filter((item) => item?.isOwner)
      ?.map((i) => i.requestJobId);

    setSelectedRowKeys(value);
  };
  const [showUpdateBatch, setShowUpdateBatch] = useState<boolean>(false);

  useEffect(() => {
    if (!listColShowManagerRequestRoleAm) {
      dispatch(changeColManagerRequestRoleAm(initColShowManagerRequestRoleAm));
    }
  }, []);

  const showModalUpdateBatch = (): void => {
    if (selectedRowKeys.length > 0) {
      setShowUpdateBatch(true);
    } else {
      notification.error({
        message: "Thông báo",
        description: "Bạn cần ít nhất 1 bản ghi",
      });
    }
  };

  const hiddenModalUpdateBatch = (): void => {
    setShowUpdateBatch(false);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: IRequestJob) => ({
      className: !record?.isOwner && "disable-checkbox-ant",
    }),
  };

  const innerRefUpdateBatch = useRef<FormikProps<ITypeFormUpdateBatch>>(null);

  const isHighPriory = (value: number): boolean => {
    return String(value) === priorities[0].key;
  };

  const columnAllRoleAm: ColumnsType<IRequestJob> = [
    {
      title: "Tên request",
      dataIndex: "name",
      key: "name",
      align: "left",
      width: "220px",
      fixed: "left",
      render: (_: string, record: IRequestJob): JSX.Element => {
        return (
          // eslint-disable-next-line jsx-a11y/no-static-element-interactions
          <span
            className="underline underline-offset-2 name-color cursor-pointer"
            // onClick={(): void => {
            //   navigatorJobDetail(record.requestJobId);
            // }}
          >
            <a
              href={`${config.PATHNAME.JOB_DETAIL}?id=${record.requestJobId}`}
              className={`hover:text-inherit ${
                isHighPriory(record.priority) && "active-name"
              }`}
              target="_blank"
              rel="noreferrer"
              // eslint-disable-next-line react/jsx-no-bind
              // onClick={(e: any): void => e.preventDefault()}
            >
              {record?.name || ""}
            </a>
          </span>
        );
      },
    },
    {
      title: "Loại request",
      dataIndex: "requestType",
      key: "requestType",
      width: "100px",
      render: (_: string, record: IRequestJob): JSX.Element => {
        return (
          <span className={renderRequest(record?.requestType)}>
            {upperCaseFirstChar(renderRequest(record?.requestType))}
          </span>
        );
      },
    },
    {
      title: "Lưu ý",
      dataIndex: "comments",
      width: "200px",
      key: "comments",
      render: (comments: any[]) => {
        const content = (
          <div>
            {comments?.map((i: any) => (
              <p className="text-white" key={i?.createdDate.toString()}>
                {i?.requestComment}
              </p>
            ))}
          </div>
        );
        return (
          <Tooltip title={content}>
            <div className="text-left limit-two-row">
              {comments?.map((i: any) => (
                <p key={i?.createdDate.toString()}>{i?.requestComment}</p>
              ))}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: "Vị trí",
      dataIndex: "positionName",
      key: "positionName",
      width: "140px",
    },
    {
      title: "AM quản lý",
      dataIndex: "managers",
      key: "managers",
      width: "120px",
      render: (_, record) => {
        return record?.managers && record?.managers?.length > 0
          ? record?.managers[0].name
          : "";
      },
    },
    {
      title: "Ngày request",
      dataIndex: "requestDate",
      key: "requestDate",
      width: "110px",
      render: (_: string, record: IRequestJob): JSX.Element => {
        return (
          <span>
            {record?.requestDate
              ? moment(record?.requestDate).format(DATE_FORMAT)
              : ""}
          </span>
        );
      },
    },
    {
      title: "Tên Khách hàng",
      dataIndex: "customerName",
      width: "130px",
      key: "customerName",
    },
    {
      title: "Khu vực",
      dataIndex: "workLocationNameCombined",
      width: "130px",
      key: "workLocationNameCombined",
    },
    {
      title: "Loại khách hàng",
      width: "130px",
      dataIndex: "rank",
      key: "rank",
      render: (rank: string) => (
        <div>{rankOptions.find((item) => item.value === rank)?.label}</div>
      ),
    },
    {
      title: "Nhãn job",
      dataIndex: "label",
      key: "label",
      width: "120px",
      render: (_: string, record: IRequestJob): JSX.Element => {
        const valueExpected = newLabels.find(
          (item) => item.value === record?.label
        );
        const labelExpected = valueExpected?.value
          ? valueExpected?.value === 0
            ? ""
            : valueExpected?.label
          : "";

        return (
          <span className={`label-request ${valueExpected?.classNames || ""}`}>
            {labelExpected}
          </span>
        );
      },
    },
    {
      title: "Trạng thái",
      dataIndex: "statusName",
      key: "statusName",
      width: "110px",
      render: (_: string, record: IRequestJob): JSX.Element => {
        return (
          <span
            className="status-job"
            style={{
              backgroundColor: getStatusRequestJob(record?.status || "")?.color,
            }}
          >
            {getStatusRequestJob(record?.status || "")?.label}
          </span>
        );
      },
    },
    {
      title: "Dịch vụ",
      dataIndex: "services",
      key: "services",
      width: "120px",
      render(value) {
        return value?.join(", ") || "";
      },
    },
    {
      title: "CV Interview",
      dataIndex: "cvInterview",
      width: "90px",
      key: "cvInterview",
    },
    {
      title: "CV Offer",
      dataIndex: "cvOffer",
      width: "80px",
      key: "cvOffer",
    },
    {
      title: "CV Sent",
      dataIndex: "cvSent",
      width: "80px",
      key: "cvSent",
    },
    {
      title: "Rate KH",
      dataIndex: "customerRateValue",
      width: "120px",
      key: "customerRateValue",
      render: (_: string, record: IRequestJob): JSX.Element => {
        return (
          <span>
            {inviteBonus(
              !!record?.customerRateType,
              Number(record?.customerRateValue || 0),
              record?.customerCurrencyType || ""
            )?.replaceAll("%", "")}
          </span>
        );
      },
    },
    {
      title: "Rate CTV (%)",
      dataIndex: "partnerRateValue",
      width: "120px",
      key: "partnerRateValue",
      render: (_: string, record: IRequestJob): JSX.Element => {
        return (
          <span>
            {inviteBonus(
              record?.partnerRateType,
              Number(record?.partnerRateValue || 0),
              record?.partnerCurrencyType
            )}
          </span>
        );
      },
    },
    {
      title: "Ngày kết thúc",
      dataIndex: "expiryDate",
      width: "120px",
      key: "expiryDate",
      render: (_: string, record: IRequestJob): JSX.Element => {
        return (
          <span>
            {record?.expiryDate
              ? moment(record?.expiryDate, DATE_FORMAT).format(DATE_FORMAT)
              : ""}
          </span>
        );
      },
    },
    {
      title: "",
      key: "action",
      dataIndex: "",
      width: "120px",
      render: (_: string, record: IRequestJob): JSX.Element => {
        const goToViewRequestJob =
          config.PATHNAME.MANAGER_REQUEST_DETAIL_PARAMS(record?.requestJobId);
        const goToEditRequestJob = config.PATHNAME.MANAGER_REQUEST_EDIT_PARAMS(
          record?.requestJobId
        );
        return (
          <div className="flex justify-center items-center">
            <Tooltip title="Xem chi tiết" placement="top">
              <Link href={goToViewRequestJob}>
                <Icon icon="textbox-line" size={20} />
              </Link>
            </Tooltip>
            {record?.isOwner && (
              <span>
                <Tooltip title="Chỉnh sửa" placement="top">
                  <Link href={goToEditRequestJob}>
                    <Icon icon="edit" size={16} className="ml-4 request-link" />
                  </Link>
                </Tooltip>
              </span>
            )}
          </div>
        );
      },
    },
  ];

  const currentColumnAll = columnAllRoleAm;

  const currentColumns = useMemo(() => {
    return currentColumnAll.filter((col) =>
      currentCol?.some((i) => i === col.key)
    );
  }, [listColShowRequest, listColShowManagerRequestRoleAm, isRoleAm]);

  const requestJob = useQuery(
    ["requestJobManager", searchParams],
    () => {
      return ApiRequestJob.getListRequestJob(searchParams as any);
    },
    {
      // enabled: isGroupBy,
      onSuccess: (data: IListRequestJob): void => {
        const newQuery = {
          // isAdvanceSearch: String(data.isAdvanceSearch),
          countries: data.countries?.length
            ? JSON.stringify(data.countries)
            : "",
          customers: data.customers?.length
            ? JSON.stringify(data.customers)
            : "",
          labels: data.labels?.length ? JSON.stringify(data.labels) : "",
          name: data.name || "",
          positionName: data.positionName || "",
          priorities: data.priorities?.length
            ? JSON.stringify(data.priorities)
            : "",
          requestTypes: data.requestTypes?.length
            ? JSON.stringify(data.requestTypes)
            : "",
          statuses: data.statuses?.length ? JSON.stringify(data.statuses) : "",
          workLocations: data.workLocations?.length
            ? JSON.stringify(data.workLocations)
            : "",
          textSearch: data.textSearch,
          pageSize: data.pageSize,
          currentPage: data.currentPage,
          services: data.services?.length ? JSON.stringify(data.services) : "",
          from: formatDateTimeValue(data?.from || ""),
          to: formatDateTimeValue(data?.to || ""),
          notUseInitStatus: data.statuses?.length ? "false" : "true",
        } as any;

        Object.keys(newQuery).forEach((key) => {
          if (!newQuery[key]) {
            delete newQuery[key];
          }
        });

        const queryParamsString = new URLSearchParams(newQuery).toString();
        dispatch(setQueryParamsPages({managerRequest: queryParamsString}));
      },
    }
  );

  const exportRequestJob = useMutation(
    (param: IListRequestJob) => {
      return ApiRequestJob.exportRequestJob(param);
    },
    {
      onSuccess: () => {
        dispatch(setLoading(false));
      },
      onError: () => {
        dispatch(setLoading(false));
      },
    }
  );

  const handlePagination = (page: number, pageSize: number): void => {
    setSearchParams({
      ...searchParams,
      currentPage: page,
      pageSize,
    });
  };

  const initialValueFormAdvance: IDataSearchFormAdvance = useMemo(() => {
    return {
      countryFilters: requestJob?.data?.countryFilters || [],
      customerFilters: requestJob?.data?.customerFilters || [],
      labelFilters: requestJob?.data?.labelFilters || [],
      priorityFilters: requestJob?.data?.priorityFilters || [],
      requestTypeFilters: requestJob?.data?.requestTypeFilters || [],
      statusFilters: requestJob?.data?.statusFilters || [],
      workLocationFilters: requestJob?.data?.workLocationFilters || [],
    };
  }, [requestJob?.data]);

  useEffect(() => {
    requestJob.refetch();
  }, [searchParams]);

  const exportData = (): void => {
    if (requestJob?.data) {
      dispatch(setLoading(true));
      exportRequestJob.mutate(requestJob.data);
    }
  };

  const initValueFormUpdateBatchRequest: ITypeFormUpdateBatch = {
    label: labelRequestBatch[0],
    requestChanges: [],
    requestType: requestTypeBatch[0],
    status: statusRequest[0],
  };

  const updateBatchRequest = useMutation(
    (data: IDataFormUpdateBatchRequest) => {
      dispatch(setLoading(true));
      return ApiRequestJob.updateBatchRequest(data);
    },
    {
      onSuccess(data) {
        dispatch(setLoading(false));
        notification.success({
          message: "Thông báo",
          description: data,
        });
        requestJob.refetch();
        hiddenModalUpdateBatch();
        innerRefUpdateBatch.current?.resetForm();
      },
      onError() {
        dispatch(setLoading(false));
      },
    }
  );

  // eslint-disable-next-line consistent-return
  const handleUpdateBatchRequest = (): any => {
    const valueRef = innerRefUpdateBatch.current?.values;

    if (valueRef) {
      const valueUpdate: IDataFormUpdateBatchRequest = {
        label:
          valueRef?.label?.value === "null" ? null : valueRef?.label?.value,
        requestType:
          valueRef?.requestType?.value === "null"
            ? null
            : valueRef?.requestType.value,
        status:
          valueRef?.status?.value === "null" ? null : valueRef?.status.value,
        requestChanges: selectedRowKeys.map((i) => ({requestJobId: Number(i)})),
      };

      if (
        ![
          valueUpdate?.label,
          valueUpdate?.requestType,
          valueUpdate?.status,
        ].some((i) => i !== null)
      ) {
        notification.error({
          message: "Thông báo",
          description: "Chưa chọn nội dung muốn chỉnh sửa",
        });
        return "";
      }

      updateBatchRequest.mutate(valueUpdate);
    }
  };

  const groupBy = useMutation(
    (data: GroupByParams) => {
      dispatch(setLoading(true));
      return ApiRequestJob.getGroupBy(data);
    },
    {
      onSuccess(data) {
        const result = data?.data || [];
        setDataGroupByOverall(result);
      },
      onSettled() {
        dispatch(setLoading(false));
      },
    }
  );

  const groupByDetail = useMutation(
    (data: GroupByParams) => {
      dispatch(setLoading(true));
      return ApiRequestJob.getGroupByDetail(data);
    },
    {
      onSuccess(data) {
        const result = dataGroupByOverall.map((item) => {
          if (item.groupByValue === keyGroupBy.current) {
            return {
              ...item,
              data: data.listRequestJob || [],
              pageNumber: data.pageNumber,
              pageSize: data.pageSize,
              totalCount: data.totalCount,
              totalPages: data.totalPages,
            };
          }

          return item;
        });

        setDataGroupByOverall(result);
      },
      onSettled() {
        dispatch(setLoading(false));
      },
    }
  );

  const onChangeGroupBySearchParams = async (data: GroupByRequestJob) => {
    setGroupBySearchParams(data);
    await groupBy.mutateAsync({
      requestJobSearchViewModel: searchParams,
      groupByRequest: data,
    });
  };

  const onClickItemGroupBy = async (id: string, currentPage: number) => {
    updateKeyGroupBy(id);
    const dataParams = {
      ...groupBySearchParams,
      groupByValue: id,
      pageNumber: currentPage,
    };

    await groupByDetail.mutateAsync({
      groupByRequest: dataParams,
      requestJobSearchViewModel: searchParams,
    });
  };

  const onPageChangeGroupBy = async (page: number, size: number) => {
    const dataParams = {
      ...groupBySearchParams,
      pageSize: size,
      pageNumber: page,
    };

    setGroupBySearchParams(dataParams);

    await groupBy.mutateAsync({
      groupByRequest: dataParams,
      requestJobSearchViewModel: searchParams,
    });
  };

  const onAdvanceSearchGroupBy = async (dataForm: ISearchParamsRequestJob) => {
    const newGroupByParams = {
      ...groupBySearchParams,
      pageNumber: 1,
    };

    setGroupBySearchParams(newGroupByParams);

    await groupBy.mutateAsync({
      groupByRequest: newGroupByParams,
      requestJobSearchViewModel: dataForm,
    });
  };

  const renderUiUpdateBatch = (): JSX.Element => {
    return (
      <Formik
        initialValues={initValueFormUpdateBatchRequest}
        innerRef={innerRefUpdateBatch}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          return (
            <div className="form-update-batch">
              <SelectInput
                name="status"
                labelselect="Trạng thái"
                value={values?.status?.value}
                free={!values?.status?.value}
                data={statusRequest.filter((item) => item.key !== "2")}
                containerclassname="mt-2"
              />
              <SelectInput
                name="label"
                labelselect="Gán nhãn"
                value={values.label.value}
                free={!values?.label?.value}
                data={labelRequestBatch}
                containerclassname="mt-2"
              />
              <SelectInput
                name="requestType"
                labelselect="Loại request"
                value={values?.requestType?.value}
                free={!values?.requestType?.value}
                data={requestTypeBatch}
                containerclassname="mt-2"
              />
              <Row className="mt-4" gutter={[16, 16]} justify="center">
                <Col xs={8}>
                  <AppButton
                    typebutton="secondary"
                    onClick={hiddenModalUpdateBatch}
                  >
                    Đóng
                  </AppButton>
                </Col>
                <Col xs={8}>
                  <AppButton
                    typebutton="primary"
                    onClick={handleUpdateBatchRequest}
                  >
                    Lưu
                  </AppButton>
                </Col>
              </Row>
            </div>
          );
        }}
      </Formik>
    );
  };

  return (
    <div className="manager-request w-full">
      <div className="manager-request-search">
        <FilterJobRequest
          dataForm={initialValueFormAdvance}
          initialValueSearch={initQueryParams}
          setSearchFormAdvance={setSearchParams as any}
          allRequestJob={currentColumnAll.map((col) => col.key as string)}
          setVisibleGroupBy={setIsGroupBy}
          groupBySearchParams={groupBySearchParams}
          onChangeGroupBySearchParams={onChangeGroupBySearchParams}
          isGroupBy={isGroupBy}
          onAdvanceSearchGroupBy={onAdvanceSearchGroupBy as any}
        />
      </div>
      <div className="flex items-center">
        {(isRoleAm || isRoleCSL) && (
          <AppButton
            typebutton="normal"
            classrow="btn-config"
            onClick={showModalUpdateBatch}
          >
            <Icon className="mr-4" icon="tab-arrow" size={24} color="#324054" />
          </AppButton>
        )}
        <AppButton
          typebutton="normal"
          classrow="manager-request-export my-2"
          disabled={exportRequestJob.isLoading}
          onClick={exportData}
        >
          <Icon icon="download-cloud-line" size={24} color="#324054" />
        </AppButton>
      </div>
      <div className="manager-request-table">
        {!isGroupBy ? (
          <>
            <AppTable
              columns={currentColumns}
              dataSource={requestJob?.data?.requestJobPagging ?? []}
              bordered
              rowClassName="row-table-applications"
              rowKey={(record: IRequestJob): any => record.requestJobId}
              key="key"
              scroll={{y: "65vh", x: "max-content"}}
              loading={requestJob.isLoading}
              rowSelection={isRoleAm ? (rowSelection as any) : undefined}
            />

            <div className="mt-2">
              <AppPagination
                defaultPageSize={searchParams.pageSize}
                current={searchParams.currentPage}
                pageSize={searchParams.pageSize}
                total={requestJob.data?.totalCount}
                onChange={handlePagination}
              />
            </div>
          </>
        ) : (
          <AppDataGroupBy
            column={currentColumns}
            dataGroupBy={dataGroupByOverall}
            onClickItem={onClickItemGroupBy}
            pageNumber={groupBySearchParams.pageNumber}
            pageSize={groupBySearchParams.pageSize}
            totalCount={groupBy?.data?.totalCount || 0}
            onPageChangeGroupBy={onPageChangeGroupBy}
            dataQueryGroupBy={searchParams}
          />
        )}
      </div>
      <AppModal
        title="Sửa request đồng loạt"
        open={showUpdateBatch}
        centered
        footer={null}
        onCancel={hiddenModalUpdateBatch}
      >
        {renderUiUpdateBatch()}
      </AppModal>
    </div>
  );
}
