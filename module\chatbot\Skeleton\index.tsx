import React from "react";
import {Skeleton} from "antd";
import "./index.scss";

export default function ChatSkeleton(): JSX.Element {
  return (
    <div className="ui-chat-skeleton">
      <Skeleton
        className="ui-chat-skeleton__message"
        active
        paragraph={{rows: 0}}
      />
      <Skeleton
        className="ui-chat-skeleton__message"
        active
        paragraph={{rows: 0}}
      />
      <Skeleton
        className="ui-chat-skeleton__message"
        active
        paragraph={{rows: 0}}
      />
      <Skeleton
        className="ui-chat-skeleton__message"
        active
        paragraph={{rows: 0}}
      />
      <Skeleton
        className="ui-chat-skeleton__message"
        active
        paragraph={{rows: 0}}
      />
      <Skeleton
        className="ui-chat-skeleton__message"
        active
        paragraph={{rows: 0}}
      />
    </div>
  );
}
