import "./index.scss";
import {Avatar, Card, Row, Tooltip, Image} from "antd";
import Icon from "@app/components/Icon/Icon";
import React, {useMemo} from "react";
import {ICandidateObject} from "@app/api/ApiCandidate";
import {formatMoney, getAbbreviatedName} from "@app/utils/constants/function";
// eslint-disable-next-line import/namespace
import {getStatusCandidate} from "@app/utils/constants/state";
import AppButton from "../AppButton";
import config from "@app/config";

interface CardViewCandidateProps {
  candidate: ICandidateObject;
  isSmallSize: boolean;
  onClick?: () => void;
  onClickIntroNow?: (id: number) => void;
  loadingIntro?: boolean;
  isSuggested?: boolean;
}

export function CardViewCandidate(props: CardViewCandidateProps): JSX.Element {
  const {
    candidate,
    isSmallSize,
    onClick,
    onClickIntroNow,
    loadingIntro,
    isSuggested = true,
  } = props;

  const statusCandidate = getStatusCandidate(candidate?.status);
  const salary = formatMoney(
    candidate?.salaryExpected,
    candidate?.currencyTypeId
  );

  const experienceYear = candidate?.experienceYear
    ? `(${candidate.experienceYear}${
        !candidate.experienceYear.includes("năm") ? " năm" : ""
      })`
    : "";

  const workHistories = useMemo(() => {
    if (!candidate?.workHistories || candidate?.workHistories?.length === 0) {
      return [];
    }
    if (candidate?.workHistories?.length <= 3) {
      return candidate.workHistories;
    }

    const dataWorkHistories = candidate.workHistories.slice(-3);
    return dataWorkHistories;
  }, [candidate.workHistories]);

  if (!isSmallSize) {
    return (
      <Card
        className="hover-pointer card-container card-padding relative w-full"
        onClick={onClick}
      >
        <Row className="flex text-color-primary">
          {candidate?.fileAvatarPath ? (
            <Avatar
              size={64}
              src={
                config.NETWORK_CONFIG.API_BASE_URL + candidate.fileAvatarPath
              }
            />
          ) : (
            <Avatar size={64} className="candidate-information-ui_avatar">
              {getAbbreviatedName(candidate?.name)}
            </Avatar>
          )}
          <Row className="flex-1">
            <div className="flex flex-col ml-5 w-6/12 text14">
              <span className="truncate text16">
                {candidate?.name || "N/A"}
              </span>
              <span className="truncate">
                {candidate?.positionExpected || "N/A"}
              </span>
              <div className="flex items-center truncate">
                <div
                  className="green-dot"
                  style={{backgroundColor: statusCandidate.color}}
                />
                <span className="ml-1.5">{statusCandidate.label}</span>
              </div>
              <div className="flex items-center">
                <Icon className="" icon="global-line" size={16} />
                <span className="ml-1 truncate flex-1">
                  {candidate?.languages?.join(", ") || "N/A"}
                </span>
              </div>
              <div className="flex items-center">
                <Icon className="" icon="circle-dolar" size={16} />
                <span className="ml-1 truncate flex-1">{salary}</span>
              </div>
            </div>
            <div className="flex flex-col ml-5 justify-center w-6/12 text14">
              <div className="flex items-center">
                <Icon icon="map-pin-line-pin-line" size={15} />
                <span className="ml-1 truncate flex-1">
                  {candidate?.workLocationNames?.toString() || "N/A"}
                </span>
              </div>
              <div className="flex items-center">
                <Icon icon="phone-line" size={15} />
                <span className="ml-1 flex-1 truncate">
                  {candidate?.phoneNumber || "N/A"}
                </span>
              </div>
              {candidate?.email && (
                <div className="flex items-center">
                  <Icon icon="mail-line" size={15} />
                  <span className="ml-1 flex-1 truncate">
                    {candidate?.email}
                  </span>
                </div>
              )}

              <span>
                <span className="font-bold">Kinh nghiệm</span>
                {experienceYear}
              </span>
              <div className="card-container__work_histories">
                {workHistories.map((item, index) => {
                  const text = `${item?.position} tại ${item?.companyName}`;
                  return (
                    <Tooltip title={text} key={index}>
                      <p className="flex items-center ml-5">
                        <Icon icon="briefcase-2-line" size={13} />
                        <span className="text-xs ml-1.5 truncate flex-1">
                          {text}
                        </span>
                      </p>
                    </Tooltip>
                  );
                })}
              </div>
            </div>
          </Row>
        </Row>
      </Card>
    );
  }

  return (
    <Card
      className="card-container card-padding relative w-full"
      onClick={onClick}
    >
      <Row className="flex">
        {candidate?.fileAvatarPath ? (
          <Avatar
            size={52}
            src={config.NETWORK_CONFIG.API_BASE_URL + candidate.fileAvatarPath}
          />
        ) : (
          <Avatar size={52} className="candidate-information-ui_avatar">
            {getAbbreviatedName(candidate?.name)}
          </Avatar>
        )}
        <Row className="flex-1">
          <div className="flex flex-col w-6/12 text14 pl-2">
            <span className="truncate font-normal text16">
              {candidate?.name || "N/A"}
            </span>
            <span className="text-xs truncate font-normal">
              {candidate?.positionExpected || "N/A"}
            </span>
            <div className="flex items-center truncate mt-2">
              <div
                className="green-dot"
                style={{backgroundColor: statusCandidate.color}}
              />
              <span className="ml-1 text-xs">{statusCandidate.label}</span>
            </div>
            <div className="flex items-center mt-4 relative">
              <Icon icon="global-line" size={13} />
              <span className="text-xs ml-1  truncate flex-1">
                {candidate?.languages?.join(", ") || "N/A"}
              </span>
              {candidate?.rule_based && (
                <div className="absolute right-[calc(100%+10px)] top-0">
                  <Image
                    src="/img/star-icon.svg"
                    width={24}
                    height={24}
                    alt="star"
                  />
                </div>
              )}
            </div>
            <div className="flex items-center">
              <Icon className="" icon="circle-dolar" size={13} />
              <span className="text-xs ml-1 truncate flex-1">{salary}</span>
            </div>
          </div>
          <div className="flex flex-col justify-center w-6/12 pl-2">
            <div className="flex items-center">
              <Icon icon="map-pin-line-pin-line" size={12} />
              <span className="text-xs ml-1 truncate flex-1">
                {candidate?.workLocationNames?.toString() || "N/A"}
              </span>
            </div>
            <div className="flex items-center">
              <Icon icon="phone-line" size={12} />
              <span className="text-xs ml-1 truncate flex-1">
                {candidate?.phoneNumber || "N/A"}
              </span>
            </div>
            {candidate?.email && (
              <div className="flex items-center">
                <Icon icon="mail-line" size={15} />
                <span className="ml-1 flex-1 truncate text-xs">
                  {candidate?.email}
                </span>
              </div>
            )}

            <span className="text-xs mt-1.5 mb-0.5">
              <span className="font-bold text-xs">Kinh nghiệm</span>
              {experienceYear}
            </span>
            <div className="card-container__work_histories">
              {workHistories.map((item, index) => {
                const text = `${item?.position} tại ${item?.companyName}`;
                return (
                  <Tooltip title={text} key={index}>
                    <p className="flex items-center ml-5" key={index}>
                      <Icon icon="briefcase-2-line" size={13} />
                      <span className="text-xs ml-1.5 truncate flex-1">
                        {text}
                      </span>
                    </p>
                  </Tooltip>
                );
              })}
            </div>
          </div>
        </Row>
      </Row>
      {candidate?.isSuitable !== null && !isSuggested && (
        <span
          className={`absolute card-container__suitable ${
            !candidate.isSuitable && "card-container__not-suitable"
          }`}
        >
          {candidate?.isSuitable ? "Phù hợp" : "Không phù hợp"}
        </span>
      )}

      {isSuggested && (
        <Row justify="end" gutter={[16, 16]}>
          <div className="flex justify-end w-full">
            <AppButton
              label="Giới thiệu ngay"
              typebutton="normal"
              classrow="btn-intro-now"
              onClick={(): void =>
                onClickIntroNow?.(Number(candidate.candidateId))
              }
              disabled={loadingIntro}
            />
          </div>
        </Row>
      )}
    </Card>
  );
}
