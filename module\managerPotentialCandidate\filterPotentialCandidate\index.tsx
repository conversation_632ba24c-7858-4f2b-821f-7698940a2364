import {SelectInput} from "@app/components/SelectInput";
import "./index.scss";
import {TextInput} from "@app/components/TextInput";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {Col, Popover, Row} from "antd";
import AppButton from "@app/components/AppButton";
import Icon from "@app/components/Icon/Icon";
import {Formik, FormikProps} from "formik";
import {FormEvent, useEffect, useRef, useState} from "react";
import {
  candidateSource,
  deadTimeFastSearch,
  listExperience,
  listLanguage,
} from "@app/utils/constants/state";
import {
  DataFormSearchPotential,
  ValuesSearchPotential,
} from "@app/api/ApiPotentialCandidate";
import {moneyToNumber} from "@app/utils/constants/function";
import {OptionSelect} from "@app/types";
import AppCheckBox from "@app/components/AppCheckbox";

interface Props {
  workLocations: OptionSelect[];
  skills: OptionSelect[];
  onResetCurrentPage: () => void;
  onSearchParams: (data: ValuesSearchPotential) => void;
}

const initialValue: ValuesSearchPotential = {
  name: "",
  currentPosition: "",
  company: "",
  previousPosition: "",
  hasContact: true,
  language: "",
  skills: [],
  currencyUnit: "",
  address: [],
  expectedSalary: "",
  keyword: "",
  source: [],
  yearOfExp: "",
  currentPage: 1,
};

const initialValueForm: DataFormSearchPotential = {
  name: "",
  currentPosition: "",
  company: "",
  previousPosition: "",
  hasContact: true,
  language: {} as OptionSelect,
  skills: [],
  currencyUnit: {value: "VND", label: "VND"},
  position: [],
  expectedSalary: "",
  keyword: "",
  source: [],
  yearOfExp: {} as OptionSelect,
};

export function FilterPotentialCandidate(props: Props): JSX.Element {
  const {workLocations, skills, onResetCurrentPage, onSearchParams} = props;

  const [showFilterAdvance, setShowFilterAdvance] = useState<boolean>(false);
  const formikRef = useRef<FormikProps<DataFormSearchPotential>>(null);
  const hasContactRef = useRef<boolean>(initialValueForm.hasContact);
  const timeOut = useRef<any>();
  const languages: OptionSelect[] = listLanguage.map((language) => ({
    label: language.id,
    value: language.id,
    key: language.id,
  }));

  const handleSearchNormal = (e?: any): void => {
    if (e?.target?.name === "hasContact") {
      hasContactRef.current = e.target.checked;
    }
    // eslint-disable-next-line no-unused-expressions
    timeOut.current && clearTimeout(timeOut.current);
    timeOut.current = setTimeout(() => {
      onResetCurrentPage();
      onSearchParams({
        ...initialValue,
        name: formikRef?.current?.values?.name?.trim(),
        currentPosition: formikRef?.current?.values?.currentPosition?.trim(),
        company: formikRef?.current?.values?.company?.trim(),
        previousPosition: formikRef?.current?.values?.previousPosition?.trim(),
        hasContact: hasContactRef.current || false,
        skills:
          formikRef?.current?.values?.skills?.map(
            (item) => (item?.key as string) || (item.value as string)
          ) || [],
        address:
          formikRef?.current?.values?.position?.map(
            (item) => item?.key as string
          ) || [],
        expectedSalary: formikRef?.current?.values?.expectedSalary
          ? String(moneyToNumber(formikRef?.current?.values?.expectedSalary))
          : "",
        currencyUnit: formikRef?.current?.values?.expectedSalary
          ? (formikRef?.current?.values?.currencyUnit?.key as string)
          : "",
        language: formikRef?.current?.values?.language?.value || "",
        yearOfExp: formikRef?.current?.values?.yearOfExp?.key || "",
        source:
          formikRef?.current?.values?.source?.map(
            (item) => item?.key as string
          ) || [],
      });
    }, deadTimeFastSearch);
  };

  const handleAdvanceSearch = () => {
    const valueRef = formikRef?.current?.values;
    const valueSearch: ValuesSearchPotential = {
      name: valueRef?.name?.trim(),
      currentPosition: valueRef?.currentPosition?.trim(),
      company: valueRef?.company?.trim(),
      previousPosition: valueRef?.previousPosition?.trim(),
      hasContact: hasContactRef.current || false,
      skills:
        valueRef?.skills?.map(
          (item) => (item?.key as string) || (item.value as string)
        ) || [],
      address: valueRef?.position?.map((item) => item?.key as string) || [],
      expectedSalary: valueRef?.expectedSalary
        ? String(moneyToNumber(valueRef?.expectedSalary))
        : "",
      currencyUnit: valueRef?.expectedSalary
        ? (valueRef?.currencyUnit?.key as string)
        : "",
      language: valueRef?.language?.value || "",
      // keyword: valueRef?.keyword?.trim(),
      yearOfExp: valueRef?.yearOfExp?.key || "",
      source: valueRef?.source?.map((item) => item?.key as string) || [],
      currentPage: 1,
    };
    onSearchParams(valueSearch);
  };

  const onResetForm = () => {
    formikRef?.current?.resetForm();
    onResetCurrentPage();
    hasContactRef.current = false;
    onSearchParams({
      name: "",
      currentPosition: "",
      company: "",
      previousPosition: "",
      hasContact: false,
      skills: [],
      currentPage: 1,
    });
  };

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  function renderFormSearch(values: DataFormSearchPotential): JSX.Element {
    return (
      <div className="ui-form-search">
        <SelectInput
          data={workLocations}
          name="position"
          labelselect="Địa điểm làm việc"
          mode="multiple"
          value={values?.position}
          free={values?.position.length === 0}
          allowClear
        />
        {/* <TextInput
          name="keyword"
          label="Từ khóa"
          containerclassname="mt-2"
          value={values?.keyword}
          free={!values?.keyword}
        /> */}

        <div className="flex w-full mt-2 salary-expect">
          <TextInput
            containerclassname="flex-1"
            label="Mức lương"
            name="expectedSalary"
            onlynumber
            typeInput="salary"
            iscurrency
            value={values.expectedSalary}
            free={!values.expectedSalary}
            maxLength={100}
          />
          <AppSelectCurrency
            name="currencyUnit"
            style={{width: "80px"}}
            defaultValue="VND"
          />
        </div>
        <SelectInput
          name="language"
          labelselect="Ngoại ngữ"
          data={languages}
          containerclassname="mt-2"
          value={values?.language?.value}
          free={!values?.language?.value}
          allowClear
        />
        <SelectInput
          name="yearOfExp"
          labelselect="Kinh nghiệm"
          data={listExperience}
          containerclassname="mt-2"
          value={values?.yearOfExp?.value}
          free={!values?.yearOfExp?.value}
          allowClear
        />
        <SelectInput
          name="source"
          labelselect="Nguồn ứng viên"
          data={candidateSource}
          containerclassname="mt-2"
          value={values?.source || []}
          free={values?.source?.length === 0}
          mode="multiple"
          allowClear
        />
        <Row className="mt-2" gutter={[16, 16]}>
          <Col xs={12}>
            <AppButton
              label="Xoá tất cả"
              typebutton="secondary"
              onClick={onResetForm}
            />
          </Col>
          <Col xs={12}>
            <AppButton
              label="Tìm kiếm"
              typebutton="primary"
              onClick={handleAdvanceSearch}
            />
          </Col>
        </Row>
      </div>
    );
  }
  return (
    <Formik
      initialValues={initialValueForm}
      innerRef={formikRef}
      onSubmit={() => {
        //
      }}
    >
      {({values, handleChange, handleBlur, handleSubmit, handleReset}) => {
        return (
          <form
            onSubmit={(e: FormEvent<HTMLFormElement>): void => {
              e.preventDefault();
            }}
          >
            <div className="search-form">
              <Row gutter={[8, 8]} justify="space-between" className="w-full">
                <Row gutter={[16, 16]} justify="start" className="w-3/4">
                  <Col xs={6}>
                    <TextInput
                      name="name"
                      label="Tên ứng viên"
                      onChange={(e: any) => handleSearchNormal(e)}
                      disabled={showFilterAdvance}
                      value={values.name}
                    />
                  </Col>
                  <Col xs={6}>
                    <TextInput
                      name="currentPosition"
                      label="Vị trí hiện tại"
                      onChange={(e: any) => handleSearchNormal(e)}
                      disabled={showFilterAdvance}
                      value={values.currentPosition}
                    />
                  </Col>
                  {/* <Col xs={4}>
                  <TextInput
                    name="company"
                    label="Công ty"
                    onChange={(e: any) => handleSearchNormal(e)}
                    disabled={showFilterAdvance}
                    value={values.company}
                  />
                </Col>
                <Col xs={4}>
                  <TextInput
                    name="previousPosition"
                    label="Những vị trí đã làm"
                    onChange={(e: any) => handleSearchNormal(e)}
                    disabled={showFilterAdvance}
                    value={values.previousPosition}
                  />
                </Col> */}
                  <Col xs={6}>
                    <SelectInput
                      name="skills"
                      labelselect="Kĩ năng"
                      data={skills}
                      mode="tags"
                      onSelect={(e: any) => handleSearchNormal(e)}
                      onDeselect={handleSearchNormal}
                      onClear={handleSearchNormal}
                      value={values?.skills}
                      free={values?.skills?.length === 0}
                      allowClear
                      disabled={showFilterAdvance}
                    />
                  </Col>
                  <Col xs={6} className="flex justify-center">
                    <AppCheckBox
                      className="check-box m-auto"
                      name="hasContact"
                      checked={hasContactRef.current}
                      onChange={(e: any) => handleSearchNormal(e)}
                      disabled={showFilterAdvance}
                    >
                      Có thông tin liên lạc
                    </AppCheckBox>
                  </Col>
                </Row>

                <Col>
                  <Popover
                    content={renderFormSearch(values)}
                    trigger={["click"]}
                    placement="bottomLeft"
                    open={showFilterAdvance}
                    onOpenChange={setShowFilterAdvance}
                    getPopupContainer={(trigger): any => trigger.parentElement}
                  >
                    <AppButton typebutton="normal" classrow="btn-filter">
                      <Icon
                        className="mr-1"
                        icon="filter-line"
                        size={12}
                        color="#324054"
                      />
                      Tìm kiếm nâng cao
                    </AppButton>
                  </Popover>
                </Col>
              </Row>
            </div>
          </form>
        );
      }}
    </Formik>
  );
}
