import ApiCollaborator, {
  ICareerType<PERSON>ist,
  ICollaborator,
  IParamEditCollaborator,
  IRecruitmentTypeList,
} from "@app/api/ApiCollaborator";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import config from "@app/config";
import {
  convertValuesToOptionsSelect,
  getAbbreviatedName,
  validatePhoneNumber,
} from "@app/utils/constants/function";
import {
  getStatusCollaborator,
  statusCollaborator,
} from "@app/utils/constants/state";
import {Avatar, Col, Row, notification} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useRef} from "react";
import "./index.scss";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import AppDatePicker from "@app/components/AppDatePicker";
import {UseQueryResult, useMutation} from "react-query";
import AppButton from "@app/components/AppButton";
import moment from "moment";
import {messageValidateEmail} from "@app/utils/constants/message";
import {IAccountRole, OptionSelect} from "@app/types";
import {selectUser} from "@app/redux/slices/UserSlice";
import {useSelector} from "react-redux";
import {REGEX_EMAIL} from "@app/utils/constants/regex";

interface CollaboratorEditProp {
  careerTypeList: ICareerTypeList[];
  recruitmentTypeList: IRecruitmentTypeList[];
  getCareerType: () => OptionSelect;
  getRecruitmentType: () => OptionSelect;
  requestCollaboratorDetail: UseQueryResult<ICollaborator, unknown>;
  onCancel: () => void;
  managerFilters: OptionSelect[];
  tagsOfCollaborator: string[];
}

export default function CollaboratorEdit(
  props: CollaboratorEditProp
): JSX.Element {
  const {
    careerTypeList,
    recruitmentTypeList,
    getCareerType,
    getRecruitmentType,
    requestCollaboratorDetail,
    onCancel,
    managerFilters,
    tagsOfCollaborator,
  } = props;
  const collaborator = requestCollaboratorDetail?.data;
  const refInfoData = useRef<FormikProps<any>>(null);
  const {user} = useSelector(selectUser);
  const isCSL =
    user?.role?.includes(IAccountRole.CSL) ||
    user?.role?.includes(IAccountRole.ADMIN);
  const isCST = user?.role?.includes(IAccountRole.CST);

  const editCollaborator = useMutation(
    (info: IParamEditCollaborator) => ApiCollaborator.editCollaborator(info),
    {
      onSuccess: () => {
        notification.success({
          message: "Thành công",
          description: "Cập nhật thông tin ctv thành công",
          duration: 3,
        });
        requestCollaboratorDetail.refetch();
        onCancel();
      },
    }
  );

  const getManager = (): OptionSelect => {
    if (!collaborator?.consultantId && isCST) {
      return {
        value: user?.name || "",
        key: user?.userId,
        label: user?.name || "",
      };
    }

    if (!isCSL) {
      return {
        value: collaborator?.consultantName || "",
        key: collaborator?.consultantId
          ? String(collaborator?.consultantId)
          : "",
        label: collaborator?.consultantName || "",
      };
    }

    if (!collaborator?.consultantId) {
      return {
        value: "",
        key: "",
        label: "",
      };
    }

    return (
      managerFilters.find(
        (e) => e.key === String(collaborator?.consultantId)
      ) || {
        value: "",
        key: "",
        label: "",
      }
    );
  };

  const getStatusSelect = (statusId?: number): OptionSelect[] => {
    const result: OptionSelect[] = [];
    // eslint-disable-next-line array-callback-return
    statusCollaborator.map((i, index) => {
      // ko thể chuyển trạng thái từ mới sang khóa nên xóa option khóa
      if (statusId === 1 && i.id === 4) {
        return;
      }
      // ko thể chuyển trạng thái từ khóa, không hoạt động, hoat động sang mới nên xóa option mới
      if (statusId !== 1 && i.id === 1) {
        return;
      }
      result.push({...i, key: String(i.id)});
    });
    return result;
  };

  const getStatus = (): any => {
    const status = getStatusCollaborator(collaborator?.statusId);
    if (status.id === 0) {
      return null;
    }
    return {
      ...status,
      key: status.id,
    };
  };

  const handleUpdate = (): void => {
    const values = refInfoData.current?.values;
    if (
      !(
        values?.name &&
        values?.phoneNumber &&
        values?.email &&
        values?.managerSelected?.key &&
        values?.careerSelected?.key &&
        values?.recruitmentSelected?.key
      )
    ) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng nhập đầy đủ thông tin cộng tác viên",
      });
      return;
    }

    if (!values?.email?.match(REGEX_EMAIL)) {
      notification.error({
        message: "Thông báo",
        description: messageValidateEmail,
      });
      return;
    }

    if (!validatePhoneNumber(values?.phoneNumber)) {
      notification.error({
        message: "Thông báo",
        description: "Vui lòng nhập đúng số điện thoại",
      });
      return;
    }

    const info = {
      addressCode: values?.addressCode,
      addressDetail: values?.addressDetail,
      addressName: values?.addressName,
      careerTypeId: values?.careerSelected?.key,
      consultantId: values?.managerSelected?.key,
      consultantName: values?.managerSelected?.value,
      createdDate: values?.createdDate,
      dateOfBirth: values?.dateOfBirth
        ? moment(values?.dateOfBirth).format(DATE_FORMAT)
        : values?.dateOfBirth,
      email: values?.email,
      facebook: values?.facebook,
      gender: values?.genderSelect?.value === "Nam",
      image: values?.image,
      job: values?.job,
      linkein: values?.linkein,
      name: values?.name,
      note: values?.note,
      phoneNumber: values?.phoneNumber,
      profileId: values?.profileId,
      recommendationUserId: values?.recommendationUserId,
      recruitmentTypeId: values?.recruitmentSelected?.key,
      skype: values?.skype,
      statusId: values?.statusSelected?.key,
      statusName: values?.statusSelected?.value,
      userId: collaborator?.userId,
      tags: values?.tags?.map((tag: OptionSelect) => tag.value) || [],
    };
    ApiCollaborator.checkEmailExisted({
      email: values?.email,
      userId: collaborator?.userId,
    }).then((result): void => {
      if (!result) {
        editCollaborator.mutate(info);
      } else {
        notification.error({
          message: "Thông báo",
          description: "Email đã tồn tại",
        });
      }
    });
  };

  const initialValues = {
    ...collaborator,
    statusSelected: getStatus(),
    recruitmentSelected: getRecruitmentType().key ? getRecruitmentType() : null,
    careerSelected: getCareerType().key ? getCareerType() : null,
    managerSelected: getManager().key ? getManager() : null,
    genderSelect: collaborator?.gender
      ? {label: "Nam", value: "Nam"}
      : {label: "Nữ", value: "Nữ"},
    dateOfBirth: collaborator?.dateOfBirth
      ? moment(collaborator?.dateOfBirth, DATE_FORMAT)
      : "",
    tags: convertValuesToOptionsSelect(collaborator?.tags || []),
  };

  const handleValidMaxlengthTag = (e: any) => {
    const {keyCode} = e;
    const maxLengthTag = 50;
    const lengthTag = Number((e.target as any)?.value?.length) + 1;
    if (keyCode !== 8 && keyCode !== 9 && lengthTag > maxLengthTag) {
      e.preventDefault();
    }
  };

  return (
    <Formik
      innerRef={refInfoData}
      initialValues={initialValues}
      onSubmit={(): void => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <div className="block">
            <Row className="border-dot p-4">
              <Col span={7} className="items-center justify-center flex">
                {collaborator?.image ? (
                  <Avatar
                    size={150}
                    src={
                      config.NETWORK_CONFIG.API_BASE_URL +
                      "/" +
                      collaborator.image
                    }
                  />
                ) : (
                  <Avatar size={150} className="avatar-collaborator">
                    {getAbbreviatedName(collaborator?.name)}
                  </Avatar>
                )}
              </Col>
              <Col span={15}>
                <span className="font-bold text16">Thông tin cá nhân</span>
                <Row className="justify-between">
                  <Col span={11}>
                    <TextInput
                      containerclassname="mt-2"
                      label="Nhập họ tên"
                      name="name"
                      value={values?.name}
                      required
                      status={values?.name ? "" : "error"}
                    />
                    <TextInput
                      containerclassname="mt-2"
                      label="Email"
                      name="email"
                      value={values?.email}
                      required
                      status={values?.email ? "" : "error"}
                      disabled
                    />
                    <TextInput
                      containerclassname="mt-2"
                      label="Số điện thoại"
                      name="phoneNumber"
                      value={values.phoneNumber}
                      required
                      isphonenumber
                      status={
                        !values?.phoneNumber ||
                        !validatePhoneNumber(values?.phoneNumber)
                          ? "error"
                          : undefined
                      }
                    />
                    <AppDatePicker
                      classNameContainer="mt-2"
                      name="dateOfBirth"
                      label="Năm sinh"
                      format={DATE_FORMAT}
                      valueAppDatePicker={values?.dateOfBirth}
                      free={!values?.dateOfBirth}
                    />
                    <SelectInput
                      containerclassname="mt-2"
                      name="tags"
                      labelselect="Tag"
                      mode="tags"
                      data={convertValuesToOptionsSelect(tagsOfCollaborator)}
                      value={values?.tags}
                      allowClear
                      onInputKeyDown={handleValidMaxlengthTag}
                    />
                  </Col>
                  <Col span={11}>
                    <SelectInput
                      containerclassname="mt-2"
                      name="statusSelected"
                      labelselect="Trạng thái"
                      data={getStatusSelect(collaborator?.statusId)}
                      value={values?.statusSelected}
                    />
                    <TextInput
                      containerclassname="mt-2"
                      label="Facebook"
                      name="facebook"
                      value={values?.facebook}
                    />
                    <TextInput
                      containerclassname="mt-2"
                      label="Skype"
                      name="skype"
                      value={values?.skype}
                    />
                    <TextInput
                      containerclassname="mt-2"
                      label="LinkedIn"
                      name="linkein"
                      value={values?.linkein}
                    />
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row className="justify-between mt-3">
              <Col span={12} className="pr-2">
                <div className="border-dot pt-2 pb-12">
                  <span className="font-bold text16 pl-6">
                    Thông tin thanh toán
                  </span>
                  <div className="flex justify-center">
                    <Col span={18}>
                      <TextInput
                        containerclassname="mt-2"
                        label="CCCD/CMND"
                        name="identifyCardNo"
                        value={values?.identifyCardNo}
                        disabled
                      />
                      <AppDatePicker
                        classNameContainer="mt-2"
                        name="issuedOn"
                        label="Ngày cấp"
                        format={DATE_FORMAT}
                        disabled
                        valueAppDatePicker={values?.issuedOn}
                      />
                      <SelectInput
                        containerclassname="mt-2"
                        name="issuedAt"
                        labelselect="Nơi cấp"
                        data={[]}
                        disabled
                        value={
                          values.issuedAt
                            ? {
                                values: values?.issuedAt,
                                label: values?.issuedAt,
                              }
                            : null
                        }
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Mã số thuê"
                        name="taxCode"
                        value={values?.taxCode}
                        disabled
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Số tài khoản ngân hàng"
                        name="bank_AccountNumber"
                        value={values?.bank_AccountNumber}
                        disabled
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Ngân hàng"
                        name="bank_Name"
                        value={values?.bank_Name}
                        disabled
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Chi nhánh"
                        name="bank_Branch"
                        value={values?.bank_Branch}
                        disabled
                      />
                    </Col>
                  </div>
                </div>
              </Col>
              <Col span={12} className="pl-2">
                <div className="border-dot pt-2 pb-12">
                  <span className="font-bold text16 pl-6">
                    Thông tin cá nhân
                  </span>
                  <div className="flex justify-center">
                    <Col span={18}>
                      <TextInput
                        containerclassname="mt-2"
                        label="Nghề nghiệp"
                        name="job"
                        value={values?.job}
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Địa chỉ"
                        name="addressName"
                        value={values?.addressName}
                      />
                      <SelectInput
                        containerclassname="mt-2"
                        name="recruitmentSelected"
                        labelselect="Ngành nghề tuyển dụng"
                        value={values.recruitmentSelected}
                        data={recruitmentTypeList.map((i) => ({
                          value: i.name,
                          label: i.name,
                          key: i.recruitmentTypeId,
                        }))}
                        required
                        status={!values.recruitmentSelected ? "error" : ""}
                      />
                      <SelectInput
                        containerclassname="mt-2"
                        name="careerSelected"
                        labelselect="Hình thức làm việc"
                        value={values.careerSelected}
                        data={careerTypeList.map((i) => ({
                          value: i.name,
                          label: i.name,
                          key: i.careerTypeId,
                        }))}
                        required
                        status={!values.careerSelected ? "error" : ""}
                      />
                      <SelectInput
                        containerclassname="mt-2"
                        name="managerSelected"
                        labelselect="Người quản lý"
                        value={values.managerSelected}
                        data={managerFilters}
                        required
                        status={!values.managerSelected ? "error" : ""}
                        disabled={!isCSL}
                      />
                      <SelectInput
                        containerclassname="mt-2"
                        name="genderSelect"
                        labelselect="Giới tính"
                        value={values.genderSelect}
                        data={[
                          {label: "Nam", value: "Nam"},
                          {label: "Nữ", value: "Nữ"},
                        ]}
                      />
                      <TextInput
                        containerclassname="mt-2"
                        label="Người giới thiệu"
                        name="recommendationEmail"
                        value={values?.recommendationEmail}
                        disabled
                      />
                    </Col>
                  </div>
                </div>
              </Col>
            </Row>
            <Row className="flex justify-center mt-10 items-center mb-12">
              <AppButton
                classrow="mr-2 w-64"
                label="Hủy bỏ"
                typebutton="secondary"
                onClick={onCancel}
              />
              <AppButton
                classrow="ml-2 w-64"
                label="Lưu thông tin"
                typebutton="primary"
                onClick={handleUpdate}
              />
            </Row>
          </div>
        );
      }}
    </Formik>
  );
}
