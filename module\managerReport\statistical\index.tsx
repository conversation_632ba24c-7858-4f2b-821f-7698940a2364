import {IApplication} from "@app/api/ApiApplication";
import {IResponseReportManager} from "@app/api/ApiReport";
import config from "@app/config";
import {Skeleton} from "antd";
import classNames from "classnames";
import React from "react";

interface IStatistical {
  data: IResponseReportManager | undefined;
  className?: string;
  loading: boolean;
  onOpenModalTableCV: (data: IApplication[]) => void;
}

interface IItemReport {
  label: string;
  count: number;
  pathRedirect?: string;
  key?: string;
}

export default function Statistical({
  data,
  className,
  loading,
  onOpenModalTableCV,
}: IStatistical): JSX.Element {
  // const dispatch = useDispatch();

  const searchParams = new URLSearchParams(window.location.search);
  const searchParamsObject = Object.fromEntries(searchParams);

  // const {queryParamsPages} = useSelector(selectUser);
  // const urlSearchParamsManagerApplication = new URLSearchParams(
  //   queryParamsPages?.managerApplication || ""
  // );
  // const objectSearchParamsManagerApplication = Object.fromEntries(
  //   urlSearchParamsManagerApplication
  // );

  const renderListReport: IItemReport[] = [
    {
      label: "CV đã tải lên",
      count: data?.cvUploadCount || 0,
      pathRedirect: `/${config.PATHNAME.MANAGER_CANDIDATE}?createdFrom=${searchParamsObject?.startDate}&createdTo=${searchParamsObject?.endDate}`,
    },
    {
      label: "Ứng viên phỏng vấn",
      count: data?.candidateInterviewCount || 0,
      // pathRedirect: config.PATHNAME.MANAGER_APPLICATION,
      key: "interview",
    },
    {
      label: "Ứng viên được offer",
      count: data?.candidateOffercount || 0,
      // pathRedirect: config.PATHNAME.MANAGER_APPLICATION,
      key: "offer",
    },
    {
      label: "Job đang chạy",
      count: data?.requestJobRunning || 0,
    },
    {
      label: "Job hunt mới",
      count: data?.requestJobNewHunt || 0,
    },
    {
      label: "Job expert mới",
      count: data?.requestJobNewExpert || 0,
    },
  ];

  const handleClickStatistical = (item: IItemReport) => {
    // if (!item.pathRedirect) return;

    if (item.key) {
      // let newSearchParamsManagerApplication: any = {
      //   ...objectSearchParamsManagerApplication,
      //   from: searchParamsObject?.startDate,
      //   to: searchParamsObject?.endDate,
      // };
      if (item.key === "interview") {
        // newSearchParamsManagerApplication = {
        //   ...newSearchParamsManagerApplication,
        //   stages: JSON.stringify([
        //     stagesApplication.find((item) => item.key === "2"),
        //   ]),
        // };
        onOpenModalTableCV(data?.listAppLicationInterview || []);
      }

      if (item.key === "offer") {
        // newSearchParamsManagerApplication = {
        //   ...newSearchParamsManagerApplication,
        //   stages: JSON.stringify([
        //     stagesApplication.find((item) => item.key === "3"),
        //   ]),
        // };

        onOpenModalTableCV(data?.listAppLicationOffer || []);
      }

      // const newSearchParamsManagerApplicationString = new URLSearchParams(
      //   newSearchParamsManagerApplication
      // ).toString();

      // dispatch(
      //   setQueryParamsPages({
      //     managerApplication: newSearchParamsManagerApplicationString,
      //   })
      // );
      return;
    }

    if (item.pathRedirect) {
      window.open(window.origin + item.pathRedirect, "_blank")?.focus();
    }
  };

  const dataTopReport = [
    {
      title: "Top 3 nhân sự log nhiều CV",
      data: data?.memberLogCV,
      product: "CV",
    },
    {
      title: "Top 3 nhân sự nhiều offer",
      data: data?.memberhasOffer,
      product: "offer",
    },
  ];

  return (
    <div>
      <div className={classNames("flex gap-6 flex-wrap", className)}>
        {renderListReport.map((item, index) => (
          <div
            role="button"
            tabIndex={0}
            key={index}
            className={classNames(
              "bg-[#E5E8EE] w-[115px] h-[115px] rounded-lg text-center flex flex-col justify-between px-2 py-2 cursor-auto",
              (item?.key || item?.pathRedirect) && "!cursor-pointer"
            )}
            onClick={() => handleClickStatistical(item)}
          >
            <div className="text-base">{item?.label}</div>
            {loading ? (
              <Skeleton.Button shape="round" active />
            ) : (
              <div className="text-[32px]">{item?.count}</div>
            )}
          </div>
        ))}
      </div>
      <div className="mt-5 flex flex-col gap-4">
        {dataTopReport.map((top, index) => {
          const renderData = top.data?.length ? (
            top.data?.map((item) => (
              <li className="text-xs" key={item.userId}>
                {`${item?.name} - ${item?.number} ${top.product}`}
              </li>
            ))
          ) : (
            <div className="text-center">No Data</div>
          );

          return (
            <div className="rounded-2xl shadow-md px-4 py-2" key={index}>
              <h2 className="text-base font-bold">{top.title}</h2>
              <ol className="list-inside list-decimal ml-1 flex flex-col gap-1.5 mt-2">
                {loading ? (
                  <Skeleton paragraph={{rows: 2}} active round />
                ) : (
                  renderData
                )}
              </ol>
            </div>
          );
        })}
      </div>
    </div>
  );
}
