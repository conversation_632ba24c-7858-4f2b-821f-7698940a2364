import {IApplicationDetail} from "@app/api/ApiApplication";
import AppDatePicker from "@app/components/AppDatePicker";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {Col, Row} from "antd";
import {Formik, FormikProps} from "formik";
import moment from "moment";
import React, {useEffect} from "react";
import "./index.scss";
import {UseQueryResult} from "react-query";
// eslint-disable-next-line import/no-cycle
import {FormUpdate} from "../ModalDetailApplication";
import {
  currencyList,
  salaryType,
  stagesApplication,
  statusesApplication,
} from "@app/utils/constants/state";
import {Input} from "formik-antd";
import Icon from "@app/components/Icon/Icon";
import {IAccountRole, OptionSelect} from "@app/types";
import {selectUser} from "@app/redux/slices/UserSlice";
import {useSelector} from "react-redux";

interface FormStatusProps {
  detailApplication: UseQueryResult<IApplicationDetail, unknown>;
  refFormStatus: React.RefObject<FormikProps<FormUpdate>>;
}
const {TextArea} = Input;

function FormStatus(props: FormStatusProps): JSX.Element {
  const {detailApplication, refFormStatus} = props;
  const application = detailApplication.data;
  const {user} = useSelector(selectUser);
  const isRoleAM = [IAccountRole.AMG, IAccountRole.AML].some((role) =>
    user?.role?.includes(role)
  );

  const getStage = (): OptionSelect | undefined => {
    let stage = stagesApplication?.find(
      (i) => Number(i.id) === application?.stage
    );
    if (stage) {
      stage = {...stage, key: stage.id as string | null};
    }
    return stage;
  };

  const getStatus = (): OptionSelect | undefined => {
    if (!application?.status) {
      return {
        id: null,
        label: "Chưa hoàn thành",
        value: "Chưa hoàn thành",
        key: null,
      };
    }
    let status = statusesApplication?.find(
      (i) => Number(i.id) === application?.status
    );
    if (status) {
      status = {...status, key: status.id as string | null};
    }
    return status;
  };

  const getCurrencyTypeId = (): OptionSelect => {
    const currencyTypeIdObj = currencyList?.find(
      (i) => i.value === application?.currencyTypeId
    ) || {
      value: "VND",
      label: "VND",
    };

    return currencyTypeIdObj;
  };

  const disabledEdit = application?.stage === 3 && application?.status === 5;

  const initialValues: FormUpdate = {
    stageSelected: getStage(),
    statusSelected: getStatus(),
    currencyTypeIdSelected: getCurrencyTypeId(),
    salaryOffered: application?.salaryOffered,
    stagePersonCare: application?.stagePersonCare,
    stageDateStarted: application?.stageDateStarted
      ? moment(application?.stageDateStarted, DATE_FORMAT)
      : undefined,
    salaryExpected: application?.salaryExpected,
    currentSalary: application?.currentSalary,
    trailWorkTime: application?.trailWorkTime,
    description: application?.description,
    currentSalaryTypeSelected:
      application?.currentSalaryType === 0 ? salaryType[0] : salaryType[1],
    salaryExpectedTypeSelected:
      application?.salaryExpectedType === 0 ? salaryType[0] : salaryType[1],
    salaryOfferedTypeSelected:
      application?.salaryOfferedType === 0 ? salaryType[0] : salaryType[1],
    summary: application?.summary,
  };

  useEffect(() => {
    if (refFormStatus.current && application) {
      refFormStatus.current?.setValues(initialValues);
    }
  }, [detailApplication]);

  const onChangeStage = (value: OptionSelect): void => {
    refFormStatus.current?.setValues({...initialValues, stageSelected: value});
  };

  const onChangeStatus = (
    statusSelected: OptionSelect,
    values: FormUpdate
  ): void => {
    const currentStageIndex = stagesApplication?.findIndex(
      (item) => Number(item.key) === Number(values?.stageSelected?.key)
    );

    const nextStageIndex = currentStageIndex + 1;

    const nextStage = stagesApplication?.[nextStageIndex];

    // statusesApplication value Pass = "3"
    if (
      statusSelected.key === "3" &&
      currentStageIndex < stagesApplication.length - 1
    ) {
      refFormStatus.current?.setValues({
        ...initialValues,
        stageSelected: nextStage,
        statusSelected: statusesApplication[0],
      });
      return;
    }

    refFormStatus.current?.setValues({
      ...initialValues,
      stageSelected: values.stageSelected,
      statusSelected: statusSelected,
    });
  };

  const disableStatus = (
    idStatus?: string | null,
    idStage?: string | null
  ): boolean => {
    if (
      !idStatus &&
      Number(idStage) === application?.stage &&
      !!application?.status
    ) {
      return true;
    }

    if (
      idStatus === "5" &&
      !(application?.stage === 3 && application?.status === 3)
    ) {
      return true;
    }

    return false;
  };

  const renderContent = (values: FormUpdate): JSX.Element | null => {
    if (
      values?.stageSelected?.key === "1" ||
      values?.stageSelected?.key === "2"
    ) {
      return (
        <Row>
          <Col span={12} className="pr-2">
            <TextInput
              containerclassname="mt-2"
              label={`Người ${
                values?.stageSelected?.key === "1" ? "review" : "phỏng vấn"
              }`}
              name="stagePersonCare"
              value={values.stagePersonCare}
            />
          </Col>
          <Col span={12} className="pl-2">
            <AppDatePicker
              classNameContainer="mt-2"
              name="stageDateStarted"
              label={`Ngày ${
                values?.stageSelected?.key === "1" ? "review" : "phỏng vấn"
              }`}
              format={DATE_FORMAT}
              free={!values?.stageDateStarted}
              valueAppDatePicker={values?.stageDateStarted}
            />
          </Col>
        </Row>
      );
    }

    const disabled =
      !(
        values?.stageSelected?.key === "3" && values.statusSelected?.key === "5"
      ) || disabledEdit;

    if (values?.stageSelected?.key === "3" && !isRoleAM) {
      return (
        <Row>
          <Col span={12} className="pr-2">
            <AppDatePicker
              classNameContainer="mt-2"
              name="stageDateStarted"
              label="Ngày onboard"
              format={DATE_FORMAT}
              valueAppDatePicker={values?.stageDateStarted}
              disabled={disabled}
              free={false}
            />
            <TextInput
              containerclassname="mt-2"
              label="Lương hiện tại"
              name="currentSalary"
              onlynumber
              iscurrency
              value={values?.currentSalary}
              typeInput="salary"
              maxLength={50}
              disabled={disabledEdit}
            />
            <TextInput
              containerclassname="mt-2"
              label="Lương mong muốn"
              name="salaryExpected"
              onlynumber
              iscurrency
              value={values?.salaryExpected}
              typeInput="salary"
              maxLength={50}
              disabled={disabledEdit}
            />
            <TextInput
              containerclassname="mt-2"
              label="Lương offer"
              name="salaryOffered"
              onlynumber
              iscurrency
              value={values?.salaryOffered}
              typeInput="salary"
              maxLength={50}
              disabled={disabledEdit}
            />
          </Col>
          <Col span={12} className="pl-2">
            <TextInput
              containerclassname="mt-2"
              label="Thời gian thử việc"
              name="trailWorkTime"
              onlynumber
              value={values?.trailWorkTime}
              maxLength={50}
              disabled={disabled}
            />
            <Row className="mt-2 input-salary">
              <AppSelectCurrency
                name="currencyTypeIdSelected"
                value={values?.currencyTypeIdSelected}
                style={{width: "80px"}}
                disabled={disabledEdit}
                isAlone
              />
              <AppSelectCurrency
                classNameContainer="ml-2"
                name="currentSalaryTypeSelected"
                value={values?.currentSalaryTypeSelected}
                style={{width: "90px"}}
                disabled={disabledEdit}
                options={salaryType}
                isAlone
              />
            </Row>
            <Row className="mt-2 input-salary">
              <AppSelectCurrency
                name="currencyTypeIdSelected"
                value={values?.currencyTypeIdSelected}
                style={{width: "80px"}}
                disabled
                isAlone
              />
              <AppSelectCurrency
                classNameContainer="ml-2"
                name="salaryExpectedTypeSelected"
                value={values?.salaryExpectedTypeSelected}
                style={{width: "90px"}}
                disabled={disabledEdit}
                options={salaryType}
                isAlone
              />
            </Row>
            <Row className="mt-2 input-salary">
              <AppSelectCurrency
                name="currencyTypeIdSelected"
                value={values?.currencyTypeIdSelected}
                style={{width: "80px"}}
                disabled
                options={salaryType}
                isAlone
              />
              <AppSelectCurrency
                classNameContainer="ml-2"
                name="salaryOfferedTypeSelected"
                value={values?.salaryOfferedTypeSelected}
                style={{width: "90px"}}
                disabled={disabledEdit}
                options={salaryType}
                isAlone
              />
            </Row>
          </Col>
        </Row>
      );
    }

    return null;
  };

  return (
    <Formik
      innerRef={refFormStatus}
      initialValues={initialValues}
      onSubmit={(): void => {
        //
      }}
    >
      {({values}): JSX.Element => {
        const isDisabledStage = (itemStage: OptionSelect) => {
          return (
            Number(itemStage.order) <
            (stagesApplication.find(
              (item) =>
                Number(item.key || item.id) === Number(application?.stage)
            )?.order || 0)
          );
        };

        return (
          <div className="mt-3 form-status">
            <span className="title-job">Thông tin người tạo</span>
            <Row className="mt-1">
              <Row className="w-1/2 ">
                <div>
                  <Icon
                    size={14}
                    icon="account-circle-line"
                    color="#324054"
                    className=""
                  />
                </div>
                <span className="text-property-candidate ml-4">
                  {detailApplication.data?.creator?.name || "N/A"}
                </span>
              </Row>
              <Row className="w-1/2 pl-2">
                <div>
                  <Icon
                    size={14}
                    icon="phone-line"
                    color="#324054"
                    className=""
                  />
                </div>
                <span className="text-property-candidate ml-4">
                  {detailApplication.data?.creator?.phoneNumber || "N/A"}
                </span>
              </Row>
              <Row className="w-1/2">
                <div>
                  <Icon
                    size={14}
                    icon="mail-line"
                    color="#324054"
                    className=""
                  />
                </div>
                <span className="text-property-candidate ml-4">
                  {detailApplication.data?.creator?.email || "N/A"}
                </span>
              </Row>
            </Row>
            <Row>
              <Col span={12} className="pr-2">
                <SelectInput
                  containerclassname="mt-2"
                  name="stageSelected"
                  labelselect="Giai đoạn"
                  data={
                    stagesApplication?.map((i: OptionSelect) => ({
                      ...i,
                      disabled: isDisabledStage(i),
                    })) || []
                  }
                  value={values.stageSelected}
                  disabled={disabledEdit}
                  handleChange={onChangeStage}
                />
              </Col>
              <Col span={12} className="pl-2">
                <SelectInput
                  containerclassname="mt-2"
                  name="statusSelected"
                  labelselect="Trạng thái"
                  data={
                    statusesApplication?.map((i: OptionSelect) => ({
                      ...i,
                      disabled: disableStatus(
                        i.id as string | null,
                        values.stageSelected?.key
                      ),
                    })) || []
                  }
                  value={values.statusSelected}
                  disabled={disabledEdit}
                  handleChange={(value) => onChangeStatus(value, values)}
                />
              </Col>
            </Row>
            {renderContent(values)}
            <div className="text14 mt-2 font-bold">Ghi chú cho CTV</div>
            <TextArea
              name="summary"
              maxLength={1000}
              className="input-note mt-1"
              style={{height: 110, resize: "none"}}
              placeholder="Ghi chú cho CTV"
              value={values.summary}
            />
            <div className="text14 mt-2 font-bold">Ghi chú cho reco</div>
            <TextArea
              name="description"
              maxLength={1000}
              className="input-note mt-1"
              style={{height: 110, resize: "none"}}
              placeholder="Ghi chú cho reco"
              value={values.description}
            />
          </div>
        );
      }}
    </Formik>
  );
}

export default FormStatus;
