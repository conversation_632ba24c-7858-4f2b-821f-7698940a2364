# Style

## 1. SCSS
### Cách tạo và sử dụng file scss
```
📦src
 ┣ 📂modules
 ┃ ┣ 📂login
 ┃ ┃ ┣ 📜index.tsx
 ┃ ┃ ┣ 📃index.scss
```

Mỗi file scss đều được tạo chung với component.

File index.tsx
```tsx
import "./index.scss" // Lưu ý phải bổ sung file index.scss vào

export default function Login() {
  return (
    <div className='login-wrapper'>
        <div className='login-wrapper-header'>
            <div className='login-wrapper-header-title'>
                ...
            </div>
            ...
        </div>
        <div className='login-wrapper-body'>
            ...
        </div>
    </div>
  )
}
```

File index.scss
```scss
.login-wrapper {
  background: yellow;
  
  &:hover {
    cursor: pointer;
  }
  
  .login-wrapper-header {
    &:after {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      bottom: 32px;
      left: 0;
      background: linear-gradient(180deg, rgba(11, 14, 18, 0.12) 0%, rgba(252, 80, 80, 0.6) 100%);
      border-radius: 2px;
    }
    
    .login-wrapper-header-title {
      ...
    }
  }
  
  .login-wrapper-body {
    ...
  }
}
```

### Lưu trữ các biến để dùng chung nhiều chỗ cho SCSS
SCSS cho phép chúng ta viết biến để sử dụng tại nhiều chỗ nên khi cần đổi lại sẽ đỡ vất vả hơn. Để đạt được điều đó thì ta phải viết các biến màu, font size, line height 

Các biến global scss được viết trong file `styles/_global.scss`
```scss
$border-color: #dee2e6;
$text-color: #000000;
$white-color: #ffffff;
$red-color: #cb2131;
$blue-color: #0092ff;
```
Các sử dụng. Ví dụ tại file ```module/home/<USER>/index.scss```
```scss
.login-wrapper {
  color: $red-color;
  ...
}
```

Bên cạnh đó, chúng ta có thể sử dụng class có chứa css đó
```scss
//File _global.scss
$color-app: (
"c-warning": #e05347,
"c-primary-1": #0053cc,
"c-primary-2": #9757d7,
"c-primary-3": #ff6838,
"c-primary-4": #58bd7d,
"c-secondary-1": #63e59a,
"c-secondary-2": #e4d7cf,
) !default;
```
Ngoài ra có thể sử dụng màu bằng class như sau. Ví dụ sử dụng biến màu `c-primary-1`
```tsx
export default function Login() {
  return (
    <div className='login-wrapper c-primary-1'>
        ...
    </div>
  )
}
```

# 2. Tailwindcss

Tailwindcss là utility css cho phép chúng ta dùng class thay cho css. Ví dụ như thay vì viết style `margin-top: 1rem` thì ta có thể sử dụng class `mt-4`

```tsx
export default function Login() {
  return (
    <div className='mt-4'>
        ...
    </div>
  )
}
```

Tham khảo thêm các class tại đây: https://tailwindcss.com/docs/

# 3. Các class tự viết global
Ngoài ra ta có thể tự viết các class tại thư mục `styles/mixin` rồi bổ sung vào file `styles/_app.scss` và tất cả các chỗ khác có thể dùng được

Ví dụ 
File `styles/mixin/_event.scss`
```scss
.no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

.hover-pointer {
  &:hover {
    cursor: pointer !important;
  }
}

.hover-grab {
  &:hover {
    cursor: grab !important;
  }
}
```
File `styles/_app.scss`
```scss
@import "global";

@import "mixin/event";
@import "mixin/shape";  // Import vào file vừa khai báo
```
Chỗ sử dụng không cần phải import file này vào nếu muốn dùng. Ví dụ
```tsx
export default function Login() {
  return (
    <div className='hover-pointer'>
        ...
    </div>
  )
}
```

## 4. Lưu ý: 
- Luôn đặt tên class cha để bọc các class con nếu không sẽ ảnh hưởng đến các component khác
- Không đặt tên class theo kiểu BEM
- Không đặt tên class trùng nhau
- Tránh dùng style để style mà dùng class thay thế (Trong trường hợp class không đáp ứng được thì vẫn sẽ được dùng style)
- Tránh dùng quá nhiều class để style component nếu không trông code sẽ quá dài (Viết class riêng cho trường hợp đó nếu dài )
- Khi tự làm thêm class global thì phải kiểm tra xem tên có bị trùng với các class khác không
