import AppDatePicker from "@app/components/AppDatePicker";
import AppModal from "@app/components/AppModal";
import AppSelectCurrency from "@app/components/AppSelectCurrency";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import {Col, Row, notification} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useCallback, useEffect, useRef} from "react";
import "./index.scss";
import AppButton from "@app/components/AppButton";
import ApiApplication, {IParamPayment} from "@app/api/ApiApplication";
import {useMutation, useQuery} from "react-query";
import moment, {Moment} from "moment";
import {
  deadTimeFastSearch,
  optionGuarantee,
  statusRateCurrencyPercent,
} from "@app/utils/constants/state";
import {
  getMonthWarrantySelected,
  moneyToNumber,
} from "@app/utils/constants/function";
import {useDispatch} from "react-redux";
import {setLoading} from "@app/redux/slices/SystemSlice";
import {
  messageCheckPartnerPaymentExist,
  messageValidate,
} from "@app/utils/constants/message";
import {OptionSelect} from "@app/types";

interface FormUpdate {
  paymentId?: number;
  applicationId?: number;
  requestJobId?: number;
  salaryOffered?: number;
  currencyOfferedType?: string;
  salaryOfferedExchangeRate?: number;
  paymentRate?: number;
  paymentRateType?: boolean;
  paymentRateUnit?: string;
  monthWarranty?: string;
  totalAmount?: number;
  onboardDate?: Moment | string | null;
  monthWarrantySelected: OptionSelect;
  currencyOfferedTypeSelected: OptionSelect;
  paymentRateTypeSelected: OptionSelect;
  paymentDateExpected: Moment | string | null;
  salaryConvert?: number;
}

interface ModalCreatePaymentProps {
  isShow: boolean;
  setIsShow: (isShow: boolean) => void;
  applicationId: number;
  onSuccess?: () => void;
  createdDateCandidate?: string;
}

export default function ModalCreatePayment(
  props: ModalCreatePaymentProps
): JSX.Element {
  const {isShow, setIsShow, applicationId, onSuccess, createdDateCandidate} =
    props;
  const dispatch = useDispatch();
  const refPayment = useRef<FormikProps<FormUpdate>>(null);
  const getPaymentByApplicationId = useQuery(
    ["getPaymentByApplicationId", applicationId],
    () => {
      return ApiApplication.getPaymentByApplicationId(applicationId);
    },
    {
      enabled: isShow,
    }
  );

  const createPartnerPayment = useMutation(
    (data: IParamPayment) => {
      return ApiApplication.createPartnerPayment(data);
    },
    {
      onSuccess: (data) => {
        if (data === true) {
          notification.success({
            message: "Thông báo",
            description: "Tạo thanh toán thành công",
            duration: 3,
          });
          onSuccess?.();
        }
      },
    }
  );

  const checkPartnerPaymentExist = useQuery(
    ["checkPartnerPaymentExistWhenCreate", applicationId],
    () => {
      return ApiApplication.checkPartnerPaymentExist(applicationId);
    },
    {
      onSuccess: (res) => {
        if (!res) {
          const values = refPayment.current?.values;
          const param: IParamPayment = {
            applicationId,
            currencyOfferedType:
              values?.currencyOfferedTypeSelected?.value || "VND",
            monthWarranty: values?.monthWarrantySelected?.value || "2",
            onboardDate: moment(values?.onboardDate).format(DATE_FORMAT),
            paymentDateExpected: moment(values?.paymentDateExpected).format(
              DATE_FORMAT
            ),
            paymentId: 0,
            paymentRate: values?.paymentRate,
            paymentRateType: values?.paymentRateTypeSelected?.value === "0", // ngược với trả về , paymentRateTypeSelected?.value===0  tỷ lệ true, : số tiền false
            paymentRateUnit: getPaymentByApplicationId?.data?.paymentRateUnit,
            requestJobId: getPaymentByApplicationId?.data?.requestJobId,
            salaryOffered: moneyToNumber(String(values?.salaryOffered)),
            salaryOfferedExchangeRate: values?.salaryOfferedExchangeRate
              ? moneyToNumber(String(values.salaryOfferedExchangeRate))
              : 1,
          };
          createPartnerPayment.mutate(param);
        } else {
          notification.error({
            message: "Thông báo",
            description: messageCheckPartnerPaymentExist,
            duration: 3,
          });
        }
      },
      enabled: false,
    }
  );

  useEffect(() => {
    dispatch(setLoading(createPartnerPayment.isLoading));
  }, [createPartnerPayment.isLoading]);

  const handleClose = (): void => {
    refPayment.current?.setValues(initialValues);
    setIsShow(false);
  };

  const data = getPaymentByApplicationId?.data;

  const initialValues: FormUpdate = {
    ...data,
    onboardDate: data?.onboardDate
      ? moment(data?.onboardDate, DATE_FORMAT)
      : null,
    monthWarrantySelected: getMonthWarrantySelected(data?.monthWarranty),
    currencyOfferedTypeSelected: data?.currencyOfferedType
      ? {value: data.currencyOfferedType, label: data?.currencyOfferedType}
      : {value: "VND", label: "VND"},
    // trả về paymentRateType : false tỷ lệ , : true  là số tiền
    paymentRateTypeSelected: !data?.paymentRateType
      ? statusRateCurrencyPercent[0]
      : statusRateCurrencyPercent[1],
    paymentDateExpected: data?.paymentDateExpected
      ? data?.paymentDateExpected
      : data?.onboardDate
      ? moment(data.onboardDate, DATE_FORMAT).add(10, "days")
      : null,
    salaryConvert: data?.salaryOffered,
  };

  useEffect(() => {
    if (refPayment.current && getPaymentByApplicationId?.data) {
      refPayment.current.setValues(initialValues);
    }
  }, [JSON.stringify(getPaymentByApplicationId?.data)]);

  const onChangeRateType = (): void => {
    refPayment.current?.setFieldValue("paymentRate", null);
    refPayment.current?.setFieldValue("totalAmount", 0);
  };

  const createPayment = (): void => {
    const values = refPayment.current?.values;
    if (values) {
      if (
        !(
          values?.onboardDate &&
          values?.paymentDateExpected &&
          values?.salaryOffered &&
          values?.paymentRate
        ) ||
        (values?.currencyOfferedTypeSelected?.value !== "VND" &&
          !values?.salaryOfferedExchangeRate)
      ) {
        notification.error({
          message: "Thông báo",
          description: messageValidate,
          duration: 3,
        });
      } else {
        checkPartnerPaymentExist.refetch();
      }
    }
  };

  const disableOnboardDate = (current: any): boolean => {
    return current && current < moment(createdDateCandidate).startOf("day");
  };

  const onChangeOnboardDate = (): void => {
    refPayment.current?.setFieldValue("paymentDateExpected", "");
  };

  return (
    <AppModal
      className="modal-create-payment"
      open={isShow}
      footer={null}
      onCancel={handleClose}
      width="60%"
      title="Tạo thông tin thanh toán cho cộng tác viên"
    >
      <Formik
        innerRef={refPayment}
        initialValues={initialValues}
        onSubmit={(): void => {
          //
        }}
      >
        {({values}): JSX.Element => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            const timeOut = setTimeout(() => {
              const salaryOffered = values?.salaryOffered
                ? moneyToNumber(String(values?.salaryOffered))
                : null;
              const rate =
                values?.salaryOfferedExchangeRate &&
                values?.currencyOfferedTypeSelected.value !== "VND"
                  ? moneyToNumber(String(values?.salaryOfferedExchangeRate))
                  : 1;

              const salaryConvert = salaryOffered
                ? Number(salaryOffered) * rate
                : null;

              refPayment.current?.setFieldValue("salaryConvert", salaryConvert);

              if (
                values?.paymentRateTypeSelected?.value === "0" &&
                salaryConvert &&
                values?.paymentRate
              ) {
                const sumAmount =
                  (salaryConvert * moneyToNumber(String(values?.paymentRate))) /
                  100;
                refPayment.current?.setFieldValue("totalAmount", sumAmount);
                if (values?.currencyOfferedTypeSelected.value === "VND") {
                  refPayment.current?.setFieldValue(
                    "salaryOfferedExchangeRate",
                    0
                  );
                }
              }
            }, deadTimeFastSearch);

            return () => {
              clearTimeout(timeOut);
            };
          }, [
            values?.salaryOffered,
            values?.salaryOfferedExchangeRate,
            values?.currencyOfferedTypeSelected,
          ]);

          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            const timeOut = setTimeout(() => {
              if (values?.paymentRateTypeSelected?.value === "0") {
                if (values.salaryConvert && values?.paymentRate) {
                  const sumAmount =
                    (values.salaryConvert *
                      moneyToNumber(String(values?.paymentRate))) /
                    100;
                  refPayment.current?.setFieldValue("totalAmount", sumAmount);
                }
              } else {
                refPayment.current?.setFieldValue(
                  "totalAmount",
                  values?.paymentRate
                );
              }
            }, deadTimeFastSearch);
            return () => {
              clearTimeout(timeOut);
            };
          }, [values?.paymentRate]);

          // eslint-disable-next-line react-hooks/rules-of-hooks
          const disablePaymentDateExpected = useCallback(
            (current: any): boolean => {
              if (!values.onboardDate) {
                return false;
              }
              return (
                current && current < moment(values.onboardDate).startOf("day")
              );
            },
            [values.onboardDate]
          );

          return (
            <div>
              <Row className="px=15">
                <Col span={12} className="pr-3">
                  <AppDatePicker
                    classNameContainer="mt-2"
                    name="onboardDate"
                    label="Ngày onboard"
                    format={DATE_FORMAT}
                    valueAppDatePicker={values?.onboardDate}
                    free={!values?.onboardDate}
                    required
                    status={values?.onboardDate ? undefined : "error"}
                    disabledDate={disableOnboardDate}
                    onChange={onChangeOnboardDate}
                  />
                  <Row className="mt-2 input-salary">
                    <TextInput
                      containerclassname="flex-1"
                      label="Lương offer"
                      name="salaryOffered"
                      onlynumber
                      iscurrency
                      value={values?.salaryOffered}
                      free={!values?.salaryOffered}
                      typeInput="salary"
                      maxLength={50}
                      required
                      status={values?.salaryOffered ? undefined : "error"}
                    />
                    <AppSelectCurrency
                      name="currencyOfferedTypeSelected"
                      value={values?.currencyOfferedTypeSelected}
                      style={{width: "90px"}}
                    />
                  </Row>
                  <TextInput
                    containerclassname="mt-2"
                    label="Tỷ giá"
                    name="salaryOfferedExchangeRate"
                    onlynumber
                    iscurrency
                    value={values?.salaryOfferedExchangeRate}
                    free={!values?.salaryOfferedExchangeRate}
                    typeInput="salary"
                    maxLength={50}
                    disabled={
                      values?.currencyOfferedTypeSelected?.value === "VND"
                    }
                    required={
                      values?.currencyOfferedTypeSelected?.value !== "VND"
                    }
                    status={
                      values?.currencyOfferedTypeSelected?.value !== "VND" &&
                      !values?.salaryOfferedExchangeRate
                        ? "error"
                        : undefined
                    }
                  />
                  <Row className="mt-2 input-salary">
                    <TextInput
                      containerclassname="flex-1"
                      label="Số tiền quy đổi được"
                      name="salaryConvert"
                      onlynumber
                      value={values?.salaryConvert}
                      free={!values?.salaryConvert}
                      typeInput="salary"
                      iscurrency
                      maxLength={50}
                      disabled
                    />
                    <AppSelectCurrency
                      name="currencyConvert"
                      value="VND"
                      style={{width: "90px"}}
                      disabled
                    />
                  </Row>
                </Col>
                <Col span={12} className="pl-3">
                  <AppDatePicker
                    classNameContainer="mt-2"
                    name="paymentDateExpected"
                    label="Ngày thanh toán dự kiến"
                    format={DATE_FORMAT}
                    valueAppDatePicker={values?.paymentDateExpected}
                    free={!values?.paymentDateExpected}
                    required
                    status={values?.paymentDateExpected ? undefined : "error"}
                    disabledDate={disablePaymentDateExpected}
                  />
                  <SelectInput
                    containerclassname="mt-2"
                    name="monthWarrantySelected"
                    labelselect="Số tháng bảo hành"
                    data={optionGuarantee}
                    value={values?.monthWarrantySelected}
                    required
                  />
                  <Row className="mt-2 input-salary">
                    <TextInput
                      containerclassname="flex-1"
                      label="Thưởng giới thiệu"
                      name="paymentRate"
                      onlynumber
                      value={values?.paymentRate}
                      free={!values?.paymentRate}
                      typeInput={
                        values?.paymentRateTypeSelected?.value === "1"
                          ? "salary"
                          : ""
                      }
                      iscurrency={
                        values?.paymentRateTypeSelected?.value === "1"
                      }
                      maxLength={50}
                      required
                      status={values?.paymentRate ? undefined : "error"}
                    />
                    <AppSelectCurrency
                      name="paymentRateTypeSelected"
                      value={values?.paymentRateTypeSelected}
                      style={{width: "100px"}}
                      options={statusRateCurrencyPercent}
                      onChange={onChangeRateType}
                    />
                  </Row>
                  <Row className="mt-2 input-salary">
                    <TextInput
                      containerclassname="flex-1"
                      label="Tổng tiền"
                      name="totalAmount"
                      onlynumber
                      value={values?.totalAmount}
                      free={!values?.totalAmount}
                      typeInput="salary"
                      iscurrency
                      maxLength={100}
                      disabled
                    />
                    <AppSelectCurrency
                      name="currencyTotalAmount"
                      value="VND"
                      style={{width: "100px"}}
                      disabled
                    />
                  </Row>
                </Col>
              </Row>
              <Row className="flex justify-center mt-10 items-center">
                <AppButton
                  classrow="mr-2 w-40 btn-cancel"
                  label="Hủy"
                  typebutton="primary"
                  onClick={handleClose}
                />
                <AppButton
                  onClick={createPayment}
                  classrow="ml-2 w-40"
                  label="Lưu"
                  typebutton="primary"
                  disabled={
                    checkPartnerPaymentExist.isLoading ||
                    createPartnerPayment.isLoading
                  }
                />
              </Row>
            </div>
          );
        }}
      </Formik>
    </AppModal>
  );
}
