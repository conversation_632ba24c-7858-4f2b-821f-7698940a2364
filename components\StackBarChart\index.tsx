import {
  Chart,
  ChartAxis,
  ChartBar,
  ChartStack,
  ChartVoronoiContainer,
} from "@patternfly/react-charts";

interface BarData {
  name: string;
  x: string;
  y: number;
}

interface StackBarChart {
  processingData: BarData[];
  passData: BarData[];
  pendingData: BarData[];
  failData: BarData[];
  cancelData: BarData[];
  onBoardData: BarData[];
  // onBoardDoneData: BarData[];
}

export function StackBarChart(props: StackBarChart): JSX.Element {
  const {
    processingData,
    passData,
    pendingData,
    failData,
    cancelData,
    onBoardData,
    // onBoardDoneData,
  } = props;

  return (
    <div className="flex">
      <Chart
        colorScale={[
          "#2F6BFF",
          "#2F6BFF99",
          "#324054",
          "#FF00004D",
          "#F4B41A99",
          "#329932B2",
          "#9D9D9D80",
        ]}
        containerComponent={
          <ChartVoronoiContainer
            labels={({datum}) =>
              datum.name && datum.y !== 0 && datum.y
                ? `${datum.name}: ${datum.y}`
                : ""
            }
            constrainToVisibleArea
          />
        }
        domainPadding={{x: [50, 15]}}
        legendData={[
          {name: "Onboard done"},
          {name: "Onboard"},
          {name: "Cancel"},
          {name: "Fail"},
          {name: "Pending"},
          {name: "Pass"},
          {name: "Processing"},
        ]}
        legendOrientation="vertical"
        legendPosition="right"
        padding={{
          bottom: 50,
          left: 60,
          right: 150,
          top: 30,
        }}
      >
        <ChartAxis />
        <ChartAxis dependentAxis />
        <ChartStack
          colorScale={[
            "#9D9D9D80",
            "#329932B2",
            "#F4B41A99",
            "#FF00004D",
            "#324054",
            "#2F6BFF99",
            "#2F6BFF",
          ]}
        >
          <ChartBar barWidth={40} data={processingData} />
          <ChartBar barWidth={40} data={passData} />
          <ChartBar barWidth={40} data={pendingData} />
          <ChartBar barWidth={40} data={failData} />
          <ChartBar barWidth={40} data={cancelData} />
          <ChartBar barWidth={40} data={onBoardData} />
          {/* <ChartBar barWidth={40} data={onBoardDoneData} /> */}
        </ChartStack>
      </Chart>
    </div>
  );
}
