.sidebar {
  .div-user {
    display: flex;
    justify-content: center;
    padding: 8px 4px;
    margin: 8px 4px;
    border-width: 1px;
    border-radius: 12px;
  }

  .div-avatar {
    svg {
      margin-top: -7px;
    }
  }

  .telegram {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 8px 8px 24px 8px;
    width: 100%;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    text-align: center;
    p {
      text-align: center;
      margin-bottom: 8px;
    }
  }

  .ant-menu-title-content {
    font-weight: 700;
  }

  .sidebar-item {
    margin: 4px 0 4px 0 !important;
  }

  .menu-sidebar-container {
    max-height: 69%;
    overflow: auto;
    box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;

    &__menu-item {
      padding: 4px 8px;
      width: 100%;
      border-radius: 8px;
      font-weight: 700;
      color: #324054;

      &:hover {
        background-color: rgba(255, 0, 0, 0.3);
        color: #324054;
        opacity: 0.6;
      }
    }

    &__menu-item-link {
      display: inline-block;
      padding: 4px 6px 4px 3px;
      width: 100%;
      border-radius: 8px;
    }

    .ant-collapse-content > .ant-collapse-content-box {
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .menu-sidebar-container__menu {
    background-color: white;
  }

  .menu-item-active {
    background-color: rgba(255, 0, 0, 0.3);
  }

  .ant-collapse-header-text {
    padding: 6px 8px;
    border-radius: 8px !important;
    margin: 0 4px;
    color: #324054;

    &:hover {
      background-color: rgba(255, 0, 0, 0.3);
      opacity: 0.6;
    }
  }

  .panel-active {
    .ant-collapse-header-text {
      background-color: rgba(255, 0, 0, 0.3);
    }
  }

  .ant-collapse-header {
    border-radius: 8px !important;
  }

  .ant-collapse > .ant-collapse-item {
    border: none;
  }

  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0px;
  }
}
