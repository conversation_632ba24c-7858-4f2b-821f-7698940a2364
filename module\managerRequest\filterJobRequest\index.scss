.filter-job-form {
  .btn-filter {
    button {
      font-size: 14px;
      font-weight: 400;
      border: 1px dashed $header_tf;
      border-radius: 8px;
      display: flex;
      align-items: center;
      align-self: center;
      color: $text-color-input;
    }
  }

  .search-form-filter-job {
    width: 312px;
  }

  .div-time {
    display: grid;
    gap: 8px;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .add-btn {
    span {
      color: $white-color;
      margin-left: 4px;
    }
    .ant-btn {
      border-radius: 10px;
      background-color: $primary-color;
    }
  }
}

.list-checkbox {
  width: 320px;
}
