import AppButton from "@app/components/AppButton";
import AppDatePicker from "@app/components/AppDatePicker";
import Icon from "@app/components/Icon/Icon";
import {SelectInput} from "@app/components/SelectInput";
import {TextInput} from "@app/components/TextInput";
import {
  activeDateOptions,
  deadTimeFastSearch,
  statusCollaborator,
} from "@app/utils/constants/state";
import {Checkbox, Col, Popover, Row} from "antd";
import {Formik, FormikProps} from "formik";
import React, {useEffect, useRef, useState} from "react";
import "./index.scss";
import type {CheckboxValueType} from "antd/es/checkbox/Group";
import AppCheckBox from "@app/components/AppCheckbox";
import {DATE_FORMAT} from "@app/utils/constants/formatDateTime";
import moment from "moment";
import {IParamsCollaborator, IPartnerPaging} from "@app/api/ApiCollaborator";
import {ColumnsType} from "antd/es/table";
import {ActiveDateEnum, OptionSelect} from "@app/types";
import {listColDefaultCollaborator} from "..";

interface IItemCol {
  label: string;
  value: string;
}
interface FilterCollaboratorProp {
  listColShow: string[];
  setListColShow: (listColShow: string[]) => void;
  setValuesFilter: (values: IParamsCollaborator) => void;
  columnAll: ColumnsType<IPartnerPaging>;
  managerFilters: OptionSelect[];
  isCSL?: boolean;
  valuesFilter: IParamsCollaborator;
  initialValuesFilterCollaborator: IParamsCollaborator;
}

export default function FilterCollaborator(
  props: FilterCollaboratorProp
): JSX.Element {
  const {
    listColShow,
    setListColShow,
    setValuesFilter,
    columnAll,
    managerFilters,
    isCSL,
    valuesFilter,
    initialValuesFilterCollaborator,
  } = props;
  const [isShowFilterAdvance, setIsShowFilterAdvance] = useState(false);
  const timeOut = useRef<any>();
  const filterAdvancedRef = useRef<FormikProps<IParamsCollaborator>>(null);

  const listColLeft = columnAll
    .slice(1, 7)
    .map((i) => ({label: i.title, value: i.key})) as IItemCol[];

  const listColRight = columnAll
    .slice(7)
    .map((i) => ({label: i.title, value: i.key})) as IItemCol[];

  const managerSelected =
    managerFilters.filter((item) =>
      valuesFilter.managers?.some((i) => String(i) === String(item.value))
    ) || [];

  const initValue: IParamsCollaborator = {
    ...valuesFilter,
    managerSelected,
    statusSelected:
      statusCollaborator.filter((item) =>
        valuesFilter.statuses?.includes(Number(item.id))
      ) || [],
    from: valuesFilter.from ? moment(valuesFilter.from, DATE_FORMAT) : "",
    to: valuesFilter.to ? moment(valuesFilter.to, DATE_FORMAT) : "",
  };

  useEffect(() => {
    filterAdvancedRef.current?.setFieldValue(
      "managerSelected",
      managerSelected
    );
  }, [JSON.stringify(managerFilters)]);

  useEffect(() => {
    return () => {
      // eslint-disable-next-line no-unused-expressions
      timeOut.current && clearTimeout(timeOut.current);
    };
  }, [timeOut.current]);

  const disableDate = (current: any): any => {
    return current && current > moment().endOf("day");
  };

  const validateTime = (): boolean => {
    const from = filterAdvancedRef.current?.values?.from;
    const to = filterAdvancedRef.current?.values?.to;
    return !from || !to || moment(from).isSameOrBefore(moment(to));
  };

  const getTimeActiveDate = (rangeActive: ActiveDateEnum) => {
    const from = moment();
    const to = moment();

    const subtractMap: Record<
      ActiveDateEnum,
      [number, moment.unitOfTime.DurationConstructor]
    > = {
      [ActiveDateEnum.SEVEN_DATE]: [7, "days"],
      [ActiveDateEnum.ONE_MONTH]: [1, "months"],
      [ActiveDateEnum.THREE_MONTH]: [3, "months"],
      [ActiveDateEnum.SIX_MONTH]: [6, "months"],
      [ActiveDateEnum.ONE_YEAR]: [1, "years"],
    };

    const subtractValue = subtractMap[rangeActive];

    if (subtractValue) {
      from.subtract(...subtractValue);
    }

    return {
      from: from.format(DATE_FORMAT),
      to: to.format(DATE_FORMAT),
    };
  };

  const handleSearch = () => {
    const values = filterAdvancedRef.current?.values;

    const activeDateValue = filterAdvancedRef.current?.values?.activeDate
      ?.value as ActiveDateEnum;

    const activeDate: {
      activeDateFrom: string | null;
      activeDateTo: string | null;
    } = {
      activeDateFrom: null,
      activeDateTo: null,
    };

    if (activeDateValue) {
      const rangeActiveDate = getTimeActiveDate(activeDateValue);
      activeDate.activeDateFrom = rangeActiveDate.from;
      activeDate.activeDateTo = rangeActiveDate.to;
    }

    setValuesFilter({
      ...values,
      ...activeDate,
      statuses: values?.statusSelected?.map((i: any) => i.key) || [],
      managers: values?.managerSelected?.map((i: any) => i.key) || [],
      from: values?.from ? moment(values.from).format(DATE_FORMAT) : "",
      to: values?.to ? moment(values.to).format(DATE_FORMAT) : "",
      currentPage: 1,
      textSearch: values?.textSearch?.trim() || "",
      pageSize: valuesFilter.pageSize,
    });
  };

  const onChangeTextSearch = (e: any): void => {
    timeOut.current = setTimeout(() => {
      handleSearch();
    }, deadTimeFastSearch);
  };

  const onClickSearch = (): void => {
    if (!validateTime()) {
      return;
    }
    handleSearch();
  };

  const resetData = (): void => {
    filterAdvancedRef?.current?.setValues(initialValuesFilterCollaborator);
    setValuesFilter({
      ...initialValuesFilterCollaborator,
      statuses: [],
      managers: [],
      isAdvanceSearch: false,
    });
    setIsShowFilterAdvance(false);
  };

  const onChangeListCol = (checkedValues: CheckboxValueType[]): void => {
    setListColShow(checkedValues as string[]);
  };

  const filterCol = (
    <Checkbox.Group
      className="group-check-box-list-col"
      value={listColShow}
      onChange={onChangeListCol}
    >
      <Row>
        <Col span={12}>
          {listColLeft.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
        <Col span={12}>
          {listColRight.map((itemCheck, index) => (
            <AppCheckBox className="w-full" key={index} value={itemCheck.value}>
              {itemCheck.label}
            </AppCheckBox>
          ))}
        </Col>
      </Row>
    </Checkbox.Group>
  );

  function contentFilterAdvance(values: IParamsCollaborator): JSX.Element {
    return (
      <div className="content-filter-advance-collaborator">
        <Row className="flex items-center justify-between mb-4">
          <span className="title-filter">Tất cả bộ lọc</span>
          <AppButton
            classrow="btn-close-popover"
            typebutton="normal"
            onClick={(): void => setIsShowFilterAdvance(false)}
          >
            <Icon className="" icon="close-circle-line" size={20} />
          </AppButton>
        </Row>
        <TextInput
          containerclassname="mt-2"
          label="Họ và tên"
          placeholder="Nhập họ và tên cộng tác viên"
          name="name"
          value={values.name}
          free={!values.name}
        />
        <TextInput
          containerclassname="mt-2"
          label="Email"
          placeholder="Nhập email cộng tác viên"
          name="email"
          value={values.email}
          free={!values.email}
        />
        <TextInput
          containerclassname="mt-2"
          label="Số điện thoại"
          placeholder="Nhập số điện thoại cộng tác viên"
          name="phoneNumber"
          value={values.phoneNumber}
          free={!values.phoneNumber}
          isphonenumber
        />

        <SelectInput
          containerclassname="mt-2"
          name="statusSelected"
          mode="multiple"
          labelselect="Trạng thái"
          data={statusCollaborator}
          value={values.statusSelected}
          free={values?.statusSelected?.length === 0}
          allowClear
        />
        {isCSL && (
          <SelectInput
            containerclassname="mt-2"
            name="managerSelected"
            mode="multiple"
            labelselect="CST Quản lý"
            data={managerFilters}
            value={values.managerSelected}
            free={values?.managerSelected?.length === 0}
            allowClear
            optionFilterProp="label"
          />
        )}
        <SelectInput
          containerclassname="mt-2"
          name="activeDate"
          labelselect="Thời gian hoạt động"
          data={activeDateOptions}
          value={values.activeDate}
          allowClear
        />
        <Row className="mt-2 div-time">
          <AppDatePicker
            name="from"
            label="Từ"
            format={DATE_FORMAT}
            free={!values.from}
            disabledDate={disableDate}
            status={!validateTime() ? "error" : ""}
            allowClear
            valueAppDatePicker={values.from}
          />
          <AppDatePicker
            name="to"
            label="Đến"
            format={DATE_FORMAT}
            free={!values.to}
            disabledDate={disableDate}
            status={!validateTime() ? "error" : ""}
            allowClear
            valueAppDatePicker={values.to}
          />
        </Row>
        <Row className="mt-6 div-time">
          <AppButton
            label="Xoá tất cả"
            typebutton="secondary"
            onClick={resetData}
          />
          <AppButton
            label="Tìm kiếm"
            typebutton="primary"
            onClick={onClickSearch}
          />
        </Row>
      </div>
    );
  }

  return (
    <Formik
      initialValues={initValue}
      innerRef={filterAdvancedRef}
      onSubmit={(): void => {
        //
      }}
    >
      {({values}): JSX.Element => {
        return (
          <Row className="flex justify-between filter-collaborator-container">
            <TextInput
              containerclassname="w-72"
              label="Nhập họ tên, sđt, email CTV"
              name="textSearch"
              value={values.textSearch}
              onChange={onChangeTextSearch}
            />
            <Row>
              <Popover
                className="mr-1"
                placement="bottom"
                trigger="click"
                content={(): React.ReactNode => contentFilterAdvance(values)}
                open={isShowFilterAdvance}
                onOpenChange={setIsShowFilterAdvance}
              >
                <AppButton typebutton="normal" classrow="btn-filter">
                  <Icon
                    className="mr-1"
                    icon="filter-line"
                    size={12}
                    color="#324054"
                  />
                  Tìm kiếm nâng cao
                </AppButton>
              </Popover>
              <Popover placement="bottom" trigger="click" content={filterCol}>
                <AppButton typebutton="normal" classrow="btn-filter">
                  <Icon className="mr-1" icon="eye" size={16} color="#324054" />
                  Hiển thị{" "}
                  {listColShow.length + listColDefaultCollaborator.length}/
                  {listColLeft.length +
                    listColRight.length +
                    listColDefaultCollaborator.length}
                </AppButton>
              </Popover>
            </Row>
          </Row>
        );
      }}
    </Formik>
  );
}
