import {DatePicker, Form, ModalProps} from "antd";
import "./index.scss";
import React, {useMemo, useState} from "react";
import AppModal from "@app/components/AppModal";
import {useQuery} from "react-query";
import ApiPaymentBonus from "@app/api/ApiPaymentBonus";
import {DATE_FORMAT, YEAR_FORMAT} from "@app/utils/constants/formatDateTime";
import {getPaymentStatus} from "@app/utils/constants/function";
import moment, {Moment} from "moment";
import ModalUpdatePayment from "../../ModalUpdatePayment";

interface Props extends ModalProps {
  valueSearch: {
    partnerId: number;
    partnerName: string;
    consultantName: string;
  };
  title: string;
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
}

interface DataFormPayment {
  amount: number | string;
  paymentDate: string | Moment;
  paymentType: string;
  note: string;
  paymentBonusId: number;
}

function ModalDetailPerformance(props: Props): JSX.Element {
  const {valueSearch, title, onOk, onCancel, open} = props;
  const [selectBonus, setSelectBonus] = useState<DataFormPayment>(
    {} as DataFormPayment
  );
  const [showModalPayment, setShowModalPayment] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [year, setYear] = useState<string>(moment().format(YEAR_FORMAT));

  const handleCancel = (): void => {
    onCancel();
    form.setFieldValue("year", moment(new Date(), YEAR_FORMAT));
    setYear(moment().format(YEAR_FORMAT));
  };

  const getApplicationsByPartner = useQuery(
    ["getApplicationsByPartner", valueSearch, year],
    () => {
      return ApiPaymentBonus.getApplicationsByPartner({
        IsFirstLoad: true,
        currentPage: 1,
        from: `01/01/${year}`,
        isFirstInitialization: true,
        partnerId: valueSearch.partnerId,
        partnerName: valueSearch.partnerName,
        to: `31/12/${year}`,
      });
    },
    {
      enabled: valueSearch.partnerId !== -1 && open,
    }
  );

  const getPerformanceBonusByPartner = useQuery(
    ["getPerformanceBonusByPartner", valueSearch, year],
    () => {
      return ApiPaymentBonus.getPerformanceBonusByPartner({
        currentPage: 1,
        from: "",
        isFirstInitialization: true,
        partnerId: valueSearch.partnerId,
        partnerName: valueSearch.partnerName,
        to: "",
        year: year,
      });
    },
    {
      enabled: valueSearch.partnerId !== -1 && open,
    }
  );

  const getPaymentBonusPolicy = useQuery(
    ["getPaymentBonusPolicy"],
    () => {
      return ApiPaymentBonus.getPaymentBonusPolicy();
    },
    {
      enabled: valueSearch.partnerId !== -1,
    }
  );

  const amountCalculatorExpected = useMemo(() => {
    if (getApplicationsByPartner.data && getPaymentBonusPolicy.data) {
      const {
        totalInterviewedAmount,
        totalInterviewedRemain,
        totalOnboardDoneAmount,
        totalOnboardDoneRemain,
        totalReviewPassedAmount,
        totalReviewPassedRemain,
      } = getApplicationsByPartner.data;
      const {bonusInterviewed, bonusOnboardDone, bonusReviewed} =
        getPaymentBonusPolicy.data;
      const totalAmountReceived: number =
        totalInterviewedAmount +
        totalOnboardDoneAmount +
        totalReviewPassedAmount;
      const totalAmount: number =
        (totalReviewPassedRemain * bonusReviewed +
          totalInterviewedRemain * bonusInterviewed +
          totalOnboardDoneRemain * bonusOnboardDone) *
          1000000 +
        totalAmountReceived;
      return {
        totalAmount: totalAmount,
        totalAmountReceived: totalAmountReceived,
      };
    }
    return {
      totalAmount: 0,
      totalAmountReceived: 0,
    };
  }, [getApplicationsByPartner.data, getPaymentBonusPolicy.data]);

  const numberToMoney = (value: number): string => {
    if (!value) return "0";
    return new Intl.NumberFormat("en-US").format(value);
  };

  const performanceSummary = useMemo(() => {
    const initValue = ["0 Processing", "0 Pass", "0 Pending", "0 Cancel/Fail"];
    if (getApplicationsByPartner.data) {
      const {
        interviewNULL,
        interviewPass,
        interviewPending,
        interviewFail,
        interviewCancel,
        offerCancel,
        offerFail,
        offerNULL,
        offerPass,
        offerPending,
        reviewCancel,
        reviewFail,
        reviewNULL,
        reviewPass,
        reviewPending,
        offerOnboard,
        offerOnboardDone,
        onboardFail,
      } = getApplicationsByPartner.data;

      const valueExpect = [
        {
          label: "Review",
          total:
            reviewCancel + reviewFail + reviewNULL + reviewPass + reviewPending,
          value: [
            `${reviewNULL} Processing`,
            `${reviewPass} Pass`,
            `${reviewPending} Pending`,
            `${reviewCancel + reviewFail} Cancel/Fail`,
          ],
        },
        {
          label: "Interview",
          total:
            interviewCancel +
            interviewNULL +
            interviewPending +
            interviewFail +
            interviewPass,
          value: [
            `${interviewNULL} Processing`,
            `${interviewPass} Pass`,
            `${interviewPending} Pending`,
            `${interviewFail + interviewCancel} Cancel/Fail`,
          ],
        },
        {
          label: "Offer",
          total: offerCancel + offerFail + offerNULL + offerPass + offerPending,
          value: [
            `${offerNULL} Processing`,
            `${offerPass} Pass`,
            `${offerPending} Pending`,
            `${offerCancel + offerFail} Cancel/Fail`,
          ],
        },
        {
          label: "Onboard",
          total: offerOnboard + offerOnboardDone + onboardFail,
          value: [
            `${offerOnboard} Onboard`,
            `${offerOnboardDone} Onboard Pass`,
            `${onboardFail} Onboard Fail`,
          ],
        },
      ];
      return valueExpect;
    }
    return [
      {
        label: "Review",
        total: 0,
        value: initValue,
      },
      {
        label: "Interview",
        total: 0,
        value: initValue,
      },
      {
        label: "Offer",
        total: 0,
        value: initValue,
      },
      {
        label: "Onboard",
        total: 0,
        value: initValue,
      },
    ];
  }, [getApplicationsByPartner.data]);

  const onRefresh = (): void => {
    onOk();
    getApplicationsByPartner.refetch();
    getPerformanceBonusByPartner.refetch();
    getPaymentBonusPolicy.refetch();
  };

  return (
    <AppModal
      open={open}
      title={title}
      onCancel={handleCancel}
      centered
      footer={null}
      width="70%"
    >
      <div className="detail-performance-ui flex flex-row flex-nowrap text-color-primary">
        <div className="detail-performance-ui__information w-1/2">
          <p className="text24 font-bold">
            {valueSearch?.partnerName || "N/A"}
          </p>
          <p className="text16 font-bold">
            CST quản lý: {valueSearch.consultantName || "N/A"}
          </p>
          <hr className="my-4" />
          <div className="flex justify-between mt-2">
            <p className="text16 font-bold">Thông tin thanh toán</p>
            <div className="flex items-center">
              <Form form={form} className="flex items-center">
                <div className="mr-2">Năm:</div>
                <Form.Item name="year">
                  <DatePicker
                    picker="year"
                    format={YEAR_FORMAT}
                    placeholder=""
                    defaultValue={moment()}
                    onChange={(): void => {
                      setYear(
                        moment(form.getFieldValue("year")).format(YEAR_FORMAT)
                      );
                    }}
                    allowClear={false}
                  />
                </Form.Item>
              </Form>
            </div>
          </div>
          <div className="flex flex-nowrap mt-8">
            <div className="w-1/2">
              <p className="text16">Tổng tiền bonus</p>
              <p className="text20 mt-1 ml-4">
                <span className="font-medium text24">
                  {numberToMoney(amountCalculatorExpected?.totalAmount)}
                </span>
                <span className="ml-1 text16">VND</span>
              </p>
            </div>
            <div className="w-1/2">
              <p className="text16">Số tiền bonus đã nhận</p>
              <p className="text20 mt-1 ml-4">
                <span className="font-medium text24">
                  {numberToMoney(amountCalculatorExpected.totalAmountReceived)}
                </span>
                <span className="ml-1 text16">VND</span>
              </p>
            </div>
          </div>
          <div className="mt-4">
            {performanceSummary.map((item, index) => (
              <div key={index} className="mt-3">
                <span className="text14 font-bold">
                  {item.label}{" "}
                  <span className="font-thin text12">({item.total} CV)</span>
                </span>
                <div className="flex justify-between mt-3 text12 px-4">
                  {item.value.map((i, index) => (
                    <p key={index}>{i}</p>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="detail-performance-ui__history w-1/2 flex flex-col ">
          <p className="text16 font-bold text-color-primary mt-3 ml-4">
            Lịch sử Bonus
          </p>
          <div className="detail-performance-ui__history-list flex-1">
            {getPerformanceBonusByPartner?.data?.dashboardPagingDatas?.map(
              (item, index) => (
                <div
                  key={index}
                  className="detail-performance-ui__history-card"
                >
                  <div className="flex justify-between">
                    <span className="text16">{item?.subTypeName || "N/A"}</span>
                    <div className="flex items-center">
                      <div
                        className="payment-status"
                        style={{
                          backgroundColor: getPaymentStatus(item.paymentStatus)
                            .color,
                        }}
                      />
                      <p className="text12">
                        {getPaymentStatus(item.paymentStatus).label}
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-between items-center mt-2 text12">
                    <div>
                      <p className="mt-1">{`Hình thức thanh toán: ${
                        item?.paymentMethod || "N/A"
                      }`}</p>
                      {/* */}

                      <p className="mt-1">{`Ngày thanh toán: ${
                        item?.paymentDate
                          ? moment(item?.paymentDate).format(DATE_FORMAT)
                          : "N/A"
                      }`}</p>
                    </div>
                    <p>{item?.amount ? numberToMoney(item?.amount) : ""}</p>
                  </div>
                  <div className="text12 mt-1">
                    <p>Ghi chú:</p>
                    <p>{item?.note || "N/A"}</p>
                  </div>
                  <div className="flex justify-end mt-2">
                    {item?.paymentStatus === 0 && (
                      <div
                        className="detail-performance-ui__history-card-btn text12"
                        role="button"
                        tabIndex={-1}
                        onClick={(): void => {
                          setSelectBonus({
                            amount: item?.amount,
                            note: item?.note || "",
                            paymentDate: item?.paymentDate
                              ? moment(item?.paymentDate, DATE_FORMAT)
                              : "",
                            paymentType: item?.paymentMethod || "",
                            paymentBonusId: item?.paymentBonusId,
                          });
                          setShowModalPayment(true);
                        }}
                      >
                        Thanh toán
                      </div>
                    )}
                  </div>
                </div>
              )
            )}
          </div>
          <div className="flex justify-end mt-2 detail-performance-ui__history-money">
            <div>
              <p className="text16">
                Đã thanh toán:{" "}
                {numberToMoney(amountCalculatorExpected.totalAmountReceived)}
              </p>
              <p className="text16 font-bold">
                Số tiền còn lại:{" "}
                {numberToMoney(
                  amountCalculatorExpected.totalAmount -
                    amountCalculatorExpected.totalAmountReceived
                )}
              </p>
            </div>
          </div>
        </div>
        <ModalUpdatePayment
          open={showModalPayment}
          title="Chi tiết thanh toán"
          onCancel={(): void => setShowModalPayment(false)}
          dataPayment={selectBonus}
          refreshList={onRefresh}
        />
      </div>
    </AppModal>
  );
}

export default React.memo(ModalDetailPerformance);
